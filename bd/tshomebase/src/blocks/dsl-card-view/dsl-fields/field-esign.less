#application {
    .fieldgroup {
        .form-horizontal {
            .form-group {

                &:has(.esignfield) {
                    .control-label {
                        width: auto;
                        max-width: calc(100% - 50px);
                    }
                }

                .controls {
                    >.esignfield {
                        outline: 1px solid var(--gray-100);
                        border-radius: var(--radius-medium);
                        background-color: var(--white);
                        box-shadow: 0px 1px 2px 1px #38383814 inset;
                        min-height: 60px;
                        padding: var(--spacing-large);
                        padding-right: var(--spacing-xxxlarge);
                        cursor: default;

                        .esign-link {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            gap: var(--spacing-large);
                            text-decoration: none;
                            cursor: default;
                            min-height: 40px;
                            max-height: 100px;

                            .esign-text-container {
                                >div:first-child {
                                    color: var(--brand-500);
                                    font-size: var(--font-size-small);
                                    font-weight: var(--font-weight-regular);
                                    translate: 0px 12px;
                                    cursor: pointer;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }

                            .esign-image {
                                display: none;
                            }

                            .edit-esign-icon {
                                opacity: 0.5;
                                cursor: not-allowed;
                                width: 24px;
                                height: 24px;
                                background-image: url("../../../public/icons/common/outlined/edit-esign-outline.svg");
                                background-repeat: no-repeat;
                                background-size: contain;
                            }

                        }

                        .esign-btn-prefill {
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }

                        &:hover {
                            outline: 2px solid var(--blue-200);
                            box-shadow: 0px 0px 1px 0px #0B1F350F, 0px 1px 2px -1px #0B1F350A;

                            .esign-link {
                                .edit-esign-icon {
                                    background-image: url('../../../public/icons/common/outlined/edit-esign-outline-hover.svg');
                                }
                            }
                        }

                    }
                }

                &:has(.has-signed) {
                    .control-label {
                        top: 30px;
                    }

                    .controls {
                        >.esignfield {
                            min-height: 120px;

                            .esign-text-container {
                                translate: 0px 15px;
                                line-height: var(--line-height-standard);

                                >div:first-child {
                                    translate: 0px;
                                    color: var(--gray-700);
                                    font-weight: var(--font-weight-semibold);

                                    &:hover {
                                        cursor: default;
                                        text-decoration: none;
                                    }
                                }

                                div {
                                    color: var(--gray-500);
                                    font-weight: var(--font-weight-medium);
                                    font-size: var(--font-size-xxsmall);
                                }
                            }

                            .esign-image {
                                flex: 1;
                                height: 110px;
                                display: block;
                            }

                            .edit-esign-icon {
                                opacity: 1;
                                cursor: pointer;
                            }

                            .esign-btn-prefill {
                                display: none !important;
                            }
                        }
                    }
                }

                &.has-error {
                    .controls {
                        >.esignfield {
                            outline: 2px solid var(--color-error);
                            box-shadow: 0px 0px 1px 0px #43130F0F, 0px 1px 2px -1px #43130F0A;

                            .esign-link {
                                .esign-text-container {
                                    >div:first-child {
                                        color: var(--color-error);
                                    }
                                }

                                .edit-esign-icon {
                                    opacity: 1;
                                    cursor: pointer;
                                    background-image: url('../../../public/icons/common/outlined/edit-esign-outline-error.svg');
                                }
                            }
                        }
                    }
                }

                &:has(.esignfield.readonly) {


                    .controls {
                        cursor: not-allowed;

                        * {
                            cursor: not-allowed !important;
                        }

                        .esignfield {
                            outline: none;
                            box-shadow: none;

                            .control-label {
                                cursor: not-allowed;
                            }

                            .esign-text-container {
                                >div:first-child {
                                    cursor: not-allowed !important;

                                    &:hover {
                                        text-decoration: none !important;
                                    }
                                }
                            }

                            .esign-link {
                                .edit-esign-icon {
                                    display: none;
                                }
                            }

                            .esign-btn-prefill {
                                display: none !important;
                            }
                        }
                    }
                }
            }

            &.form-col-3 {
                .form-group {

                    .controls {
                        .esignfield {
                            .esign-btn-prefill {
                                transform: translate(20%, -50%);
                            }
                        }
                    }

                    &:has(.has-signed) {
                        .controls {
                            .esign-link {
                                min-height: 100px;

                                .esign-text-container {
                                    translate: 0 15px;
                                }

                                .esign-image {
                                    height: 45px;
                                }
                            }
                        }
                    }
                }
            }

            &.form-col-4 {
                .form-group {
                    .controls {
                        .esignfield {
                            .esign-btn-prefill {
                                top: unset;
                                left: 65%;
                                bottom: -8px;
                                padding: 0px 8px;
                                min-height: 25px;
                                font-weight: normal;
                            }
                        }
                    }
                }

                .form-group:has(.has-signed) {
                    .controls {
                        .esignfield {

                            .esign-link {
                                min-height: 100px;

                                .esign-text-container {
                                    translate: 0 15px;
                                }

                                .esign-image {
                                    height: 34px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}