import type { FC } from "react";
import React, { useEffect, useRef, useState } from "react";
import { blockUntilTimePassed, renderItemData, toPresentableLabel, uuid } from "@utils/fx";
import "./dsl-card-view.less";
import "./dsl-card-view.responsive.less";
import { useNavigation, UseNavigationProps } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";
import { ServerActionButtons, DSLCardActionButtonGroup, DSLActionBtnIcon } from "./dsl-card-action-btns";
import { joinPaths } from "@utils/fx";
import _ from "lodash";
import { IFormData, useUpdateHash, useOnScreen } from "@hooks/index";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import { TabData } from "../tab-list/tab-list";
import WizardBottomBar from "../wizard-bottom-bar";
import { TabViewActions } from "../dsl-tab-view/dsl-tab-view";
import PublicIcon from "@public/icons";
import { CRRequestOptions, request } from "@core/request/request";
import { PatientTabActions } from "@modules/patient/patient-snapshot";
import { normalizeNESEndpoint, sectionShowOnly, serverActionPerform } from "@utils/dsl-fx";
import { IDSLDrawSubForm } from "@typedefs/coffee/dsl-draw";
import { openListPopover } from "@blocks/dsl-list-view/list-popover";
import { DSLListViewProps } from "@blocks/dsl-list-view";
import { toast } from "@components/toast";
import { FormActiveUsers } from "@blocks/dsl-card-view/dsl-form-active-users";
import { SnapshotHeaderForm } from "@blocks/snapshot-header/snapshot-header";

export type TabModes = "read" | "edit" | "add" | "addfill";

export const CARD_MODES = ["read", "edit", "add", "addfill"];

export type TabClick =
	| "special_event"
	| "special_event_1"
	| "special_event_2"
	| "special_event_3"
	| "print"
	| "save"
	| "edit"
	| "verify"
	| "review"
	| "cancel"
	| "sections"
	| string;

export const TAB_CLICK_BTN_NAME: TabClick[] = [
	"special_event",
	"special_event_1",
	"special_event_2",
	"special_event_3",
	"special_event_4",
	"print",
	"save",
	"edit",
	"verify",
	"review",
	"cancel",
	"sections",
];

export type TabActionButton = {
	enabled?: boolean;
	label?: string;
	icon?: DSLActionBtnIcon;
	class?: string;
	override?: boolean;
};

export type TabActions = Partial<Record<TabClick, TabActionButton>>;

export type SavedFormDataObject = {
	record: string;
	values: IFormData;
	[key: string]: unknown;
};

export type DuelCardsRefs = Record<
	"master" | "slave",
	{
		name?: string;
		player?: string;
		position: "left" | "right";
		record: DSLCardViewProps["record"];
		form: string;
		wrapper: IDSLDrawForm | null;
	}
>;

export interface IDSLDrawForm {
	form_rendered: boolean;
	subforms: {
		formmap: Record<string, IDSLDrawSubForm>;
		recmap: Record<string, IDSLDrawSubForm>; // TODO Write correct type for recmap
		forms: IDSLDrawSubForm[];
	};
	options: {
		parent: {
			link: string;
			links: Record<string, string>;
			linkid: string;
		};
	};
	tab_do_cancel: (cwe?: boolean) => void;
	tab_do_refresh: (cwe?: boolean) => void;
	get_formdata: () => IFormData;
	count_missing_required_fields: () => void;
	save_form_values: (silent?: boolean) => IFormData | false;
	save_type_web: (dfd: JQuery.Deferred<unknown>, values: IFormData) => void;
	verify: () => Record<string, unknown>;
	duelCardRefs?: DuelCardsRefs;
}

export type DSLCardViewRef = {
	$: any;
	$el: JQuery;
	record: string | number;
	cardread: {
		tab_can_edit: () => boolean;
		tab_do_edit: () => void;
		tab_do_cancel: (cwe?: boolean) => void;
		tab_do_refresh: () => void;
		mode: TabModes;
		ddr: IDSLDrawForm;
	};
	validation_errors?: Record<string, unknown>;
	subscribe: (event: string, callback: (args: any) => void) => string;
	unsubscribe: (event: string, id?: string) => void;
	cardform: {
		tab_do_cancel: (cwe?: boolean) => void;
		tab_do_refresh: () => void;
		mode: TabModes;
		ddf: IDSLDrawForm;
	};
	tab_action: (action: string, args?: any) => boolean | string;
	tab_do: (action: string, args?: any) => void;
	mode: (mode: TabModes, record?: string | number) => void;
	scrolling: boolean;
	preset?: Record<string, unknown>;
	parent: DSLCardViewRef;
	options: any;
	form: string;
	tabData?: TabData;
	$cardform: JQuery;
	$cardread: JQuery;
	xid: string;
	curview: TabModes;
	card: TabModes;
	unload?: () => void;
};

export type DSLCardOnChangeModeCallback = (mode: TabModes, id: string, tab: TabData, ref: DSLCardViewRef) => void;

export type DSLCardOnArchiveCallBack = (fd: IFormData, tab: TabData, ref?: DSLCardViewRef) => void;

export type DSLCardOnUnArchiveCallBack = (fd: IFormData, tab: TabData, ref?: DSLCardViewRef) => void;

export type DSLCardOnSaveCallBack = (fd: SavedFormDataObject, tab: TabData, ref?: DSLCardViewRef) => void;

export type DSLCardOnCancelCallBack = (id: string, tab: TabData, ref?: DSLCardViewRef) => void;

interface DSLRendererProps extends Partial<RoutedComponentProps>, Partial<DSLDrawLinkMap> {
	card: TabModes;
	form: string;
	forceReadOnly?: boolean;
	record?: number | undefined | string;
	preset?: Record<string, unknown>;
	xid?: string;
	showSections?: string[];
	tabData?: TabData;
	allowModal?: boolean;
	isFlyout?: boolean;
	label?: string;
	autoRecoverEnabled?: boolean;
	form_override_url?: string;
	validation_errors?: Record<string, unknown>;
	setDSLRef?: (ref: DSLCardViewRef) => void;
	tabViewActions?: TabViewActions | PatientTabActions;
	triggerActionsChange?: () => void;
	onServerActions?: (actions: ServerActionButtons[], message?: string) => void;
	onFormReady?: () => void;
	actionButtonShowHide?: (action: string, show: boolean) => void;
	changeMode?: DSLCardOnChangeModeCallback;
	onSaved?: DSLCardOnSaveCallBack;
	onCancel?: DSLCardOnCancelCallBack;
	audit_mode?: boolean;
	closeWithOutAck?: boolean;
	tab_can_edit_override?: () => boolean;
	tab_can_archive_override?: () => boolean;
	tab_can_save_override?: () => boolean;
}

export interface DSLCardViewProps extends DSLRendererProps {
	routePrepend?: string;
	routeAppend?: string;
	disableRouting?: boolean;
	ddRef?: (ref: DSLCardViewRef) => void;
	configOpts?: Record<string, any>;
	overrideDos?: TabActions;
	tabData?: TabData | any;
	maxRowOne?: boolean;
	hideActionBtns?: boolean;
	PreviewComponent?: FC<any>;
	previewComponentProps?: Record<string, unknown>;
	flyoutAllowWidget?: boolean;
}

export const FORM_MODE: Record<string, string> = {
	read: "form-read-mode",
	edit: "form-edit-mode",
	add: "form-add-mode",
	addfill: "form-addfill-mode",
};

const DSLRenderer = React.memo(
	(props: DSLRendererProps) => {
		const dslCard = useRef<DSLCardViewRef | null>(null);
		const domUIDRef = useRef(uuid());
		const domUID = domUIDRef.current;

		const [isOnScreen, elementRef] = useOnScreen(true);
		const isRendered = useRef(false);

		useEffect(() => {
			if (isRendered.current) {
				return;
			}
			if (!isOnScreen) {
				return;
			}
			renderDSLCardCoffee(props.card, props.record, null);
			isRendered.current = true;
		}, [isOnScreen, props.isActive, props.form, props.record]);

		const renderDSLCardCoffee = async (
			card: TabModes,
			record?: string | number,
			ref: DSLCardViewRef | null = null
		) => {
			console.info(
				`%c DSL Fragmented Renderer %c Form: %c${props.form}%c Mode: %c${card}%c Record: %c${record || "New"}`,
				"background: #1a1a1a; color: #00bcd4; font-weight: bold; padding: 2px 6px; border-radius: 3px;",
				"color: #666;",
				"color: #2196f3; font-weight: bold;",
				"color: #666;",
				"color: #4caf50; font-weight: bold;",
				"color: #666;",
				"color: #ff9800; font-weight: bold;"
			);
			try {
				dslCard?.current?.unload?.();
			} catch (error) {
				console.error("DSLRendererNew: Error unloading DSLCard", error);
			}
			const loadingContainer = $("#" + domUID + "_loadingContainer");
			if (!loadingContainer.length) {
				$("#" + domUID).prepend(
					`<div id="${domUID}_loadingContainer" style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
						<i class="fa-duotone fa-solid fa-circle-notch fa-spin" style="font-size: 3em; color: grey;" aria-hidden="true"></i>
					</div>`
				);
				$("#" + domUID + "_view")
					.children()
					.hide();
			}
			const targetElement = document.getElementById(domUID + "_view");
			if (!targetElement) {
				console.error("DSLRenderer: Target element not found", domUID + "_view");
				return;
			}
			$(targetElement).children().remove();

			const fragment = document.createDocumentFragment();

			const tempContainer = document.createElement("div");
			tempContainer.id = domUID + "_temp_container";
			tempContainer.style.height = "100%";
			tempContainer.style.width = "100%";

			fragment.appendChild(tempContainer);
			if (ref && ref?.options?.card == card) {
				if (!props.validation_errors || !Object.keys(props.validation_errors || {}).length) {
					props.validation_errors = ref?.validation_errors || {};
				}
			}

			const parent = {
				cancel: () => {},
				save: () => {},
				...props,
				renderDSLCardCoffee: renderDSLCardCoffee,
				onDrawShown: () => {
					const loadingContainer = $("#" + domUID + "_loadingContainer");
					if (loadingContainer.length) {
						loadingContainer.remove();
					}
					try {
						$(tempContainer)
							.find(".form-group")
							.filter(function () {
								return $(this).css("display") === "block";
							})
							.css("display", "");
					} catch (error) {
						console.error("DSLRendererNew: Error removing display:block from form-group", error);
					}
					targetElement.appendChild(fragment);
				},
			};
			try {
				const cardViewInstance = new window.CRCardView({
					...props,
					card: card,
					record: record,
					viewid: domUID,
					el: $(tempContainer),
					parent,
				}) as DSLCardViewRef;
				dslCard.current = cardViewInstance;
				if (ref && props.changeMode) {
					props.changeMode(card, record as string, props.tabData as TabData, ref);
				}
				if (dslCard.current && props.setDSLRef) {
					props.setDSLRef?.(dslCard.current);
					if (props.showSections && props.showSections.length) {
						sectionShowOnly(dslCard.current, props.form, props.showSections);
					}
				}
			} catch (error) {
				console.error("DSLRendererNew: Error initializing CRCardView", error);
				if (tempContainer.parentNode === fragment) {
					fragment.removeChild(tempContainer);
				}
			}
		};

		useEffect(() => {
			return () => {
				dslCard?.current?.unload?.();
				isRendered.current = false;
			};
		}, []);

		return (
			<div id={domUID} className="dsl-templ-area" ref={elementRef} data-dsl-form={props.form}>
				<div
					id={domUID + "_loadingContainer"}
					style={{
						height: "100%",
						width: "100%",
						display: "flex",
						flexDirection: "column",
						justifyContent: "center",
						alignItems: "center",
					}}
				>
					<i
						className="fa-duotone fa-solid fa-circle-notch fa-spin"
						style={{ fontSize: "3em", color: "grey" }}
						aria-hidden="true"
					/>
				</div>
				<div id={domUID + "_view"} style={{ height: "100%", width: "100%" }}></div>
			</div>
		);
	},
	(prev, next) => {
		if (prev.form != next.form || prev.record != next.record) {
			return false;
		}
		return true;
	}
);

export const DSLCardView = (props: DSLCardViewProps) => {
	const { record, xid, card, ddRef, form } = props;
	const ps = props.preset as IFormData;
	const dslForm = window.DSL[form];
	const [btnRefreshHash, refreshBtnHash] = useUpdateHash();
	const [readOnlyDiv, setReadOnlyDiv] = useState<HTMLElement | null>(null);
	const [actionEnabled, setActionsEnabled] = useState<Record<string, boolean>>({});
	const [warning, setWarning] = useState<string | null>(ps?._meta?.warning || null);
	if (!props.disableRouting)
		useNavigation(
			props as UseNavigationProps,
			joinPaths(props.routePrepend || "", `/${record || xid}/${card}`, props.routeAppend || "")
		);

	const [btns, setBtns] = useState<TabActions>({
		sections: {
			class: "base-form-button",
			enabled: true,
			label: "Sections",
		},
	});

	const [serverActionsBtns, setServerActionsBtns] = useState<ServerActionButtons[]>(ps?._meta?.actions || []);

	const dslRef = useRef<DSLCardViewRef | null>(null);

	const onServerActions = (actions: ServerActionButtons[], message?: string) => {
		if (Array.isArray(actions)) {
			setServerActionsBtns(actions);
		} else {
			setServerActionsBtns([]);
			console.error("Server actions should be an array");
		}
		setWarning(message || "");
	};

	const triggerActionsChange = () => {
		refreshBtnHash();
	};

	useEffect(() => {
		if (!dslRef.current?.tab_action) {
			return;
		}
		setBtns((bt) => getDos());
	}, [btnRefreshHash, actionEnabled]);

	const actionButtonShowHide = (action: string, show: boolean) => {
		setActionsEnabled((ao) => {
			ao[action] = show;
			return { ...ao };
		});
	};

	useEffect(() => {
		if (!dslRef?.current) {
			return;
		}
		if (dslRef.current.$cardread.find(".readonlymode")?.length) {
			setReadOnlyDiv(dslRef.current.$cardread.find(".cardbar")[0]);
		}
	}, [
		dslRef.current?.tab_action,
		dslRef.current?.cardread?.ddr?.form_rendered,
		dslRef.current?.cardform?.ddf?.form_rendered,
		serverActionsBtns,
	]);

	const getDos = () => {
		const dslCard = dslRef.current;
		const fxHandler = dslCard?.tab_action;
		if (!fxHandler) {
			return {};
		}
		if (!dslCard?.card) {
			return {};
		}
		let btns: TabActions = {
			sections: {
				enabled: true,
				label: "Sections",
				icon: "sections",
				class: "base-form-button",
			},
		};
		TAB_CLICK_BTN_NAME.forEach((f) => {
			btns[f] = {
				enabled: fxHandler("can", f) as boolean,
				label: fxHandler("label", f) as string,
				icon: f as DSLActionBtnIcon,
				class: `btn-${f.replaceAll(" ", "-")} base-form-button`,
			};
		});
		const overrideDos: TabActions = props.overrideDos || {};
		const curview = dslRef?.current?.curview;
		if (!props.isFlyout) {
			if (!overrideDos["cancel"]) {
				if (curview == "read") {
					overrideDos["cancel"] = {
						enabled: true,
						label: "Close",
						icon: "cancel",
						class: "btn-cancel base-form-button",
					};
				}
				if (props.maxRowOne && curview == "edit") {
					overrideDos["cancel"] = {
						enabled: true,
						label: "Close",
						icon: "cancel",
						class: "btn-cancel base-form-button",
					};
				}
			}
			if (props.maxRowOne && curview == "read") {
				overrideDos["cancel"] = {
					enabled: false,
				};
			}
		}
		for (let b of serverActionsBtns) {
			if (curview == "edit" && b.type == "view") {
				continue;
			}
			if (curview == "read" && b.type == "edit") {
				continue;
			}
			let enabled = true;
			let key = b.action as string;
			overrideDos[key as TabClick] = {
				enabled: enabled,
				label: b.label,
				override: true,
				icon: b.icon,
				class: `btn-${key?.replaceAll?.(" ", "-")}${b.style ? ` style-${b.style}` : ""} base-form-button`,
			};
		}
		const obtns = { ...btns, ...overrideDos };
		for (let k in obtns) {
			if (obtns[k] && actionEnabled[k] !== undefined) {
				obtns[k].enabled = actionEnabled[k];
			}
		}
		return obtns;
	};

	const setDSLRef = (ref: DSLCardViewRef) => {
		// Only called once on load after that updated through ref
		dslRef.current = ref;
		triggerActionsChange();
		if (ddRef) {
			ddRef(ref);
		}
	};

	const saveFormRecord = (): Promise<boolean> => {
		return new Promise((resolve) => {
			try {
				const v = dslRef.current?.cardform?.ddf.save_form_values(false);
				if (!v) {
					return resolve(false);
				}
				const dfd = $.Deferred();
				dslRef.current?.cardform?.ddf.save_type_web(dfd, v);
				dfd.done(() => {
					resolve(true);
				});
				dfd.fail(() => {
					resolve(false);
				});
			} catch (error) {
				console.error(error);
				resolve(false);
			}
		});
	};

	const getFormData = () => {
		return (dslRef.current?.cardform?.ddf || dslRef.current?.cardread?.ddr)?.save_form_values?.(
			true
		) as unknown as FormData;
	};

	const handleActionConfirmation = async (action: ServerActionButtons) => {
		if (action.type == "list") {
			openListPopover({
				title: action.label,
				listConfig: action as DSLListViewProps,
				tabViewActions: props.tabViewActions as PatientTabActions,
				addConfig: {
					form: action.form as string,
					preset: action?.preset as any,
					linkMap: action?.linkMap as DSLDrawLinkMap,
				},
			});
			return;
		}
		if (action.requirements == "required" && getMode() != "read") {
			if (!dslRef.current?.cardform?.ddf.verify()) {
				toast({
					type: "error",
					position: "bottom-center",
					theme: "dark",
					message: "Please fill in all missing required fields to perform this action",
				});
				return;
			}
		}
		if (action.show_loading_spinner) {
			window.prettyNotify(action.loading_spinner_text || "Loading...");
		}

		let fd: FormData | undefined;

		if (action.callback_type == "POST" && getMode() == "edit") {
			if (!action.suppress_save) {
				const saved = await saveFormRecord();
				if (!saved) {
					toast({
						type: "error",
						position: "bottom-center",
						theme: "dark",
						message: "Unexpected error occurred while performing action",
					});
					window.prettyNotify();
					return;
				}
			}
		}

		if (action.callback_type == "POST") {
			fd = getFormData();
		}

		const requestConfig: CRRequestOptions = {
			url: action.callback_url
				? normalizeNESEndpoint(action.callback_url)
				: `/form/${props.form}/${props.record}/?perform_action=${action.action}`,
			method: action.callback_type == "POST" ? "POST" : "GET",
			data: fd,
		};

		request(requestConfig)
			.then((sr) => {
				if (sr.data) {
					const lm = {
						link: props.link,
						linkid: props.linkid,
						links: props.links,
					} as DSLDrawLinkMap;
					serverActionPerform(
						sr.data,
						props.form,
						lm,
						dslRef.current,
						props.tabViewActions as PatientTabActions
					);
				} else {
					window.prettyError("Action Failed", "Unsupported action contact support.");
				}
			})
			.catch((error) => {
				console.error(error);
				window.prettyError("Action Failed", "Unexpected error occurred while performing action");
			})
			.finally(() => {
				window.prettyNotify();
				props.tabViewActions?.refreshSnap?.("", true);
			});
	};

	const tabDoSpecialAction = async (action: ServerActionButtons) => {
		if (action.confirmation_dialog) {
			window.prettyConfirm(
				action.confirmation_dialog_title || "Confirmation",
				action.confirmation_dialog_message || "Are you sure you want to perform this action?",
				action.confirmation_dialog_type == "yesno"
					? "Yes"
					: action.confirmation_dialog_type == "okcancel"
					? "Ok"
					: "Confirm",
				action.confirmation_dialog_type == "yesno"
					? "No"
					: action.confirmation_dialog_type == "okcancel"
					? "Close"
					: "Close",
				async () => {
					handleActionConfirmation(action);
				},
				() => {
					return;
				},
				window.BootstrapDialog.TYPE_SUCCESS
			);
		} else {
			handleActionConfirmation(action);
		}
	};

	const tabDo = blockUntilTimePassed((action: string) => {
		const tabAction = dslRef?.current?.tab_action;
		const type = getMode() == "read" ? "view" : "edit";
		if (btns[action as TabClick]?.override) {
			let sa = serverActionsBtns.find((a) => a.action === action && (a.type == "list" || a.type == type));
			if (sa) tabDoSpecialAction(sa);
			return;
		}
		let rendered = dslRef.current?.cardread?.ddr?.form_rendered || dslRef.current?.cardform?.ddf?.form_rendered;
		if (!rendered) {
			return;
		}
		if (!tabAction) {
			return;
		}
		if (tabAction("can", action)) {
			tabAction("do", action);
			triggerActionsChange();
		}
	}, 1500);

	const getMode = () => {
		return dslRef.current?.curview || dslRef.current?.tabData?.mode || props.card || "read";
	};

	const getContainerClasses = () => {
		const classes = ["form-container"];
		const mode = getMode() as string;
		if (FORM_MODE[mode]) classes.push(FORM_MODE[mode]);
		return classes.join(" ");
	};

	const np = _.omit(props, "id", "mode");

	const mode = getMode();

	return (
		<CRErrorBoundary>
			<FlyoutContainer
				form={props.form}
				record={props.record as string}
				flyoutAllowWidget={props.flyoutAllowWidget || false}
				isFlyout={props.isFlyout || false}
				label={props.label || renderItemData(dslForm?.view?.label || toPresentableLabel(props.form))}
				dimensions={dslForm?.view?.dimensions}
				onClose={() => tabDo("cancel")}
			>
				<div className={getContainerClasses()}>
					{props.PreviewComponent ? (
						<props.PreviewComponent
							key={props.previewComponentProps?.tkey || "preview"}
							isFlyout={false}
							hideActionBtns={true}
							disableRouting={true}
							{...(props.previewComponentProps || {})}
						/>
					) : null}
					<DSLRenderer
						{...np}
						setDSLRef={setDSLRef}
						actionButtonShowHide={actionButtonShowHide}
						triggerActionsChange={triggerActionsChange}
						onServerActions={onServerActions}
					/>
				</div>
				{props.hideActionBtns
					? null
					: Object.keys(btns)?.length > 0 && (
							<WizardBottomBar className="wizard-bottom-bar">
								<div className="dsl-information">
									{warning && <div className="action-warning">{`Warning: ${warning}`}</div>}
									<FormActiveUsers
										key={props.form + props.card + props.record}
										form={props.form}
										record={props.record as number}
										mode={mode}
									/>
								</div>
								<div className="action-btn-cnt">
									{dslRef.current?.card === "read" && (
										<div
											dangerouslySetInnerHTML={{ __html: readOnlyDiv?.outerHTML as TrustedHTML }}
										/>
									)}
									<DSLCardActionButtonGroup btns={btns} tabDo={tabDo} />
								</div>
							</WizardBottomBar>
					  )}
			</FlyoutContainer>
		</CRErrorBoundary>
	);
};

export const FlyoutContainer = ({
	children,
	isFlyout,
	form,
	record,
	flyoutAllowWidget,
	dimensions = { width: "", height: "" },
	label,
	onClose,
}: {
	children: React.ReactNode;
	isFlyout: boolean;
	flyoutAllowWidget?: boolean;
	label: string;
	form?: string;
	record?: string;
	dimensions?: {
		width?: string;
		height?: string;
	};
	onClose: (event: React.MouseEvent<HTMLButtonElement>) => void | null;
}) => {
	if (!isFlyout) {
		return children;
	}

	return (
		<div className="form-segment-container-flyout" style={dimensions}>
			{flyoutAllowWidget && form && record && <SnapshotHeaderForm formName={form} formId={record as string} />}
			<div className="form-container-flyout">
				<div className="form-flyout-header-bar">
					<span>{label}</span>
					<span className="close-flyout" onClick={onClose}>
						<img src={PublicIcon.general.closeWindow} alt={"Close"} />
					</span>
				</div>
				<div className="layout-form-content">{children}</div>
			</div>
		</div>
	);
};

interface FlyoutHTMLViewerProps {
	html: string;
	parent: {
		cancel: () => void;
	};
	label: string;
}

export const FlyoutHTMLViewer = (props: FlyoutHTMLViewerProps) => {
	const htmlRef = useRef<HTMLDivElement>(null);
	const btns = {
		cancel: {
			enabled: true,
			label: "Close",
		},
	};
	const tabDo = (action: string) => {
		if (action === "cancel") {
			props.parent.cancel();
		}
	};
	useEffect(() => {
		const html = htmlRef.current;
		if (html) $(html).html(props.html);
		$;
	}, [htmlRef.current]);

	return (
		<CRErrorBoundary>
			<FlyoutContainer isFlyout={true} label={props.label || "Viewer"} onClose={() => tabDo("cancel")}>
				<div className={`form-container`}>
					<div className="dsl-templ-area" ref={htmlRef} style={{ padding: "20px" }}>
						{props.html}
					</div>
				</div>
				{Object.keys(btns)?.length > 0 && (
					<WizardBottomBar className="wizard-bottom-bar">
						<div className="action-btn-cnt">
							<DSLCardActionButtonGroup btns={btns} tabDo={tabDo} />
						</div>
					</WizardBottomBar>
				)}
			</FlyoutContainer>
		</CRErrorBoundary>
	);
};

DSLCardView.defaultProps = {
	disableRouting: false,
	maxRowOne: false,
	autoRecoverEnabled: true,
	audit_mode: false,
};

export default DSLCardView;
