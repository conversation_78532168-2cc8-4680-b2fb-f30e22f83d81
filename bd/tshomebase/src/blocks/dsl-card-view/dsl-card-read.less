#application {
    .form-container {

        &.form-read-mode {

            .dsl-templ-area {

                .cardread {
                    box-shadow: none !important;
                    height: 100%;

                    .cardarea {
                        box-shadow: none !important;
                        padding: var(--cardread-card-area-p);
                        background-color: transparent;

                        .fieldgroup {
                            >h3 {}

                            .form-horizontal {}
                        }

                        .container {
                            width: 100%;
                            padding: var(--spacing-large) var(--spacing-xxxlarge);

                            .fieldgroup {
                                h3 {
                                    // margin-bottom: 0;
                                }

                                >h4 {
                                    &.required-section {
                                        color: inherit !important;

                                        &::after {
                                            display: none;
                                        }
                                    }
                                }
                            }
                        }

                        &.patient_medical_hx {
                            .container {
                                padding-left: 15px;
                                padding-right: 15px;
                            }
                        }
                    }

                    .scrolltarget {
                        &.container.tab-list-default {
                            margin-bottom: 0px !important;
                        }
                    }

                    .carddrop {
                        // padding-top: var(--spacing-standard) !important;
                    }

                    .form-horizontal {
                        margin-bottom: 0;

                        .form-group {
                            padding: var(--spacing-xsmall) 0px;
                            margin: 0px;

                            .control-label {
                                span {
                                    cursor: text;
                                }

                                &.required-label {
                                    color: var(--color-error) !important;

                                    &::after {
                                        display: none;
                                    }
                                }
                            }

                            .controls {

                                .dropdown-container {
                                    display: none;
                                }

                                >textarea {
                                    min-height: 25px !important;
                                }

                                >.checkboxes {
                                    label.checkbox {

                                        input:checked+span::before {
                                            content: none !important;
                                        }

                                        &:not(:has(input:checked)) {
                                            display: none;
                                        }

                                        span {}
                                    }
                                }

                                .dsl-field-embed {
                                    min-height: auto !important;
                                }

                                .btn-group {

                                    .btn:not(.active) {
                                        display: none !important;
                                    }

                                    .btn {
                                        background-color: transparent !important;
                                        color: var(--gray-700) !important;
                                        font-weight: var(--font-weight-bold) !important;
                                        font-size: var(--font-size-small) !important;
                                        padding: 0px !important;
                                        border: none !important;
                                        min-height: auto;
                                    }
                                }
                            }

                            .dsl-field-embed {
                                .dsl-grid-view {
                                    border: none;
                                }
                            }

                            .dsl-grid-view {
                                .repeaterwrap {
                                    >div {
                                        min-height: auto !important;
                                    }

                                    .dataTables_scroll {

                                        .dataTables_scrollHead,
                                        .dataTables_scrollBody {
                                            tr {
                                                background-color: transparent !important;
                                            }

                                            table {

                                                thead,
                                                tbody {
                                                    cursor: default;

                                                    tr {
                                                        cursor: default;
                                                        background-color: transparent;

                                                        &:hover {
                                                            background-color: transparent;
                                                        }

                                                        th {
                                                            cursor: default;
                                                        }
                                                    }

                                                    .sorting {
                                                        cursor: default !important;
                                                    }
                                                }

                                                tr,
                                                th,
                                                td {
                                                    cursor: default;
                                                }

                                                .sorting_asc {
                                                    cursor: default;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            .table {

                                thead,
                                tbody,
                                tfoot {

                                    tr {

                                        th,
                                        td {
                                            padding-left: 0px;
                                        }
                                    }

                                }

                            }

                            .checkbox {
                                padding: 0px !important;
                            }
                        }

                        &.form-col-1:not(:has(table)) {
                            display: inline-block;
                        }


                        &.form-col-1:not(:has(table, .json-viewer, .fileupload)),
                        &.form-col-2:not(:has(.json-viewer, .fileupload)),
                        &.form-col-3:not(:has(.json-viewer, .fileupload)),
                        &.form-col-4:not(:has(.json-viewer, .fileupload)),
                        &.form-col-addr_1,
                        &.form-col-addr_2,
                        &.form-col-addr_city,
                        &.form-col-addr_state,
                        &.form-col-addr_zip {

                            .form-group {
                                flex-direction: row !important;
                                align-items: center;
                                flex-wrap: nowrap;

                                >label.control-label {
                                    position: relative;
                                    width: 50% !important;
                                    text-align: left;
                                    left: 0;
                                    top: 0;
                                }

                                .controls {
                                    width: 50% !important;

                                    textarea[readonly] {
                                        min-height: 30px !important;
                                    }

                                    .select2-container {

                                        .select2-choice,
                                        a {
                                            display: flex;
                                            flex-direction: row;
                                            align-items: center;
                                        }

                                        .select2-choices {
                                            .select2-search-choice-close {
                                                display: none;
                                            }
                                        }

                                        &:hover {
                                            * {
                                                text-decoration: none;
                                            }
                                        }
                                    }
                                }

                                .disabledControl,
                                input,
                                input[disabled],
                                input[readonly],
                                textarea[disabled],
                                textarea[readonly],
                                select[readonly],
                                select[disabled],
                                .checkboxes,
                                .btn-group,
                                .select2-container .select2-choice .select2-chosen,
                                .select2-container a span {
                                    color: var(--gray-700) !important;
                                    font-weight: var(--font-weight-bold) !important;
                                    font-size: var(--font-size-small) !important;

                                    cursor: text;

                                    * {
                                        cursor: text !important;
                                    }
                                }

                                .field-field-media-viewer {
                                    width: 100%;
                                }

                                .help-container {
                                    display: none;

                                    .help-block {
                                        &.note {

                                            b,
                                            span {
                                                display: none;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .dsl-card-duel-card {
        .form-container {
            .form-horizontal {

                &.form-col-3:not(:has(.json-viewer)),
                &.form-col-4:not(:has(.json-viewer)),
                &.form-col-addr_1,
                &.form-col-addr_2,
                &.form-col-addr_city,
                &.form-col-addr_state,
                &.form-col-addr_zip {
                    width: 50%;
                }
            }
        }
    }
}