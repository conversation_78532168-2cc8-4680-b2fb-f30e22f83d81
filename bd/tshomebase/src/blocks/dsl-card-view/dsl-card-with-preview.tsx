import DSLCardView, { DSLCardViewProps } from "@blocks/dsl-card-view/dsl-card-view";
import React, { FC } from "react";
import "./dsl-card-with-preview.less";
import { MediaRenderer, MediaRendererProps } from "@components/popups/media-viewer";

export type CardViewPreview =
	| {
			preview: DSLCardViewProps & {
				type: "DSLCardView";
			};
	  }
	| {
			preview: MediaRendererProps & {
				type: "MediaRenderer";
			};
	  };

type DSLCardWithPreviewProps = DSLCardViewProps & CardViewPreview;

const PreviewMediaContainer: FC<MediaRendererProps> = ({ file }) => {
	return (
		<div className="dsl-templ-area">
			<MediaRenderer file={file} />
		</div>
	);
};

const getPreviewComponent = (type?: string): FC<any> | undefined => {
	switch (type) {
		case "DSLCardView":
			return DSLCardView as FC<any>;
		case "MediaRenderer":
			return PreviewMediaContainer;
		default:
			return undefined;
	}
};

export const DSLCardWithPreview: React.FC<DSLCardWithPreviewProps> = (props) => {
	const PV = getPreviewComponent(props?.preview?.type);
	return (
		<div className="card-with-preview">
			<DSLCardView {...props} PreviewComponent={PV} previewComponentProps={props.preview as any} />
		</div>
	);
};

export default DSLCardWithPreview;
