@import (reference) "../../less/style/main.less";
@import "../../dsl-form/styles/main.less";
@import "../../styles/base/less/fonts.less";
@import "./tab-level-1.less";
@import "./tab-level-2.less";
@import "./tab-level-3.less";
@import "./dsl-flyout.less";


@tab-action-btn-color: @white;
@tab-action-btn-bg: #2098dc;
@tab-action-cnt-height: 40px;

.dsl-container {
  border-radius: 12px;
}

.carddrop.pull-right {
  display: none !important;
  visibility: hidden !important;
}

.dsl-tab-container {
  background-color: transparent;
  border-radius: var(--radius-medium);
  min-height: 100% !important;
  height: auto;
  display: flex;
  flex-direction: column;

  &::-webkit-scrollbar-track {
    background: white!important;
  }
  .dsl-tab-header {
    overflow-y: hidden;
    flex-shrink: 0;
    &::-webkit-scrollbar-track {
      background: white!important;
    }
  }

  .dsl-tab-area {
    background-color: var(--white);
    min-height: calc(100% - 50px);
    height: auto;
    flex: 1;

    .container {
      padding: 0px !important;
      background-color: transparent;
    }

    &.level_1 {
      padding: 12px 16px;

      .dsl-tab-container {

        &.level_2 {
          .dsl-tab-area {
            border-radius: 8px;
            background-color: #FAFAFA;
            padding: 5px;
            border: 1px solid var(--gray-200);

            &:has(> [style="display: none;"]) {
              .dsl-tab-area {

                &.level_1,
                &.level_2,
                &.level_3,
                &.level_4 {
                  &:has(> [style="display: none;"]) {
                    border: none !important;
                  }
                }
              }
            }
          }

          .dsl-tab-header {
            &.level_2.tab-list-default {
              gap: 0;
              .level-2-tab-list;
            }

            &.level_3.tab-list-default {
              .tab-level-3-list;
              box-shadow: 0px 0px 2px 0px #0000001F, 0px 2px 3px -1px #00000014;
            }
          }
        }

        &.level_3 {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin: 8px;

          .dsl-tab-area {
            background: white;

            &.level_3 {
              border: 1px solid var(--gray-200) !important;
            }
          }
        }
      }
    }

    &.level_3 {
      .scrolltarget {
        width: 100%;
      }
    }
  }

  &.level_1,
  &.level_2,
  &.level_3,
  &.level_4 {
    &:has(> [style="display: none;"]) {
      .dsl-tab-area {
        background: #FAFAFA !important;

        &.level_1,
        &.level_2,
        &.level_3,
        &.level_4 {
          &:not(:has(.dsl-tab-container)) {
            border: none !important;
          }
        }
      }
    }
  }

  .dsl-tab-header[style*="display: none"]+.dsl-tab-area {
    padding: 0px !important;
  }
}

.dsl-tab-container~div.container {
  padding-left: var(--spacing-xxxlarge) !important;
  padding-right: var(--spacing-xxxlarge) !important;
}

.dsl-tab-area {
  display: none; // Default hidden

  &:has(:not([style*="display: none"]):not([style*="visibility: hidden"])) {
    display: block; // Show if any child is visible
  }
}

.dsl-tab-header.level_1 {
  &.tab-list-default {
    .level-1-tab-list;
  }
}

.dsl-information {
  display: flex;
  flex-direction: column;

  .action-warning {
    color: @reddish;
    font-weight: bold;
  }
}

.input-checkbox {
  accent-color: @purple;
  border: 2px solid @purple;
  color: @purple;
  height: 18px;
  width: 18px;
}

.tab-toggle:checked:disabled {
  background-size: inherit;
  background-position: center;
  border: 2px solid #9974cf;
  background-image: url(../../public/icons/common/checkbox.svg);
  content: "" !important;
  width: 18px;
  height: 18px;
  border-radius: 3px;
}

.form-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: var(--spacing-large);
  height: calc(~"100% - "@wizard-bottom-bar-height - var(--spacing-large));
  width: 100%;

  .section-compact {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  .carddrop {
    input[type="checkbox"] {
      .checkbox-one;
    }
  }

  .tab-toggle {
    .input-checkbox;
    margin-top: auto;
    margin-bottom: auto;
  }

  &.hide-cardmenu {

    .cardmenu-show,
    .cardmenu.dropmenu.affix {
      display: none !important;
    }
  }

  .cardform {
    height: 100%;
    width: 100%;

    &.hasmenu {
      flex-direction: column !important;
      display: flex;
      justify-content: space-evenly;
    }
  }

  .cardarea {
    height: 100%;
    width: 100%;

    >.container:first-child {
      border-radius: 12px;
      margin: var(--spacing-xxxlarge) 0px;
    }

    .container {
      padding: 0px var(--spacing-xxxlarge);
      background-color: transparent;
    }

    .dsl-tab-container {
      .dsl-tab-header {
        &.container {
          background-color: transparent;
        }
      }
    }

    .hl-container {
      display: flex;
      flex-direction: column;
      z-index: 99999;
      position: absolute;
      border: 1px solid rgba(5, 5, 5, 0.2);
      width: 300px;
      right: 250px;
      background-color: rgba(245, 247, 250, 1);
      border-radius: 3px;

      .h-content-continer {
        padding: var(--h-content-container);

        .h-content {
          display: flex;
          justify-content: space-between;
          font-size: 10px;
          font-weight: 550;
          padding-bottom: var(--h-content-pb);
          padding-left: var(--h-content-pl);
        }

        hr {
          margin: 0px;
          border-top: 1px solid rgba(5, 5, 5, 0.2);
          width: -webkit-fill-available;
        }

        &:hover {
          background-color: rgb(214, 220, 228);
        }
      }

      .heading {
        margin: 0px;
        margin-top: 10px;
        margin-top: var(--hl-container-heading-mt);
        color: #0070bd;

        .heading-text {
          margin-left: var(--hl-container-heading-text-ml);
        }

        hr {
          margin: 0px;
          margin-top: var(--hl-container-heading-hr-mt);
          border-top: 1px solid rgba(5, 5, 5, 0.2);
          width: -webkit-fill-available;
        }
      }
    }

    >h3 {
      padding: 7px 7px 7px 8px;
      padding-bottom: var(--card-area-h3-pb);
      text-transform: capitalize;
      font-size: var(--font-size-small);
      margin-bottom: 0px;
      margin-top: var(--card-area-h3-tmp-mt);
      color: var(--color-tertiary) !important;
      border-bottom: none;
      line-height: 31px;
    }

    >h3[tabgroupid] {
      padding-top: var(--tab-group-id-h4-pt);
      background-color: transparent;
    }

    .fieldgroup {
      >div:first-child.sectionnote {
        margin-top: 10px !important;
      }

      >h3 {
        padding-bottom: var(--spacing-large);
        font-size: var(--font-size-small);
        border-bottom: 1px solid @soft-gray;
        color: var(--color-tertiary) !important;
        margin-bottom: 0px;
        width: 100%;
        margin: var(--spacing-large) 0px;
        background-color: transparent;
      }

      >h4 {
        padding: var(--field-group-h4-p);
        .form-section;
        color: var(--color-tertiary) !important;
        margin: 6px 0px;
        margin: var(--field-group-h4-m);
        width: 100%;
        padding-left: var(--spacing-xsmall);
        font-size: var(--font-size-small) !important;

        &.required-section {
          color: @coral-red !important;
          .required-asterisk;
        }
      }

      &.highlight-section {
        .card-one;
        background-color: @brown;
        transition: background-color 0.5s ease;
        animation: fadeOutBackground 1s forwards 1s;
      }

      @keyframes fadeOutBackground {
        from {
          background-color: @brown;
        }

        to {
          background-color: @white;
        }
      }

      .form-horizontal {
        vertical-align: top;

        &.section-required-check {
          width: 100% !important;

          >div {
            >label {
              width: max-content !important;
            }

            display: flex !important;
            flex-direction: row !important;
            gap: 10px !important;
            justify-content: flex-end !important;
            align-items: center !important;
          }
        }

        &.form-col-1 {
          width: 100%;
        }

        &.form-col-2 {
          width: 50%;
          display: inline-block;
        }

        &.form-col-3 {
          width: 33.3333%;
          display: inline-block;
        }

        &.form-col-4,
        &.form-col-modifier {
          width: 25%;
          display: inline-block;
        }

        &.form-col-addr_1 {
          width: 33%;
          display: inline-block;

          input {}
        }

        &.form-col-addr_2 {
          width: 16.8%;
          display: inline-block;

          input {}
        }

        &.form-col-addr_city {
          width: 16.8%;
          display: inline-block;

          input {}
        }

        &.form-col-addr_state {
          width: 21%;
          display: inline-block;

          .select2-container>a.select2-choice {}
        }

        &.form-col-addr_zip {
          width: 12.4%;
          display: inline-block;

          input {}
        }

        .form-group {
          .controls {
            >.checkboxes {
              label.checkbox {
                cursor: pointer;
                width: 100%;
                float: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-wrap: auto;

                span {
                  pointer-events: auto;
                  .checkbox-one-inner;
                }

                input {
                  opacity: 0;
                  position: absolute;
                  z-index: 12;
                  width: 0px;
                  height: 18px;
                  margin-left: 0px;
                  cursor: pointer;
                }

                input:checked+span {
                  font-weight: bold;
                }

                /* Disable outline when clicked*/
                input:checked,
                input:focus {
                  outline: none !important;
                }

                /* Basic Checkbox styles */
                input+span::before {
                  display: inline-block !important;

                  display: inline-block;
                  vertical-align: middle;

                  line-height: 18px;
                  margin-right: var(--checkbox-input-span-before-mr);
                  margin-top: var(--checkbox-input-span-before-tmp-mt);
                  margin-bottom: 2px;
                  .checkbox-one-before;
                }

                /* Checked state styles */
                input:checked+span::before {
                  display: inline-block;
                  // .checkbox-one-ckecked-before;
                  // background-image: url(../../public/icons/common/checkbox.svg);
                }

                input[disabled="disabled"]+span {
                  color: #aaa;
                }

                input:checked+span {
                  color: #333;
                }

                /* Hover state styles */
                input:hover+span::before,
                input+span:hover::before {
                  border-color: @dark-gray;
                }

                /* Shadow when checked or Active */
                input:active+span::before,
                input:checked:active+span::before {
                  box-shadow: none !important;
                }

                /* Disabled state styles */
                input:disabled+span::before,
                input[disabled]+span::before,
                input.disabled+span::before {
                  background-color: #ddd !important;
                  border-color: #ccc !important;
                  box-shadow: none !important;
                  color: #bbb;
                }

                &:focus-visible {
                  outline: none;
                }
              }

              &.checkbox-1 {
                label.checkbox {
                  width: 100%;
                }
              }

              &.checkbox-2 {
                label.checkbox {
                  width: 45%;
                  margin-right: var(--checkboxes-checkbox-2-mr);
                }
              }

              &.checkbox-3 {
                label.checkbox {
                  width: 30%;
                  margin-right: var(--checkboxes-checkbox-3-mr);
                }
              }

              &.checkbox-4 {
                label.checkbox {
                  width: 23%;
                  margin-right: var(--checkboxes-checkbox-4-mr);
                }
              }

              &.checkbox-5 {
                label.checkbox {
                  width: 18%;
                  margin-right: var(--checkboxes-checkbox-4-mr);
                }
              }

              &.checkbox-6 {
                label.checkbox {
                  width: 14%;
                  margin-right: var(--checkboxes-checkbox-4-mr);
                }
              }

              &.checkbox-7 {
                label.checkbox {
                  width: 12%;
                  margin-right: var(--checkboxes-checkbox-4-mr);
                }
              }

              &.dsl-button {
                >label.checkbox {
                  all: unset;
                  display: flex;
                  width: auto;
                  justify-content: center;
                  align-items: center;
                  .btn-primary;
                  min-height: 20px !important;
                  width: auto !important;
                  padding: var(--btn-primary-p) !important;

                  &:hover {
                    background-color: @soft-dark-gray;
                    color: @dark-gray;
                  }

                  &:has(> input:checked) {
                    .btn-tertiary !important;
                    min-height: 20px !important;

                    &:hover {
                      background-color: @bright-teal !important;
                    }
                  }

                  span {
                    all: unset !important;

                    &:hover,
                    &::before {
                      display: none !important;
                      all: unset !important;
                      background: transparent;
                    }
                  }

                  input+span {
                    &::before {
                      display: none !important;
                      all: unset;
                    }
                  }
                }
              }
            }

            .barcode {
              // .barcode;
            }

            // .select2-container .select2-container-disabled .select2-choice .select2-chosen,
            // .select2-container .select2-container-disabled span a
            input[disabled],
            input[readonly],
            select[readonly],
            select[disabled] {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .manage {
            width: 36px;
            height: calc(100% - 20px);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            position: absolute;
            right: 0;
            color: @semi-transparent-light-brown-5;
            padding: 2px var(--spacing-large) 2px var(--spacing-standard);

            a {
              line-height: normal;
              font-weight: 500;
              color: @semi-transparent-light-brown-5;
              text-decoration: none;
              width: 20px;
              height: 20px;
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;

              &::before {
                content: "abl";
                color: transparent;
                background-repeat: no-repeat;
                background-size: 14px;
                background-position: center;
                min-height: 20px;
              }


              &:hover {
                &::before {
                  background-size: 20px;
                }
              }

              &.manage-add {
                &::before {
                  background-image: url('../../public/icons/common/outlined/plus-circle-outline.svg');
                }
              }

              &.manage-edit,
              &.manage-list {
                &::before {
                  background-image: url('../../public/icons/common/outlined/edit-manage-outline.svg');
                }
              }
            }
          }

          .control-label {
            width: calc(100% - 40px);
            display: flex;
            gap: var(--control-label-g);
            color: var(--gray-500);
            font-size: var(--font-size-xxsmall);
            font-weight: var(--font-weight-regular);
            line-height: normal;
            position: absolute;
            z-index: 5;
            left: 20px;
            top: 22px;

            &::before {
              flex-shrink: 0;
            }

            &.clickable-label {
              >span {
                cursor: pointer !important;
                color: #5B83B0 !important;

                &:hover {
                  text-decoration: underline;
                }

                >i {
                  color: #5B83B0 !important;
                }

                .required-label {
                  color: var(--color-error) !important;
                }
              }

              &::before {
                filter: brightness(0) saturate(100%) invert(48%) sepia(11%) saturate(1000%) hue-rotate(178deg) brightness(90%) contrast(95%);
              }
            }

            span {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            &.important {
              .important-vc;
            }

            &.money {
              .money-vc;
            }

            &.discount {
              .discount-vc;
            }

            &.status {
              .status-vc;
            }

            &.claim-field {
              .claim-field-vc;
            }

            &.check-field {
              .check-field-vc;
            }

            &.fdb-field {
              .fdb-field-vc;
            }

            &.cms-1500-field {
              .cms-1500-field-vc;
            }

            .required-label {
              color: var(--color-error);
              font-size: 20px;
              line-height: 5px;
              padding-top: 10px;
            }

            i.audit-trail {
              display: none;
            }
          }

          .help-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-small);

            &:has(span:empty):not(:has(> span > span:not(:empty))) {
              display: none;
            }

            .note {
              clear: both;
              margin: 0px;

              b {
                padding-right: var(--spacing-standard);
                font-size: var(--font-size-xxsmall);
                color: @reddish;
                font-weight: var(--font-weight-medium);
              }

              span {
                font-size: var(--font-size-xxsmall);
                color: var(--color-text-400);
                font-weight: var(--font-weight-medium);
              }
            }

            span {
              display: -webkit-box !important;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              word-wrap: break-word;
              line-height: 1.2;
              max-height: 3em;
              margin: 0;

              &:not(:empty) {
                min-width: 50%;
              }
            }

            .errormsg {
              display: none;
              color: var(--color-error);
              font-size: var(--font-size-xxsmall);
              font-weight: var(--font-weight-semibold);
              padding-right: var(--spacing-large);
              text-align: right;
            }

            .warningmsg {
              display: none;
              color: var(--color-warning);
              font-size: var(--font-size-xxsmall);
              font-weight: var(--font-weight-semibold);
              text-align: right;
            }
          }

          &.has-error {
            >label.control-label {
              color: var(--color-error) !important;

              * {
                color: var(--color-error) !important;
              }

              &::before {
                filter: invert(70%) sepia(40%) saturate(500%) hue-rotate(330deg) brightness(140%) contrast(90%);
              }
            }

            .help-container {
              display: flex !important;
            }

            .errormsg {
              display: block;
            }
          }

          &.has-warning {
            .warningmsg {
              display: block;
            }
          }

          .subform {
            .has-error {
              .errormsg {
                margin-top: -5px !important;
              }
            }

            .has-warning {
              .warningmsg {
                margin-top: -5px !important;
              }
            }
          }
        }

        &:has(div.barcode) {
          display: none;
        }
      }
    }
  }

  &.form-add-mode,
  &.form-addfill-mode,
  &.form-edit-mode {

    .cardform {
      .carddrop {
        &.pull-right {
          .fa-history {
            display: none;
          }
        }
      }

      .form-horizontal {
        .form-group {
          .control-label {
            i.audit-trail {
              display: none;
            }

            i.audit-trail-grid {
              display: none;
            }
          }

          &:has(.manage) {
            .control-label {
              width: calc(~"100% - " 120px) !important;
            }
          }
        }
      }
    }
  }

  &.form-add-mode,
  &.form-addfill-mode,
  &.form-edit-mode {
    .cardarea {
      .scrolltarget {
        &:has(:not([style*="display: none"])) {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
        }
      }
    }

    .form-horizontal {
      .form-group {
        &:has(.manage) {
          .control-label {
            width: calc(~"100% - " 120px) !important;
          }
        }
      }
    }
  }

  &.form-edit-mode,
  &.form-read-mode {
    .dsl-templ-area {
      .cardread {
        &.hasmenu {
          flex-direction: row-reverse !important;
          display: flex;
          justify-content: space-evenly;
        }
      }
    }

    .carddrop {
      display: flex;
      justify-content: flex-end;
      gap: var(--edit-read-cardrop-g) !important;

      &.pull-right {
        .fa-history {
          display: none;
        }
      }

      &:hover {
        &.pull-right {
          .fa-history {
            display: block;
          }
        }
      }
    }

    .form-horizontal {
      .form-group {
        .control-label {
          .fld-adt-hsty-con {}

          i.audit-trail {
            display: none;
          }

          i.audit-trail-grid {
            margin-top: var(--cl-i-audit-trail-grid-mt);
            display: none;
            padding-left: var(--cl-i-audit-trail-grid-tmp-pl);
          }

          &:hover {
            cursor: pointer;

            i.audit-trail {
              display: block !important;
            }

            i.audit-trail-grid {
              // TODO show after demo
              display: block !important;
            }
          }
        }

      }
    }
  }
}

.action-btn-cnt {
  display: flex;
}

.wizard-bottom-bar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-shrink: 0;
  gap: var(--wizard-bottom-bar-g);

  .cardbar {
    .para-two;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: var(--wizard-bottom-bar-cardbar-p);
    color: @reddish;
    font-weight: 600;
    background-color: transparent !important;

    .readonlymode {
      width: max-content !important;
    }
  }

  .action-btn-cnt {
    overflow: hidden;
  }
}

.action-btn {
  user-select: none;
  display: flex;
  display: flex;
  align-items: center;
  padding: var(--action-btn-p);
  margin: var(--action-btn-m);
  background: @tab-action-btn-bg;
  color: @tab-action-btn-color;
  border-radius: 20px;
}

.form-container {
  display: flex;
  flex-direction: row;

  .form-card-menu {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }
}

.dsl-templ-area {
  width: 100%;
  overflow-y: scroll;
  position: relative;
  border-radius: var(--radius-medium);
  background-color: var(--white);

  .section-menu-header-hc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }

  *[tabgroupid] {
    margin-top: 0px !important;
    border-image: linear-gradient(rgb(219, 219, 221), rgb(255, 255, 255) 200px) 1 / 1 / 0 stretch;

    >h4 {
      padding-top: var(--tab-group-id-h4-pt);
    }

    >h3 {
      margin-top: var(--tab-group-id-h4-pt) !important;
      background-color: transparent;
    }
  }

  div[tabgroupid][style="display: none;"]+div[tabgroupid] {}

  *[tabcontroller] {
    margin-bottom: 0px !important;

    padding: 0px !important;
    border-bottom: 1px solid @soft-gray;
    border-left: 0px;
    overflow-x: scroll !important;

    >.tab-list-button {
      border-bottom: none;

      // &:first-child:not(.hide) {
      //   border-top-left-radius: @border-radius-8;
      // }

      // &:last-child:not(.hide) {
      //   border-top-right-radius: @border-radius-8;
      // }
    }
  }

  div[tabcontroller]+div[tabgroupid] {
    border-left: 1px solid;
  }

  .sf-table-container {
    overflow-y: auto;
  }

  table.subform-editable-table {

    tfoot {
      display: none;
    }

    .gridedit-subrow {
      >* {
        min-width: 100% !important;
      }

      br {
        display: none;
      }
    }
  }

  &:has(.dsl-tab-container) {
    background-color: transparent;
  }
}

.dsl-templ-area:has(.formload:not([style="display: none;"])) {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

////// CSS format Start /////

.form-horizontal:has(> [style="display: none;"]) {
  padding: unset !important;
  display: none !important;
}


.cardarea {
  .form-group {
    .controls:has(input.no-val) {
      &::before {
        content: "-";
        position: absolute;
        top: 35px;
        color: var(--gray-700);
        font-weight: var(--font-weight-bold);
        font-size: var(--font-size-small);
      }
    }
  }
}

.cardread {
  .form-group {
    .controls:has(input.no-val) {
      &::before {
        top: 0;
        line-height: normal;
      }
    }
  }
}

.form-add-mode .form-group .controls-grid .control-label:has(.audit-trail-grid) {
  // TODO show after demo
  display: none !important;
}

.fieldgroup {
  counter-reset: label-counter;
}

#application .fieldgroup .form-horizontal .form-group:has(.btn-group, .checkbox-group, .fileupload, .barcode, .controls-grid, .json-viewer) {
  .control-label {
    position: relative;
    top: 0;
    left: 0;
  }
}

#application .form-container:not(&.form-read-mode) .fieldgroup .form-horizontal .form-group:has(input[readonly], input[disabled], textarea[readonly], textarea[disabled]) {
  .control-label {
    left: 10px;
  }

  .controls {
    cursor: not-allowed;

    * {
      cursor: not-allowed;
    }
  }
}

.fieldgroup .form-horizontal:has(.label-directions) {
  counter-increment: label-counter;
}

.form-group .controls:has(.label-directions) {
  display: flex;
  gap: var(--controls-has-label-directions-g);
  align-items: flex-end;
}

.form-group .controls:has(.label-directions)::before {
  content: "_";
  color: transparent;
  background-image: url("../../public/icons/common/filled/menu-single-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  font-size: var(--form-fs-lmd);
  font-weight: 500;
  line-height: 24px;
  padding-bottom: 1px;
}

/* Hide consecutive <br> tags except the first one */
.form-container {
  br {
    display: block;
    content: "";
    width: 100%;
  }

  br+br {
    display: none;
  }

  .esign-link {
    br {
      display: block;
    }
  }

  /* If there are more than two consecutive <br> tags, hide all except the first one */
  br+br+br {
    display: none;
  }

  /* if  <br> tag before and after hidden field hide all except first */
  br+.form-horizontal:has(> [style="display: none;"])+br {
    display: none;
  }

  .form-horizontal:has(> [style="display: none;"])+br+br {
    display: none;
  }

  br+br+.form-horizontal:has(> [style="display: none;"])+br+br {
    display: none;
  }
}

////// CSS format Start /////

//// Fields Files Higher Priority ////
@import "./dsl-fields/field-input.less";
@import "./dsl-fields/field-textarea.less";
@import "./dsl-fields/field-checkbox-only.less";
@import "./dsl-fields/field-checkbox-multi.less";
@import "./dsl-fields/field-radio-buttons.less";
@import "./dsl-fields/field-barcode.less";
@import "./dsl-fields/field-esign.less";
@import "./dsl-fields/field-file.less";
@import "./dsl-fields/field-select.less";
@import "./dsl-fields/field-grid.less";
@import "./dsl-fields/field-subform-grid.less";
@import "./dsl-card-read.less";
@import "./dsl-card-read.responsive.less";