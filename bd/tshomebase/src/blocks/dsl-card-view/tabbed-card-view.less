@import "./mini-card-view.less";


.dsl-tabbed-card-view {

    @colorTextDark: #656463;
    @colorTextLight: #8e8d8c;
    @colorTextInverted: #fbfbfb;
    @colorTextAccent: #0bc1d8;
    @colorLine: #ededee;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    height: 100%;
    width: 100%;

    .only-show-form-area;


    .section-tabs {
        flex: none;
    }

    .form-container {
        margin-top: 0px !important;

        .cardarea {
            border-radius: 0px !important;
        }
    }

    .dsl-templ-area {
        border-radius: 0px !important;

        >div {
            margin-right: unset !important;
        }

        width: 100%;
        flex: 1;

        h3,
        h4 {
            display: none;
        }
    }

    #no-data-available {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bolder;
        color: @colorTextDark;
        padding: 30px;
    }

    .section-tabs {
        display: flex;
        border-bottom: 2px solid @colorLine;
        color: @colorTextLight;
        overflow-y: auto;
        width: 100%;

        .section-tab {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 10px;
            border: 1px solid @colorLine;
            border-right: none;
            border-bottom: none;
            font-weight: bolder;
            min-width: fit-content;
            cursor: pointer;

            &.active {
                background-color: @colorTextDark;
                color: @colorTextInverted;
                border-color: @colorTextDark;
            }

            &:first-child {
                border-top-left-radius: 10px;
            }

            &:last-child {
                border-top-right-radius: 10px;
                border-right: 1px solid @colorLine;
            }
        }
    }

}