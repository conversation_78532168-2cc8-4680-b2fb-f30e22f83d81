import React, { useEffect, useRef, useState } from "react";
import { useFormFilters } from "@hooks/form-data";
import { useNavigation } from "@core/navigation";
import type { DSLCardOnChangeModeCallback, DSLCardOnSaveCallBack, DSLCardViewProps, TabModes } from "./dsl-card-view";
import DSLCardView from "./dsl-card-view";
import { uuid } from "@utils/fx";
import { useUpdateHash } from "@hooks/update-hash";
import { RoutedComponentProps } from "@typedefs/routed-component";
import LoaderNoData from "@components/common/loader-no-data";
import NoDataFound from "@components/common/no-data-found";

interface CardMaxRowOneProps extends DSLCardViewProps {
	maxRowFilter: { [key: string]: unknown };
}

const CardMaxRowOne: React.FC<CardMaxRowOneProps> = (props) => {
	if (!props.disableRouting) {
		useNavigation(props as RoutedComponentProps, "/");
	}
	const [hash, updateHash] = useUpdateHash();
	if (!props.form) {
		return null;
	}
	if (!window.DSL[props.form]) {
		return null;
	}
	const [mode, setMode] = useState<string>("");
	const [Id, setID] = useState<string | number | null>(null);

	const [fd, refresh] = useFormFilters(props.form, {
		filter: props.maxRowFilter,
		sortProperty: "id",
		sortDirection: "desc",
		fields: "min",
		limit: 1,
	});

	useEffect(() => {
		if (fd.loading || fd.failed) return;
		setMode(fd.data.length ? "read" : "addfill");
		setID(fd.data.length ? fd.data[0].id : uuid());
	}, [fd]);

	const onSaved: DSLCardOnSaveCallBack = (fd, tab, ref) => {
		if (!ref) {
			return;
		}
		let cv = `card${ref.card}` as "cardform" | "cardread";
		const cm = ref[cv].mode;
		setID(fd.record);
		setMode(cm);
	};

	const changeMode: DSLCardOnChangeModeCallback = (mode, id, tab, ref) => {
		if (mode == ("list" as TabModes)) {
			refresh();
			setMode("read");
			updateHash();
			return;
		}
		setMode(mode);
		setID(`${id}`);
	};

	const rxOpts = {
		onSaved: onSaved,
		changeMode,
	};

	if (fd.loading || !Id) return <LoaderNoData loading={fd.loading} />;
	if (fd.failed)
		return (
			<NoDataFound>
				{" "}
				<div>{"Unable to fetch data"}</div>
			</NoDataFound>
		);
	return (
		<DSLCardView
			key={`card-${Id}=${hash}`}
			{...props}
			isActive={true}
			isParentActive={true}
			disableRouting={true}
			card={mode as TabModes}
			xid={Id as string}
			form={props.form}
			record={["add", "addfill"].includes(mode) ? undefined : Id}
			ddRef={(ref) => {
				if (props.ddRef) props.ddRef(ref);
			}}
			tabData={{
				tkey: Id,
				id: Id,
				label: "",
				form: props.form,
				mode: mode,
			}}
			maxRowOne={true}
			overrideDos={{
				archive: {
					enabled: false,
				},
				unarchive: {
					enabled: false,
				},
			}}
			{...rxOpts}
		/>
	);
};

export default CardMaxRowOne;
