@import (reference) "../../less/style/main.less";

.base-style-primary {
	background-color: #837bb2 !important;
	&:hover,
	&:focus {
		background: #f6f5ff !important;
		.inner-cont {
			.icon {
				filter: brightness(0) saturate(100%) invert(54%) sepia(31%) saturate(505%) hue-rotate(209deg)
					brightness(87%) contrast(84%) !important;
			}
			p.label {
				color: #837bb2 !important;
			}
			> i {
				color: #837bb2 !important;
			}
		}
	}

	.inner-cont {
		.icon {
			filter: brightness(0) saturate(100%) invert(99%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(105%)
				contrast(101%) !important;
		}
		p.label {
			color: white !important;
		}
		> i {
			color: white !important;
		}
	}
}

.base-style-info {
	background-color: #5b83b0 !important;
	&:hover,
	&:focus {
		background: #c6ddf7 !important;
		.inner-cont {
			color: #5b83b0 !important;
			.icon {
				filter: brightness(0) saturate(100%) invert(51%) sepia(18%) saturate(1006%) hue-rotate(171deg)
					brightness(94%) contrast(90%) !important;
			}
			p.label {
				color: #5b83b0 !important;
			}
			> i {
				color: #5b83b0 !important;
			}
		}
	}
	.inner-cont {
		.icon {
			filter: brightness(0) saturate(100%) invert(99%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(105%)
				contrast(101%) !important;
		}
		p.label {
			color: white !important;
		}
		> i {
			color: white !important;
		}
	}
}

.base-style-error {
	background-color: #e58787 !important;
	&:hover,
	&:focus {
		background: #ffe5e5 !important;
		.inner-cont {
			color: #e58787 !important;
			.icon {
				filter: brightness(0) saturate(100%) invert(63%) sepia(7%) saturate(3082%) hue-rotate(314deg)
					brightness(104%) contrast(80%) !important;
			}
			p.label {
				color: #e58787 !important;
			}
			> i {
				color: #e58787 !important;
			}
		}
	}
	.inner-cont {
		.icon {
			filter: brightness(0) saturate(100%) invert(99%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(105%)
				contrast(101%) !important;
		}
		p.label {
			color: white !important;
		}
		> i {
			color: white !important;
		}
	}
}

.base-style-warning {
	background-color: #e89e64 !important;
	&:hover,
	&:focus {
		background: #ffead9 !important;
		.inner-cont {
			color: #e89e64 !important;
			.icon {
				filter: brightness(0) saturate(100%) invert(92%) sepia(9%) saturate(6335%) hue-rotate(312deg)
					brightness(96%) contrast(90%) !important;
			}
			p.label {
				color: #e89e64 !important;
			}
			> i {
				color: #e89e64 !important;
			}
		}
	}
	.inner-cont {
		.icon {
			filter: brightness(0) saturate(100%) invert(99%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(105%)
				contrast(101%) !important;
		}
		p.label {
			color: white !important;
		}
		> i {
			color: white !important;
		}
	}
}

.base-style-accept {
	background-color: #7db8a5 !important;
	&:hover,
	&:focus {
		background: #e1faf2 !important;
		.inner-cont {
			color: #7db8a5 !important;
			.icon {
				filter: brightness(0) saturate(100%) invert(75%) sepia(32%) saturate(320%) hue-rotate(110deg)
					brightness(87%) contrast(86%) !important;
			}
			p.label {
				color: #7db8a5 !important;
			}
			> i {
				color: #7db8a5 !important;
			}
		}
	}
	.inner-cont {
		.icon {
			filter: brightness(0) saturate(100%) invert(99%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(105%)
				contrast(101%) !important;
		}
		p.label {
			color: white !important;
		}
		> i {
			color: white !important;
		}
	}
}

.base-style-default {
	&:hover,
	&:focus {
		background: #fafafa;
	}
	.style-imp {
		color: #414651;

		.icon {
			filter: brightness(0) saturate(100%) invert(23%) sepia(23%) saturate(325%) hue-rotate(183deg)
				brightness(100%) contrast(89%) !important;
		}
		p.label {
			color: #414651;
		}
		> i {
			color: #414651 !important;
		}
	}
	.style-imp;
	.inner-cont {
		.style-imp;
	}
}

.dsl-action-btn.wizard-bottom-btn-default.disabled {
	cursor: not-allowed !important;
	&:hover,
	&:focus {
		background: #f5f5f5 !important;
		color: #a4a7ae !important;
		box-shadow: 0px -1px 2px 0px #00000038 inset, 0px 1px 2px 0px #ffffff1f inset !important;
	}
	.style-imp {
		background: #f5f5f5 !important;
		color: #a4a7ae !important;

		.icon {
			filter: brightness(0) saturate(100%) invert(69%) sepia(12%) saturate(141%) hue-rotate(184deg)
				brightness(96%) contrast(87%) !important;
		}
		p.label {
			color: #a4a7ae !important;
		}
		> i {
			color: #a4a7ae !important;
		}
	}
	.style-imp;
	.inner-cont {
		.style-imp;
	}
}

.base-form-button {
	height: 36px;
	gap: 4px;
	padding-top: 8px !important;
	padding-right: 12px !important;
	padding-bottom: 8px !important;
	padding-left: 12px !important;
	border-radius: 8px !important;
	box-shadow: 0px -1px 2px 0px #00000038 inset, 0px 1px 2px 0px #ffffff1f inset;
	.inner-cont {
		.icon {
			height: var(--spacing-xxxxlarge) !important;
			width: var(--spacing-xxxxlarge) !important;
		}
		p.label {
			font-weight: 500;
			font-size: 14px;
			line-height: 20px;
			letter-spacing: 0;
			color: #414651;
		}
		> i {
			color: #414651 !important;
		}
	}
	&:hover,
	&:focus {
		box-shadow: inset 1px 1px 4px rgba(000, 000, 000, 0.14), inset -1px -1px 2px rgba(255, 255, 255, 0.45) !important;
	}
	&.style-primary {
		.base-style-primary;
	}
	&.style-info {
		.base-style-info;
	}
	&.style-error {
		.base-style-error;
	}
	&.style-warning {
		.base-style-warning;
	}
	&.style-default {
		.base-style-default;
	}
	&.style-accept {
		.base-style-accept;
	}
}

.dsl-action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-around;
	height: auto;
	min-width: 30px !important;
	flex-shrink: 0;
	cursor: pointer;
	text-transform: capitalize;
	width: auto;
	max-width: fit-content;
	&:not(.wizard-bottom-btn-default) {
		&:hover:not(.disabled) {
			.inner-cont {
				.icon {
					filter: brightness(5);
				}

				.label {
					background-color: @black;
					color: @white;
				}
			}
		}
	}
	.inner-cont {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		min-width: 50px;
		gap: 5px;

		.icon {
			height: var(--btn-img-height);
			width: var(--btn-img-width);
			align-self: center;
		}

		.label {
			background-color: inherit;
			color: inherit;
			.para-three;
			margin: 0;
			padding: 0;
		}
	}
}

.action-btn-group-cnt {
	user-select: none;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	gap: var(--spacing-standard);
	overflow-x: auto;
	width: auto;
	padding: 0px 4px;

	&.nolabel {
		.label {
			display: none;
		}

		.dsl-action-btn {
			max-width: 34px !important;
		}
	}

	&.vertical {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		min-width: fit-content;
		background-color: @light-soft-gray !important;
		box-shadow: 0 1px 4px 0 rgba(24, 16, 10, 0.08) !important;
		background: @white !important;
		padding: 10px;
		border-radius: 12px;

		.dsl-action-btn {
			min-width: 100% !important;
		}
	}

	.btn-print {
		.base-style-default;
	}

	.btn-archive {
		.base-style-error;
	}
	.btn-unarchive {
		.base-style-accept;
	}

	.btn-verify {
		.base-style-default;
	}

	.btn-review {
		.base-style-accept;
	}

	.btn-cancel {
		.base-style-error;
	}

	.btn-more {
		.base-style-default;
	}

	.wizard-bottom-btn-default {
		border: 0px solid transparent;
		border-radius: var(--radius-xlarge);
		font-size: var(--font-size-13);
		line-height: var(--line-height-16);
		font-weight: var(--font-weight-medium);
		cursor: pointer;
		background-color: var(--color-text-50);
		margin: 4px 0px;
		padding: 8px 12px !important; // todo
		color: var(--white);
		box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;

		.inner-cont {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			width: auto;
			gap: var(--spacing-xsmall);

			.icon {
				height: var(--spacing-xxxxxlarge);
				width: var(--spacing-xxxxxlarge);
				align-self: center;
				filter: brightness(0);
			}

			.label {
				line-height: var(--line-height-medium);
				font-weight: var(--font-weight-medium);
				font-size: var(--font-size-xsmall);
				color: var(--color-text-700);
			}
		}
	}

	.btn-save {
		.base-style-primary;
	}

	.btn-edit {
		.base-style-primary;
	}

	.btn-cancel {
		.base-style-error;
	}

	.btn-print {
		.base-style-default;
	}

	.btn-sections {
		display: none;
	}

	.btn-special_event {
		.base-style-default;
	}

	@media (max-width: 740px) {
		width: 100%;
		position: relative;
		flex-direction: row-reverse;
		height: 60px;
		overflow: visible;
		padding: 5px;
		z-index: 3;

		.btn-sections {
			.centered-col;
		}
	}
}
