@import (reference) "../../less/style/main.less";
@import "../../dsl-form/styles/main.less";

.tab-level-3-list {
    display: flex;
    width: 100%;
    // padding: 14px 18px;
    background-color: #F5F5F5 !important;
    gap: 16;
    flex-wrap: wrap;
    padding: 12px 16px !important;
    border-radius: 8px !important;

    .tab-list-button {
        display: flex;
        min-width: max-content !important;
        cursor: pointer;
        background-color: none;
        align-items: center;
        gap: 0;

        .tab-label {
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            user-select: none;
            color: #535862;
            padding: 0 !important;
            font-size: 14px;
            font-weight: 500;
            border-bottom: none;
        }

        &.toggle-tab {
            box-shadow: none;
            accent-color:  #668EBA !important;

            .tab-toggle {
            &:checked {
                &:disabled {
                background-image: none;
                }
            }
            }

            .tab-label {
            background-color: none !important;
            color: #707580;
            border-bottom: none;
            margin-top: 3px !important;
            }

            input {
                &[disabled]{
                    -webkit-text-fill-color: white;
                }

            &[type="checkbox"] {
                border-radius: 30%;
                border: 2px solid #668EBA; 
                appearance: none;
                -webkit-appearance: none;
                outline: none;
                cursor: pointer; 
                position: relative;
    
                &:checked {
                    background-color: #668EBA;
                }
                &:checked::after {
                    content: '\2713';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 12px;
                    color: white; 
                }
                }
            }
        }

        &.tab-active {
            background: #FFFFFF !important;

            .tab-label {
                color: var(--color-tertiary);
                // padding: 8px 12px !important;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 700;
                border-bottom: none;
                box-shadow: 0px -1px 2px 0px #00000038 inset;
                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset;
            }

            cursor: pointer;

            &::after {
                cursor: pointer;
            }
        }
    }

    &::before {
        content: none;
    }
}