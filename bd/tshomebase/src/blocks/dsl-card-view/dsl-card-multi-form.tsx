import DSLCardView, {
	DSLCardOnChangeModeCallback,
	DSLCardViewProps,
	DSLCardViewRef,
	FlyoutContainer,
	TAB_CLICK_BTN_NAME,
	TabActions,
	TabClick,
	TabModes,
} from "@blocks/dsl-card-view/dsl-card-view";
import React, { FC, useMemo } from "react";
import "./dsl-card-mutli-form.less";
import { useFormTabController } from "@hooks/form-tab-controller";
import { TabData, TabList, getTabKey } from "@blocks/tab-list/tab-list";
import { uuid } from "@utils/fx";

interface DSLCardMultiFormProps extends DSLCardViewProps {
	cards: (DSLCardViewProps & {
		label?: string;
		icon?: string;
		tabBtns: Record<TabModes, TabClick[]>;
		preview: any;
	})[];
	isFlyout?: boolean;
	label?: string;
	cancel?: () => void;
}

const getOverrideBtns = (tabBtns: Record<TabModes, TabClick[]>) => {
	const overrideDos: Record<TabModes, TabActions> = {
		read: {
			cancel: {
				enabled: false,
			},
		},
		add: {
			cancel: {
				enabled: false,
			},
		},
		addfill: {
			cancel: {
				enabled: false,
			},
		},
		edit: {
			cancel: {
				enabled: true,
				label: "Close",
			},
		},
	};
	for (const [mode, actions] of Object.entries(tabBtns || {})) {
		if (!overrideDos[mode as TabModes]) {
			overrideDos[mode as TabModes] = {};
		}
		for (const action of TAB_CLICK_BTN_NAME) {
			overrideDos[mode as TabModes][action] = {
				enabled: actions.includes(action),
			};
		}
	}
	return overrideDos;
};

export const DSLCardMultiForm: React.FC<DSLCardMultiFormProps> = (props) => {
	const tabs = useMemo(() => {
		return props.cards.map((tab, index) => {
			let mode = tab.card ? tab.card : "read";
			let id = tab.record || "";
			if (!tab.record) {
				mode = "addfill";
				id = uuid();
			}
			if (tab.preview) {
				tab.previewComponentProps = tab.preview;
				tab.PreviewComponent = DSLCardView as FC<unknown>;
			}

			return {
				id,
				tkey: getTabKey(id, tab.form),
				form: tab.form,
				label: tab.label,
				mode: tab.card ? tab.card : "read",
				componentProps: {
					...tab,
					overrideDos: getOverrideBtns(tab.tabBtns),
				},
			};
		}) as TabData[];
	}, []);

	const [openTabs, activeTabId, controller] = useFormTabController(tabs, tabs?.[0]?.tkey);

	const changeMode: DSLCardOnChangeModeCallback = (mode, id, tab, ref) => {
		if (ref?.xid?.toString().includes("-")) {
			return;
		}
		if ((mode as string) == "list") {
			mode == "read";
		}
		controller.setOpenTabs((p: TabData[]) => {
			const ctkey = getTabKey(id, tab.form);
			const ci = p.findIndex((t) => t.tkey == ctkey);
			return p.map((t, ti) => {
				if (ti == ci && t.mode != mode) {
					return { ...t, mode: mode };
				}
				return t;
			});
		});
	};

	const rxOpts = {
		onSaved: controller.onSaved,
		onArchive: controller.onArchive,
		onUnArchive: controller.onUnArchive,
		rowClicked: controller.rowClicked,
		openTab: controller.openTab,
		changeMode: changeMode,
	};

	const onLoad = (view: DSLCardViewRef) => {
		const card = view.card as TabModes;
	};

	return (
		<FlyoutContainer
			isFlyout={props.isFlyout || false}
			label={props.label || ""}
			onClose={() => {
				props?.cancel?.();
			}}
		>
			<div className="dsl-card-multi-form">
				<TabList
					activeTabId={activeTabId}
					openTabs={openTabs}
					optionsProps={{
						enabled: false,
					}}
					addProps={{
						enabled: false,
					}}
					tabCanClose={false}
					draggable={false}
					onTabClick={(tab: TabData) => {
						controller.setActiveTabId(tab.tkey);
					}}
					styles={{
						tabListStyle: "lvl-2-tab-list",
					}}
					onTabClose={(tab: TabData) => {
						controller.onTabCancel(tab);
					}}
				/>
				<div className="tab-content-container">
					{openTabs.map((tab) => {
						const { id, tkey, form } = tab;
						const card = tab.mode || ("read" as TabModes);
						const record = ["add", "addfill"].includes(tab.mode) ? undefined : id;
						const overrideDos = (tab.componentProps?.overrideDos || {}) as Partial<
							Record<TabModes, TabActions>
						>;
						return (
							<div
								key={tab.tkey + tab.mode}
								className="dsl-container"
								style={{ display: activeTabId == tab.tkey ? undefined : "none" }}
							>
								<DSLCardView
									{...tab.componentProps}
									disableRouting={true}
									tabData={tab}
									isFlyout={props.isFlyout || false}
									key={tkey}
									card={(tab.mode || "read") as TabModes}
									xid={id}
									form={form}
									record={record}
									ddRef={(view) => {
										onLoad(view);
										controller.setDSLFormRef(tab, view);
									}}
									overrideDos={{
										...(overrideDos[card as TabModes] || {}),
										print: {
											enabled: false,
										},
										archive: {
											enabled: false,
										},
									}}
									{...rxOpts}
								/>
							</div>
						);
					})}
				</div>
			</div>
		</FlyoutContainer>
	);
};

export default DSLCardMultiForm;
