@import (reference) "../../less/style/main.less";

.form-active-users {
    display: flex;
    gap: 10px;

    .active-user {
        display: flex;
        flex-direction: row;
        gap: 10px;

        .active-user-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .no-photo {
                width: 100%;
                height: 100%;
                background-color: @mid-gray;
            }
        }

        .active-user-info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .active-user-name {
                font-size: 14px;
                font-weight: 500;
                color: @black;
            }

            .active-user-timestamp {
                font-size: 13px;
                font-weight: 500;
                color: @charcoal-gray;
            }
        }
    }
}