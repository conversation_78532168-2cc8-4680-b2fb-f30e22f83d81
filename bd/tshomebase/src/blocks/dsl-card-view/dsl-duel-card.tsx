import DSLCardView, {
	DSLCard<PERSON>iewProps,
	DSLCardViewRef,
	DuelCardsRefs,
	IDSLDrawForm,
} from "@blocks/dsl-card-view/dsl-card-view";
import React, { FC, useRef, useState } from "react";
import "./dsl-card-with-preview.less";
import { useUpdateHash } from "@hooks/update-hash";
import { DSLDrawLinkMap } from "@typedefs/coffee/dsl";

export type DSLDuelCardProps = {
	master: DSLCardViewProps;
	slave: DSLCardViewProps;
	setValidationErrors: (errors: any) => void;
	validationErrors: any;
	flip?: boolean;
};

export const DSLDuelCard: React.FC<DSLDuelCardProps> = (props) => {
	const { master, slave } = props;
	const [updateHash, refreshHash] = useUpdateHash();
	const [card, setCard] = useState(props.master.card);
	const refs = useRef<DuelCardsRefs>({
		master: {
			name: "Player 1",
			player: "<PERSON><PERSON><PERSON>",
			position: props.flip ? "left" : "right",
			record: master.record,
			form: master.form,
			wrapper: null,
		},
		slave: {
			name: "Player 2",
			player: "<PERSON><PERSON>/<PERSON><PERSON>/Dyanae/Dinael/Dinay/<PERSON>h/Donna/Dyane",
			position: props.flip ? "right" : "left",
			record: slave.record,
			form: slave.form,
			wrapper: null,
		},
	});

	const setWrapper = (type: "master" | "slave", cr: DSLCardViewRef) => {
		const wrap = cr.cardform?.ddf || cr.cardread?.ddr;
		refs.current[type].wrapper = wrap;
		if (wrap) {
			wrap.duelCardRefs = refs.current;
		}
	};

	const updateSlaveLinkMap = () => {
		if (!refs.current.master.wrapper || !refs.current.slave.wrapper) {
			return;
		}
		const master = refs.current.master.wrapper;
		const slave = refs.current.slave.wrapper;
		const link = master?.options?.parent?.link;
		const links = master?.options?.parent?.links;
		const linkid = master?.options?.parent?.linkid;
		if (master?.options?.parent) {
			slave.options.parent.link = link;
			slave.options.parent.links = links;
			slave.options.parent.linkid = linkid;
			slave.subforms.forms.forEach((formX) => {
				formX.options.link = link;
				formX.options.links = links;
				formX.options.linkid = linkid;
			});
		}
	};

	return (
		<div className={`dsl-card-duel-card ${props.flip ? "flip" : ""}`}>
			<DSLCardView
				key={updateHash}
				{...props.master}
				autoRecoverEnabled={false}
				disableRouting={props.master.isFlyout ? true : false}
				card={card}
				validation_errors={props.validationErrors || {}}
				ddRef={(ref) => {
					setWrapper("master", ref);
					master?.ddRef?.(ref);
				}}
				onFormReady={() => {
					try {
						props.master?.onFormReady?.();
						updateSlaveLinkMap();
					} catch (error) {
						console.error(error);
					}
				}}
				changeMode={(mode, id, td, ref) => {
					props.setValidationErrors(ref.validation_errors || {});
					if (card != mode) {
						setCard(mode);
						refreshHash();
					}
					props.master?.changeMode?.(mode, id, td, ref);
				}}
				PreviewComponent={DSLCardView}
				previewComponentProps={{
					...props.slave,
					links: props.slave?.links || props.master?.links || [],
					link: props.slave?.link || props.master?.link || "",
					linkid: props.slave?.linkid || props.master?.linkid || {},
					autoRecoverEnabled: false,
					tkey: updateHash,
					card: slave.card ? slave.card : card,
					disableRouting: true,
					ddRef: (ref: DSLCardViewRef) => setWrapper("slave", ref),
					onFormReady: () => {
						try {
							props.slave?.onFormReady?.();
							updateSlaveLinkMap();
						} catch (error) {
							console.error(error);
						}
					},
				}}
			/>
		</div>
	);
};
export default DSLDuelCard;
