.card-with-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    // margin-top: 10px;

    .cardmenu {
        display: none !important;
    }

    .form-container {
        >.form-container {
            padding: 0px !important;
            margin: 0px !important;
            height: 100%;
        }
    }

    .layout-form-content {
        .form-container {
            >div:first-child {
                flex: 1 1 40% !important;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            >div:last-child {
                flex: 1 1 60% !important;
            }
        }
    }
}

.dsl-card-duel-card {
    margin-inline: 10px;
    .card-with-preview;
    width: 100%;

    >.form-container {
        >div:first-child {
            flex-shrink: 0;
            flex-grow: 0;
            width: 50%;
            flex-basis: calc(50% - 10px);
        }

        >div:last-child {
            flex-shrink: 0;
            flex-grow: 0;
            width: 50%;
            flex-basis: calc(50% - 10px);
        }
    }

    >div.form-container {
        >div.form-container {
            >div.dsl-templ-area {
                .cardarea {
                    >h3 {
                        display: none;
                    }
                }
            }
        }
    }

    &.flip {
        >.form-container {
            flex-direction: row-reverse;
        }
    }

    .layout-form-content {
        .form-container {
            >div:first-child {
                flex: 1 1 50% !important;
            }

            >div:last-child {
                flex: 1 1 50% !important;
                height: 100%;
            }
        }
    }
}