body.has-flyout {
    #application {
        #flyout {
            position: absolute;
            right: 0;
            bottom: 0;

            .cardmenu {
                width: 300px - 20px;
                background: transparent;
            }

            .form {
                width: 100%;
                padding: var(--spacing-large);
                position: fixed;
                z-index: 20000;
                top: 0;
                right: 0;
                overflow: hidden;
                overflow-y: auto;
                display: flex;
                align-items: center;
                height: 100vh;
                justify-content: center;
                background-color: rgba(67, 67, 67, 0.5);
                backdrop-filter: blur(4px);

                .form-segment-container-flyout {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    width: 100%;
                    height: 100%;
                    .inc-container {
                        border: 2px solid #fafafad9;
                        opacity: .99;
                        width: 100%;
                        margin: 0px;
                        background-color: var(--gray-80-percent);
                        .header-container {
                            padding-bottom: 0px;
                        }
                    }
                }

                .form-container-flyout {
                    display: flex;
                    flex-direction: column;
                    border-radius: var(--radius-xlarge);
                    height: 100%;
                    background-color: var(--gray-80-percent);
                    padding: var(--spacing-xxxlarge);
                    gap: var(--spacing-standard);
                    width: 100%;
                    border: 2px solid rgba(255, 255, 255, 0.5);
                    min-height: 400px;

                    .form-flyout-header-bar {
                        min-height: var(--header-min-height);
                        max-height: var(--header-max-height);
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        color: var(--color-secondary);
                        padding: 0px var(--spacing-standard);
                        font-size: var(--font-size-standard);
                        font-weight: var(--font-weight-medium);

                        .close-flyout {
                            cursor: pointer;

                            img {
                                width: 14px;
                                height: 14px;
                            }
                        }
                    }

                    .layout-form-content {
                        display: flex;
                        flex-direction: column;
                        gap: var(--spacing-standard);
                        height: calc(~"100% - "(var(--wizard-bottom-bar-height) - var(--layout-form-content-container)));
                        flex: 1;

                        .form-container {
                            padding: 0px var(--flyout-form-container-p);
                            padding-bottom: 0;
                            max-height: calc(100vh - var(--flyout-top-tmpp));
                        }
                    }

                    .carddrop {
                        .fa-history {
                            display: none;
                        }
                    }

                    .cardform {
                        &:has(.cardarea > h3) {
                            .carddrop {
                                padding-top: var(--spacing-small-standard);

                            }
                        }
                    }
                }


                #sections {
                    height: 1px;
                    overflow-y: hidden;
                }

                .select2-dropdown-open {

                    .select2-choice,
                    .select2-choices {
                        z-index: 29999;
                    }
                }

                .form-group:has(.select2-dropdown-open) {
                    .control-label {
                        z-index: 30000 !important;
                    }
                }

                .select2-drop {
                    z-index: 29998;
                }

                .select2-drop-mask {
                    z-index: 29997;
                }

                .tabhead {
                    visibility: hidden !important;
                    background: lighten(#1F9C71, 5%);
                    z-index: 30000;

                    h1,
                    h2 {
                        color: #fff;
                    }
                }

                .tabarea {
                    min-height: 100%;

                    .compare {

                        .cardmenu,
                        .readonlymode {
                            display: none;
                        }

                        .newdata {
                            display: block !important;
                            position: absolute;
                            left: 0;
                        }

                        .cardarea {
                            box-shadow: none;

                            .form-horizontal {
                                width: 70%;

                                .newvalue {
                                    position: absolute;
                                    font-weight: bold;
                                    color: #090;
                                }
                            }
                        }
                    }

                    .flyout_html {
                        padding: 12px 24px 24px;

                        h4 {
                            padding-top: 12px;
                        }

                        ul {
                            list-style-type: disc;
                            margin-left: 16px;

                            li {
                                margin-bottom: 8px;
                            }
                        }
                    }
                }

                // @reduce-height: var(--flyout-height);
                @reduce-height: 45px;


                // &.lvl-1-flyout {
                //     height: calc(100vh - @reduce-height);
                //     top: @reduce-height;

                //     .form-container-flyout {
                //         height: 100% !important;
                //     }

                //     .form-container {
                //         height: calc(100vh - (var(--flyout-top-tmpp) + var(--reduce-height))) !important;
                //     }
                // }

                // &.lvl-2-flyout {
                //     height: calc(100vh - (@reduce-height * 2));
                //     top: (@reduce-height * 2);

                //     .form-container-flyout {
                //         height: 100% !important;
                //     }

                //     .form-container {
                //         height: calc(100vh - (var(--flyout-top-tmpp) + (var(--reduce-height) * 2))) !important;
                //     }
                // }

                // &.lvl-3-flyout {
                //     height: calc(100vh - (@reduce-height * 3));
                //     top: (@reduce-height * 3);

                //     .form-container-flyout {
                //         height: 100% !important;
                //     }

                //     .form-container {
                //         height: calc(100vh - (var(--flyout-top-tmpp) + (var(--reduce-height) * 3))) !important;
                //     }
                // }

                // &.lvl-4-flyout,
                // &.lvl-5-flyout,
                // &.lvl-6-flyout,
                // &.lvl-7-flyout,
                // &.lvl-8-flyout,
                // &.lvl-9-flyout,
                // &.lvl-10-flyout {
                //     height: calc(100vh - (@reduce-height * 4));
                //     top: (@reduce-height * 4);

                //     .form-container-flyout {
                //         height: 100% !important;
                //     }

                //     .form-container {
                //         height: calc(100vh - (var(--flyout-top-tmpp) + (var(--reduce-height) * 4))) !important;
                //     }
                // }
            }

            .blocker {
                position: fixed;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                background: rgba(67, 67, 67, 0.5);
                z-index: 19999;
                opacity: 1;
                pointer-events: auto;
                inset: 0px;
                background-color: rgba(67, 67, 67, 0.5);
                backdrop-filter: blur(4px);
            }


            .hide-close-button {
                .close-flyout {
                    visibility: hidden;
                }

                .form-flyout-header-bar {
                    color: #999;
                }
            }
        }
    }
}