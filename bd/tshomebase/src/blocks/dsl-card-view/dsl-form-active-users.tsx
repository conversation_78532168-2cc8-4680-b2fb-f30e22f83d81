import { EllipsesLoader } from "@components/common";
import { EditingUser } from "@typedefs/window";
import React, { FC, useEffect, useRef, useState } from "react";
import { useReadLocalStorage } from "usehooks-ts";
import "./dsl-form-active-users.less";

type FormActiveUsersBaseProps = {
	form: string;
	record: number;
	mode: string;
};

interface ActiveUserList extends Partial<EditingUser> {
	last_edit: string;
}

export const FormActiveUsers: FC<FormActiveUsersBaseProps> = (props) => {
	const { form, record, mode } = props;
	const [loading, setLoading] = useState(true);
	const renderTs = useRef<number>(Date.now());
	const ts = useReadLocalStorage<number>("editing_changed");
	const [activeList, setActiveList] = useState<ActiveUserList[]>([]);

	useEffect(() => {
		setLoading(mode == "read" ? false : true);
		const to = setTimeout(() => {
			setLoading(false);
		}, window.Auth.status_time());
		return () => clearTimeout(to);
	}, [mode]);

	useEffect(() => {
		if (!ts || ts < renderTs.current) {
			return;
		}
		const users: ActiveUserList[] = [];
		const editing = window.App.user.editing;
		if (editing?.forms && editing.forms[form]) {
			const ei = editing.forms[form]?.[record] || {};
			for (const [id, ts] of Object.entries(ei)) {
				users.push({
					...(window.App.user.editing?.users?.[id] || {}),
					last_edit: window.moment(new Date(ts)).format("MM/DD/YYYY hh:mma"),
				});
			}
		}
		setActiveList(users);
		if (loading) {
			setLoading(false);
		}
	}, [ts]);

	const TeamsLink: FC<{ sso?: string | null; children: React.ReactElement }> = (props) => {
		if (props.sso) {
			return <a href={`msteams://l/chat/0/0?users=${props.sso}`}>{props.children}</a>;
		}
		return props.children;
	};

	return (
		<div key={"no-render"} className="form-active-users hide" {...props}>
			{loading && <EllipsesLoader />}
			{!loading &&
				activeList.map((user) => {
					return (
						<div key={user.id} className="active-user">
							<div className="active-user-photo" key={user.id}>
								<TeamsLink sso={user.sso}>
									{user.photo ? (
										<img key={user.id} src={user.photo} alt={user.name} />
									) : (
										<div className="no-photo"></div>
									)}
								</TeamsLink>
							</div>
							<div className="active-user-info">
								<div className="active-user-name">
									{window.joinValid(["Editing:", user?.firstname, user?.lastname], " ")}
								</div>
								<div className="active-user-timestamp" key={user.last_edit}>
									{window.joinValid(["Since: ", user.last_edit], " ")}
								</div>
							</div>
						</div>
					);
				})}
		</div>
	);
};
