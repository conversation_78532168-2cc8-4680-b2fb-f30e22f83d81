@import (reference) '../../less/style/main.less';


@media (max-width: 1200px) {

    .form-container {
        .cardarea {
            .fieldgroup {
                .form-horizontal {

                    &.form-col-3,
                    &.form-col-4 {
                        width: 50% !important;
                    }

                    .form-group {

                        .controls {

                            >.checkboxes {

                                &.checkbox-3,
                                &.checkbox-4,
                                &.checkbox-5,
                                &.checkbox-6,
                                &.checkbox-7,
                                &.checkbox-8 {
                                    label.checkbox {
                                        width: 45% !important;
                                    }
                                }
                            }
                        }
                    }
                }


            }
        }

    }
}

@media (max-width: 992px) {
    .form-card-menu {
        display: none !important;
        z-index: -1;
        transition: 0.5s ease;

        &.cardmenu-show {
            display: block !important;
            z-index: 3;
        }
    }

    .cardform,
    .cardread {
        margin-right: 0px;
    }

    .cardarea {
        // padding: 10px;
    }

    .form-horizontal .form-group {
        display: flex;
        flex-direction: column;

        >label {
            text-align: left;
            margin: 0px;
            .form-label-one;
        }

        .manage {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            width: 43%;
        }

        .controls-grid {
            overflow: auto;
            width: 95%;
        }

        .note {
            width: 90%;
        }
    }

    .cardform,
    .cardread {
        .cardbar {
            display: flex;
            flex-direction: column;

            .formerror {
                width: 100%;
            }

            .form-buttons-bottom {
                display: flex;
                flex-direction: row-reverse;
                justify-content: space-between;
                width: 100%;
            }
        }
    }
}


@media (max-width: 768px) {
    .form-container {
        .cardarea {
            .fieldgroup {
                .form-horizontal {

                    &.form-col-2,
                    &.form-col-3,
                    &.form-col-4 {
                        width: 100% !important;
                    }

                    &.form-col-addr_1 {
                        width: 100% !important;
                    }

                    &.form-col-addr_2,
                    &.form-col-addr_city,
                    &.form-col-addr_state,
                    &.form-col-addr_zip {
                        width: 50% !important;
                    }
                }
            }
        }
    }

    .action-btn-cnt {
        height: auto;
    }
}

@media (max-width: 576px) {
    .form-container {
        .cardarea {
            .fieldgroup {
                .form-horizontal {
                    .form-group {
                        .controls {
                            >.checkboxes {

                                &.checkbox-2,
                                &.checkbox-3,
                                &.checkbox-4,
                                &.checkbox-5,
                                &.checkbox-6,
                                &.checkbox-7,
                                &.checkbox-8 {
                                    label.checkbox {
                                        width: 100% !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


@media (max-width: 425px) {
    .form-container {
        .cardarea {
            .fieldgroup {
                .form-horizontal {
                    &.form-col-modifier {
                        width: 50% !important;
                    }
                }
            }
        }
    }
}


@media (max-width: 320px) {
    .form-container {
        .cardarea {
            .fieldgroup {
                .form-horizontal {

                    &.form-col-addr_1,
                    &.form-col-addr_2,
                    &.form-col-addr_city,
                    &.form-col-addr_state,
                    &.form-col-addr_zip {
                        width: 100% !important;
                    }
                }
            }
        }
    }
}