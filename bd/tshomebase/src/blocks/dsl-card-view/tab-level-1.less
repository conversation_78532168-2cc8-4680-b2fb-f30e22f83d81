.level-1-tab-list {
    border-top-right-radius: 8px;
    background-color: transparent;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100% !important;
    margin-top: 0px !important;
    flex-wrap: nowrap !important;

    .right-side-curve {
        position: relative;
        z-index: 1;
        &:first-child {
            display: none;
        }
        //right bottom curve
        &:not(:first-child) {
            &::before {
                content: "";
                border-bottom: 1px solid #FFFFFF70;
                border-left: 1px solid #FFFFFF70;
                border-bottom-left-radius: 16px;
                width: 10px;
                height: 12px;
                position: absolute;
                top: 92%;
                left: 8px;
                -webkit-mask-image: linear-gradient(to top, black 70%, transparent 100%);
                mask-image: linear-gradient(to top, #000 100%, #0000 -100%);
                transform: rotate(-9deg);
            }
        }

    }

    .tab-list-button {
        display: flex;
        min-width: max-content !important;
        cursor: pointer;
        position: relative;
        --r: 0.8em;

        background-color:#FAFAFA70;
        color: #707580;
        border-inline: var(--r) solid #0000;
        border-radius: calc(2 * var(--r)) calc(2 * var(--r)) 0 0 / var(--r);
        mask:
            radial-gradient(var(--r) at var(--r) 0, #0000 98%, #000 101%) calc(-1 * var(--r)) 101% / 100% var(--r) repeat-x,
            conic-gradient(#000 0 0) padding-box;
        gap: 0;
        margin: 1px -9px -2px 0;

        &::before{
            content: "";
            border-top-left-radius: 11px;
            border-top-right-radius: 11px;
            pointer-events: none;
            z-index: 1;
            background: none;
            border: 1px solid #FFFFFF70;
            border-bottom: none;
            position: absolute;
            inset: 0px 0px 0;
            height: 80%;
        }
        //left bottom curve
        &::after {
            content: "";
            border-bottom: 1px solid #FFFFFF70;
            border-left: 1px solid #FFFFFF70;
            border-bottom-left-radius: 16px;
            width: 10px;
            height: 12px;
            position: absolute;
            top: 76.5%;
            left: -10px;
            transform: rotate(268deg);
            -webkit-mask-image: linear-gradient(to top, black 40%, transparent 100%);
            mask-image: linear-gradient(to top, #000 40%, #0000 100%);
        }

        &:first-child::after {
            content: "";
            border-bottom-left-radius: 0;
            border-bottom: none;
            border-left: 1px solid #FFFFFF70;
            width: 10px;
            height: 12px;
            position: absolute;
            top: 80%;
            left: 0px;
            mask-image: unset;
            -webkit-mask-image: unset;
            transform: rotate(0deg);
        }

        &:hover {
            background-color: #CFCFCF;
        }

        &.tab-active {
            background-color: white;
            position: relative;
            z-index: 2;
            mask:
                radial-gradient(var(--r) at var(--r) 0, #0000 98%, #000 101%) calc(-1 * var(--r)) 91% / 100% var(--r) repeat-x,
                conic-gradient(#000 0 0) padding-box;
            padding-bottom: 11px;
            margin-bottom: -4px;
            box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
            &::before{
                display: none;
            }
            &::after {
                display: none;
            }
        }

        &:first-child {
            margin-left: -11px;
        }

        &:not(:first-child) {
            margin-left: -9px;
            border-bottom-left-radius: 8px;
        }

        .tab-label {
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 24ch;
            overflow: hidden;
            user-select: none;
            padding-right: 4px;
            color: #707580;
            font-size: 14px;
            font-weight: 500;
            padding: 6px 8px 6px 12px;
        }

        &.toggle-tab {
            box-shadow: none;
            accent-color: #668EBA !important;

            .tab-toggle {
                &:checked {
                    &:disabled {
                        background-image: none;
                    }
                }
            }

            .tab-label {
                background-color: none !important;
                color: #707580;
                border-bottom: none;
                margin-top: 3px !important;
                padding: 3px 4px 5px 12px;
            }

            input {
                &[disabled] {
                    -webkit-text-fill-color: white;
                }

                &[type="checkbox"] {
                    border-radius: 30%;
                    border: 2px solid #668EBA;
                    appearance: none;
                    -webkit-appearance: none;
                    outline: none;
                    cursor: pointer;
                    position: relative;

                    &:checked {
                        background-color: #668EBA;
                    }

                    &:checked::after {
                        content: '\2713';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 12px;
                        color: white;
                    }
                }
            }
        }

        &.tab-active {
            background: #FFFFFF !important;

            .tab-label {
                color: var(--color-tertiary);
                border-radius: 4px;
                font-size: 14px;
                font-weight: 700;
                border-bottom: none;
                box-shadow: 0px -1px 2px 0px #00000038 inset;
                box-shadow: 0px 1px 2px 0px #FFFFFF1F inset;
            }

            cursor: pointer;

            &::after {
                cursor: pointer;
            }
        }

        .tab-avatar {
            cursor: pointer;
            user-select: none;

            img {
                height: 22px;
                width: 22px;
                padding-right: 4px;
            }
        }
    }
}