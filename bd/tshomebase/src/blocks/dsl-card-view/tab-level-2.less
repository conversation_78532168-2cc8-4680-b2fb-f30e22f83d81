.dsl-templ-area [tabcontroller] {
    border-bottom: none !important;
}

.level-2-tab-list {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px !important;
    width: 100% !important;
    flex-wrap: wrap !important;
    padding: 12px 2px !important;

    &.tab-list-sub {
        background-color: none;
        border-left: none;
    }

    .tab-list-button {
        display: flex;
        min-width: max-content !important;
        cursor: pointer;
        background-color: none;
        padding: 0 !important;
        gap: 0;
        transition: background-color 0.3s ease, border-radius 0.3s ease;

        .tab-label {
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            user-select: none;
            color: #535862;
            padding: 0 !important;
            font-size: 14px;
            font-weight: 500;
            border-bottom: none;

        }

        &.toggle-tab {
            box-shadow: none;
            accent-color:  #668EBA !important;

            .tab-toggle {
                color:#a2adc6;
            &:checked {
                color: #707580!important;
                &:disabled {
                background-image: none;
                }
            }
            }

            .tab-label {
            background-color: none !important;
            color: #707580;
            border-bottom: none;
            margin-top: 3px !important;
            margin-bottom: 2px !important;
            margin-left: 8px !important;
            }

            input {
                &[disabled]{
                    -webkit-text-fill-color: white;
                }

            &[type="checkbox"] {
                border-radius: 30%;
                border: 2px solid #668EBA; 
                appearance: none;
                -webkit-appearance: none;
                outline: none;
                cursor: pointer; 
                position: relative;
                
                &:checked {
                    background-color: #668EBA;
                }
                &:checked::after {
                    content: '\2713';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 12px;
                    color: white; 
                }
                }
            }
            &.tab-active {
                transition: border-radius 0.2s ease;
                .tab-label {
                    padding-top: 4px !important;
                    padding-bottom: 4px!important;
                    padding-left: 2px!important;
                    padding-right: 12px!important;
                    color: white;
                }
            }
        }

        &:first-child{
            gap: 8px;
            // &.tab-active {
            //     box-shadow: 0px 1px 2px 0px #FFFFFF1F inset, 0px -1px 2px 0px #00000038 inset,  0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #E58787, 0px 2px 3px 0px #544F731F, 0px 0px 2px 0px #00000054;
            // }
        &::after {
            content: '\2a';
            font-family: 'Font Awesome 6 Pro';
            color: var(--color-error);
            margin-right: 10px;
            align-self: center;
        }
    }


        &.tab-active {
            background-color: none;
            .tab-label {
                color: white;
                padding: 4px 12px !important;
                border-radius: 6px;
                font-size: 16px;
                font-weight: 700;
                border-bottom: none;
            }

        input {

            &[type="checkbox"] {
                margin-left: 8px !important;
                border: 2px solid white; 
                &:checked {
                    background-color: white;
                }
                &:checked::after {
                    font-size: 14px;
                    color: #668EBA; 
                }
                }
            }

        }
        &:first-child.tab-active  {
            border-radius: 6px !important;
            .tab-label {
                padding: 4px 0 4px 12px !important;
            }
        }
    }

    &::before {
        content: none;
    }
}