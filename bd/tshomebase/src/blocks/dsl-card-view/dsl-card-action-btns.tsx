import "./dsl-card-action-btns.less";
import type { FC } from "react";
import React, { useMemo } from "react";
import actionBtns from "@public/icons/dsl/action-btns";
import { TAB_CLICK_BTN_NAME, TabActionButton, TabActions, TabClick } from "./dsl-card-view";
import { toPresentableLabel } from "@utils/fx";

export type DSLActionBtnIcon = keyof typeof actionBtns;

/**
 * Interface representing server action buttons configuration
 * @interface ServerActionButtons
 */
export interface ServerActionButtons {
	style: "default" | "info" | "warning" | "primary" | "danger" | "error";
	action: "view" | "prefill" | "edit" | "create" | "popup" | "print";
	label: string;
	icon?: DSLActionBtnIcon;
	type: "view" | "edit" | "list";
	form?: string;
	requirements?: string;
	class?: string;
	callback_url?: string;
	suppress_save?: boolean;
	callback_type?: "POST" | "GET" | "PUT" | "NONE";
	show_loading_spinner?: boolean;
	loading_spinner_text?: string;
	confirmation_dialog?: boolean;
	confirmation_dialog_message?: string;
	confirmation_dialog_title?: string;
	confirmation_dialog_type?: "yesno" | "okcancel" | "confirmcancel";
}

export interface DSLCardActionButtonProps {
	icon?: DSLActionBtnIcon;
	label?: string;
	action: string;
	className?: string;
	disabled?: boolean;
	onClick: (action: string) => void;
}

export const DSLCardActionButton: FC<DSLCardActionButtonProps> = (props) => {
	const { icon, label, onClick, action, disabled, className } = props;
	let svg = null; //TODO default icon
	let fontIcon = null;
	if (icon && actionBtns[icon]) {
		svg = actionBtns[icon];
	} else if (icon && typeof icon === "string" && icon.includes("fa-")) {
		fontIcon = icon;
	}
	return (
		<div
			className={`dsl-action-btn wizard-bottom-btn-default btn-${action}${disabled ? " disabled" : ""} ${
				className ? ` ${className}` : ""
			}`}
			onClick={() => {
				if (disabled) return;
				onClick(action);
			}}
			tabIndex={0}
		>
			<div className="inner-cont">
				{svg && <img className="icon" src={svg} />}
				{fontIcon && <i className={fontIcon} />}
				{label && <p className="label">{label}</p>}
			</div>
		</div>
	);
};

interface DSLCardActionButtonGroupProps {
	btns: TabActions;
	className?: string;
	tabDo: (action: string) => void;
}

export const DSLCardActionButtonGroup: FC<DSLCardActionButtonGroupProps> = (props) => {
	const { btns, className, tabDo } = props;
	// Move all the no standard buttons to the start of the list keeping the order
	const ordered = useMemo(() => {
		return Object.keys(btns).sort((a, b) => TAB_CLICK_BTN_NAME.indexOf(a) - TAB_CLICK_BTN_NAME.indexOf(b));
	}, [btns]);

	return (
		<div className={`action-btn-group-cnt${className ? ` ${className}` : ""}`}>
			{ordered.map((f) => {
				let btn = btns[f] as TabActionButton;
				if (btn && btn.enabled) {
					return (
						<DSLCardActionButton
							classPrefix="dsl"
							key={f}
							icon={btn.icon}
							label={btn.label || toPresentableLabel(f)}
							action={f}
							className={btn.class}
							onClick={tabDo}
						/>
					);
				}
			})}
		</div>
	);
};
