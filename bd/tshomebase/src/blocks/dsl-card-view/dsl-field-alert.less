.dsl-field-alert-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	border: 1px solid;
	border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
	box-shadow: 0px 17px 30px -4px #0000000f;
}

.dsl-field-alert {
	background: #fafafad9;
	border-radius: 12px;
	padding: 24px;
	min-width: 400px;
	min-height: 316px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	display: flex;
	
	flex-direction: column;
	align-items: center;
	gap: 16px;
	max-height: 100%;
	max-width: 100%;
	border: 1px solid;
	border-color: #fff #fafafa transparent #fafafa;	
	.dsl-field-alert-header {
		display: flex;
		width: 100%;
		justify-content: flex-start;
		gap: 10px;
		align-items: center;
	}

	&-title {
		color: #181d27;
		font-weight: 500;
		font-size: 18px;
		line-height: 28px;
		letter-spacing: 0%;
	}

	&-content {
		width: 100%;
		min-height: 128px;
		padding: 10px;
		border-radius: 8px;
		background: #ffffff;
		gap: 10px;
		border: 1px solid #e3e5e8;
		box-shadow: 0px 1px 2px 1px #38383814 inset;
		display: flex;
		flex-direction: column;
		gap: 10px;
	}

	&-label {
		color: #838894;
		font-family: Inter;
		font-weight: 400;
		font-size: 14px;
		line-height: 100%;
		letter-spacing: 0%;
		.required-mark {
			color: #e58787;
		}
	}

	&-value {
		color: #43464d;
		font-family: Inter;
		font-weight: 600;
		font-size: 16px;
		line-height: 100%;
		letter-spacing: 0%;
		width: 100%;
		border: none;
		padding: 0;
		min-height: auto;
		font-family: Inter;
		font-weight: 600;
		font-size: 16px;
		line-height: 100%;
		letter-spacing: 0%;
		min-height: 82px;
	}

	&-form,
	&-record,
	&-mode {
		display: none; // Hide these elements as they appear to be for data purposes
	}

	&-actions {
		width: 100%;
		display: flex;
		justify-content: center;
		gap: 10px;
		margin-top: 16px;
	}

	&-button {
		border: none;
		cursor: pointer;

		width: 100%;
		height: 44;
		border-radius: 8px;
		gap: 6px;
		padding-top: 10px;
		padding-right: 16px;
		padding-bottom: 10px;
		padding-left: 16px;
		box-shadow: 0px -1px 2px 0px #00000038 inset;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		letter-spacing: 0%;

		&.confirm {
			background-color: #e58787;
			color: white;

			&:hover {
				background-color: darken(#e98080, 5%);
			}
		}

		&.change {
			background-color: #f0f0f0;
			color: #333;

			&:hover {
				background-color: darken(#f0f0f0, 5%);
			}
		}
	}
}
