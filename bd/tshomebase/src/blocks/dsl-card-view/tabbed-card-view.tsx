import { FC, useEffect, useRef, useState } from "react";
import "./tabbed-card-view.less";
import { sectionShowOnly, getDSLVisibleSections } from "@utils/dsl-fx";
import { TabData } from "../tab-list/tab-list";
import DSLCardView, {
	DSLCardOnChangeModeCallback,
	DSLCardOnSaveCallBack,
	DSLCardViewRef,
	TabModes,
} from "./dsl-card-view";

export interface TabbedCardViewProps {
	form: string;
	id?: string;
	mode?: TabModes;
}

export const TabbedCardView: FC<TabbedCardViewProps> = (props) => {
	const form = props.form;
	const dslRef = useRef<DSLCardViewRef | null>(null);

	const [visibleSections, setVisibleSections] = useState<string[]>([]);
	const [activeSection, setActiveSection] = useState<string>("");
	const [Id, setID] = useState<string>(props.id || "");
	const [mode, setMode] = useState<TabModes>(props.mode || "read");

	const setActiveDSLSection = async (section: string) => {
		setActiveSection(section);
		sectionShowOnly(dslRef.current, form, [section]);
		const jqEl = dslRef.current?.$el;
		if (!jqEl) return;
		const secEl = jqEl.find(`[section="${section.toLowerCase()}"]`);
		const visible = secEl.find(".form-horizontal").find(":visible").length;
		const naId = "no-data-available";
		secEl.find(`#${naId}`).remove();
		if (!visible) {
			secEl.prepend(`<div id="${naId}">No data available.</div>`);
		}
	};

	const onSaved: DSLCardOnSaveCallBack = (fd, tab, ref) => {
		if (!ref) {
			return;
		}
		let cv = `card${ref.card}` as "cardform" | "cardread";

		const nm = ref[cv].mode;
		setID(fd.record);
		setMode(nm);
	};

	const changeMode: DSLCardOnChangeModeCallback = (mode, id, tab, ref) => {
		if (mode == ("list" as TabModes)) {
			setMode("read");
			return;
		}
		setMode(mode);
		setID(`${id}`);
	};

	const rxOpts = {
		onSaved: onSaved,
		changeMode,
	};

	if (!form || !Id) {
		return <div className="wf-inspect-comp dsl-inspect-form-view dsl-fi-error">Invalid data: Unable to load.</div>;
	}

	return (
		<div className="dsl-tabbed-card-view">
			<div className="section-tabs">
				{visibleSections.map((section, index) => {
					return (
						<div
							key={section + index}
							className={`section-tab ${activeSection == section ? "active" : ""}`}
							onClick={() => setActiveDSLSection(section)}
						>
							{section}
						</div>
					);
				})}
			</div>
			<DSLCardView
				isActive={true}
				isParentActive={true}
				disableRouting={true}
				card={mode}
				xid={Id}
				form={props.form}
				record={["add", "addfill"].includes(mode) ? undefined : Id}
				onFormReady={() => {
					let vs = getDSLVisibleSections(dslRef, form) || [];
					setVisibleSections(vs);
					if (vs.length == 0) {
						return;
					}
					if (!activeSection || !vs.includes(activeSection)) setActiveDSLSection(vs[0]);
				}}
				ddRef={(ref) => {
					dslRef.current = ref;
				}}
				tabData={
					{
						tkey: Id,
						id: Id,
						form: props.form,
						mode: mode,
					} as TabData
				}
				overrideDos={{
					archive: {
						enabled: false,
					},
					unarchive: {
						enabled: false,
					},
					print: {
						enabled: false,
					},
				}}
				{...rxOpts}
			/>
		</div>
	);
};
