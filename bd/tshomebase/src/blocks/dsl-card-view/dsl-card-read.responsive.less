@import (reference) "../../less/style/main.less";

.form-container {
    &.form-read-mode {
        .dsl-templ-area {
            .cardread {
                @media (max-width: 1200px) {
                    .form-horizontal {

                        &.form-col-1:not(:has(table)),
                        &.form-col-2,
                        &.form-col-3,
                        &.form-col-4 {
                            width: 100% !important;

                            .form-group {

                                label.control-label,
                                .controls {
                                    width: 45% !important;
                                }
                            }
                        }
                    }
                }

                @media (max-width: 992px) {}

                @media (max-width: 768px) {}

                @media (max-width: 576px) {
                    .form-horizontal {

                        &.form-col-1:not(:has(table)),
                        &.form-col-2,
                        &.form-col-3,
                        &.form-col-4 {
                            .form-group {
                                flex-direction: column !important;

                                label.control-label,
                                .controls {
                                    width: 100% !important;
                                    margin-right: 0% !important;
                                }
                            }
                        }
                    }
                }

                @media (max-width: 425px) {}

                @media (max-width: 320px) {}
            }
        }
    }
}