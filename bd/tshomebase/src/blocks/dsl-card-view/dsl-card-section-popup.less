.dsl-card-section-popup {

    .dsl-templ-area {
        max-height: 50vh;
        overflow-y: auto;
    }
    &.popup-warning {
        background-color: var(--color-error-400) !important; 
        .header {
            background-color: var(--color-error-400) !important; 
            .title {
                color: var(--gray-700) !important; 
            }
        }
    }
    &.force-single-column {
        .form-horizontal {
            width: 100% !important;
        }
    }
    &.readonly-popup {
        .form-horizontal {
            textarea {
                min-height: 250px !important;
                padding-top: 0px !important;
                margin-top: 35px !important;
            }
        }
    }
    .form-horizontal {
        min-width: 200px !important;
    }

    .body {
        .form-container {
            height: unset !important;

            h3 {
                display: none;
            }
        }
    }


    &.single-section {
        .body {
            div[section] {
                h4 {
                    display: none;
                }
            }
        }
    }
}