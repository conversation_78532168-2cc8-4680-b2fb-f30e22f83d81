import * as style from './style.module.less';
import './style.module.less';

export interface WizardBottomBarPropsTypes {
    children: React.ReactNode;
    className?: string;
}

const WizardBottomBar = (props: WizardBottomBarPropsTypes) => {
    const { children, className } = props;
    return (
        <div className={`${style.wizardBottomBar} ${className && className || ""}`}>
            {children}
        </div>
    )
}

export default WizardBottomBar