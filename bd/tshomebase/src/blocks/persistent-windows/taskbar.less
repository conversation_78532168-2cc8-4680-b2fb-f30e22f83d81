
* {
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(91,131,176,0.44)transparent; /* For Firefox */
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-thumb {
  background-color: rgba(91,131,176,0.44);
  border-radius: 4px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

.taskbar-tabs {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-large);
  width: 100%;
  overflow-x: scroll;

  .taskbar-tab {
    display: flex;
    flex-direction: row;
    background-color: #FAF9F8;
    gap: var(--spacing-large);
    align-items: center;
    justify-content: center;
    padding: 4px 12px;
    border-radius: 8px;
    gap: 8px;
    cursor: pointer;
    box-shadow: 0px 2px 3px -1px #00000014;
    box-shadow: 0px 0px 2px 0px #0000001F;
    justify-content: space-between;
    border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
    .task-labels {
      display: flex;
      flex-direction: column;
      flex-basis: 90%;

      .title {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-align: left;
        text-decoration-skip-ink: none;
        color:rgba(94, 99, 107, 1);
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-user-select: none;
        user-select: none;
        max-width: 13ch;
        overflow: hidden;
      }

      .subtitle {
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        text-align: left;
        text-decoration-skip-ink: none;
        color:rgba(94, 99, 107, 1);
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        -webkit-user-select: none;
        user-select: none;
        max-width: 13ch;
        overflow: hidden;
      }

      &:hover,
      &:focus {
        .title {
          color: var(--color-tertiary);
        }
      }
    }

    .task-edit-icon {
      filter: invert(54%) sepia(5%) saturate(500%) hue-rotate(180deg);
    }

    &.active {
      background-color: white;
      .task-edit-icon {
        filter: invert(54%) sepia(22%) saturate(550%) hue-rotate(184deg);
      }

      .task-labels {
        .subtitle {
          color: var(--color-tertiary);
          font-weight: 500;
        }

        .title {
          color: var(--color-tertiary);
          font-size: 14px;
          font-weight: 700;
        }

        &:hover,
        &:focus {
          box-shadow: none !important;
        }
      }
    }
  }

}