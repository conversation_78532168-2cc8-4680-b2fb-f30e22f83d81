@import (reference) "../../less/style/main.less";
@import "../../components/nav-header/header.vars.less";

#application:has(.animated-tab-container.active) {
	.animated-tab-container {
		backdrop-filter: blur(4px) !important;
		opacity: 0.99;
		.window-container {
			.window-manager-header {
				margin: 12px 10px 0 10px;
			}
		}
		.inc-container {
			.header-container {
				padding: 16px 14px;
			}
		}
	}
	.persistent-windows-container,
	.has-tabs {
		box-shadow: 0 4px 30px rgba(25, 23, 23, 0.1);
		backdrop-filter: blur(4px);
	}
}

.persistent-windows-container {
	height: @persistent-windows-height;
	padding: var(--spacing-xlarge);
	padding-top: 0px;
}
