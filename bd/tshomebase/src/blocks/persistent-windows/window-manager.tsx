import React, { useCallback, memo } from "react";
import "./window-manager.less";
import { WindowManagerListItem } from "./types";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
import { DSLCardView, DSLCardViewProps, DSLCardViewRef, SavedFormDataObject, TabModes } from "@blocks/dsl-card-view";
import { DSLListView } from "@blocks/dsl-list-view";
import Icons from "@public/icons/general/index";

import { TabData } from "@blocks/tab-list/tab-list";
import { OpenTabCallback } from "@hooks/index";
import { getFormConfig } from "@dsl-form/config";
import { isTempId } from "@utils/dsl-fx";
import { SnapshotHeaderForm } from "@blocks/snapshot-header/snapshot-header";

interface VirtualWindowProps {
	active?: boolean;
	tab?: WindowManagerListItem;
	children: React.ReactNode;
}

export const VirtualWindow = memo<VirtualWindowProps>(({ active = true, tab, children }) => {
	return <div className={`animated-tab-container ${active ? "" : "inactive"}`}>{children}</div>;
});

interface WindowManagerProps {}

const WindowHeader = memo<{
	title: string;
	onMinimize: () => void;
	onClose: () => void;
}>(({ title, onMinimize, onClose }) => (
	<div className="window-manager-header">
		<div className="window-manager-header-title" key={"don_t_change_this_key"}>
			{title}
		</div>
		<div className="window-manager-header-actions">
			<div className="window-manager-header-action-button">
				<img src={Icons.minimizeWindow} alt={"Minimize " + title} onClick={onMinimize} />
			</div>
			<div className="window-manager-header-action-button">
				<img src={Icons.closeWindow} alt={"Close " + title} onClick={onClose} />
			</div>
			<div className="window-manager-header-setting-button"></div>
		</div>
	</div>
));

const WindowManagerBase: React.FC<WindowManagerProps> = () => {
	const {
		activeWindow: activeTab,
		windows: tabs,
		closeWindow,
		minimizeWindow,
		addWindow,
		updateCardRef,
		updateWindow,
	} = usePersistentWindowsStore();

	const openTab = useCallback<OpenTabCallback>(
		(id, label, mode, form, componentProps, renderComponent, extraKV) => {
			if (["read", "edit"].includes(mode) && (!id || isTempId(id))) {
				return;
			}
			addWindow({
				wid: "",
				rid: "",
				type: "form",
				form: form,
				title: label,
				renderComponent: renderComponent as React.FC<DSLCardViewProps>,
				record: isTempId(id) ? undefined : id,
				card: mode as TabModes,
				...componentProps,
				...extraKV,
			});
		},
		[addWindow]
	);

	return (
		<div className="window-manager">
			{tabs.map((tab) => {
				const isActive = tab.wid === activeTab?.wid;
				let RenderComponent = null;

				if (tab.type === "form") {
					RenderComponent = DSLCardView;
					const formConfig = getFormConfig(tab.form);
					if (formConfig.renderComponents[tab.card]) {
						RenderComponent = formConfig.renderComponents[tab.card];
					}
					if (tab.renderComponent) {
						RenderComponent = tab.renderComponent;
					}
				} else {
					RenderComponent = DSLListView;
				}
				return (
					<VirtualWindow key={tab.wid + tab.rid} active={isActive} tab={tab}>
						{tab.type == "form" && tab.record && (
							<SnapshotHeaderForm formName={tab.form} formId={tab.record} />
						)}
						<div className="window-container">
							<WindowHeader
								title={tab.title}
								onMinimize={() => minimizeWindow(tab)}
								onClose={() => closeWindow(tab)}
							/>
							<div className="window-manager-content" data-form={tab.form}>
								{tab.type == "form" ? (
									<RenderComponent
										{...tab}
										changeMode={(mode: string) => {}}
										onSaved={(fd: SavedFormDataObject, t: TabData, ref?: DSLCardViewRef) => {
											const title = fd.values.auto_name || tab.title;
											closeWindow(tab);
											if (tab.onEvent) {
												try {
													tab.onEvent({ type: "saved", cardRef: ref, fd: fd.values });
												} catch (e) {
													console.error("Error in onEvent", e);
												}
											}
											if (!tab.gridRef) {
												return;
											}
											if (tab.gridRef?.advanced?.config?.form == ref?.form) {
												if (["add", "addfill"].includes(tab.card)) {
													tab.gridRef?.advanced.row("add", fd.values);
												} else {
													tab.gridRef?.advanced.row("edit", fd.values);
												}
											} else {
												tab.gridRef?.advanced.refresh();
											}
										}}
										tabViewActions={
											{
												openTab: openTab,
												closeWindow: () => closeWindow(tab),
											} as any
										}
										tabData={tab}
										onCancel={(id: string, t: TabData, ref?: DSLCardViewRef) => {
											closeWindow(tab);
										}}
										ddRef={(ref: DSLCardViewRef) => {
											updateCardRef(tab, ref);
											tab.refCallback?.(ref);
										}}
									/>
								) : (
									<DSLListView {...tab} />
								)}
							</div>
						</div>
					</VirtualWindow>
				);
			})}
		</div>
	);
};

export const WindowManager = memo(WindowManagerBase);
