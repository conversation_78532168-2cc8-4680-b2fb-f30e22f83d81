import React from "react";
import "./taskbar.less";
import Icons from "@public/icons/general/index";
import { usePersistentWindowsStore } from "@blocks/persistent-windows/store";
interface TaskbarProps {}

export const Taskbar: React.FC<TaskbarProps> = () => {
	const { activeWindow: activeTab, windows: tabs, closeWindow, maximizeWindow } = usePersistentWindowsStore();

	return (
		<div className="taskbar-tabs">
			{tabs.map((tab) => (
				<div
					key={tab.wid + tab.rid}
					className={`taskbar-tab ${tab.wid === activeTab?.wid ? "active" : ""}`}
					onClick={() => maximizeWindow(tab)}
				>
					<div className="task-icon">
						<img src={Icons.iconCrossExit} alt={"Close " + tab.title} onClick={() => closeWindow(tab)} />
					</div>
					<div className="task-labels">
						<div className="subtitle">{tab.subtitle || "--"}</div>
						<div className="title">{tab.title}</div>
					</div>
					{tab.type == "form" && tab.missing && (
						<div className="task-icon">
							<img src={Icons.iconRequired} alt={"Missing " + tab.title} />
						</div>
					)}
					<div className="task-edit-icon" onClick={() => maximizeWindow(tab)}>
						<img src={Icons.iconEdit} alt={"Open " + tab.title} />
					</div>
				</div>
			))}
		</div>
	);
};
