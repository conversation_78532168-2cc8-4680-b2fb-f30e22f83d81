@import "../../components/nav-header/header.vars.less";

.animated-tab-container:has(.arrowDownContainer.open) {
	.header-container {
		.insurance-info {
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
			box-shadow: inset 2px 0 0 0 var(--color-tertiary), inset -2px 0 0 0 var(--color-tertiary),
				inset 0 2px 0 0 var(--color-tertiary);
		}
	}
}

.animated-tab-container {
	backdrop-filter: blur(4px) !important;
	transition: all 0.3s ease-in-out;
	transform: translateY(0);
	margin: 10px;
	border-radius: var(--radius-xlarge);
	height: calc(100vh - @persistent-windows-height - 20px);
	max-height: calc(100vh - @persistent-windows-height - 20px);
	opacity: 0.99;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;

	&.inactive {
		transform: translateY(100%);
		opacity: 0;
		pointer-events: none;
		bottom: 0;
		left: 0;
		right: 0;
		border-radius: var(--radius-medium);
	}

	&.active {
		transform: translateY(0);
		opacity: 1;
		pointer-events: auto;
		left: 0;
	}

	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;
	gap: var(--spacing-standard);

	.inc-container {
		margin: 8px 0 0;
		border: 2px solid #fafafad9;
		.header-container {
			padding-bottom: 0;
			.insurance-info {
				margin-right: 8px;
				.ins-top {
					.wgdt-container {
						display: none !important;
					}
				}
			}
		}
		&.cond-background {
			padding: 8px 0;
			background-color: #ebebeb;
			border-radius: 12px;
			opacity: 0.99;
		}
	}

	@media (max-width: 1440px) {
		.inc-container {
			.header-container {
				.med-info {
					max-width: 20%;
				}
			}
		}
	}
}

.window-manager {
	.window-container {
		display: flex;
		flex-direction: column;
		flex: 1;
		padding: 0px var(--spacing-standard);
		gap: var(--spacing-standard);
		min-height: 0;
		background: #d4d4d4;
		background-color: #ebebeb;
		opacity: 0.99;
		padding: 2px;
		border-radius: 12px;
		border: 1px solid rgba(255, 255, 255, 0.45);

		.window-manager-content {
			display: flex;
			flex-direction: column;
			flex: 1;
			padding: 0px var(--spacing-standard);
			gap: var(--spacing-standard);
			min-height: 0;

			.wizard-bar-bottom {
				align-self: flex-end;
				flex-shrink: 0;
			}
		}

		.window-manager-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 34px;
			padding: 0px var(--spacing-standard);

			.window-manager-header-title {
				margin-left: 12px;
				margin-top: 4px;
				font-size: 14px;
				font-weight: 500;
				color: #5b83b0;
			}

			.window-manager-header-actions {
				display: flex;
				gap: 15px;

				.window-manager-header-action-button {
					cursor: pointer;
					align-self: center;

					img {
						width: 14px;
						height: 14px;
					}
				}

				.window-manager-header-setting-button {
					cursor: pointer;

					&::before {
						content: "\f142";
						font-family: "Font Awesome 6 Pro";
						color: rgba(164, 167, 174, 1);
						font-size: 20px;
					}
				}
			}
		}
	}

	// Your existing task-window styles
}
