import { create } from "zustand";
import { WindowManagerListItem } from "./types";
import { DSLListViewProps } from "@blocks/dsl-list-view";
import { useShallow } from "zustand/react/shallow";
import { DSLCardViewRef } from "@blocks/dsl-card-view/dsl-card-view";

export interface PersistentWindowsState {
	windows: WindowManagerListItem[];
	activeWindow: WindowManagerListItem | null;

	addWindow: (window: WindowManagerListItem, restore?: boolean) => void;
	markMissingForm: (wid: string | WindowManagerListItem, missing: boolean) => void;
	closeWindow: (wid: string | WindowManagerListItem) => void;
	minimizeWindow: (wid: string | WindowManagerListItem) => void;
	maximizeWindow: (wid: string | WindowManagerListItem) => void;
	updateWindow: (
		wid: string | WindowManagerListItem,
		updates: Partial<WindowManagerListItem>,
		restore?: boolean
	) => void;
	updateCardRef: (wid: string | WindowManagerListItem, ref: DSLCardViewRef) => void;
}

const getKeyFromPreset = (linkMap: DSLListViewProps["linkMap"]) => {
	if (!linkMap) return "";
	let uniqueKey = "";
	for (const [key, value] of Object.entries(linkMap.linkid)) {
		uniqueKey += key + "_" + value;
	}
	return uniqueKey;
};

const generateWindowId = (tab: WindowManagerListItem) => {
	if (tab.type === "form") {
		if (tab.card == "addfill" || tab.card == "add") {
			return tab.form + "_" + tab.record + "_" + window.generateUUID();
		}
		return tab.form + "_" + tab.record;
	}
	if (tab.type === "list") {
		return tab.form + "_" + getKeyFromPreset(tab.linkMap);
	}
	console.error("Unknown window type", tab);
	return "";
};

export const usePersistentWindowsStore = create<PersistentWindowsState>((set, get) => ({
	windows: [],
	activeWindow: null,
	updateWindow: (wid: string | WindowManagerListItem, updates: Partial<WindowManagerListItem>, restore = true) => {
		set((state) => {
			const id = typeof wid === "string" ? wid : wid.wid;
			const existingWindowIndex = state.windows.findIndex((w) => w.wid === id);
			if (existingWindowIndex > -1) {
				const updatedWindows = [...state.windows];
				const existingWindow = updatedWindows[existingWindowIndex];
				const existingWindowUpdated = {
					...existingWindow,
					...updates,
				} as WindowManagerListItem;
				if (!restore) {
					existingWindowUpdated.rid = window.generateUUID();
					existingWindowUpdated.wid = generateWindowId(existingWindowUpdated);
				}
				if (!existingWindowUpdated.wid) {
					existingWindowUpdated.wid = generateWindowId(existingWindowUpdated);
				}
				updatedWindows[existingWindowIndex] = existingWindowUpdated;

				return { windows: updatedWindows, activeWindow: updatedWindows[existingWindowIndex] };
			}
			return {};
		});
	},

	updateCardRef: (wid: string | WindowManagerListItem, ref: DSLCardViewRef) => {
		set((state) => {
			const id = typeof wid === "string" ? wid : wid.wid;
			const existingWindowIndex = state.windows.findIndex((w) => w.wid === id);
			if (existingWindowIndex > -1) {
				const updatedWindows = [...state.windows];
				updatedWindows[existingWindowIndex].cardRef = ref;
				return { windows: updatedWindows };
			}
			return {};
		});
	},

	addWindow: (tab, restore = true) => {
		set((state) => {
			if (!restore) {
				tab.rid = window.generateUUID();
				tab.wid = generateWindowId(tab);
			}
			if (!tab.wid) {
				tab.wid = generateWindowId(tab);
			}
			const existingWindowIndex = state.windows.findIndex((w) => w.wid === tab.wid);
			if (existingWindowIndex > -1) {
				const updatedWindows = [...state.windows];
				updatedWindows[existingWindowIndex] = {
					...updatedWindows[existingWindowIndex],
					...tab,
				};

				return { windows: updatedWindows, activeWindow: updatedWindows[existingWindowIndex] };
			}

			return {
				windows: [...state.windows, tab],
				activeWindow: tab,
			};
		});
	},

	markMissingForm: (wid: string | WindowManagerListItem, missing: boolean) => {
		set((state) => {
			const id = typeof wid === "string" ? wid : wid.wid;
			const newWindows = state.windows.map((w) => (w.wid === id ? { ...w, missing } : w));
			return { windows: newWindows };
		});
	},

	closeWindow: (wid: string | WindowManagerListItem) => {
		set((state) => {
			const id = typeof wid === "string" ? wid : wid.wid;
			const newWindows = state.windows.filter((w) => w.wid !== id);
			if (!state.activeWindow || state.activeWindow.wid === id) {
				return { windows: newWindows, activeWindow: null };
			}
			return { windows: newWindows };
		});
	},

	minimizeWindow: () => {
		set((state) => {
			return { activeWindow: null };
		});
	},

	maximizeWindow: (wid: string | WindowManagerListItem) => {
		set((state) => {
			const id = typeof wid === "string" ? wid : wid.wid;
			const newWindows = state.windows.find((w) => w.wid === id);
			return { activeWindow: newWindows };
		});
	},
}));
export const useActiveWindows = () => {
	const hasOpenWindows = usePersistentWindowsStore(useShallow((state) => state.windows.length > 0));
	return hasOpenWindows;
};
