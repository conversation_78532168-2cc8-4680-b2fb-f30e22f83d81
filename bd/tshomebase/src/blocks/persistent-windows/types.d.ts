import { DSLCardViewProps } from "@blocks/dsl-card-view";
import { DSLListViewProps } from "@blocks/dsl-list-view";
import { DSLCardViewRef } from "@blocks/dsl-card-view/dsl-card-view";
import { AdvancedGridRef } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";
import { IFormData } from "@hooks/form-data";

export type PersistentWindowEvent = {
	type: "saved";
	cardRef?: DSLCardViewRef;
	fd?: IFormData;
};
export type PersistentWindowEventCallback = (event: PersistentWindowEvent) => void;

export type SharedManagerListItem = {
	wid: string; // window id
	rid: string; // render id (if changed, it will be a new window with same params)
	title: string;
	subtitle?: string; // shows at of tab etc patient name
	group?: string; // in future gonna be used for grouping tabs together
	gridRef?: AdvancedGridRef;
	cardRef?: DSLCardViewRef;
	renderComponent?: React.FC<DSLCardViewProps>;
	onEvent?: PersistentWindowEventCallback;
};

export type WindowManagerListItem = SharedManagerListItem &
	(
		| ({ type: "form"; missing?: boolean; refCallback?: (ref: DSLCardViewRef) => void } & DSLCardViewProps)
		| ({ type: "list" } & DSLListViewProps)
	);
