import "./persistent-windows.less";
import { Taskbar } from "./taskbar";
import { WindowManager } from "@blocks/persistent-windows/window-manager";
import { useActiveWindows } from "./store";
const PersistentWindows = () => {
	const hasTabs = useActiveWindows();
	return (
		<>
			{hasTabs && (
				<div className="persistent-windows-container">
					<Taskbar />
					<WindowManager />
				</div>
			)}
		</>
	);
};

export default PersistentWindows;
