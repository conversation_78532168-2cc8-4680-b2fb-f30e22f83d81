import type { FC } from "react";
import React, { useMemo, useRef, useState } from "react";
import DSLGridView, { DSLGridOnRowEventCallBack } from "../dsl-grid-view/dsl-grid-view";
import "./dsl-list-compact-view.less";
import { request } from "@core/request/request";
import _, { intersection } from "lodash";
import { useNavigation, UseNavigationProps } from "@core/navigation";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";
import { DSLListViewProps } from "../dsl-list-view/dsl-list-view";
import { DSLCardActionButton, DSLCardActionButtonProps } from "@blocks/dsl-card-view/dsl-card-action-btns";
import { TabData } from "@blocks/tab-list";
import { RecursivePartial } from "@utils/type-utils";
import { useFormConfig } from "@dsl-form/config";
import { AdvancedGridRef, DSLAdvancedGrid } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

interface ListCompactButton extends Omit<DSLCardActionButtonProps, "onClick"> {
	enabled: boolean;
	alwaysEnabled?: boolean;
	onClick?: (action: string, id?: string | number | null) => void;
}

type ListCompactButtons = {
	add?: ListCompactButton;
	edit?: ListCompactButton;
	delete?: ListCompactButton;
};

interface DSLListCompactViewProps extends DSLListViewProps {
	customColumns: string[];
	columnsWidth?: number[];
	newPreset?: Record<string, unknown>;
	readOnly?: boolean;
	rowClickCallback?: (data: TabData & Record<string, any>) => void;
	onRef?: (ref: AdvancedGridRef | null) => void;
	buttonOverrides?: RecursivePartial<ListCompactButtons>;
	canEdit?: boolean;
	canAdd?: boolean;
	canDelete?: boolean;
	className?: string;
}

export const DSLListCompactView: FC<DSLListCompactViewProps> = (props) => {
	let { form, linkMap, newPreset = {}, customColumns, readOnly = false, className, buttonOverrides = {} } = props;

	if (props.urlCallback) {
		useNavigation(props as UseNavigationProps, "/");
	}

	const formConfig = useFormConfig(form);

	const fields = Object.keys(window.DSL[form].fields);
	customColumns = intersection(customColumns, fields);
	const gridRef = useRef<AdvancedGridRef | null>(null);
	const [selected, setSelected] = useState<(number | string)[]>([]); // Doing this for multi support in future

	const onRowClick: DSLGridOnRowEventCallBack = (opts) => {
		if (opts.type === "dblclick") {
			setSelected([]);
			return;
		}
		setSelected((p) => {
			p = Array.from(p);
			if (p.some((id) => id == opts.id)) {
				return [];
			}

			return [opts.id];
		});
	};

	const onNew = (action: string, id = null) => {
		const dfd = window.Flyout.open({
			form: form,
			...linkMap,
			preset: newPreset,
			autoRecoverEnabled: false,
		});
		dfd.done((data) => {
			gridRef.current?.advanced.row("add", data);
		});
	};

	const onArchive = (action: string, id = null) => {
		request({
			url: `/form/${form}/${selected[0]}/archive/`,
			method: "PUT",
			data: {
				archived: true,
			},
		})
			.then((data) => {
				gridRef.current?.advanced.row("archive", data);
			})
			.catch((err) => {
				console.log(err);
			});
	};

	const onEdit = (action: string, id = null) => {
		const dfd = window.Flyout.open({
			form: form,
			record: selected[0],
			autoRecoverEnabled: false,
		});
		dfd.done((data) => {
			gridRef.current?.advanced.row("edit", data);
		});
	};

	const btns = useMemo(() => {
		const defaults = {
			add: {
				enabled: true,
				permitted: window.Auth.can_create(form) && formConfig.allowAdd,
				alwaysEnabled: true,
				label: "Add",
				icon: "add",
				action: "add",
				onClick: onNew,
			},
			edit: {
				enabled: true,
				permitted: window.Auth.can_update_any(form),
				label: "Edit",
				icon: "edit",
				action: "edit",
				onClick: onEdit,
			},
			delete: {
				enabled: true,
				permitted: window.Auth.can_update_any(form),
				label: "Archive",
				icon: "archive",
				action: "archive",
				onClick: onArchive,
			},
		};
		return _.merge(defaults, buttonOverrides);
	}, [buttonOverrides, gridRef.current, selected]);

	const disable = selected.length != 1;
	const allowSelection = [btns.add, btns.edit, btns.delete].some((btn) => btn.permitted && btn.enabled);

	return (
		<div className={`dsl-list-compact${className ? ` ${className}` : ""}`}>
			<CRErrorBoundary>
				<DSLAdvancedGrid
					{...props}
					highlightOnRowSelection={allowSelection ? "single" : undefined}
					customColumns={customColumns}
					filtersPresetFixed={{
						...(props.filtersPresetFixed || {}),
					}}
					selectedHighlight={true}
					form={form}
					onRef={(ref) => {
						gridRef.current = ref;
						props.onRef?.(ref);
					}}
					rowClicked={(opts, event) => {
						!readOnly ? onRowClick(opts, event) : null;
						if (props.rowClickCallback) {
							props.rowClickCallback(opts);
						}
					}}
				/>
			</CRErrorBoundary>
			{readOnly ? null : (
				<div className="compact-btns-container">
					{[btns.add, btns.edit, btns.delete].map((btn) => {
						return btn.enabled && btn.permitted ? (
							<DSLCardActionButton
								key={btn.action}
								label={btn.label}
								icon={btn.icon}
								action={btn.action}
								disabled={btn.alwaysEnabled ? false : disable}
								onClick={(action) => {
									const id = selected && selected.length ? selected[0] : null;
									btn.onClick(action, id as any);
									if (action === "archive") {
										setSelected([]);
									}
								}}
							/>
						) : null;
					})}
				</div>
			)}
		</div>
	);
};
