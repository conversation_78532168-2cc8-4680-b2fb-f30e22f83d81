@import (reference) "../../less/style/main.less";

.dsl-list-compact {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;

	.tr-select {
		background: fade(@charcoal-gray, 20%) !important;
	}

	tr {
		background-color: transparent !important;
	}

	tr:hover:not(.tr-select) {
		background: fade(@charcoal-gray, 5%) !important;
	}

	.dsl-grid-view {
		border-bottom: none;
		border-radius: 8px !important;
	}

	table {
		margin: 0px !important;

		thead {
			tr {
				th:first-child {
					border-top-left-radius: unset !important;
				}

				th:last-child {
					border-top-right-radius: unset !important;
				}
			}
		}
	}

	.dataTables_scroll {
		box-shadow: none !important;
		min-height: 50px !important;
		max-height: calc(100%) !important;
	}

	.dataTables_scrollBody {
		border-bottom-left-radius: 8px !important;
		border-bottom-right-radius: 8px !important;
		padding-bottom: 2px;
		background-color: transparent;
	}

	.dataTables_info {
		display: none !important;
	}

	.dataTables_paginate {
		display: none !important;
	}

	.compact-btns-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding-top: 10px;
		padding-bottom: 10px;
		gap: 10px;

		.dsl-action-btn {
			margin-top: 0px !important;
			margin: var(--spacing-large) 0px 4px 4px; // to show outline in focus state
			cursor: pointer;
			padding: var(--spacing-standard) var(--spacing-xlarge);
			text-transform: capitalize;
			gap: var(--spacing-xsmall);
			border-radius: var(--radius-medium);
			width: auto;
			min-width: 30px !important;
			max-width: fit-content;
			min-height: 36px;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;

			.inner-cont {
				.icon {
					width: 20px;
					height: 20px;
					background-repeat: no-repeat;
					background-position: center;
					background-size: contain;
					filter: none !important;
				}

				.label {
					font-size: var(--font-size-xsmall) !important;
					font-weight: var(--font-weight-medium);
					background-color: transparent;
					filter: none !important;
				}
			}

			&.disabled {
				cursor: not-allowed !important;
				opacity: 1 !important;
				filter: none !important;
			}

			&.btn-add,
			&.btn-archive {
				box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;
				background-color: var(--color-text-50) !important;

				.inner-cont {
					.label {
						color: var(--brand-700) !important;
					}
					.icon {
						filter: brightness(0) saturate(100%) invert(46%) sepia(19%) saturate(745%) hue-rotate(209deg)
							brightness(99%) contrast(92%) !important;
					}
				}

				&:hover {
					background-color: var(--brand-50) !important;

					.inner-cont {
						.label {
							color: var(--brand-800) !important;
							background-color: transparent;
						}
					}
				}

				&:focus {
					background-color: var(--brand-100);
					box-shadow: 0px 0px 0px 1px #0a0d122e inset, 0px -2px 0px 0px #0a0d120d inset,
						0px 1px 2px 0px #0a0d120d, 0px 0px 0px 2px #ffffff, 0px 0px 0px 4px var(--color-tertiary);

					.inner-cont {
						.label {
							color: var(--brand-700);
						}
					}
				}

				&.disabled {
					background-color: var(--white) !important;
					outline: 1px solid var(--brand-100);
					box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;

					.inner-cont {
						.icon {
							filter: invert(65%) sepia(6%) saturate(120%) hue-rotate(180deg) brightness(95%)
								contrast(85%) !important;
						}

						.label {
							color: var(--color-text-400) !important;
						}
					}
				}
			}

			&.btn-edit {
				background-color: var(--color-text-50);
				box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;

				.inner-cont {
					.icon {
						filter: invert(77%) sepia(6%) saturate(150%) hue-rotate(180deg) brightness(90%) contrast(90%) !important;
					}

					.label {
						color: var(--color-text-700) !important;
					}
				}

				&:hover {
					background-color: var(--brand-50) !important;

					.inner-cont {
						.icon {
							filter: invert(12%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(90%)
								contrast(100%) !important;
							mix-blend-mode: difference;
						}

						.label {
							color: var(--color-text-800) !important;
							background-color: transparent;
						}
					}
				}

				&:focus {
					background-color: var(--white);
					box-shadow: 0px -2px 0px 0px #0a0d120d inset, 0px 0px 0px 1px #0a0d122e inset,
						0px 0px 0px 2px #ffffff, 0px 0px 0px 4px #837bb2;

					.inner-cont {
						.icon {
							filter: invert(25%) sepia(6%) saturate(300%) hue-rotate(180deg) brightness(90%)
								contrast(95%) !important;
							mix-blend-mode: luminosity;
						}

						.label {
							color: var(--brand-700);
						}
					}
				}

				&.disabled {
					background-color: var(--color-text-100) !important;
					box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;
					opacity: 1 !important;
					outline: none;

					.inner-cont {
						.icon {
							filter: invert(16%) sepia(8%) saturate(300%) hue-rotate(190deg) brightness(90%)
								contrast(95%) !important;
							mix-blend-mode: luminosity;
							opacity: 0.5;
						}

						.label {
							color: var(--color-text-400) !important;
							filter: none !important;
						}
					}
				}
			}

			&.btn-archive {
				background-color: var(--color-error-25);
				box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;

				.inner-cont {
					.icon {
						filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%) contrast(85%) !important;
					}

					.label {
						color: var(--color-error-700) !important;
					}
				}

				&:hover {
					background-color: var(--color-error-50) !important;

					.inner-cont {
						.label {
							color: var(--color-error-700) !important;
							background-color: transparent;
						}
					}
				}

				&:focus {
					background-color: var(--white);
					box-shadow: 0px -2px 0px 0px #0a0d120d inset, 0px 0px 0px 1px #0a0d122e inset,
						0px 0px 0px 2px #ffffff, 0px 0px 0px 4px #837bb2;

					.inner-cont {
						.icon {
							filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%)
								contrast(85%) !important;
						}

						.label {
							color: var(--color-error-700);
						}
					}
				}

				&.disabled {
					background-color: var(--color-text-100) !important;
					box-shadow: 0px 1px 2px 0px #ffffff1f inset, 0px -1px 2px 0px #00000038 inset;
					opacity: 1 !important;
					outline: none;

					.inner-cont {
						.icon {
							filter: invert(57%) sepia(36%) saturate(320%) hue-rotate(330deg) brightness(90%)
								contrast(85%) !important;
							opacity: 0.5;
						}

						.label {
							color: var(--color-error-400) !important;
							filter: none !important;
						}
					}
				}
			}
		}
	}
}
