import type { FC, MutableRefObject } from "react";
import React, { useMemo, useRef } from "react";
import Modal from "react-modal";
import "./portal-modal.less";
import ReactModal from "react-modal";
import _ from "lodash";
import { getNextZIndexFlyoutModelInception } from "@utils/dsl-fx";
interface ModalRendererProps {
	modalProps: ModalPropsExtended;
	componentProps: ComponentProps;
	component: FC<unknown>;
}
interface ModalPropsExtended extends ModalProps {
	root: {
		render: () => void;
		unmount: () => void;
	};
	node: HTMLElement;
}

interface ModalProps extends Partial<ReactModal.Props> {
	onRequestClose?: () => unknown;
	onAfterOpen?: () => void;
}
interface ComponentProps {
	[key: string]: unknown;
}
export interface PopupModalRef {
	closeModal: () => void;
	ref: MutableRefObject<unknown>;
}

const ModalRenderer: FC<ModalRendererProps> = (props) => {
	const modalRef = useRef(null);
	const getModal = (): PopupModalRef => ({
		ref: modalRef,
		closeModal,
	});

	const nextZIndex = useMemo(() => getNextZIndexFlyoutModelInception(), []);

	const style = {
		content: {
			display: "flex",
			justifyContent: "center",
			height: "100%",
			width: "100%",
			position: "unset !important",
			inset: "unset !important",
			border: "unset !important",
			background: "unset !important",
			overflow: "auto !important",
			borderRadius: "unset !important",
			outline: "unset !important",
			padding: "unset !important",
			alignItems: "center",
			zIndex: 9999,
		},
		overlay: {
			zIndex: 9999,
			backgroundColor: "rgba(67, 67, 67, 0.50)",
			WebkitBackdropFilter: "blur(4px)",
			backdropFilter: "blur(4px)",
		},
	};

	const closeModal = () => {
		modalRef.current?.removePortal();
		props.modalProps.root.unmount();
		props.modalProps.node.remove();
	};

	const overrideStyle = _.merge(style, props.modalProps.style || {});

	overrideStyle.content.zIndex = nextZIndex;
	overrideStyle.overlay.zIndex = nextZIndex;

	return (
		<Modal
			id={"application"}
			ref={modalRef}
			isOpen={true}
			onAfterOpen={props.modalProps.onAfterOpen}
			onRequestClose={() => {
				props.modalProps.onRequestClose?.();
			}}
			{...props.modalProps}
			style={overrideStyle}
		>
			<props.component {...props.componentProps} getModal={getModal} />
		</Modal>
	);
};

export const createPortalModal = async (
	component: FC<unknown>,
	modalProps: ModalProps,
	componentProps: ComponentProps
) => {
	const node = document.createElement("div");
	document.body.appendChild(node);
	const root = ReactDOM.createRoot(node);
	root.render(
		<ModalRenderer
			component={component}
			modalProps={{ ...modalProps, root, node }}
			componentProps={componentProps}
		/>
	);
	return root;
};
