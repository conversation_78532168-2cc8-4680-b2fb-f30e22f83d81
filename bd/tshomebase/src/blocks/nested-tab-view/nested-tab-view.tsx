import React, { type FC, useMemo } from "react";
import "./nested-tab-view.less";
import { GenericTabView } from "../generic-tab-view/generic-tab-view";
import { useNavigation } from "@core/navigation";
import type { RoutedComponentProps } from "@typedefs/routed-component";
import _ from "lodash";
import { AdditionTabItem } from "../tab-list/tab-list";

export interface NestedTabLevelProps {
	levels?: {
		[key: number]: TabLevelProps;
	};
	dsl?: TabLevelProps;
}

export interface TabLevelProps {
	style?: {
		tabListStyle?: string;
	};
	listProps?: {
		additionTabItems?: AdditionTabItem[];
	};
}

export type MapItem = {
	label: string;
	id: string;
	component?: {
		renderComponent: React.ComponentType<any>;
		componentProps?: Record<string, any>;
	};
	view?: MapItem[];
	form?: string;
};

const defaultLevelProps = {
	levels: {
		0: {
			style: {
				tabListStyle: "lvl-1-tab-list",
			},
		},
		1: {
			style: {
				tabListStyle: "lvl-2-tab-list",
			},
		},
	},
	dsl: {
		style: {
			tabListStyle: "dsl-tab-list",
		},
	},
};
const overrideTabProps = (tabLevelProps: NestedTabLevelProps) => _.merge(_.cloneDeep(defaultLevelProps), tabLevelProps);

interface NestedTabViewProps extends RoutedComponentProps {
	map: MapItem[];
	tabLevelProps?: NestedTabLevelProps;
}
export const NestedTabView: FC<NestedTabViewProps> = (props) => {
	const nav = useNavigation(props, "/");
	const { tabLevelProps, map } = props;

	const getOverrideStyle = useMemo(() => overrideTabProps, [tabLevelProps]);
	if (!map?.length) {
		return null;
	}
	// @ts-ignore
	return <GenericTabView {...{ ...props, ...nav, map, tabLevelProps: getOverrideStyle(tabLevelProps || {}) }} />;
};
