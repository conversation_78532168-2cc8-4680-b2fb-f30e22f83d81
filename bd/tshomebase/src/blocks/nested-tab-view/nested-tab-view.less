// dsl tab form
@import "./../tab-list/tab-list.vars.less";
@import (reference) "../../less/style/main.less";

.dsl-tab-list {
  background-color: transparent;
  vertical-align: bottom;
  flex-grow: 0;
  flex-shrink: 0;

  &.tab-add {
    background: transparent;
    color: @tab-btn-active-color;

    &::after {
      background: transparent !important;
      color: @tab-btn-active-color;
    }
  }

  .tab-list-button {
    background: @tab-btn-inactive-color;
    align-self: end;
    padding-left: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 20px;
    font-weight: 600;
    font-size: 16px;
    margin-right: 16px;
    align-items: center;

    &::after {
      display: block;
      height: 34px;
      width: 16px;
      background: @tab-btn-inactive-color;
      color: @tab-btn-inactive-txt-color;
      content: "";
      left: 4px;
      position: relative;
      transform: skewX(30deg);
      border-top-right-radius: 10px;
    }

    &.tab-active {
      background: #c0def8;
      color: @tab-btn-active-txt-color;

      &::after {
        background: #c0def8;
        color: @tab-btn-active-txt-color;
      }
    }
  }
}

// Level 1
.lvl-1-tab-list {
  background-color: @transparent;
  display: flex;
  flex-direction: row;
  vertical-align: bottom;
  flex-grow: 0;
  flex-shrink: 0;
  margin: 4px 0px;
  height: 46px;
  gap: var(--l1-tab-list-button-gap);

  .dynamic-tab-list {
    width: 100%;
    background-color: transparent;
    display: flex;
    flex-direction: row;
    gap: var(--l1-tab-list-button-gap);
    height: 46px;
  }

  &.lvl-3-to-1-tab-list {
    .dynamic-tab-list {
      width: auto;
    }

    .tab-list-button {
      &.has-avatar {
        padding-left: 10px;
      }
    }

    >.tab-list-button {
      &.tab-list-button-add {
        height: var(--l1-tab-list-min-height);
        width: var(--l1-tab-list-min-height);
        padding: 0;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &.tab-add {
    background: transparent;
    color: #fec866;
  }

  .tab-list-button {
    align-self: center;
    align-items: center;
    background-color: transparent;
    height: 36px;

    &.tab-active {
      border-radius: 8px;
      background-color: var(--color-tertiary);
    }
  }

  @media (max-width: 768px) {
    gap: 0px;
    padding-right: 0px !important;

    .dynamic-tab-list {
      gap: 10px !important;
    }
  }
}

// Level Two

.lvl-2-tab-list {
  background-color: transparent !important;
  vertical-align: bottom;
  justify-content: flex-start;
  flex-grow: 0;
  flex-shrink: 0;
  height: 46px;
  margin: 4px 0 8px 0;

  .dynamic-tab-list {
    background: var(--color-text-100-75-percent);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 8px;
      padding: 1px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    } 
  }

  &.tab-add {
    background: transparent;
    color: #fec866;
  }

  .tab-list-button {
    .tab-item-lvl-two;
    align-self: center;
    align-items: center;

    &.tab-active {
      background-color: var(--color-tertiary);
      height: 36px;

      .tab-label {
        font-size: 14px;
      }
    }
  }
}


// dsl tab view container add form

.dsl-tab-view-container {

  >.tab-list {
    height: 46px;
    margin: 4px 0px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .dynamic-tab-list {
      height: 46px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .tab-list-button {
        height: 36px;
        border: none;
        background-color: transparent;

        &.tab-active {
          background-color: var(--color-tertiary);
        }
      }
    }
  }
}