import type { FC } from "react";
import React, { useState } from "react";
import { useRef } from "react";
import "./dsl-list-inspect-view.less";
import { DSLInspectView, GenericInspectComponentProps, InspectViewRef } from "../dsl-inspect-view/dsl-inspect-view";
import { DSLListView, DSLListViewProps } from "../dsl-list-view/dsl-list-view";
import type { TabData } from "../tab-list/tab-list";
import { CRErrorBoundary } from "../error-boundary/cr-error-boundary";

interface DSLListInspectViewProps extends DSLListViewProps, Partial<GenericInspectComponentProps> {
	form: string;
	canAddNew: boolean;
	listLabel?: string;
	minListHeight?: string;
	InspectRenderComponent: FC<unknown>;
	inspectRenderComponentProps: { [key: string]: unknown };
	inspectIDField?: string;
	inspectLabelField?: string;
	inspectStyles?: React.CSSProperties;
	inspectType?: "bottom" | "detail";
	rowClicked?: (event?: TabData) => void;
}

export const DSLListInspectView: FC<DSLListInspectViewProps> = (props) => {
	const {
		form,
		linkMap,
		canAddNew,
		disableRouting,
		listLabel,
		minListHeight,
		InspectRenderComponent,
		noGridSearchBar,
		inspectIDField,
		inspectLabelField,
		inspectType = "bottom",
		rowClicked,
	} = props;
	const [isOpen, setOpen] = useState(false);
	const refs = useRef({
		inspect: null,
	}) as unknown as { inspect: InspectViewRef | null };

	const onRowClicked = (event: TabData) => {
		if (inspectType !== "bottom") {
			if (rowClicked) {
				rowClicked(event);
			}
			return;
		}
		let label = event.label;
		let id = event.id;
		let data = event.rowData || {};
		if (data.__row_data) {
			data = { ...data, ...(data.__row_data || {}) };
		}
		if (inspectIDField) {
			id = (event.rowData?.[inspectIDField] || event["id"]) as string;
		}
		if (inspectLabelField) {
			label = (event.rowData?.[inspectLabelField] || "") as string;
		}
		refs.inspect?.openTab(id, label, event.rowData || {});
	};

	if (!form) {
		return null;
	}

	return (
		<div className="dsl-list-inspect-view">
			<div className={`inspect-list${isOpen ? " open" : ""}`} style={{ minHeight: minListHeight }}>
				<DSLListView
					{...props}
					disableRouting={disableRouting}
					label={listLabel}
					noGridSearchBar={noGridSearchBar}
					findWidgets={props.findWidgets}
					form={form}
					keyField={(() => {
						if (form.startsWith("__wf_queue")) {
							return "tag";
						} else if (form.startsWith("__query_module")) {
							return "dynamic";
						} else {
							return "id";
						}
					})()}
					customColumns={props.customColumns}
					customFindFields={props.customFindFields}
					customColumnDefs={props.customColumnDefs}
					selectedHighlight={true}
					rowClicked={onRowClicked}
					filtersPresetFixed={props.filtersPresetFixed || {}}
					linkMap={linkMap}
					isActive
					isParentActive={props.isActive}
					canAdd={canAddNew}
				/>
			</div>
			{inspectType === "bottom" && (
				<CRErrorBoundary>
					<DSLInspectView
						{...props}
						containerStyles={props.inspectStyles}
						TabComponent={InspectRenderComponent}
						form={form}
						isOpen={setOpen}
						ref={(refv) => {
							refs.inspect = refv;
						}}
					/>
				</CRErrorBoundary>
			)}
		</div>
	);
};

DSLListInspectView.defaultProps = {
	disableRouting: false,
	canAddNew: false,
};
