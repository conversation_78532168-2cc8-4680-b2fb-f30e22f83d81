.dsl-list-inspect-view {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    gap: 5px;
    overflow-y: auto;

    .dynamic-tab-list {
        width: 100%;
        border-bottom: 1px solid #dbdbdd;

        .tab-list-button {
            width: 100%;
            justify-content: space-between;
            border: unset !important;
            border-radius: 0px !important;


            .tab-counter {
                .count {
                    background-color: #58505b;          // Bubble background color
                    color: white;                   // Text color inside the bubble
                    font-size: 12px;                // Adjust font size as needed
                    font-weight: bold;              // Make the text bold
                    border-radius: 50%;             // Create the circular shape
                    padding: 2px 6px;               // Padding for the bubble
                    display: flex;
                    min-width: 20px;
                    align-items: center;
                    justify-content: center;
                    overflow-y:visible;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); // Optional shadow for depth
                }
            }
            .tab-label {
                max-width: unset;
            }
        }
    }

    .tr-select {
        background: #C6DDF5 !important;

        th {
            color: #2098DC !important;
        }
    }

    .dsl-list-tab-container {
        padding-left: 0px;
        padding-right: 0px;
    }

    .inspect-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 250px;

        &.open {
            .dsl-list-bottom {
                border-bottom-right-radius: 0px;
                border-bottom-left-radius: 0px;
            }
        }
    }

}