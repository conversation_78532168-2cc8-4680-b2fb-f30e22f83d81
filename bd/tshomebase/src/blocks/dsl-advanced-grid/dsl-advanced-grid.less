@row-left-padding: 37px;
@row-right-padding: 32px;

.dsl-advanced-grid {
	height: 100%;
	width: 100%;
	--ag-checkbox-checked-background-color: var(--color-primary-500);
	--ag-checkbox-checked-border-color: var(--color-primary-500);
	// .ag-filter-menu.ag-popup-child {
	// 	.ag-react-container {
	// 		min-height: 315px;
	// 		max-width: 315px;
	// 	}
	// }
	.ag-tool-panel-wrapper {
		.ag-react-container {
			width: 100%;
		}
	}
	.ag-header-cell-label {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.ag-header-cell-text {
			font-size: 14px;
		}
	}
	.ag-popup-child {
		.context-menu-action {
			.ag-menu-option-icon {
				i {
					color: #414651;
				}
			}
			.ag-menu-option-text {
				color: #414651;
			}
		}
		.context-menu-action-danger {
			.ag-menu-option-icon {
				i {
					color: #e58787;
				}
			}
			.ag-menu-option-text {
				color: #e58787;
			}
		}
		.context-menu-action-primary {
			.ag-menu-option-icon {
				i {
					color: #837bb2;
				}
			}
			.ag-menu-option-text {
				color: #837bb2;
			}
		}
	}
	.agc-toolbar-container {
		display: flex;
		flex-direction: column;
		cursor: default;
		line-height: normal;
		white-space: normal;
		-webkit-font-smoothing: antialiased;
		font-family: var(--ag-font-family);
		font-size: var(--ag-font-size);
		.agc-title {
			height: var(--ag-header-height);
			justify-content: center;
			font-weight: 500;
		}
		.agc-toolbar-layout {
			align-items: flex-start;
			display: flex;
			flex-direction: column;
			padding: 10px;
			gap: 10px;
			padding-left: var(--ag-widget-container-horizontal-padding);
			border-bottom: var(--ag-tool-panel-separator-border);
			.agc-toolbar-btn-group {
				display: flex;
				flex-direction: row;
				gap: 10px;
			}
			.dsl-action-btn {
				padding: 4px 6px !important;
				border-radius: 4px !important;
			}
		}
	}
	.ag-icon-config {
		&:before {
			background-color: transparent !important;
		}
	}
	.ag-header {
		.ag-header-viewport {
			background-color: var(--color-border-500);
			color: var(--color-text-800);
		}
	}

	.ag-cell-value {
		font-size: 12px;
		font-weight: 700;
	}

	.ag-header-cell-sorted-asc {
		font-weight: 900;
		line-height: 18px;
		font-size: 12px;
		background-color: var(--color-border-500);
		color: var(--color-text-800);
	}

	.ag-header-cell-sorted-desc {
		font-weight: 700;
		line-height: 18px;
		font-size: 12px;
		background-color: var(--color-border-500);
		color: var(--color-text-800);
	}

	.ag-header-cell-sortable .ag-header-cell-label .ag-sort-indicator-container {
		order: -1;
		margin-right: 5px;
	}

	.ag-side-bar .ag-side-buttons {
		display: flex;
		flex-direction: column;
		background-color: var(--color-border-500);
		gap: 4px;
		padding: 2px;
	}

	.ag-side-bar .ag-side-buttons .ag-side-button {
		background-color: white;
		padding: 12px 5px;
		color: #8a79ba;
		font-size: 14px;
		font-weight: 500;
		overflow-y: auto;
	}

	.ag-row {
		border-width: 0 !important;
	}

	.ag-side-bar .ag-side-buttons .ag-side-button:first-child {
		border-radius: 0 10px 0 0;
	}

	.ag-side-bar .ag-side-buttons .ag-side-button .ag-side-button-button {
		padding: 0 !important;
	}

	.ag-cell.ag-cell-last-left-pinned {
		border-right: none !important;
		border-color: transparent !important;
	}

	.ag-cell.ag-cell-first-right-pinned {
		border-left: none !important;
		border-color: transparent !important;
	}

	.ag-cell.ag-cell-last-right-pinned {
		border-left: none !important;
		border-color: transparent !important;
	}

	.even-background {
		background-color: var(--color-background-400);
	}

	.odd-background {
		background-color: "#FFFFFF";
	}

	.sorted-column {
		font-weight: 700;
		color: #707580;
		font-size: 14px;
	}

	.unsorted-columns {
		font-weight: 500;
		color: #707580;
		font-size: 14px;
		padding-left: 12px;
	}

	.header-style {
		font-weight: 500;
		line-height: 18px;
		font-size: 12px;
		background-color: var(--color-border-500);
		color: #5a5e67;
		padding-left: 8px;
	}
	.ag-header-cell,
	.ag-header-group-cell-with-group {
		background-color: var(--color-border-500);
	}

	.default-style {
		padding-left: @row-left-padding;
	}

	@media (max-width: 768px) {
		.ag-side-bar .ag-side-buttons .ag-side-button {
			font-size: 10px;
		}
	}
}
