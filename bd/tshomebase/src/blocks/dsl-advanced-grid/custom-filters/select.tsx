import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useMemo, useRef, useState } from "react";
import { Select, Spin } from "antd";
import type { SelectProps } from "antd";
import debounce from "lodash/debounce";
import type { CustomFilterProps } from "ag-grid-react";
import { DSLField } from "@typedefs/coffee/dsl";
import { request } from "@core/request";
import { ValueGetterParams } from "ag-grid-enterprise";

export interface DebounceSelectProps<ValueType = any>
	extends Omit<SelectProps<ValueType | ValueType[]>, "options" | "children"> {
	v: DSLField;
	debounceTimeout?: number;
}

export function DebounceSelect<
	ValueType extends { key?: string; label: React.ReactNode; value: string | number } = any
>({ v, debounceTimeout = 800, ...props }: DebounceSelectProps<ValueType>) {
	const [fetching, setFetching] = useState(false);
	const [options, setOptions] = useState<ValueType[]>([]);
	const fetchRef = useRef(0);

	const fetchOptions = (inputValue: string): Promise<ValueType[]> => {
		const form = v.model?.source;
		const sourceId = v.model?.sourceid;
		let ss = "&filter=auto_name:!..";
		if (inputValue) {
			ss = "&filter=auto_name:*" + inputValue + "*";
		}
		return new Promise((resolve, reject) => {
			request({
				url: `/form/${form}/?fields=${sourceId == "id" ? "min" : "list"}&sort=${"auto_name"}&limit=100${ss}`,
			})
				.then((res) => {
					const opts: ValueType[] = res.data.map((row: any) => ({
						value: row[sourceId],
						label: row.auto_name,
						key: row.id,
						_raw: row,
					}));
					resolve(opts);
				})
				.catch((err) => {
					console.log(err);
					reject([]);
				});
		});
	};
	const loadOptions = (value: string) => {
		fetchRef.current += 1;
		const fetchId = fetchRef.current;
		setOptions([]);
		setFetching(true);

		fetchOptions(value).then((newOptions) => {
			if (fetchId !== fetchRef.current) {
				return;
			}
			setOptions(newOptions as any);
			setFetching(false);
		});
	};

	const debounceFetcher = useMemo(() => {
		return debounce(loadOptions, debounceTimeout);
	}, [fetchOptions, debounceTimeout]);

	useEffect(() => {
		loadOptions("");
	}, []);

	return (
		<Select
			labelInValue
			getPopupContainer={(triggerNode) => {
				return (
					triggerNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode ||
					document.body
				);
			}}
			optionLabelProp="label"
			mode={v.model?.multi ? "multiple" : "multiple"}
			style={{ width: "100%", minWidth: "200px" }}
			showSearch
			placeholder={"Select " + v.view?.label}
			filterOption={false}
			onSearch={debounceFetcher}
			notFoundContent={fetching ? <Spin size="small" /> : null}
			{...props}
			options={options}
		/>
	);
}

export const SelectGridFilter = forwardRef((props: CustomFilterProps<any, any, { value: any }>, ref) => {
	const { model, onModelChange, rowModel, colDef, getValue } = props;
	useImperativeHandle(ref, () => {
		return {
			componentMethod(message: string) {},
			doesFilterPass(params: ValueGetterParams<any>) {
				if (model?.value) return true;
				return false;
			},
		};
	});
	const v = colDef.filterParams.v;

	return (
		<div style={{ borderRadius: "5px", padding: "10px" }}>
			<DebounceSelect
				value={(model?.value as any) || []}
				onChange={(newValue) => {
					if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
						onModelChange(null);
					} else {
						onModelChange({
							value: newValue,
							filterType: v.model?.multi ? "multiSelect" : "multiSelect",
						});
					}
				}}
				allowClear={true}
				onClear={() => {
					onModelChange(null);
				}}
				v={v}
			/>
		</div>
	);
});
