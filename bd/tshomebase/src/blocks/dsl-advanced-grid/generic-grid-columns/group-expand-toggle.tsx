import { CustomCellRendererProps } from "ag-grid-react";
import { useState } from "react";

export const GroupExpandToggle = (props: CustomCellRendererProps) => {
	const [expanded, setExpanded] = useState(props.node.expanded);
	const toggleDetails = (e: React.MouseEvent<HTMLDivElement>) => {
		e.stopPropagation();
		props.node.setExpanded(!expanded);
		setExpanded(!expanded);
	};
	return (
		<div>
			<i
				style={{ color: "#837BB2" }}
				className={`custom-cell-button fa-solid fa-chevron-${expanded ? "up" : "down"}`}
				onClick={toggleDetails}
			></i>
		</div>
	);
};
