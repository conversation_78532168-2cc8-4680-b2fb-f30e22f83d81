import { CustomCellRendererProps } from "ag-grid-react";

export const CellClickIcon = (
	props: CustomCellRendererProps & {
		icon: string;
		color?: string;
		onClick: (data: CustomCellRendererProps) => void;
		bindField?: string;
	}
) => {
	const { data, icon, onClick, bindField, color } = props;
	if (!icon || !onClick) {
		return null;
	}
	const bindData = bindField ? data[bindField] : null;
	if (!bindData) {
		return null;
	}
	return (
		<div>
			<i
				style={{ color: color || "#837BB2" }}
				className={`custom-cell-button ${icon}`}
				onClick={() => {
					if (onClick) {
						onClick(props);
					}
				}}
			></i>
		</div>
	);
};
