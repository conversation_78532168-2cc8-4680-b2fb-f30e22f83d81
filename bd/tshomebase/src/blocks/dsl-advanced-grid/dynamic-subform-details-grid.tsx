import React from "react";
import { CustomCellRendererProps } from "ag-grid-react";
import "./dynamic-subform-details-grid.less";
import { DSLAdvancedGrid } from "@blocks/dsl-advanced-grid/dsl-advanced-grid";

export const DynamicSubformDetailsGrid = (
	props: CustomCellRendererProps & {
		form: string;
		children: React.ReactNode;
	}
) => {
	const { data, form } = props;
	if (!data || !form) {
		return null;
	}
	const { _meta, id: parentId } = data;
	const { source: parentForm } = _meta;
	if (!parentForm || !parentId) {
		return null;
	}

	return (
		<div className="dynamic-details-grid">
			{props.children || null}
			<div className="details-view-inner-container">
				<DSLAdvancedGrid
					domLayout="autoHeight"
					form={form}
					parentForm={parentForm}
					parentId={parentId}
					hideFilter
				/>
			</div>
		</div>
	);
};
