import React, { useEffect, useMemo, useRef, useState } from "react";
import { AgGridReact, CustomCellRendererProps, CustomToolPanelProps } from "ag-grid-react";
import "./dsl-advanced-grid.less";
import {
	ColDef,
	IServerSideDatasource,
	IFilterDef,
	ValueFormatterParams,
	RowClickedEvent,
	FilterModel,
	AdvancedFilterModel,
	GetRowIdFunc,
	RowDoubleClickedEvent,
	IRowNode,
	RowClassRules,
	GridReadyEvent,
	IServerSideGroupSelectionState,
	ColumnState,
	ColumnEvent,
	GridApi,
} from "ag-grid-enterprise";
import { DSLDrawLinkMap, DSLField, DSLForm } from "@typedefs/coffee/dsl";
import { request } from "@core/request";

import { SelectGridFilter } from "./custom-filters/select";
import { fetchFormFilters, useOnScreen } from "@hooks/index";
import { TabData } from "@blocks/tab-list";
import { GridSumInfo } from "@hooks/query";
import _ from "lodash";
import { GroupExpandToggle } from "@blocks/dsl-advanced-grid/generic-grid-columns/group-expand-toggle";
import { getUserPreference } from "@contexts/preference/provider";
import { toast } from "@components/toast";
import { blockUntilTimePassed } from "@utils/fx";

export interface AdvancedGridRef extends AgGridReact {
	advanced: {
		config: DSLAdvancedGridProps;
		refresh: (purge?: boolean) => void;
		search: (search: string) => void;
		filter: {
			apply: (filter: Record<string, unknown>) => void;
			patch: (filter: Record<string, unknown>) => void;
			clear: () => void;
			applied: () => Record<string, unknown>;
		};
		row: (action: "add" | "archive" | "edit", data: any) => void;
		selectedRows: () => any[];
	};
}

const getSortColumnsMap = (form: string) => {
	if (!window.DSL[form]) return null;
	if (form.startsWith("__")) {
		return null;
	}
	const fields = window.DSL[form].fields;
	const sort = window.DSL[form].view.grid.sort || [];

	const sortColumns: Record<string, "asc" | "desc"> = {};
	for (const s of sort) {
		if (typeof s !== "string") continue;
		const dir = s.startsWith("-") ? "desc" : "asc";
		const f = s.replace("-", "");
		if (fields[f]) {
			sortColumns[f] = dir;
		}
	}
	if (!Object.keys(sortColumns).length) {
		if (fields["id"]) {
			sortColumns["id"] = "desc";
		}
	}
	return sortColumns;
};

const getColumnType = (v: DSLField) => {
	const type = v.model.type;

	let colType = "text";
	if (type === "date" || type === "datetime") colType = "date";
	if (type === "time") colType = "time";
	if (type === "decimal" || type === "int") colType = "number";
	if (type === "image" || type === "json") colType = "object";
	if (type === "password") colType = "text";
	if (type === "subform") colType = "object";

	if (typeof v.model.source === "string" && window.DSL[v.model.source]) {
		if (v.model.multi) {
			colType = "object";
		} else colType = "object";
	}
	return colType;
};

const valueFormatter = (f: string, v: DSLField) => {
	const type = v.model.type;
	const source = v.model.source;
	const multi = v.model.multi;
	if (type === "text" && !source)
		return (params: ValueFormatterParams<any>) => {
			return params.data[f];
		};
	return (params: ValueFormatterParams<any>) => {
		if (!params.colDef.field) return null;
		const value = params.data[params.colDef.field] || params.value;
		if (!value) return null;
		if (source) {
			if (Array.isArray(source)) {
				return params.data[params.colDef.field];
			} else if (typeof source == "object") {
				if (multi) {
					if (Array.isArray(params.data[params.colDef.field])) {
						return params.data[params.colDef.field].map((item: string) => source[item]);
					}
					return null;
				} else {
					return source[params.data[params.colDef.field]] || null;
				}
			} else if (typeof source == "string") {
				return params.data[params.colDef.field + "_auto_name"] || null;
			}
		} else if (type === "date" || type === "datetime") {
			const date = window.moment(new Date(value));
			if (!date.isValid()) return null;
			if (type === "date") return date.format("MM/DD/YYYY");
			else if (type === "datetime") return date.format("MM/DD/YYYY hh:mm A");
		} else if (type === "time") return new Date(value).toLocaleTimeString();
		else if (type === "decimal" || type === "int") {
			if (v.view.class.includes("numeral") && v.view.format) {
				return window.numeral(value).format(v.view.format);
			}
			const i = Number(value);
			if (isNaN(i)) return null;
			if (Number.isInteger(i)) return i;
			return i.toFixed(2);
		} else if (type === "image" || type == "json") {
			if (typeof value === "object") return value;
			try {
				return JSON.parse(value);
			} catch (e) {
				return null;
			}
		} else if (type === "password") return null;
		else if (type === "subform") return null;
		return value;
	};
};

const getFilter = (f: string, formDef: DSLForm, customFindFields: string[]) => {
	const fieldDefs = formDef.fields;
	const field = fieldDefs[f];

	const canFilter = (field: string) => {
		if (customFindFields && customFindFields.length > 0) {
			if (customFindFields.includes(field)) return true;
			return false;
		}
		return true;
	};
	if (!field.view.label) {
		return {
			filter: false,
		};
	} else if (!canFilter(f)) {
		return {
			filter: false,
		};
	}
	const type = field.model.type;
	let filter = {
		filter: "agTextColumnFilter" as String | React.FC<any>,
		enablePivot: true,
		enableRowGroup: true,
		enableValue: true,
		filterParams: {} as IFilterDef["filterParams"],
	};
	if (type === "date" || type === "datetime") {
		filter.filter = "agDateColumnFilter";
	} else if (type === "text") {
	} else if (type === "time") {
		filter.filter = "agTextColumnFilter";
	} else if (type === "decimal" || type === "int") {
		filter.filter = "agNumberColumnFilter";
	} else if (type === "image" || type === "json") {
		filter.filter = "agTextColumnFilter";
		filter.enablePivot = false;
		filter.enableRowGroup = false;
	} else if (type === "password") {
		filter.filter = "agTextColumnFilter";
		filter.enablePivot = false;
		filter.enableRowGroup = false;
		filter.enableValue = false;
	} else if (type === "subform") {
		filter.filter = "agTextColumnFilter";
		filter.enablePivot = false;
		filter.enableRowGroup = false;
		filter.enableValue = false;
	}
	if (field.model.source) {
		if (field.model.multi) {
			filter.filterParams.defaultToNothingSelected = true;
			filter.filter = "agMultiColumnFilter";
		} else {
			filter.filterParams.defaultToNothingSelected = true;
			filter.enablePivot = true;
			filter.enableRowGroup = true;
			filter.enableValue = true;
			filter.filter = "agSetColumnFilter";
		}
		if (Array.isArray(field.model.source)) {
			filter.filterParams.values = field.model.source;
		} else if (field.model.source && typeof field.model.source === "object") {
			filter.filterParams.values = Object.keys(field.model.source || {});
			filter.filterParams.valueFormatter = (params: ValueFormatterParams) => {
				return (field.model.source as Record<string, string>)[params.value];
			};
		}
		if (typeof field.model.source === "string" && window.DSL[field.model.source]) {
			filter.filter = SelectGridFilter;
			filter.filterParams.v = field;
			filter.filterParams.type = field.model.multi ? "multiSelect" : "singleSelect";
			filter.filterParams.type = "multiSelect";
		}
	}
	return filter;
};

const getColumnDefs = (
	props: DSLAdvancedGridProps,
	form: string,
	customColumnsInitial: string[] = [],
	customFindFields: string[] = [],
	customColumnDefs: CustomColumnDef[] = [],
	initialFilters: Record<string, unknown> = {}
): [ColDef[], Record<string, unknown>] => {
	let customColumns = _.cloneDeep(customColumnsInitial);
	const isMaterializedForm = form.startsWith("__");
	const initFilters: Record<string, unknown> = { ...initialFilters };
	const defOverrides: Record<string, CustomColumnDef> = {};
	let columnState: ColumnState[] = window?.UserPreference?.grid_configurations?.[form]?.columnState || [];
	if (props.suppressUserConfig) {
		columnState = [];
	}
	for (const colDef of customColumnDefs) {
		if (colDef.field) {
			defOverrides[colDef.field] = colDef;
		}
	}
	if (columnState?.length) {
		customColumns = Array.from(new Set(_.cloneDeep(columnState.map((col: ColumnState) => col.colId)))).filter(
			(col: string) => !defOverrides[col]
		);
	}

	for (const colState of columnState) {
		if (defOverrides[colState.colId]) {
			continue;
		}
		if (!defOverrides[colState.colId]) {
			defOverrides[colState.colId] = {};
		}
		if (defOverrides[colState.colId]) {
			defOverrides[colState.colId].sort = colState.sort;
			defOverrides[colState.colId].sortIndex = colState.sortIndex;
			if (colState.width) {
				defOverrides[colState.colId].width = colState.width;
				defOverrides[colState.colId].flex = colState.flex as any;
			}
		}
	}

	const sortColumns = getSortColumnsMap(form);
	const formDef = window.DSL[form];
	if (customFindFields.length == 0) {
		customFindFields = [...(formDef.view.find.basic || []), ...(formDef.view.find.advanced || [])];
	}
	const fieldDefs = formDef.fields;
	const colDefs: ColDef[] = [];
	const gridFields = [];
	const usedFields = new Set();
	if (customColumns.length) {
		gridFields.push(...customColumns);
	} else {
		gridFields.push(...formDef.view.grid.fields);
	}
	for (const field of gridFields) {
		if (!fieldDefs[field]) continue;
		if (!fieldDefs[field].view.label) continue;
		if (formDef.view.grid.hide_columns.includes(field) && !customColumns.includes(field)) continue;
		usedFields.add(field);
		if (fieldDefs[field].view.findfilter) {
			initFilters[field] = fieldDefs[field].view.findfilter;
		}
		colDefs.push({
			field: field,
			sort: sortColumns?.[field],
			headerName: fieldDefs[field].view.label,
			resizable: true,
			cellStyle: isMaterializedForm ? getCellStyle : undefined,
			type: getColumnType(fieldDefs[field]),
			...getFilter(field, formDef, customFindFields),
			valueFormatter: valueFormatter(field, fieldDefs[field]),
			...(defOverrides[field] || {}),
		});
	}
	for (const [field, v] of Object.entries(fieldDefs)) {
		if (!v.view.label) continue;
		if (usedFields.has(field)) continue;
		if (v.model.type == "subform") continue;
		if (v.model.type == "json") continue;
		if (v.model.type == "image") continue;
		if (v.model.type == "password") continue;
		if (v.view.findfilter) {
			initFilters[field] = v.view.findfilter;
		}
		colDefs.push({
			field: field,
			initialHide: true,
			sort: sortColumns?.[field],
			headerName: v.view.label,
			resizable: true,
			cellStyle: isMaterializedForm ? getCellStyle : undefined,
			type: getColumnType(v),
			...getFilter(field, formDef, customFindFields),
			valueFormatter: valueFormatter(field, v),
			...(defOverrides[field] || {}),
		});
	}
	for (const [field, colDef] of Object.entries(customColumnDefs)) {
		if (colDef.pinned == "left") {
			colDefs.unshift(colDef);
		} else if (colDef.pinned == "right") {
			colDefs.push(colDef);
		}
	}
	if (props.detailCellRenderer) {
		colDefs.push({
			field: "options",
			headerName: "  ",
			pinned: "right",
			cellRenderer: GroupExpandToggle,
			type: "text",
			maxWidth: 45,
			suppressSizeToFit: true,
			filter: false,
			resizable: false,
			suppressHeaderMenuButton: true,
		});
	}
	return [colDefs, initFilters];
};

const useColumnDefs = (
	props: DSLAdvancedGridProps,
	form: string,
	customColumns: string[] = [],
	customFindFields: string[] = [],
	customColumnDefs: CustomColumnDef[] = [],
	initialFilters: Record<string, unknown> = {}
) => {
	return useMemo(() => {
		return getColumnDefs(props, form, customColumns, customFindFields, customColumnDefs, initialFilters);
	}, []);
};

const getDataSource = (
	props: DSLAdvancedGridProps,
	keywords: string,
	onDataRefreshed: (response: { rowData: any[]; rowCount: number; pivotResultFields: string[] }) => void
): IServerSideDatasource => {
	const generateQueryParams = (props: DSLAdvancedGridProps) => {
		const { form, fields = "list", linkMap, filtersPresetFixed } = props;

		const params = [];
		if (!props.gridSourceOverrideURL) {
			params.push(`fields=${fields}`);
		}
		if (props.parentForm && props.parentId) {
			params.push(`parent_form=${props.parentForm}`);
			params.push(`parent_id=${props.parentId}`);
		}

		const allFilters = _.merge({}, filtersPresetFixed || {});

		for (const [key, value] of Object.entries(allFilters)) {
			if (!key || key.endsWith("_auto_name")) continue;
			if (Array.isArray(value)) {
				value.forEach((v) => {
					params.push(`filter=${key}:${v}`);
				});
			} else if (typeof value != "object") {
				params.push(`filter=${key}:${value}`);
			}
		}

		if (linkMap?.linkid) {
			for (const [key, value] of Object.entries(linkMap.linkid)) {
				if (key && key.endsWith("_id")) {
					params.push(`filter=${key}:${value || "-1"}`);
				} else {
					params.push(`filter=${key}_id:${value || "-1"}`);
				}
			}
		}

		return params;
	};

	const { form, gridSourceOverrideURL } = props;

	return {
		getRows: (params) => {
			const filterParams = generateQueryParams(props);
			if (keywords) {
				filterParams.push(`keywords=${keywords}`);
			}
			const generateSelectFilters = (k: string, v: any) => {
				if (!v || !v.value) return;
				if (v.filterType === "singleSelect") {
					filterParams.push(`filter=${k}:${v.value.value}`);
				} else if (v.filterType === "multiSelect") {
					v.value.forEach((r: any) => {
						if (typeof r == "object" && r.value) {
							filterParams.push(`filter=${k}:${r.value}`);
						}
					});
				}
			};

			const sourceURL = () => {
				const baseURL = gridSourceOverrideURL || `/api/form/${form}/list`;
				const queryString = filterParams.length ? filterParams.join("&") : "";

				if (!queryString) return baseURL;

				const separator = baseURL.includes("?") ? "&" : "?";
				return `${baseURL}${separator}${queryString}`;
			};

			const newFilterModel: FilterModel | AdvancedFilterModel | null = {};
			for (const [k, v] of Object.entries(params.request.filterModel || {})) {
				if (["singleSelect", "multiSelect"].includes(v.filterType)) {
					generateSelectFilters(k, v);
				}
				newFilterModel[k] = v;
			}
			params.request.filterModel = newFilterModel;
			request({
				url: sourceURL(),
				method: "POST",
				data: params.request,
			})
				.then((response) => {
					const fields = (response.data.pivotFields || [])
						.filter((f: string) => !f.endsWith("_auto_name"))
						.filter(Boolean);
					const r = {
						rowData: response.data.rows,
						rowCount: response.data.lastRow,
						pivotResultFields: fields.length ? fields : null,
					};

					onDataRefreshed(r);
					params.success(r);
				})
				.catch((err) => {
					console.log("error", err);
					params.fail();
				});
		},
	};
};

export type GridRowCountChangeEvent = (opts: {
	ref?: AdvancedGridRef | null;
	form: string;
	props: DSLAdvancedGridProps;
}) => void;

export type GridRowClickedEvent = (
	row: TabData,
	event: RowClickedEvent<any, any> | RowDoubleClickedEvent<any, any>
) => void;

export type GridRefCallback = (ref: AdvancedGridRef | null) => void;
export interface DSLAdvancedGridProps {
	// Configuration
	domLayout?: "autoHeight" | "normal";
	form: string;
	parentForm?: string;
	parentId?: string;
	fields?: "min" | "list" | "all";
	keyField?: string;
	hideFilter?: boolean;
	linkMap?: DSLDrawLinkMap | null | undefined;
	rowSelection?: "singleRow" | "multiRow";
	highlightOnRowSelection?: "single" | "multi" | undefined;
	selectedHighlight?: boolean;
	rowClassRules?: RowClassRules<any>;
	suppressLoadOnScreen?: boolean;
	suppressUserConfig?: boolean;
	rowModelType?: "serverSide" | "clientSide";
	rowData?: any[];
	sort?: {
		direction?: "asc" | "desc";
		property?: string;
	};

	detailCellRenderer?: any;
	detailCellRendererParams?: any;

	initialFilters?: Record<string, unknown>;
	filtersPresetFixed?: Record<string, unknown>;

	sumInfo?: GridSumInfo;

	// Callbacks
	onRef?: GridRefCallback;
	rowClicked?: GridRowClickedEvent;

	// Customization
	customColumns?: string[];
	customColumnDefs?: CustomColumnDef[];
	customFindFields?: string[];
	gridSourceOverrideURL?: string;

	// events
	onRowCountChangeEvent?: GridRowCountChangeEvent;
}

export interface GridRowNode extends IRowNode<any> {}

type CustomColumnDef = ColDef & {
	dslDriver?: boolean;
	position?: "start" | "end";
};

export const getSelectFilterValues = async (form: string, vals: string[] | number[], sourceIdField: string) => {
	const n = await fetchFormFilters(form, {
		filter: {
			[sourceIdField]: vals,
		},
		fields: "min",
		limit: 999,
	});
	if (n.success) {
		return n.data.map((d) => {
			return {
				disabled: undefined,
				key: d[sourceIdField],
				label: d.auto_name,
				title: d.auto_name,
				value: d[sourceIdField],
			};
		});
	}
	return [];
};

// Create a reusable function to build filter models
const buildFilterModel = async (filters: Record<string, unknown>, api: any): Promise<FilterModel> => {
	const filterModel: FilterModel = {};

	for (let [field, value] of Object.entries(filters || {})) {
		if (value === null || value === undefined) {
			continue;
		}

		const colDef = api.getColumnDef(field);
		if (!colDef) {
			continue;
		}

		if (colDef.filter === "agTextColumnFilter") {
			filterModel[field] = {
				type: "contains",
				filter: value as string,
			};
		} else if (colDef.filter === "agNumberColumnFilter") {
			filterModel[field] = {
				type: "equals",
				filter: value as number,
			};
		} else if (colDef.filter === "agDateColumnFilter") {
			filterModel[field] = {
				type: "equals",
				dateFrom: value as string,
			};
		} else if (colDef.filter === SelectGridFilter) {
			const v = colDef.filterParams.v as DSLField;
			const sourceForm = v.model.source as string;
			const sourceIdField = (v.model.sourceid || "id") as string;
			if (!Array.isArray(value)) {
				value = [value];
			}
			value = await getSelectFilterValues(sourceForm, value as number[], sourceIdField);
			if (Array.isArray(value)) {
				filterModel[field] = {
					filterType: "multiSelect",
					value: value,
				};
			} else {
				filterModel[field] = {
					filterType: "singleSelect",
					value: {
						disabled: undefined,
						key: value,
						label: `Record: ${value}`,
						title: `Record: ${value}`,
						value: value,
					},
				};
			}
		} else if (["agSetColumnFilter", "agMultiColumnFilter"].includes(colDef.filter as any)) {
			if (Array.isArray(value)) {
				filterModel[field] = {
					filterType: "set",
					values: value,
				};
			} else {
				filterModel[field] = {
					filterType: "set",
					values: [value],
				};
			}
		}
	}

	return filterModel;
};

const ConfigurationToolPanel = (
	props: CustomToolPanelProps & {
		handleColumnChange: (api: GridApi<any>, action: "save" | "reset") => void;
	}
) => {
	const onConfigAction = async (action: "save" | "reset") => {
		props.handleColumnChange(props.api, action);
	};
	return (
		<div className="agc-toolbar-container">
			<div className="agc-toolbar-layout agc-title">Configuration</div>

			<div className="agc-toolbar-layout">
				<div className="agc-toolbar-title">Layout Configuration</div>
				<div className="agc-toolbar-btn-group">
					{/* <div
						className="dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button base-style-accept"
						onClick={() => onConfigAction("save")}
					>
						<div className="inner-cont">
							<p className="label">Save</p>
						</div>
					</div> */}
					<div
						className="dsl-action-btn wizard-bottom-btn-default btn-save  btn-save base-form-button base-style-error"
						onClick={() => onConfigAction("reset")}
					>
						<div className="inner-cont">
							<p className="label">Reset</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export const getCellStyle = (params: any) => {
	const data = params.data;
	if (!data) return undefined;

	const rowData = data.__row_data || data;

	const textColor = rowData.__text_color;
	const fontWeight = rowData.__font_weight;

	if (!textColor && !fontWeight) {
		return undefined;
	}

	const styles: Record<string, string> = {};
	if (textColor) styles.color = textColor;
	if (fontWeight) styles.fontWeight = fontWeight;
	return styles;
};

export const getRowStyle = (params: any) => {
	const data = params.data;
	if (!data) return undefined;

	const rowData = data.__row_data || data;

	const rowColor = rowData.__row_color;

	if (!rowColor) {
		return undefined;
	}

	const styles: Record<string, string> = {};

	if (rowColor) styles.background = rowColor;
	return styles;
};

export const DSLAdvancedGrid = React.memo(
	(props: DSLAdvancedGridProps) => {
		const selectedRow = useRef<any>(null);
		const {
			form,
			onRef,
			fields = "list",
			rowClicked,
			customColumns,
			rowSelection,
			rowModelType = "serverSide",
			keyField = "dynamic",
			customFindFields,
			highlightOnRowSelection,
			sort,
			suppressLoadOnScreen = false,
			customColumnDefs = [],
			initialFilters,
		} = props;
		const [colDefs, initFilters] = useColumnDefs(
			props,
			form,
			customColumns,
			customFindFields,
			customColumnDefs,
			initialFilters
		);
		const internalRef = useRef<AgGridReact | null>(null);
		const [keywords, setKeywords] = useState("");
		const appliedFilters = useRef<Record<string, unknown>>({});

		const [isOnScreen, elementRef] = useOnScreen(true);
		const isRendered = useRef(suppressLoadOnScreen ? true : false);
		const initialDataLoaded = useRef(false);
		const pivotColumnHeaders = useRef<Record<string, string>>({});

		const initialFiltersApplied = useRef(false);

		const onDataRefreshed = (response: { rowData: any[]; rowCount: number; pivotResultFields: string[] }) => {
			if (!initialDataLoaded.current) {
				setTimeout(() => {
					initialDataLoaded.current = true;
				}, 1000);
				return;
			}

			pivotColumnHeaders.current = {};
			const { rowData, rowCount, pivotResultFields } = response;
			if (!pivotResultFields) return;
			if (!pivotResultFields.length) return;
			for (const field of pivotResultFields) {
				const groupId = field.split("__")[0];
				if (pivotColumnHeaders.current[groupId]) continue;
				for (const row of rowData) {
					if (row[field + "_auto_name"]) {
						pivotColumnHeaders.current[groupId] = row[field + "_auto_name"];
						break;
					}
				}
			}
		};

		useEffect(() => {
			if (!isOnScreen) {
				return;
			}
			if (!isRendered.current) {
				isRendered.current = true;
			}
		}, [isOnScreen]);

		useEffect(() => {
			return () => {
				isRendered.current = false;
			};
		}, []);

		// Apply initial filters when grid is ready
		const updateFilterModel = async (filter: Record<string, unknown>, reload: boolean = false) => {
			if (!internalRef.current?.api) return;
			const filterModel = await buildFilterModel(filter, internalRef.current.api);
			internalRef.current.api.setFilterModel(filterModel);
			refresh(reload);
		};

		const refresh = (purge: boolean = true) => {
			internalRef.current?.api.refreshServerSide({ purge });
		};

		const search = (keywords: string) => {
			setKeywords(keywords);
			refresh();
		};

		const apply = (filter: Record<string, unknown> = {}) => {
			appliedFilters.current = { ...appliedFilters.current, ...filter };
			updateFilterModel(filter, true);
		};

		const patch = async (filter: Record<string, unknown> = {}) => {
			if (!internalRef.current?.api) return;
			appliedFilters.current = { ...appliedFilters.current, ...filter };
			const filterModel = internalRef.current.api.getFilterModel();
			const filterToPatch = await buildFilterModel(filter, internalRef.current.api);
			internalRef.current.api.setFilterModel({ ...filterModel, ...filterToPatch });
			refresh(true);
		};

		const clear = () => {
			if (!internalRef.current?.api) return;
			appliedFilters.current = {};
			internalRef.current.api.setFilterModel(null);
			setKeywords("");
			updateFilterModel({ ...(props.filtersPresetFixed || {}) }, true);
		};

		const applied = () => {
			return appliedFilters.current;
		};

		const selectedRows = () => {
			const api = internalRef.current?.api;
			if (!api) return [];
			const selectionState = api.getServerSideSelectionState();
			if (!selectionState) return [];
			const selectedRows: any[] = [];
			if (selectionState.toggledNodes && selectionState.toggledNodes.length > 0) {
				selectionState.toggledNodes.forEach((nodeId: IServerSideGroupSelectionState) => {
					const rowNode = api.getRowNode(nodeId as string);
					if (rowNode) {
						selectedRows.push(rowNode.data);
					}
				});
			} else if ("selectAll" in selectionState && selectionState.selectAll) {
				api.forEachNode((node) => {
					selectedRows.push(node.data);
				});
			}
			return selectedRows;
		};

		const row = (action: "add" | "archive" | "edit", data: any) => {
			if (!internalRef.current?.api) return;
			const api = internalRef.current.api;

			if (action === "add") {
				api.refreshServerSide({ purge: false });
			} else if (action === "archive" || action === "edit") {
				api.forEachNode((node) => {
					if (node.data?.id === data.id) {
						if (action === "archive") {
							api.applyTransactionAsync({ remove: [node.data] });
						} else {
							node.setData(data);
						}
					}
				});
				api.refreshServerSide({ purge: false });
				callRowCountChangeEvent();
			}
		};

		const callRowCountChangeEvent = () => {
			if (!props.onRowCountChangeEvent) return;
			props.onRowCountChangeEvent?.({
				ref: getAdvancedGridRef(internalRef.current),
				form,
				props,
			});
		};

		const onRowClicked = (
			params: RowClickedEvent<any> | RowDoubleClickedEvent<any>,
			type: "click" | "dblclick" = "click"
		) => {
			const rowNode = params.node;
			const gridApi = params.api;
			const isClickFromCustomElement = (params.event?.target as HTMLElement)?.closest(".custom-cell-button");
			if (isClickFromCustomElement) {
				return;
			}
			if (params.node.detail) {
				return;
			}
			if (gridApi.isPivotMode()) {
				return;
			}
			if (!params.data) return;
			if (!rowNode) return;
			if (props.selectedHighlight) {
				const internalSelectedRow = selectedRow.current;
				if (selectedRow.current) {
					selectedRow.current = null;
					params.api.redrawRows({
						rowNodes: [internalSelectedRow],
					});
				}
				if (internalSelectedRow?.rowIndex !== params.node.rowIndex) {
					selectedRow.current = rowNode;
				}
				params.api.redrawRows({
					rowNodes: [rowNode],
				});
			}
			const isArchived = params.data?._meta?.archived || params.data?.archived || false;
			let mode = window.DSL[form].view.open || "read";
			if (
				mode == "edit" &&
				!isArchived &&
				window.Auth.can_update_any(form) &&
				window.Auth.not_blocked_update(form)
			) {
				mode = "edit";
			} else {
				mode = "read";
			}
			if (!rowClicked) return;
			rowClicked(
				{
					id: params.data.id,
					label: params.data.auto_name,
					mode,
					form,
					type,
					rowData: params.data,
					rowNode: params.node,
					gridRef: getAdvancedGridRef(internalRef.current),
				} as TabData,
				params
			);
		};

		const getAdvancedGridRef = (r: AgGridReact | null) => {
			if (!r) return null;
			(r as any).advanced = {
				config: props,
				refresh,
				search,
				filter: {
					apply,
					patch,
					clear,
					applied,
				},
				row,
				selectedRows,
			};
			return r as AdvancedGridRef;
		};
		const getRowIdByData = (data: any) => {
			let key = "";
			if (keyField === "dynamic") {
				key = data.id || data.tag;
			} else if (keyField) {
				key = data[keyField];
			}
			if (!key) {
				key = JSON.stringify(data);
			}
			return `${key}`;
		};
		const getRowId: GetRowIdFunc<any> = (params) => {
			return getRowIdByData(params.data);
		};

		const DEFAULT_CONFIG: ColDef = {
			filter: true,
			flex: 1,
			minWidth: 100,
			resizable: true,
			floatingFilter: false,
			enableCellChangeFlash: true,
			sortable: true,
			enablePivot: true,
			enableRowGroup: true,
			enableValue: true,
			cellClass: (params) => {
				const isSorted = params.column.getSort();
				const baseClass = "default-style";
				return isSorted === "asc" || isSorted === "desc" ? `${baseClass} sorted-column` : "unsorted-columns";
			},
			headerClass: () => {
				return "header-style";
			},
		};

		const onGridReady = (params: GridReadyEvent) => {
			if (!initialFiltersApplied.current) {
				if (Object.keys(initFilters).length > 0) {
					updateFilterModel(initFilters, true);
				}
				initialFiltersApplied.current = true;
			}
		};

		const handleColumnChangeInternal = (event: ColumnEvent) => {
			handleColumnChange(event.api, "save");
		};

		const handleColumnChange = blockUntilTimePassed(async (api: GridApi<any>, action: "save" | "reset") => {
			if (props.suppressUserConfig) {
				return;
			}
			if (!initialDataLoaded.current) {
				return;
			}
			try {
				let pref = await getUserPreference();

				if (!pref.grid_configurations) {
					pref.grid_configurations = {};
				}
				try {
					if (typeof pref.grid_configurations === "string") {
						pref.grid_configurations = JSON.parse(pref.grid_configurations) as any;
					}
				} catch (err) {
					console.error(err);
					toast({
						type: "error",
						message: "Unable to save grid configuration",
					});
					return;
				}
				const columns = (api.getColumnState() || []).filter((col) => !col.hide);
				if (!columns.length) {
					throw new Error("No columns found");
				}
				let columnStateToSave: ColumnState[] = [];
				const dslFields = window.DSL[form].fields;
				// only include active fields
				for (const col of columns) {
					if ("colId" in col && dslFields[col.colId as string]) {
						columnStateToSave.push(col);
					}
				}
				if (action == "save") {
					if (!pref.grid_configurations) {
						pref.grid_configurations = {};
					}
					pref.grid_configurations[form] = {
						columnState: columnStateToSave,
					};
				} else {
					if (!pref.grid_configurations) {
						pref.grid_configurations = {};
					}
					toast({
						type: "info",
						message: "Resetting grid configuration",
					});
					delete pref.grid_configurations[form];
				}
				request({
					url: `/my/preference/`,
					method: "PUT",
					data: {
						id: pref.id,
						grid_configurations: pref.grid_configurations,
					},
				})
					.then(async () => {
						console.info(
							`%c Grid Configuration Saved %c ${props.form}`,
							"background: #1a1a1a; color: #00bcd4; font-weight: bold; padding: 2px 6px; border-radius: 3px;",
							"color: #2196f3; font-weight: bold;"
						);
						pref = await getUserPreference();
						window.UserPreference = pref;
						if (action == "reset") {
							updateGridConfig(api);
						}
					})
					.catch((err) => {
						console.error(err);
						toast({
							type: "error",
							message: "Unable to save grid configuration",
						});
					});
			} catch (err) {
				console.error(err);
				toast({
					type: "error",
					message: "Unable to save grid configuration",
				});
			}
		}, 500);

		const updateGridConfig = (api: GridApi<any>) => {
			if (!api) return;
			const [newColumnDefs] = getColumnDefs(
				props,
				form,
				customColumns,
				customFindFields,
				customColumnDefs,
				initialFilters
			);
			api.setGridOption("columnDefs", newColumnDefs);
			toast({
				type: "info",
				message: "Grid configuration reset",
			});
		};

		return (
			<div className="dsl-advanced-grid" ref={elementRef}>
				{suppressLoadOnScreen || isRendered.current || isOnScreen ? (
					<AgGridReact
						key={`${form}-${props.domLayout}`}
						ref={(r) => {
							if (!r) return;
							(internalRef as any).current = r;
							if (typeof onRef === "function") {
								onRef(getAdvancedGridRef(r));
							}
						}}
						onGridReady={onGridReady}
						masterDetail={props?.detailCellRenderer ? true : undefined}
						detailCellRenderer={props?.detailCellRenderer}
						detailRowAutoHeight={true}
						detailCellRendererParams={props?.detailCellRendererParams}
						rowSelection={
							rowSelection
								? {
										mode: rowSelection,
								  }
								: undefined
						}
						getRowId={getRowId}
						rowClassRules={{
							"even-background": (params) => params.rowIndex % 2 === 1,
							"odd-background": (params) => params.rowIndex % 2 === 0,
							"ag-row-selected": (params) => {
								if (highlightOnRowSelection === "single") {
									return params.node === selectedRow.current;
								} else if (highlightOnRowSelection === "multi") {
									return params.node.isSelected() || false;
								}
								return false;
							},
							...(props.rowClassRules || {}),
						}}
						getRowStyle={getRowStyle}
						rowModelType={rowModelType || "serverSide"}
						rowData={rowModelType === "serverSide" ? undefined : props.rowData || []}
						rowHeight={40}
						onRowClicked={(event) => onRowClicked(event, "click")}
						onRowDoubleClicked={(event) => onRowClicked(event, "dblclick")}
						domLayout={props.domLayout || "normal"}
						columnDefs={colDefs}
						serverSideDatasource={
							rowModelType === "serverSide" ? getDataSource(props, keywords, onDataRefreshed) : undefined
						}
						serverSidePivotResultFieldSeparator={"__"}
						onColumnMoved={handleColumnChangeInternal}
						onColumnVisible={handleColumnChangeInternal}
						onColumnResized={handleColumnChangeInternal}
						onSortChanged={(event) => {
							handleColumnChange(event.api, "save");
						}}
						sideBar={{
							toolPanels: [
								"columns",
								"filters",
								{
									id: "configuration",
									labelDefault: "Configuration",
									labelKey: "configuration",
									iconKey: "config fa-solid fa-sliders",
									toolPanel: ConfigurationToolPanel,
									toolPanelParams: {
										config: props,
										handleColumnChange,
									},
								},
							],
							hiddenByDefault: props.hideFilter || false,
						}}
						processPivotResultColGroupDef={(event) => {
							if (event.groupId && pivotColumnHeaders.current[event.groupId]) {
								event.headerName = pivotColumnHeaders.current[event.groupId];
							}
						}}
						multiSortKey={"ctrl"}
						defaultColDef={DEFAULT_CONFIG}
						suppressHorizontalScroll={false}
						enableCellTextSelection={true}
					/>
				) : null}
			</div>
		);
	},
	(prev, next) => {
		return prev.form === next.form;
	}
);
