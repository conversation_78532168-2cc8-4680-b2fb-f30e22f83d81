@import (reference) './tab-list.vars.less';
@import (reference) '../../less/style/main.less';

.tab-list-default {
    display: flex;
    justify-content: flex-start;
    flex-direction: row;
    flex-grow: 0;
    flex-shrink: 0;

    .dynamic-tab-list {
        height: 46px;
        display: flex;
        background-color: #fbfbfbb8;
        flex-direction: row;
        border-radius: 8px;
        overflow-x: auto;
        overflow-y: clip;
        padding: 0 4px;
        gap: 16px;

        &::-webkit-scrollbar {
            height: 0px;
        }

        &:hover {
            &::-webkit-scrollbar {
                height: 2.5px;
            }
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.5);

        }

        &::-webkit-scrollbar-track {
            background-color: rgba(0, 0, 0, 0.2);
        }

    }


    &.tab-add {
        cursor: pointer;
    }

    .tab-list-button {
        display: flex;
        min-width: max-content !important;
        cursor: pointer;
        .tab-list-button-common;

        .tab-label {
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 24ch;
            overflow: hidden;
            user-select: none;
        }

        .tab-avatar {
            cursor: pointer;
            user-select: none;

            img {
                height: 22px;
                width: 22px;
            }
        }



        &.tab-active {
            cursor: pointer;

            &::after {
                cursor: pointer;
            }
        }

    }

    .tab-list-button-add {
        .tab-label-add {
            color: var(--color-tertiary);

            .tab-label-add-container {
                .add-container {
                    font-weight: 700;
                    font-size: 14px;
                    margin-right: 4px;
                }

                .tab-label-add-text {
                    font-size: 14px !important;
                    font-weight: 500 !important;
                }
            }
        }
    }


    @media (max-width: 768px) {
        // .dynamic-tab-list::-webkit-scrollbar {
        //     height: 2px;
        // }
    }
}


.tab-list {
    .tab-bar-lvl-three;
    background-color: transparent;
    flex-grow: 0;
    flex-shrink: 0;

    &.tab-add {
        background: transparent;
        color: @tab-btn-active-color;

        &::after {
            background: transparent !important;
            color: @tab-btn-active-color;
        }
    }

    .tab-list-button {
        align-items: center;
        .tab-item-lvl-three;
    }

    >.tab-list-button {
        // &:first-child {
        //     border-top-left-radius: @border-radius-8;
        // }


        &.tab-list-button-add {
            border: 0;
            background-color: transparent;
        }

        .tab-label-add {
            border: 0;
            font-size: 25px;
        }
    }

    .dynamic-tab-list {
        .tab-list-button {

            // &:last-child {
            //     border-top-right-radius: @border-radius-8;
            // }
        }
    }
}


.tab-list-sub {

    &[parent_tabgroupid][tabcontroller] {
        border-left: 1px solid #dbdbdd;
        margin-top: 0px !important;
    }

    .tab-list-default;

    .tab-list-button {
        .tab-list-button-common;
        display: flex;
        min-width: max-content !important;
        cursor: pointer;
        padding: 5px 16px;

        .tab-label {
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 24ch;
            font-size: 12px;
            line-height: 24px;
            font-weight: 500;
            color: @black;
            overflow: hidden;
            user-select: none;
        }

        &.tab-active {
            .tab-label {
                border-bottom: 2px solid @purple;
                border-radius: 2px;
                font-weight: 600;
            }
        }

    }
}

.tab-list-sub-sep {
    // .tab-list-sub;

    .tab-list-button {
        // border-right: 1px solid #DCDCDC;
    }

    .tab-list-button:last-child {
        // border-right: unset;
    }
}