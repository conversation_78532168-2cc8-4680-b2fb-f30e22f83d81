@import (reference) "../../less/style/main.less";

@filter-action-btn-color: #4d4e8d;

#application {
  .findbar {
    .findbarcontainer {
      .findbasic,
      .findadvanced {
        .control-label {
          .checkboxes {
            &.checkbox-only {
              width: inherit;

              label > span {
                display: none;
              }
            }
          }
        }
      }
    }
  }

  .popup {
    display: flex;
    z-index: 1;
    border-radius: 0px 0px 8px 8px;
    right: 15px;
    margin-top: -35px;
    z-index: 2;
    position: absolute;
    flex-direction: column;
    border: 3px solid #f8f5fc;
    flex-grow: 0;
    width: calc(100% - 40px);
    padding: var(--dsl-list-bottom-popup-p);
    .card-two;
    height: auto;
    box-shadow: @find-view-popup-box-shadow;

    .slideHeightWidth(0, 100vh, 400ms, 0, "inherit", 100ms);
  }

  .find-view-popup {
    margin-bottom: 10px;

    .findbar .findtab {
      .findbasic,
      .findadvanced {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        .gap-7;

        label {
          display: flex;
          width: 22%;
          flex-direction: column;
          gap: 10px;
          .fadeInLoop(0.2s);

          .select2-container-multi {
            overflow-x: auto;

            .select2-choices {
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              overflow-x: auto;

              .select2-search-choice {
                max-width: fit-content;

                > div {
                  min-width: max-content;
                }
              }
            }
          }

          :first-child {
            text-align: start;
            width: 100%;
          }

          > input {
            width: 100%;
          }

          input[type="checkbox"] {
            .checkbox-one;
          }

          > div {
            width: 100%;
            padding: 0px;
          }

          select {
            width: 100%;
          }
        }
      }
    }

    .find-action-btn {
      position: absolute;
      right: 0px;
      bottom: 15px;
      flex-shrink: 0;
      gap: 10px;
      display: flex;
      flex-direction: row;
      padding: 10px;
      justify-content: flex-end;

      .filter-icon-btn {
        display: none;
      }

      .action {
        user-select: none;
        display: flex;
        flex-direction: row;
        gap: 10px;

        .filter-btn {
          .flex-justify-center(row, space-between);
          .btn-primary;
          gap: 10px;
        }
      }
    }

    .dsl-find-view {
      display: flex;

      .findbar {
        padding: unset;
        border: unset;
        background: unset;
        width: 100%;

        .findbarcontainer {
          padding: 4px;
          background: unset;
          border: unset;
          border-radius: unset;
          overflow-y: auto;
          height: 100%;
        }

        .findtab {
          border-right: unset;
          padding: unset;
          display: flex;
          flex-direction: column;
          gap: 10px;
        }
      }
    }
  }

  .find-view {
    display: flex;
    flex: 1;
    border-radius: 0px 0px 8px 8px;
    background: white;
    justify-content: space-between;
    height: 100%;

    .find-action-btn {
      flex-shrink: 0;
      gap: 10px;
      display: flex;
      flex-direction: row;
      padding: 10px;
      justify-content: flex-end;

      .filter-icon-btn {
        justify-content: flex-start;
        display: flex;

        > div > img {
          height: 24px;
          width: 24px;
          transition-duration: 500ms;
          transition-property: "transform";
        }

        background-color: transparent;
        border: unset;

        &.filter-flip-icon > div > img {
          transform: rotate(180deg);
          transition-duration: 500ms;
          transition-property: "transform";
        }
      }

      .action {
        user-select: none;
        display: flex;
        flex-direction: row;
        gap: 10px;

        .filter-btn {
          .btn-primary;
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          gap: 10px;
          align-items: center;
        }
      }
    }

    .dsl-find-view {
      display: flex;

      .findbar {
        padding: unset;
        border: unset;
        background: unset;

        .findbarcontainer {
          padding: 4px;
          background: unset;
          border: unset;
          border-radius: unset;
          overflow-y: auto;
          height: 100%;
        }

        .findtab {
          border-right: unset;
          padding: unset;
          gap: 10px;
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .popup {
      padding: 10px;

      .findbar .findtab {
        .findbasic,
        .findadvanced {
          flex-wrap: wrap;

          gap: 10px;

          label {
            width: 32%;

            input {
              width: 100%;
            }
          }
        }
      }

      .dsl-find-view {
        flex-direction: column !important;
      }
    }
  }

  @media (max-width: 900px) {
    .popup {
      .findbar .findtab {
        .findbasic,
        .findadvanced {
          label {
            width: 45%;
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .find-view-popup {
      width: 90%;
      padding: 10px;

      .findbar .findtab {
        .findbasic,
        .findadvanced {
          display: flex;
          flex-direction: column;
          align-items: center;

          gap: 10px;

          label {
            width: 100% !important;
            margin: 0;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            input {
              width: 100%;
            }
          }
        }
      }

      .dsl-find-view {
        flex-direction: column !important;
      }
    }
  }
}

.non-popup {
  .findbar .findtab {
    .findbasic,
    .findadvanced {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      .gap-7;

      label {
        display: flex;
        width: 22%;
        flex-direction: column;

        gap: 10px;

        .select2-container-multi {
          overflow-x: auto;

          .select2-choices {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            overflow-x: auto;

            .select2-search-choice {
              max-width: fit-content;

              > div {
                min-width: max-content;
              }
            }
          }
        }

        :first-child {
          text-align: start;
          width: 100%;
        }

        > input {
          width: 100%;
        }

        > div {
          width: 100%;
          padding: 0px;
        }

        select {
          width: 100%;
        }
      }
    }
  }
}
