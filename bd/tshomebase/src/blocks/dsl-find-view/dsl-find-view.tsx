import React, { SyntheticEvent, useEffect, useRef, useState } from "react";
import { useToggle } from "usehooks-ts";
import _ from "lodash";
import { uuid } from "@utils/fx";
import "./dsl-find-view.less";
import filter from "@public/icons/filter";
import PublicIcon from "@public/icons";
import type { DSLDrawLinkMap } from "@typedefs/coffee/dsl";

export type DSLFindViewRef = {
	dds: {
		onPressEnter: () => void;
		$el: JQuery;
		values: (opts: { allow_date_range: boolean }) => Record<string, unknown>;
		clear: () => void;
		value_field: (
			field: string,
			value?: any,
			allow_null?: boolean,
			allow_hidden?: boolean,
			change?: boolean
		) => any;
	};
};

export type DSLFindOnFindCallback = (opts: { event: SyntheticEvent; filter: Record<string, unknown> }) => void;

export type DSLFindOnClearCallback = (opts: { event: SyntheticEvent }) => void;

export type DSLFindOnChangeCallback = (opts: { event: SyntheticEvent; filter: Record<string, unknown> }) => void;

export type DSLFindSetRefCallback = (ref: DSLFindViewRef) => void;

export interface DSLFindViewRendererProps extends Partial<DSLDrawLinkMap> {
	setFindRef?: DSLFindSetRefCallback;
	form: string;
	ignoreFindFilter?: boolean;
	initialFilters?: { [key: string]: any };
	filtersPresetFixed?: Record<string, unknown>;
	preset?: Record<string, unknown>;
	allowRadio?: boolean;
	onFind?: DSLFindOnFindCallback;
	onClear?: DSLFindOnClearCallback;
	onChange?: DSLFindOnChangeCallback;
	className?: string;
	children?: React.ReactNode;
}

const DSLFindTemplate = () => (
	<>
		<div className="findbar hide">
			<h3 className="formtitle hide" />
			<div className="findbarcontainer">
				<fieldset className="form-inline findtab">
					<span className="findbasic" />
					<span className="findadvanced hide" />
				</fieldset>
			</div>
		</div>
	</>
);

const DSLFindViewRenderer = React.memo(
	(props: DSLFindViewRendererProps) => {
		const { form, links, linkid, preset, allowRadio, onChange, children } = props;
		let dds = null;
		const domUID = useRef(uuid());
		useEffect(() => {
			const parent = {
				...props,
			};
			dds = new window.DSLDrawFind({
				...props,
				viewid: domUID.current,
				id: `${domUID.current}`,
				el: $("#" + domUID.current).find(".findbar"),
				parent,
			});
			if (
				$("#" + domUID.current)
					.find(".findbar")
					.hasClass("hide")
			) {
				dds.draw();
				props.setFindRef?.({ dds });
			}
			return;
		}, [form, links, linkid, preset, allowRadio]);
		return (
			<div id={domUID.current} className="dsl-find-view">
				<DSLFindTemplate />
				{children}
			</div>
		);
	},
	(prev, next) => {
		if (prev.form != next.form || prev.links != next.links || prev.linkid != next.linkid) {
			return false;
		}
		return true;
	}
);

export interface DSLFindViewProps extends DSLFindViewRendererProps {
	setRef: DSLFindSetRefCallback;
}

const DSLFindView = (props: DSLFindViewProps) => {
	const findRef = useRef<DSLFindViewRef | null>(null);
	const [advanced, toggleFilter] = useToggle(false);
	const [showAdvance, setShowAdvance] = useState(false);

	const onFindClick = (e: SyntheticEvent) => {
		props.onFind?.({ event: e, filter: findRef.current?.dds.values({ allow_date_range: true }) || {} });
	};

	const onClearClick = (e: SyntheticEvent) => {
		findRef.current?.dds.clear();
		props.onClear?.({ event: e });
	};

	const toggleAdvanceFilter = (e: SyntheticEvent) => {
		const adv = $(e.target).closest(".find-view").find(".findadvanced");
		if (!adv.length) return;
		toggleFilter();
		if (advanced) {
			adv.addClass("hide");
			return;
		}
		adv.removeClass("hide");
	};

	useEffect(() => {
		if (
			_.difference(window.DSL[props.form]?.view.find.basic, window.DSL[props.form].view.find.advanced).length > 0
		) {
			setShowAdvance(true);
		}
	}, []);

	return (
		<div className={`find-view ${props.className ? ` ${props.className}` : ""}`}>
			<DSLFindViewRenderer
				{...props}
				setFindRef={(r) => {
					findRef.current = r;
					props.setRef?.(r);
				}}
			>
				<div className="find-action-btn">
					{showAdvance && (
						<button
							className={`filter-icon-btn ${advanced ? "filter-flip-icon" : ""}`}
							onClick={toggleAdvanceFilter}
						>
							<div>
								<img src={filter.arrow} />
							</div>
						</button>
					)}
					<div className="action">
						<button className="filter-btn" onClick={onFindClick}>
							<div>
								<img src={PublicIcon.common.searchOutlinedIcon} />
							</div>
							<p>Find</p>
						</button>
						<button className="filter-btn" onClick={onClearClick}>
							<div>
								<img src={PublicIcon.common.cancelOutlinedIcon} />
							</div>
							<p>Clear</p>
						</button>
					</div>
				</div>
			</DSLFindViewRenderer>
		</div>
	);
};

const DSLFindViewMemo = React.memo(DSLFindView, (prev, next) => {
	if (prev.form != next.form || prev.links != next.links || prev.linkid != next.linkid) {
		return false;
	}
	return true;
});

DSLFindView.defaultProps = {
	form: "",
	link: "",
	links: [],
	linkid: {},
	preset: {},
	allowRadio: false,
};

export default DSLFindViewMemo;
