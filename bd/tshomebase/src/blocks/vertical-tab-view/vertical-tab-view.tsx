import { useState } from "react";
import type { SideNavItem } from "./types/vertical-tab-view";
import "./vertical-tab-view.less";
import { SubTab, Tab, Tabs } from "./components/tabs";
export interface VerticalTabViewProps {
	id: string;
	tabs: SideNavItem[];
	icon: string;
}

export function VerticalTabView(props: VerticalTabViewProps) {
	const { id, tabs, icon } = props;
	const [activeTab, setActiveTab] = useState(tabs[0].id);
	const [activeSubTab, setActiveSubTab] = useState<Record<string, string | undefined>>({
		[tabs[0].id]: tabs[0]?.subTabs?.[0]?.id,
	});
	const handleTabClick = (tabId: string, subId?: string) => {
		if (subId) {
			setActiveSubTab({
				...activeSubTab,
				[tabId]: subId,
			});
		}
		setActiveTab(tabId);
	};
	const activeTabItem = tabs.find((tab) => tab.id === activeTab);
	const activeSubTabComponents =
		activeTabItem?.subTabs?.filter((subTab) => subTab.id === activeSubTab[activeTab]) || [];

	return (
		<div className="vertical-tab-view-container">
			<div className="vertical-tab-view">
				<Tabs>
					{tabs.map((item) => (
						<Tab
							key={item.id}
							label={item.label}
							icon={item.icon}
							onClick={() => handleTabClick(item.id, item.subTabs?.[0]?.id)}
							active={activeTab === item.id}
							hasActiveSubTab={
								!!item.subTabs && activeTab === item.id && activeSubTab[item.id] !== undefined
							}
						>
							{item.subTabs?.map((subtab) => (
								<SubTab
									key={subtab.id}
									label={subtab.label}
									active={activeSubTab[item.id] === subtab.id}
									parentActive={activeTab === item.id}
									onClick={() => handleTabClick(item.id, subtab.id)}
								/>
							))}
						</Tab>
					))}
				</Tabs>
			</div>
			<div className="vertical-tab-view-content">
				{activeSubTabComponents.map((subTab) => {
					if (subTab.renderComponent) {
						const Component = subTab.renderComponent;
						return (
							<div
								key={subTab.id}
								className="vertical-tab-view-content-item"
								style={{
									height: "100%",
									display: activeSubTab[activeTab] === subTab.id ? undefined : "none",
								}}
							>
								<Component {...{ ...props, ...subTab.renderComponentProps }} />
							</div>
						);
					}
					return null;
				})}
				{tabs.map((tab) => {
					if (tab.renderComponent) {
						const Component = tab.renderComponent;
						return (
							<div
								key={tab.id}
								className="vertical-tab-view-content-item"
								style={{
									height: "100%",
									display: activeTab === tab.id ? undefined : "none",
								}}
							>
								<Component {...{ ...props, ...tab.renderComponentProps }} />
							</div>
						);
					}
					return null;
				})}
			</div>
		</div>
	);
}
