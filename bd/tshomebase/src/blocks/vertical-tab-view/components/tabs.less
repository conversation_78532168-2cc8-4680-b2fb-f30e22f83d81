.tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-standard);
    position: relative;
    flex-shrink: 0;

    .tab-item {
        padding: var(--spacing-standard) var(--spacing-standard) var(--spacing-standard) 0;
        cursor: pointer;
        border-radius: var(--radius-medium);
        transition: all 0.2s ease;
        position: relative;

        &:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.5);
        }

        &.active {
            background-color: #fff;
            padding: var(--spacing-standard);
            box-shadow: 0px 1px 2px -1px #0000000F,
                0px 0px 1px 0px #00000014;

            .tab-item-label {
                color: var(--color-tertiary);
                font-weight: var(--font-weight-bold);

                img{
                    transform: rotate(-20deg);
                    -webkit-transform-style: preserve-3d;
                    transform-style: preserve-3d;
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;

                    -webkit-transform: rotate(-20deg) translateZ(0);
                    transform: rotate(-20deg) translateZ(0);

                    image-rendering: -webkit-optimize-contrast;
                    image-rendering: crisp-edges;
                    filter: invert(54%) sepia(34%) saturate(476%) hue-rotate(171deg) brightness(93%) contrast(94%);

                    animation: rotateSwing 0.7s ease-in-out forwards;
                }
                i{
                    animation: rotateSwing 0.7s ease-in-out forwards;
                }
            }

            &::after {
                content: '';
                position: absolute;
                right: -9px;
                top: 0;
                height: 100%;
                width: 4px;
                background-color: var(--color-tertiary);
                border-radius: var(--radius-xsmall);
                transition: all 0.3s ease;
            }
        }

        .tab-item-label {
            font-family: 'Inter';
            font-weight: var(--font-weight-medium);
            line-height: var(--line-height-medium);
            font-size: var(--font-size-xsmall);
            color: var(--gray-700);
            display: flex;
            align-items: center;
            gap: 5px;

            i {
                font-size: var(--font-size-xsmall);
            }

            &:not(:has(.count)) i {
                margin-right: var(--spacing-xxsmall);
            }

            .tab-item-label-text {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .spacer {
                flex: 1;
            }

            .count {
                flex-shrink: 0;
                margin-left: var(--spacing-xsmall);
            }
        }

        &:has(.count) i {
            padding-inline: 5px;
        }

        &:not(.with-subtabs) {
            .tab-item-label {
                .tab-item-label-text {
                    flex: 0 1 auto;
                }
            }
        }

        &.with-subtabs {
            .subtabs {
                margin-left: var(--spacing-xxxxxxlarge);
                position: relative;
                left: var(--spacing-standard);

                &::before {
                    content: '';
                    position: absolute;
                    left: -27px;
                    top: 0;
                    bottom: 30px;
                    width: 2px;
                    background-color: #D7DAE0;
                }

                .subtab-item {
                    padding: var(--spacing-standard) var(--spacing-standard) var(--spacing-standard) 0;
                    cursor: pointer;
                    border-radius: var(--radius-medium);
                    position: relative;
                    margin: var(--spacing-xsmall) 0;
                    transition: all 0.2s ease;

                    &:first-child {
                        margin-top: 8px;
                      }

                    &::before {
                        content: '';
                        position: absolute;
                        left: -27px;
                        top: 12%;
                        width: var(--spacing-xxlarge);
                        height: var(--spacing-xxlarge);
                        border-left: 2px solid #D7DAE0;
                        border-bottom: 2px solid #D7DAE0;
                        border-bottom-left-radius: var(--radius-xxxlarge);
                    }

                    &:first-child::before {
                        top: 0;
                        height: 50%;
                        border-bottom-left-radius: var(--radius-xxxlarge);
                    }

                    &:last-child::before {
                        top: auto;
                        bottom: 18px;
                        height: 50%;
                    }

                    &.active {
                        background-color: #fff;
                        box-shadow: 0px -1px 2px 0px #00000038 inset,
                            0px 1px 2px 0px #FFFFFF1F inset;
                        padding-left: var(--spacing-standard);

                        .subtab-item-label {
                            color: var(--color-tertiary);
                            font-weight: var(--font-weight-bold);
                        }

                        &::after {
                            content: '';
                            position: absolute;
                            right: -9px;
                            top: 0;
                            height: 100%;
                            width: 4px;
                            background-color: var(--color-tertiary);
                            border-radius: var(--radius-xsmall);
                            transition: all 0.3s ease;
                        }
                    }

                    &:hover:not(.active) {
                        background-color: rgba(255, 255, 255, 0.5);
                    }

                    .subtab-item-label {
                        font-family: 'Inter';
                        font-size: var(--font-size-xsmall);
                        font-weight: var(--font-weight-medium);
                        color: var(--gray-700);
                        display: flex;
                        align-items: center;
                        overflow-wrap: anywhere;
                    }
                }
            }
        }
    }
}

@keyframes rotateSwing {
    0% {
      transform: rotate(-20deg);
    }
    25% {
      transform: rotate(20deg);
    }
    50% {
      transform: rotate(-20deg);
    }
    75% {
      transform: rotate(10deg);
    }
    100% {
      transform: rotate(-20deg);
    }
  }