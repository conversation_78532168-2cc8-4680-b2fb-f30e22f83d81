
.barContainer {
    display: none !important;
    background: rgba(0, 0, 0, 0.04);
    box-shadow: 0px 1px 2px 1px rgba(56, 56, 56, 0.08) inset;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    padding: 10px 16px;
    display: flex;
    flex-direction: row;
    gap: 10px;

    p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .heading,
    .orderStatusContainer,
    .refillInfo,
    .nexFillInfo,
    .flag-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .heading > p,
    .orderStatusContainer > p,
    .flag-container > p {
        font-size: 12px;
        font-weight: 700;
    }

    .heading > p {
        font-size: 16px;
        color: rgba(94, 99, 107, 1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .orderStatusContainer > p,
    .flag-container > p {
        color: white;
        padding: 2px 8px;
        border-radius: 16px;
    }

    .orderStatusContainer > p {
        background-color: rgba(148, 148, 145, 1);
    }

    .flag-container > p {
        background-color: rgba(131, 136, 148, 1);
    }

    .refillInfo::before,
    .nexFillInfo::before,
    .flag-container::before {
        font-family: "Font Awesome 6 Pro";
        font-weight: 600;
        font-size: 14px;
        color: rgba(155, 159, 168, 1);
    }

    .refillInfo::before {
        content: '\e5c0';
    }

    .nexFillInfo::before {
        content: '\f0d1';
    }

    .flag-container::before {
        content: '\f21e';
    }
}
