import React, { useState } from "react";

import { useFormFilters } from "@hooks/index";
import "./header-bar.less";
import { renderItemData } from "@utils/fx";

interface HeaderBarProps {
	order: Record<string, any>;
}

const HeaderBar: React.FC<HeaderBarProps> = (props) => {
	const { order } = props;

	if (!order) {
		return;
	}

	const getDaysUntil = (dateString: string): number => {
		if (!dateString) return 0;
		const inputDate = new Date(dateString);
		const today = new Date();

		today.setHours(0, 0, 0, 0);
		inputDate.setHours(0, 0, 0, 0);

		if (inputDate <= today) {
			return 0;
		}

		const diffInMs: number = inputDate - today;
		const daysDifference = diffInMs / (1000 * 60 * 60 * 24);

		return daysDifference;
	};

	const [refilldata, setRefillData] = useState(getDaysUntil(order?.next_fill_date));

	const SetTherapies = ({ therapies }: { therapies: string[] }) => {
		return (
			<>
				{therapies.map((therapy, index) => (
					<div className="flag-container" key={index}>
						<p>{therapy}</p>
					</div>
				))}
			</>
		);
	};

	return (
		<div className="barContainer">
			<div className="heading">
				<p>{renderItemData(order.auto_name)}</p>
			</div>
			<div className="orderStatusContainer">
				<p>5A-Pending-PA</p>
			</div>
			<div className="refillInfo">
				<p>{`${renderItemData(order?.next_fill_number)} fill remaining`}</p>
			</div>
			{refilldata != 0 && (
				<div className="nexFillInfo">
					<p>{`Next fill in ${getDaysUntil(order?.next_fill_date)} Days`}</p>
				</div>
			)}
			{order?.therapy_id_auto_name && order?.therapy_id_auto_name != null && (
				<SetTherapies therapies={order?.therapy_id_auto_name} />
			)}
		</div>
	);
};

export default HeaderBar;
