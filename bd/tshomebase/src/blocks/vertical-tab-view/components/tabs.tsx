import React from "react";
import "./tabs.less";
import { Tooltip } from "@mui/material";
import { getTooltipStyles } from "@utils/style-helper";

interface TabProps {
	label: string;
	count?: number;
	icon?: string | React.ReactNode;
	onClick: () => void;
	active?: boolean;
	hasActiveSubTab?: boolean;
	children?: React.ReactNode;
	className?: string;
}

interface SubTabProps {
	label: string;
	count?: number;
	icon?: string | React.ReactNode;
	active?: boolean;
	parentActive?: boolean;
	onClick?: () => void;
	className?: string;
}

export const SubTab: React.FC<SubTabProps> = ({
	label,
	count,
	icon,
	active,
	parentActive,
	onClick,
	className = "",
}) => {
	if (!parentActive) return null;

	return (
		<div
			className={`subtab-item ${active ? "active" : ""} ${parentActive ? "parent-active" : ""} ${className}`}
			onClick={(e) => {
				e.stopPropagation();
				onClick?.();
			}}
		>
			{/* <Tooltip arrow title={label} componentsProps={getTooltipStyles("primary")}> */}
			<div className="subtab-item-label">
				{icon && <i className={icon}></i>}
				<span>{label}</span>
				{typeof count === "number" && <span className="count"></span>}
			</div>
			{/* </Tooltip> */}
		</div>
	);
};

export const Tab: React.FC<TabProps> = ({
	label,
	count,
	icon,
	onClick,
	active,
	hasActiveSubTab,
	children,
	className = "",
}) => {
	const showArrowIcon = children ? (
		active || hasActiveSubTab ? (
			<i className="fa-light fa-angle-down"></i>
		) : (
			<i className="fa-light fa-angle-right"></i>
		)
	) : null;

	const showIcon = children ? (
		showArrowIcon
	) : icon ? (
		typeof icon === "string" ? (
			icon.includes("data:") ? (
				<img src={icon} />
			) : (
				<i className={icon}></i>
			)
		) : (
			icon
		)
	) : null;

	return (
		<div
			className={`tab-item ${active && !hasActiveSubTab ? "active" : ""} ${
				children ? "with-subtabs" : ""
			} ${className}`}
			onClick={onClick}
		>
			{/* <Tooltip arrow title={label} componentsProps={getTooltipStyles("primary")}> */}
			<div className="tab-item-label">
				{showIcon}
				<span className="tab-item-label-text">{label}</span>
				{!children && typeof count === "number" && <span className="count"></span>}
				{children && (
					<>
						<span className="spacer"></span>
						{typeof count === "number" && <span className="count"></span>}
					</>
				)}
			</div>
			{/* </Tooltip> */}
			{children && <div className="subtabs">{children}</div>}
		</div>
	);
};

export const Tabs: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = "" }) => {
	return <div className={`tabs ${className}`}>{children}</div>;
};
