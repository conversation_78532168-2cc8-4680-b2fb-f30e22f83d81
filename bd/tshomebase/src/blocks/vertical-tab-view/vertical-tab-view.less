@import "../../styles/base/less/colors.less";
@import "../../styles/base/less/dimensions.less";
@import "../../styles/base/less/fonts.less";

.vertical-tab-view-container {
    border: 2px solid #ffffff;
    background-color: #ffffffb8;
    width: 100%;
    backdrop-filter: blur(25px);
    border-radius: var(--radius-medium);
    display: flex;
    .vertical-tab-view {
        background-color: transparent;
        width: 250px;
        padding: var(--spacing-xxxlarge) var(--spacing-xxxlarge);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-y: auto;
        flex-shrink: 0;

        &::-webkit-scrollbar {
            width: var(--spacing-xsmall);
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: #D7DAE0;
            border-radius: var(--radius-xsmall);
        }
    }
    .vertical-tab-view-content {
        width: 100%;
        padding: var(--spacing-xxxlarge) 0px;
        overflow-y: auto;
    }
}