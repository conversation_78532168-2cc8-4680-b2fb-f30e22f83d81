import React, { FC, FunctionComponent, useEffect } from 'react';
import { FormTabController, useFormTabController } from '@hooks/form-tab-controller';
import { TabData, TabList } from '@blocks/tab-list/tab-list';
import { RoutedComponentProps } from '@typedefs/routed-component';
import DSLCardView, { TabModes } from '@blocks/dsl-card-view/dsl-card-view';
import "./generic-snapshot.less";
import { useNavigation } from '@core/navigation';
import { toPresentableLabel } from '@utils/fx';

interface GenericSnapshotProps extends RoutedComponentProps {
    tabData: TabData;
    mode: TabModes;
    form: string;
    tabRenderComponentProps: {
        defaultOpenTab: string,
        defaultTabs: TabData[]
    };
}
export interface BaseSnapRenderComponentProps extends RoutedComponentProps, TabData, FormTabController {
    [key: string]: unknown;
}
export const GenericSnapshot: React.FC<GenericSnapshotProps> = (props) => {
    const { tabRenderComponentProps, tabData } = props;
    const { defaultTabs, defaultOpenTab } = tabRenderComponentProps;
    const [openTabs, activeTabId, controller, dslRefs, updateHash] = useFormTabController(defaultTabs, defaultOpenTab);
    const nav = useNavigation(props, '/');


    useEffect(() => {
        if (!props.navPath?.length) {
            return;
        }
        const r = props.navPath;
        if (r.length == 2 && r[1] == "add") {
            controller.setActiveTabId(defaultOpenTab);
            return;
        }
        const id = r.splice(0, 1).toString();
        const mode = r.splice(0, 1).toString();
        const form = r.splice(0, 1).toString() || props.form;
        let label = form == props?.form ? toPresentableLabel(mode || "edit") : "";
        if (mode == 'snap' || !mode) {
            controller.setActiveTabId(defaultOpenTab);
            return;
        }
    }, [nav.globalNav.lrct]);

    return (
        <div className="generic-snap">
            <TabList
                activeTabId={activeTabId}
                openTabs={openTabs}
                optionsProps={{
                    enabled: false
                }}
                addProps={{
                    enabled: false
                }}
                tabCanClose={true}
                draggable={false}
                onTabClick={(tab: TabData) => {
                    controller.setActiveTabId(tab.tkey);
                }}
                styles={{
                    tabListStyle: "snap-tab-list",
                }}
                onTabClose={(tab: TabData) => {
                    controller.onTabCancel(tab);
                }}
            />
            {openTabs.map((tab) => {
                const { tkey, id } = tab;
                const RenderComponent = tab.renderComponent as FC<any>;
                return (
                    <div key={tkey} className='dsl-container' style={{ display: activeTabId == tkey ? undefined : "none" }}>
                        {!tab.renderComponent ?
                            <DSLCardView
                                isActive={activeTabId == tkey}
                                isParentActive={props.isActive}
                                routeAppend={"/" + tab.form}
                                tabData={tab}
                                key={id}
                                card={(tab.mode || "read") as TabModes}
                                xid={id}
                                form={tab.form}
                                record={["add", "addfill"].includes(tab.mode) ? undefined : id}
                                ddRef={(ref) => {
                                    controller.setDSLFormRef(tab, ref);
                                }}
                                {...controller}
                                {...tab.componentProps}
                                {...nav}
                            />
                            :
                            <RenderComponent
                                {...props}
                                key={tkey + updateHash}
                                isActive={activeTabId == tkey}
                                isParentActive={props.isActive}
                                tabData={tab as TabData}
                                {...tab.componentProps}
                                {...controller}
                                {...tabData}
                                {...nav}
                            />
                        }
                    </div>
                );
            })}
        </div>
    );
};
