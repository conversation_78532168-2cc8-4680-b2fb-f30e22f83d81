@import (reference) "./components/navbar/navbar.vars.less";
@import "@mescius/activereportsjs/styles/ar-js-ui.css";
@import "@mescius/activereportsjs/styles/ar-js-designer.css";
@import "@mescius/activereportsjs/styles/ar-js-viewer.css";
@import (reference) "./components/nav-header/header.vars.less";
@import (reference) "./less/style/main.less";
@import (reference) "./styles/base/less/index.less";

#viewer-host {
	width: 100%;
	height: 85vh;
}

/* Set the width and height of the inner divs based on the parent div with "ar-reportitemplace" class */
.app {
	display: flex;
	flex-direction: row;
	height: calc(100vh - @nav-header-height);

	&.has-tabs {
		height: @app-usage-height;
	}

	width: 100vw;
	font-family: Inter;
	padding: var(--spacing-xxxlarge);
	padding-top: var(--spacing-large);
	justify-content: center;

	div {
		min-height: 0;
		min-width: 0;
	}

	p {
		margin: 0px;
		font-family: Inter;
	}
}

.module-area {
	display: flex;
	position: relative;
	width: 100vw;
	height: 100%;
}

.windows {
	* {
		.default-scroll;
	}
}

.module-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
}

:root {
	--flyout-height: 45px;
	--reduce-height: 45px;
	--header-min-height: 34px;
	--header-max-height: 34px;
	--flyout-top-tmpp: 172px;
	--header-font-size: 20px;
}

.compact {
	--flyout-height: 35px;
	--reduce-height: 15px;
	--header-min-height: 20px;
	--header-max-height: 20px;
	--flyout-top-tmpp: 145px;
	--header-font-size: 18px;
}

.ui-sortable-helper {
	display: table;
}

.Mui-selected {
	color: #9974ce !important;
}

svg [data-testid="AddOutlinedIcon"]:last-child {
	display: none;
}
