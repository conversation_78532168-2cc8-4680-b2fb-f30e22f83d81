import React, { createContext, useRef } from "react";
interface TabContextProps {
	children: React.ReactNode;
}
export interface TabHistory {
	open: {
		patient: Record<string, any>;
		inventory: Record<string, any>;
	};
	close: {
		patient: Record<string, any>;
		inventory: Record<string, any>;
	};
}

export type HistoryModule = "patient" | "inventory";

export const DEFAULT_TAB_HISTORY = {
	open: {
		patient: {},
		inventory: {},
	},
	close: {
		patient: {},
		inventory: {},
	},
}


export const OpenTabContext = createContext(DEFAULT_TAB_HISTORY);
export const TabContextProvider: React.FC<TabContextProps> = ({ children }) => {
	const tabHistory = useRef<TabHistory>(DEFAULT_TAB_HISTORY);
	return (
		<OpenTabContext.Provider value={tabHistory.current}>
			{children}
		</OpenTabContext.Provider>
	);
};
