import React, { createContext, useRef, useEffect, useState } from "react";
import _ from "lodash";
import { fetchFormFilters, FetchFormResponse, IFormData } from "@hooks/form-data";
import { IFilterOpts } from "@utils/dsl-fx";

interface SharedDataContextProps {
	children: React.ReactNode;
}

export type ISharedDataValue = {
	data: IFormData[];
	state: "loading" | "success" | "error";
};

const defaultData = {
	queues: {
		data: [],
		state: "loading",
	} as ISharedDataValue,
};

export type ISharedDataKey = keyof typeof defaultData;

const dataFetchMap: Record<
	ISharedDataKey,
	{
		form: string;
		params: IFilterOpts;
	}
> = {
	queues: {
		form: "workflow",
		params: {
			filter: {
				type: "Queue",
				active: "Yes",
			},
			sortDirection: "asc",
			sortProperty: "sort_order",
			limit: 1000,
		},
	},
};

export type ISharedData = Record<ISharedDataKey, ISharedDataValue>;

export const SharedDataStore = createContext<ISharedData>(defaultData);

export const SharedDataContextProvider: React.FC<SharedDataContextProps> = ({ children }) => {
	const [sharedData, setSharedData] = useState<ISharedData>(defaultData);

	const fetchData = async () => {
		const dataKeys = Object.keys(defaultData) as ISharedDataKey[];
		const tick: Promise<FetchFormResponse>[] = [];
		dataKeys.forEach((key) => {
			const { form, params } = dataFetchMap[key];
			tick.push(
				fetchFormFilters(form, {
					...params,
					limit: 100000,
				})
			);
		});
		const resolved = await Promise.allSettled(tick);
		const d: ISharedData = _.cloneDeep(defaultData);
		resolved.forEach((r, i) => {
			if (r.status != "fulfilled" || !r.value.success) {
				d[dataKeys[i] as ISharedDataKey].state = "error";
				return;
			}
			d[dataKeys[i] as ISharedDataKey].data = r.value.data satisfies IFormData[];
			d[dataKeys[i] as ISharedDataKey].state = "success";
		});
		setSharedData(d);
	};
	useEffect(() => {
		fetchData().catch((error) => {
			console.error("SharedDataContext Provider", error);
		});
	}, []);

	return <SharedDataStore.Provider value={sharedData}>{children}</SharedDataStore.Provider>;
};
