import React, { createContext, useRef, useEffect, useState } from "react";
import { request } from "@core/request/request";
import _ from "lodash";
import { useLocalStorage } from "usehooks-ts";
import LocalStore from "@enum/local-store";
import ViewMode from "@enum/view-mode";
import { ColumnState } from "ag-grid-enterprise";
interface PreferenceContextProps {
	children: React.ReactNode;
}
type IPreferenceData = [
	boolean,
	{
		id: number | null;
		view_mode: ViewMode;
		search_shortcut_key: string;
		document_printer: string;
		label_printer: string;
		subform_printers: any[];
		default_prefill_site_id: string;
		default_prefill_site_id_auto_name: string;
		grid_configurations: null | Record<
			string,
			{
				columnState?: ColumnState[];
			}
		>;
	},
	(k: string, v: any, site_id?: string) => void,
	() => void
];

const DEFAULT_PREFERENCE = {
	id: null,
	view_mode: ViewMode.NORMAL,
	search_shortcut_key: "F1",
	document_printer: "",
	label_printer: "",
	subform_printers: [],
	default_prefill_site_id: "",
	default_prefill_site_id_auto_name: "",
	grid_configurations: null,
};

export const PreferenceSettingContext = createContext([
	false,
	DEFAULT_PREFERENCE,
	(k: string, v: any, site_id?: string) => {
		console.info("PreferenceContextProvider: Not Loaded");
	},
	() => {
		console.info("PreferenceContextProvider: Not Loaded");
	},
] as IPreferenceData);

export const getUserPreference = () =>
	new Promise<IPreferenceData[1]>((resolve, reject) => {
		request({
			url: `/my/preference/`,
			method: "GET",
		})
			.then((resp) => {
				resolve(resp.data as IPreferenceData[1]);
			})
			.catch((err) => {
				reject(err);
			});
	});

export const PreferenceContextProvider: React.FC<PreferenceContextProps> = ({ children }) => {
	const [pref, setPref] = useState(DEFAULT_PREFERENCE);
	const [vmode, setViewMode] = useLocalStorage(LocalStore.VIEW_MODE, ViewMode.NORMAL);
	const [labelPrinter, setLabelPrinter] = useLocalStorage(LocalStore.LABEL_PRINTER, "");
	const [documentPrinter, setDocumentPrinter] = useLocalStorage(LocalStore.DOCUMENT_PRINTER, "");
	const [isLoading, setIsLoading] = useState(false);

	const updatePref = (k: string, v: any, site_id?: string) => {
		setIsLoading(true);

		setPref((p) => ({
			...p,
			[k]: v,
		}));
		request({
			url: `/my/preference/`,
			method: "PUT",
			data: {
				[k]: v,
				site_id: site_id,
			},
		})
			.then(() => {
				setPref((p) => ({
					...p,
					[k]: v,
				}));
				refreshPref();
			})
			.catch((err) => {
				console.error("PreferenceContextProvider: Error updating preferences:", err);
			})
			.finally(() => {
				setIsLoading(false);
			});
	};

	const refreshPref = () => {
		request({
			url: `/my/preference/`,
			method: "GET",
		})
			.then((resp) => {
				if (resp.data) {
					setPref((p) => ({ ...p, ...resp.data }));
					window.UserPreference = resp.data;
				}
			})
			.catch((err) => {
				console.error("PreferenceContextProvider: Error fetching preference data:", err);
			});
	};

	useEffect(() => {
		refreshPref();
	}, []);

	useEffect(() => {
		if (!pref.view_mode) {
			pref.view_mode == DEFAULT_PREFERENCE.view_mode;
		}
		if (pref.view_mode !== vmode) {
			setViewMode(pref.view_mode as ViewMode);
		}
	}, [pref.view_mode]);

	useEffect(() => {
		if (pref.label_printer !== labelPrinter) {
			setLabelPrinter(pref.label_printer);
		}
		if (pref.document_printer !== documentPrinter) {
			setDocumentPrinter(pref.document_printer);
		}
	}, [pref.label_printer, pref.document_printer]);

	return (
		<PreferenceSettingContext.Provider value={[isLoading, pref, updatePref, refreshPref]}>
			{children}
		</PreferenceSettingContext.Provider>
	);
};
