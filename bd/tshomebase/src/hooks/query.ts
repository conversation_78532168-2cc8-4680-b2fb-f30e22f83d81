import { request } from "@core/request";
import { IFormData, IFormFiltersResponse, useFormFilters } from "@hooks/index";
import { DSLForm } from "@typedefs/coffee/dsl";
import { IFilterOpts } from "@utils/dsl-fx";
import { useEffect, useMemo, useRef, useState } from "react";
export type GridSumInfo = {
	sumColumn: string;
	sumColumnLabel: string;
	sumFormat: string;
};

type QueryFormConfig = {
	form: string;
	label: string;
	gridSourceOverrideURL: string;
	state: "loading" | "failed" | "success";
	sumInfo: GridSumInfo;
	customColumns: string[];
	customFindFields: string[];
};

const getCustomQueryFieldConfig = (dsl: DSLForm) => {
	const customColumns = dsl.view.grid.fields.filter((field) => !dsl.view.grid.hide_columns.includes(field));
	const customFindFields = [...(dsl.view.find.basic || []), ...(dsl.view.find.advanced || [])];
	return {
		customColumns,
		customFindFields,
	};
};

export const useQueryForm = (code: string, parameters?: string, label?: string): QueryFormConfig => {
	const formName = `__query_module_${code}`;
	const [fd] = useFormFilters("query", { filter: { code: code } });

	const initial: QueryFormConfig = {
		state: "loading",
		form: formName,
		label: "",
		gridSourceOverrideURL: "",
		sumInfo: {
			sumColumn: "",
			sumColumnLabel: "",
			sumFormat: "",
		},
		customColumns: [],
		customFindFields: [],
	};

	const state: QueryFormConfig = useMemo(() => {
		if (fd.loading) return initial;
		if (fd.failed || fd.data.length == 0) {
			initial.state = "failed";
			return initial;
		}
		const query = fd.data[0];

		if (!window.DSL[formName]) {
			window.DSL[formName] = query.dsl_struct;
		}
		return {
			state: "success",
			form: formName,
			label: label || (query.name as string) || "",
			sumInfo: {
				sumColumn: query.sum_column,
				sumColumnLabel: query.sum_column_label,
				sumFormat: query.sum_format,
			},
			...getCustomQueryFieldConfig(query.dsl_struct),
			gridSourceOverrideURL: `/query/${code}/${parameters ? `?${parameters}` : ""}`,
		};
	}, [fd.data, fd.failed, fd.loading]);

	return state;
};

export const useQueryData = <T = IFormData>(
	queryCode: string,
	params: Array<string | number>,
	options?: IFilterOpts,
	suppressInitialRefresh?: boolean
): [
	IFormFiltersResponse<T>,
	(paramsOverride?: Array<string | number>, optionsOverride?: IFilterOpts) => void,
	() => void
] => {
	const paramsRef = useRef<Array<string | number>>(params);
	const optionsRef = useRef<IFilterOpts | undefined>(options);
	const [queryData, setQueryData] = useState({
		loading: true,
		failed: false,
		data: [],
	});

	const refresh = (paramsOverride?: Array<string | number>, optionsOverride?: IFilterOpts) => {
		if (paramsOverride) {
			paramsRef.current = paramsOverride;
		}
		if (optionsOverride) {
			optionsRef.current = optionsOverride;
		}
		const qp: string[] = [];
		for (let i = 0; i < paramsRef.current.length; i++) {
			qp.push(`${i + 1}=${paramsRef.current[i]}`);
		}
		if (optionsRef.current?.extraParams) {
			qp.push(optionsRef.current.extraParams);
		}
		const fs = qp.join("&");
		request({
			url: `/query/${queryCode}/${fs ? `?${fs}` : ""}`,
			pool: optionsRef.current?.pool || false,
		})
			.then((resp) => {
				setQueryData({ ...queryData, loading: false, failed: false, data: resp.data });
			})
			.catch((error) => {
				setQueryData({ ...queryData, loading: false, failed: true });
			});
	};

	const reset = () => {
		setQueryData({ loading: true, failed: false, data: [] });
	};

	useEffect(() => {
		if (!suppressInitialRefresh) {
			refresh();
		}
	}, []);

	return [queryData, refresh, reset];
};
