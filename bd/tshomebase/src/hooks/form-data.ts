import { SetStateAction, useEffect, useState } from "react";
import { request } from "@core/request";
import type { IFilterOpts } from "@utils/dsl-fx";
import { generateDSLFormQueryParams } from "@utils/dsl-fx";
import { ServerActionButtons } from "@blocks/dsl-card-view";
import _ from "lodash";

interface IFormDataResponse {
	loading: boolean;
	failed: boolean;
	data: IFormData;
}

export interface IFormFiltersResponse<T = IFormData> {
	loading: boolean;
	failed: boolean;
	data: T[];
}

export interface IFormData {
	id: string | number;
	created_on: string;
	created_by: string | number;
	created_by_auto_name: string | null;
	updated_on: string;
	updated_by: string | number;
	auto_name: string;
	updated_by_auto_name: string | null;
	_meta: {
		source: string;
		actions?: ServerActionButtons[];
		warning?: string;
	};
	[key: string]: any;
}

export interface FetchFormResponse {
	data: IFormData[];
	success: boolean;
	error?: unknown;
}

export interface FetchFormDataResponse extends Omit<FetchFormResponse, "data"> {
	data: IFormData;
}
export interface FetchFormsDataResponse extends Omit<FetchFormResponse, "data"> {
	data: IFormData[];
}

export const getFormDataWithServerActions = (form: string, id: string | number) => {
	return new Promise<
		FetchFormDataResponse & {
			error?: any;
			serverActions: ServerActionButtons[];
		}
	>((resolve, reject) => {
		fetchFormData(form, id, true, "all")
			.then((resp) => {
				if (resp.success) {
					resolve({
						success: true,
						data: resp.data || {},
						serverActions: resp.data?._meta?.actions || [],
					});
				} else {
					resolve({
						success: false,
						data: resp.data || {},
						serverActions: [],
					});
				}
			})
			.catch((error) => {
				console.error(error);
				reject({
					success: false,
					error: error,
					data: {},
					serverActions: [],
				});
			});
	});
};

export const fetchFormData = (form: string, id: string | number, pool?: boolean, fields?: "min" | "list" | "all") =>
	new Promise<FetchFormDataResponse>((resolve, reject) => {
		let qp = fields ? `?fields=${fields}` : "";
		if (fields == "all") {
			qp += "&get_actions=true";
		}

		request({
			url: `/form/${form}/${id}${qp}`,
			pool: pool || false,
		})
			.then((resp) => {
				resolve({
					success: true,
					data: resp.data?.length ? resp.data[0] : {},
				});
			})
			.catch((error) => {
				reject({
					success: false,
					data: error,
				});
			});
	});

export const fetchFilteredFormsData = (
	form: string,
	filters: string[],
	limit: number = 100,
	sort: string = "-id",
	pool?: boolean
) =>
	new Promise<FetchFormsDataResponse>((resolve, reject) => {
		request({
			url: `/form/${form}?filter=${filters.join("&filter=")}&limit=${limit}&sort=${sort}`,
			pool: pool || false,
		})
			.then((resp) => {
				resolve({
					success: true,
					data: resp.data?.length ? resp.data : [],
				});
			})
			.catch((error) => {
				reject({
					success: false,
					data: error,
				});
			});
	});

export const useFormData = (
	form: string,
	id: string | number,
	pool?: boolean,
	manualFetch?: boolean
): [IFormDataResponse, () => void, (data: object, refresh?: boolean) => Promise<unknown>, (data: object) => void] => {
	const [formData, setFormData] = useState({
		loading: true,
		failed: false,
		data: {} as IFormData,
	});
	const refresh = () => {
		setFormData({ ...formData, loading: true, failed: false });
		request({
			url: `/form/${form}/${id}`,
			pool: pool || false,
		})
			.then((resp) => {
				setFormData({
					...formData,
					loading: false,
					failed: false,
					data: resp?.data?.length ? resp.data[0] : {},
				});
			})
			.catch(() => {
				setFormData({ ...formData, loading: false, failed: true });
			});
	};

	const update = (data: object, ref = false) => {
		setFormData((fd) => {
			return { ...fd, data: { ...fd.data, ...data } };
		});
		return request({
			url: `/form/${form}/${id}/`,
			method: "PUT",
			data: data,
		})
			.then((resp) => {
				if (ref) {
					const d = _.head(resp.data) || {};
					setFormData((fd) => {
						return { ...fd, data: { ...fd.data, ...d } };
					});
				}
			})
			.catch((error) => {
				console.log(error);
			});
	};
	const mutate = (data: object) => {
		setFormData({ ...formData, data: { ...formData.data, ...data } });
	};
	useEffect(() => {
		if (!manualFetch) refresh();
	}, []);

	return [formData, refresh, update, mutate];
};

export const fetchFormFilters = (form: string, options: IFilterOpts) =>
	new Promise<FetchFormResponse>((resolve, reject) => {
		const qp = generateDSLFormQueryParams(form, options);
		if (!window.DSL[form]) {
			console.error("fetchFormFilters: form not found", form);
			reject({
				success: false,
				data: [],
				error: "form not found",
			});
			return;
		}
		request({
			url: `/form/${form}/?${qp.join("&")}${options.extraParams ? "&" + options.extraParams : ""}`,
			pool: options.pool || false,
		})
			.then((resp) => {
				const ro = {
					success: true,
					data: resp.data,
				};
				if (options.fields == "count") ro.data = resp.data?.[0];
				resolve(ro);
			})
			.catch((error) => {
				reject({
					success: false,
					data: [],
					error: error,
				});
			});
	});

export const useFormFilters = (
	form: string,
	options: IFilterOpts
): [IFormFiltersResponse, () => void, (data: object) => void] => {
	const [formData, setFormData] = useState({
		loading: true,
		failed: false,
		data: [],
	});

	const updateFilters = (data: object) => {
		const filteredData = Object.fromEntries(Object.entries(data).filter(([key, value]) => value));
		options.extraParams = filteredData.__params || "";
		delete filteredData.__params;
		options.filter = filteredData;
		refresh();
	};
	const refresh = () => {
		setFormData({ ...formData, loading: true, failed: false });
		fetchFormFilters(form, options)
			.then((resp) => {
				setFormData({ ...formData, loading: false, failed: false, data: resp?.data });
			})
			.catch(() => {
				setFormData({ ...formData, loading: false, failed: true });
			});
	};
	useEffect(() => {
		refresh();
	}, []);
	return [formData, refresh, updateFilters];
};

export const useFormsCounts = (
	forms: string[],
	options: IFilterOpts
): [Record<string, number>, (form: string) => void, React.Dispatch<SetStateAction<Record<string, number>>>] => {
	options.fields = "count";
	options.limit = 9999999999;
	const [count, setCount] = useState<Record<string, number>>({});

	const fetchCounts = (fnames: string[]) => {
		fnames.forEach((form) => {
			fetchFormCount(form, options)
				.then((resp) => {
					setCount((prev) => ({ ...prev, [form]: resp.count }));
				})
				.catch(() => {
					setCount((prev) => ({ ...prev, [form]: 0 }));
				});
		});
	};

	useEffect(() => {
		fetchCounts(forms);
	}, []);

	const refreshFormCount = (form: string) => {
		fetchCounts([form]);
	};
	return [count, refreshFormCount, setCount];
};

export const fetchFormCount = (form: string, options: IFilterOpts) =>
	new Promise<{
		success: boolean;
		count: number;
	}>((resolve, reject) => {
		options.fields = "count";
		options.limit = 9999999999;
		options.pool = true;
		fetchFormFilters(form, options)
			.then((resp) => {
				if (!Array.isArray(resp.data)) {
					let c = parseInt(resp.data?.count);
					if (isNaN(c)) {
						c = 0;
					}
					resolve({
						success: true,
						count: c,
					});
				} else {
					console.error("useFormCount: Invalid response data");
					reject({
						success: false,
						count: 0,
					});
				}
			})
			.catch(() => {
				reject({
					success: false,
					count: 0,
				});
			});
	});

export const useFormCount = (form: string, options: IFilterOpts): [number, () => void] => {
	const [count, setCount] = useState(0);
	const refresh = () => {
		fetchFormCount(form, options)
			.then((resp) => {
				setCount(resp.count);
			})
			.catch(() => {
				setCount(0);
			});
	};
	useEffect(() => {
		refresh();
	}, []);
	return [count, refresh];
};
