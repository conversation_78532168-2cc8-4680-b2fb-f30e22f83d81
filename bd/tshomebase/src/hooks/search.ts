// searchSuggestionHooks.ts
import { useCallback, useEffect, useRef } from "react";
import _, { words } from "lodash";
import { sendRequest, getUrl } from "@utils/search";
const getSearchResultCount = (results: Record<string, any>) => {
    return Object.values(results).reduce((total, sr) => total + sr.length, 0);
}
export const useDebouncedSearch = (setSearchResult: React.Dispatch<React.SetStateAction<Record<string, any>>>, setSearchLoading: React.Dispatch<React.SetStateAction<boolean>>, setFilterKey: React.Dispatch<React.SetStateAction<string>>) => {
    const latestRequestUrl = useRef<string | null>(null);
    return useCallback(
        _.debounce(async (words: string) => {
            const url = `/search?limit=20&keywords=${words}`;
            latestRequestUrl.current = url;
            if (latestRequestUrl.current === url) {
                setSearchLoading(true);
                const result = await sendRequest(url);
                setSearchResult(result);
                if (!getSearchResultCount(result)) {
                    setFilterKey('Patient');
                }
                Object.keys(result).some((key) => {
                    if (result[key]?.length) {
                        setFilterKey(key);
                        return true;
                    }
                });
                setSearchLoading(false);
            }
        }, 200),
        [setSearchResult, setSearchLoading, words]
    );
};
const getItemAtIndex = (index: number, searchResults: string | any[], tabHistory: Record<string, any>) => {
    // Check if index is within searchResults
    if (index < searchResults.length) {
        return searchResults[index];
    }
    const openCount = tabHistory.open.patient.length + searchResults.length
    // Check if index is within tabHistory.open
    if (index < openCount) {
        return tabHistory.open.patient[index - searchResults.length];
    }

    // Check if index is within tabHistory.close
    return tabHistory.close.patient[index - openCount];
};
export const useKeyDownHandler = ({
    filterKey,
    searchResult,
    selectedSuggestion,
    clickHandler,
    setSelectedSuggestion,
    blurHandler,
    highlightedRef,
    patientUri,
    totalCount = 0,
    tabHistory = {},
}: {
    filterKey: string;
    searchResult: Record<string, any>;
    selectedSuggestion: number;
    clickHandler: (url: string) => void;
    setSelectedSuggestion: React.Dispatch<React.SetStateAction<number>>;
    blurHandler: () => void;
    highlightedRef: React.RefObject<HTMLDivElement>;
    patientUri: string;
    totalCount: number;
    tabHistory: Record<string, any>;
}) => {
    const suggestionSectionKeyDownHandler = useCallback((e: KeyboardEvent) => {
        const scrollOption: ScrollIntoViewOptions = {
            behavior: "smooth",
            block: "start",
        }
        if (e.key === "ArrowDown") {
            e.preventDefault();
            highlightedRef.current?.scrollIntoView(scrollOption);
            setSelectedSuggestion((prev) => (prev + 1) % totalCount);
        } else if (e.key === "ArrowUp") {
            e.preventDefault();
            highlightedRef.current?.scrollIntoView(scrollOption);
            setSelectedSuggestion((prev) => (prev - 1 + totalCount) % (totalCount));
        } else if (e.key === "Escape") {
            blurHandler();
        } else if (e.key === "Enter") {
            // const item = searchResult[filterKey]?.[selectedSuggestion];
            const item = getItemAtIndex(selectedSuggestion, searchResult[filterKey], tabHistory);
            if (item) {
                const url = getUrl(item.id, item, patientUri, filterKey, "snap");
                if (url) {
                    clickHandler(url);
                    blurHandler();
                }
            }
        }
    }, [filterKey, searchResult, selectedSuggestion, clickHandler, setSelectedSuggestion, blurHandler, highlightedRef, patientUri]);

    useEffect(() => {
        window.addEventListener("keydown", suggestionSectionKeyDownHandler);

        return () => {
            window.removeEventListener("keydown", suggestionSectionKeyDownHandler);
        };
    }, [suggestionSectionKeyDownHandler]);
};

export const useUpdateScroll = (highlightedRef: React.RefObject<HTMLDivElement>, scrollerRef: React.RefObject<HTMLDivElement>) => {
    useEffect(() => {
        if (scrollerRef.current && highlightedRef.current) {
            scrollerRef.current.scrollTop = highlightedRef.current.offsetTop - 60;
        }
    }, [highlightedRef.current]);
};
