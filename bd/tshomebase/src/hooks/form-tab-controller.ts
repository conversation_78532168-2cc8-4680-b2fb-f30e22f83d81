import { useRef, useState } from "react";
import { TabData, getTabKey } from "@blocks/tab-list/tab-list";
import { useUpdateHash } from "./update-hash";
import {
	CARD_MODES,
	DSLCardOnArchiveCallBack,
	DSLCardOnCancelCallBack,
	DSLCardOnChangeModeCallback,
	DSLCardOnSaveCallBack,
	DSLCardOnUnArchiveCallBack,
	DSLCardViewRef,
	TabModes,
} from "@blocks/dsl-card-view/dsl-card-view";
import { DSLGridOnRowEventCallBack, DSLGridViewRef } from "@blocks/dsl-grid-view/dsl-grid-view";

export type OpenTabCallback = (
	id: string | number,
	label: string,
	mode: string,
	form: string,
	componentProps: Record<string, unknown>,
	renderComponent?: unknown,
	extraKV?: Record<string, unknown>
) => void;

export type DSLCloseTabCallback = (id: string, tab: TabData, ref?: DSLCardViewRef) => void;

export interface FormTabController {
	onSaved: DSLCardOnSaveCallBack;
	onArchive: DSLCardOnArchiveCallBack;
	onUnArchive: DSLCardOnUnArchiveCallBack;
	onCancel: DSLCardOnCancelCallBack;
	rowClicked: DSLGridOnRowEventCallBack;
	openTab: OpenTabCallback;
	setActiveTabId: (tabId: string) => void;
	changeMode: DSLCardOnChangeModeCallback;
	setDSLFormRef: (tab: TabData, ref: DSLCardViewRef | undefined) => string;
	onTabCancel: (tab: TabData) => void;
	setOpenTabs: React.Dispatch<React.SetStateAction<TabData[]>>;
	getRefByKey: (tkey: string) => unknown;
}

export const useFormTabController = (
	defaultTabs: TabData[],
	defaultTab: string
): [TabData[], string, FormTabController, Record<string, DSLCardViewRef | undefined>, string] => {
	const [openTabs, setOpenTabs] = useState<TabData[]>(defaultTabs);
	const [activeTabId, setActiveTabId] = useState(defaultTab);
	const [updateHash, setUpdateHash] = useUpdateHash();
	const dslRefs = useRef({}) as unknown as Record<string, DSLCardViewRef | undefined>;

	const openTab: OpenTabCallback = async (id, label, mode, form, componentProps, renderComponent, extraKV) => {
		id = id + "";
		const gridRef = componentProps?.gridRef as DSLGridViewRef;
		extraKV = extraKV || {};
		const tkey = getTabKey(id, form);
		const nt: TabData = { ...extraKV, tkey: tkey, id: id, label: label, mode: mode, componentProps, form, gridRef };
		if (renderComponent) {
			nt.renderComponent = renderComponent;
			nt.renderComponentProps = componentProps;
		}
		setOpenTabs((p) => {
			p = Array.from(p);
			const tindex = p.findIndex((t) => t.tkey == tkey);
			if (tindex > -1) {
				if (CARD_MODES.includes(nt.mode)) {
					if (p[tindex].mode != nt.mode) {
						p[tindex].mode = nt.mode;
						p[tindex].forceRender = window.generateUUID();
						return [...p];
					} else {
						return p;
					}
				} else {
					return p;
				}
			}
			p = [...p, nt];
			return p;
		});
		setActiveTabId(tkey);
	};

	const onTabCancel = (tab: TabData) => {
		const tkey = closeDSLForm(tab);
		if (!tkey) return;
		if (tab.renderComponent) {
			setOpenTabs((p) => {
				p = Array.from(p);
				p = p.filter((t) => t.tkey != tkey);
				return p;
			});
		}
		setActiveTabId(defaultTab);
	};

	const closeTab: DSLCloseTabCallback = (id, tab, ref) => {
		//coffee Will call this function automatically
		const tkey = removeDSLFormRef({ ...tab, id, form: ref?.form as string }, ref);
		setOpenTabs((p) => {
			p = Array.from(p);
			p = p.filter((t) => t.tkey != tkey);
			return p;
		});
		setActiveTabId(defaultTab);
	};

	const onSaved: DSLCardOnSaveCallBack = (fd, tabData, ref) => {
		if (tabData?.gridRef) {
			tabData.gridRef.advanced.row(tabData.mode as any, fd.values);
		}
		setUpdateHash();
		setOpenTabs((p) => {
			let ptkey: string;
			if (ref?.xid?.toString().includes("-")) {
				// Add/Addfill Delete Temporary Ref
				ptkey = removeDSLFormRef({ ...tabData, id: ref?.xid }, ref);
			}
			const tkey = setDSLFormRef({ ...tabData, id: fd.record }, ref);
			const ci = p.findIndex((t) => t.tkey == ptkey);
			setActiveTabId(tkey);
			return p.map((t, ti) => {
				if (ti == ci) {
					return {
						...t,
						tkey: tkey,
						id: fd.record,
						label: fd?.values?.auto_name,
						mode: "read",
						form: ref?.form as string,
					};
				}
				return t;
			});
		});
	};

	const onArchive: DSLCardOnArchiveCallBack = (fd, tab, ref) => {
		if (tab?.gridRef) {
			tab.gridRef.advanced.row("archive", fd.values);
		}
		closeTab(tab.id, tab, ref);
	};

	const onUnArchive: DSLCardOnUnArchiveCallBack = (fd, tab) => {
		if (tab?.gridRef) {
			tab.gridRef.advanced.refresh();
		}
		openTab(tab.id, tab.label, "read", tab.form, {});
	};

	const changeMode: DSLCardOnChangeModeCallback = (mode, id, tab, ref) => {
		//TODO: Add logic to handle mode change
	};

	const rowClicked: DSLGridOnRowEventCallBack = (event) => {
		if (event.type == "dblclick") {
			openTab(event.id, event.label, event.mode, event.form, { gridRef: event.gridRef });
		} else {
			openTab(event.id, event.label, event.mode, event.form, { gridRef: event.gridRef });
		}
	};

	const setDSLFormRef = (tab: TabData, ref: DSLCardViewRef | undefined) => {
		const tkey = getTabKey(tab.id, tab.form);
		dslRefs[tkey] = ref;
		return tkey;
	};

	const getRefByKey = (tkey: string) => {
		return dslRefs[tkey];
	};

	const removeDSLFormRef = (tab: TabData, ref: unknown) => {
		let ptkey = getTabKey(tab.id, tab.form);
		delete dslRefs[ptkey];
		return ptkey;
	};

	const closeDSLForm = (tab: TabData) => {
		const tkey = getTabKey(tab.id, tab.form);
		const ref = dslRefs[tkey];
		if (!ref?.tab_do) {
			return tkey;
		}
		try {
			if (ref.cardform && !ref.cardform.ddf.form_rendered) {
				return false;
			}
			if (ref.cardread && !ref.cardread.ddr.form_rendered) {
				return false;
			}
			ref?.tab_do("cancel");
			return false;
		} catch (e) {
			//pass
		}
	};

	const controller = {
		onSaved: onSaved,
		onArchive: onArchive,
		onUnArchive: onUnArchive,
		onCancel: closeTab,
		rowClicked,
		setDSLFormRef,
		openTab,
		setActiveTabId,
		onTabCancel,
		setOpenTabs,
		getRefByKey,
		changeMode,
	};

	return [openTabs, activeTabId, controller, dslRefs, updateHash];
};
