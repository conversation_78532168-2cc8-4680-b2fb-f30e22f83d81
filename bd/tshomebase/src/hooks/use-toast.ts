import { toast, toastDismiss } from "@components/toast";
import { useState } from "react";
import { ToastProps } from "@components/toast";

const useToast = () => {
	const [toastId, setToastId] = useState<string | number | undefined>(undefined);

	const showToast = (opts: ToastProps, closePrevious: boolean = true) => {
		if (closePrevious && toastId) {
			toastDismiss(toastId);
		}
		const id = toast(opts);
		setToastId(id);
	};

	return [showToast];
};

export default useToast;
