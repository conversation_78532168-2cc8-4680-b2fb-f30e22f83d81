import type { LegacyRef, Ref, RefObject } from "react";
import { useCallback, useEffect, useRef, useState } from "react";

const defaultIntersectionObserverInit: IntersectionObserverInit = {
	root: null,
	rootMargin: "0px",
	threshold: Array.from({ length: 100 }, (v: undefined, i: number) => i * 0.01),
};

export const useOnScreen = (onlyFirst = false): [number, LegacyRef<HTMLDivElement> | undefined] => {
	const [isOnScreen, setIsOnScreen] = useState<number>(0);
	const targetRef = useRef<HTMLElement>(null);

	const observerRef = useRef<IntersectionObserver | null>(null);

	const observerCallback: IntersectionObserverCallback = useCallback(
		(entries: IntersectionObserverEntry[]) => {
			entries.forEach((entry: IntersectionObserverEntry) => {
				const ratio = entry.intersectionRatio;
				setIsOnScreen((prev) => {
					if (onlyFirst && prev > 0) return prev;
					if (ratio !== prev) return ratio;
					return prev;
				});
			});
		},
		[onlyFirst]
	);

	useEffect(() => {
		if (!targetRef.current) {
			return;
		}

		if (observerRef.current) {
			observerRef.current.disconnect();
			observerRef.current = null;
		}

		observerRef.current = new IntersectionObserver(observerCallback, defaultIntersectionObserverInit);
		observerRef.current.observe(targetRef.current);

		return () => {
			observerRef.current?.disconnect();
			observerRef.current = null;
		};
	}, [targetRef, observerCallback]);

	useEffect(() => {
		if (onlyFirst && isOnScreen > 0 && observerRef.current) {
			observerRef.current.disconnect();
			observerRef.current = null;
		}
	}, [onlyFirst, isOnScreen]);

	return [isOnScreen, targetRef];
};
