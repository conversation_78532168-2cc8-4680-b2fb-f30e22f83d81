import { FunctionComponent, useState } from "react";
import { TabData } from "@blocks/tab-list/tab-list";
import { DSLGridViewRef } from "@blocks/dsl-grid-view/dsl-grid-view";

interface TabController {
	closeTab: (tab: TabData) => void;
	openTab: (
		id: string,
		label: string,
		componentProps: Record<string, unknown>,
		renderComponent?: FunctionComponent<unknown>,
		extraKV?: Record<string, unknown>
	) => void;
	setActiveTabId: (id: string) => void;
	setOpenTabs: React.Dispatch<React.SetStateAction<TabData[]>>;
}

export const useTabController = (defaultTabs: TabData[], defaultTab: string): [TabData[], string, TabController] => {
	const [openTabs, setOpenTabs] = useState<TabData[]>(defaultTabs);
	const [activeTabId, setActiveTabId] = useState(defaultTab);

	const openTab = async (
		id: string,
		label: string,
		componentProps: Record<string, unknown>,
		renderComponent?: FunctionComponent<unknown>,
		extraKV?: Record<string, unknown>
	) => {
		id = id + "";
		const gridRef = componentProps?.gridRef as DSLGridViewRef;
		const tkey = id;
		extraKV = extraKV || {};
		const nt = { ...extraKV, tkey: tkey, id: id, label: label, componentProps, gridRef } as TabData;
		if (renderComponent) {
			nt.renderComponent = renderComponent;
			nt.renderComponentProps = componentProps;
		}

		setOpenTabs((p) => {
			p = Array.from(p);
			if (p.some((t) => t.tkey == tkey)) {
				return p;
			}
			p = [...p, nt];
			return p;
		});
		setActiveTabId(tkey);
	};

	const closeTab = (tab: TabData) => {
		const tkey = tab?.tkey || tab?.id;
		setOpenTabs((p) => {
			p = Array.from(p);
			p = p.filter((t) => t.tkey != tkey);
			return p;
		});
		setActiveTabId(defaultTab);
	};

	const controller = {
		closeTab,
		openTab,
		setActiveTabId,
		setOpenTabs,
	};

	return [openTabs, activeTabId, controller];
};
