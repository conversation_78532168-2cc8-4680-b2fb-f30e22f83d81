// useWindowsClass.ts
import { useEffect } from "react";

export const useWindowsClass = (): void => {
    useEffect(() => {
        const detectOS = (): string => navigator.platform.toLowerCase().includes("win") ? "windows" : "other";

        const osClass: string = detectOS();
        document.documentElement.classList.add(osClass);

        return () => {
            document.documentElement.classList.remove(osClass);
        };
    }, []);
};
