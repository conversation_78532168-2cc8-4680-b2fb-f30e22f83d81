{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": false,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./src",
    "paths": {
      "@blocks/*": ["blocks/*"],
      "@components/*": ["components/*"],
      "@contexts/*": ["contexts/*"],
      "@core/*": ["core/*"],
      "@dsl/*": ["dsl/*"],
      "@dsl-form/*": ["dsl-form/*"],
      "@enum/*": ["enum/*"],
      "@hooks/*": ["hooks/*"],
      "@less/*": ["less/*"],
      "@modules/*": ["modules/*"],
      "@public/*": ["public/*"],
      "@typedefs/*": ["typedefs/*"],
      "@utils/*": ["utils/*"],
    },
  },
  "include": ["src"],
}