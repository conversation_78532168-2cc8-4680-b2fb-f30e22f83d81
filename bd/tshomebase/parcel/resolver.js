import { Resolver } from '@parcel/plugin';
import path from 'path';
import { existsSync } from 'fs';

export default new Resolver({
    async resolve({ specifier }) {
        const resolve_list = [".ts", ".tsx", ".js", ".jsx",
            "/index.tsx", "/index.ts", "/index.jsx", "/index.js"
        ];

        if (specifier.startsWith('public/')) {
            return {
                filePath: path.resolve(path.join("./dist", specifier))
            };
        }
        if (specifier.startsWith('@')) {
            const src_path = path.resolve(
                path.join("./src/", specifier.substring(1))
            );

            if (existsSync(path.dirname(src_path))) {
                for (const file_name of resolve_list) {
                    const try_path = path.resolve(src_path + file_name);

                    if (existsSync(try_path)) {
                        return {
                            filePath: try_path
                        };
                    }

                }
            }
        }

        // Let the next resolver in the pipeline handle this dependency.
        return null;
    }
});