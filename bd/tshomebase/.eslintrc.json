{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "ignorePatterns": ["node_modules/", "**/node_modules/", "/**/node_modules/*", "out/", "dist/", "build/", "public/", "homebase/"], "overrides": [{"files": ["*.ts", "*.tsx"], "parserOptions": {"project": ["./tsconfig.json"]}}], "rules": {"space-before-function-paren": ["error", "always"], "semi": ["error", "always"], "quotes": ["error", "double"], "no-multi-spaces": "error", "no-multiple-empty-lines": ["error", {"max": 2, "maxEOF": 1}], "indent": ["error", "tab"], "arrow-body-style": ["error", "as-needed"], "react/self-closing-comp": ["error", {"component": true, "html": true}], "react-hooks/exhaustive-deps": "warn", "react/boolean-prop-naming": ["error", {"propTypeNames": ["bool", "mutuallyExclusiveTrueProps"]}], "react/destructuring-assignment": "off", "react/function-component-definition": ["error", {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "react/hook-use-state": ["warn", {"allowDestructuredState": true}], "react/display-name": "off", "react/iframe-missing-sandbox": "error", "react/jsx-closing-bracket-location": [1, "tag-aligned"], "react/jsx-closing-tag-location": "error", "react/jsx-curly-brace-presence": "error", "react/sort-prop-types": "warn", "@typescript-eslint/naming-convention": ["warn", {"selector": "function", "format": ["PascalCase", "camelCase"]}, {"selector": "variable", "format": ["camelCase", "PascalCase"]}], "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/consistent-type-exports": "error", "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/no-duplicate-enum-values": "error", "@typescript-eslint/no-duplicate-type-constituents": "error", "@typescript-eslint/no-empty-interface": "warn", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-unnecessary-boolean-literal-compare": "error", "@typescript-eslint/no-unnecessary-condition": "off", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/prefer-enum-initializers": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/prefer-string-starts-ends-with": "error", "@typescript-eslint/restrict-plus-operands": "error", "@typescript-eslint/switch-exhaustiveness-check": "error", "@typescript-eslint/unified-signatures": "error", "@typescript-eslint/no-explicit-any": "error"}}