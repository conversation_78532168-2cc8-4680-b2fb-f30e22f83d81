const { program } = require("commander");
const readline = require("readline");
const { loadConfig } = require("./src/core/config");
const MigrationExecutor = require("./src/core/executor");
const HostConnector = require("./src/connectors/hostConnector");
const Logger = require("./src/utils/logger");
const Table = require("cli-table3");
const wrap = require("word-wrap");
const DocumentMapManager = require("./src/utils/document_map_manager");

// ANSI color codes
const CYAN = "\x1b[36m";
const RESET = "\x1b[0m";

const logger = new Logger("MAIN");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function displayMigrationHistory(history) {
  if (!Array.isArray(history)) {
    logger.warn("No migration history available");
    return;
  }

  try {
    const table = new Table({
      head: [
        `${CYAN}Migration Name${RESET}`,
        `${CYAN}Last Run${RESET}`,
        `${CYAN}Entries Changed${RESET}`,
        `${CYAN}Errors${RESET}`,
        `${CYAN}Summary${RESET}`,
      ],
      colWidths: [20, 25, 15, 20, 40],
      wordWrap: true,
      wrapOnWordBoundary: true, // Enable word boundary wrapping
      style: {
        head: [],
        border: [],
      },
    });

    history.forEach((entry) => {
      try {
        let errorsDisplay = "None";
        try {
          if (entry.errors) {
            const errors = JSON.parse(
              Array.isArray(entry.errors)
                ? JSON.stringify(entry.errors)
                : entry.errors,
            );
            errorsDisplay =
              errors.length === 0 ? "None" : `${errors.length} errors`;
          }
        } catch (e) {
          errorsDisplay = "Error parsing errors";
          logger.error(`Error parsing errors: ${e.message}`);
        }

        // Format summary with proper line breaks
        const summaryDisplay = entry.summary
          ? wrap(entry.summary, {
              width: 35,
              indent: "",
              trim: true,
              cut: true,
            })
          : "N/A";

        table.push([
          entry.migration_name || "Unknown",
          entry.last_run ? new Date(entry.last_run).toLocaleString() : "N/A",
          entry.entries_changed || 0,
          errorsDisplay,
          summaryDisplay,
        ]);
      } catch (err) {
        logger.error(
          `Error processing migration history entry: ${err.message}`,
        );
      }
    });

    console.log(table.toString());

    // Display error details if present
    const entriesWithErrors = history.filter(
      (entry) =>
        entry.errors &&
        JSON.parse(
          Array.isArray(entry.errors)
            ? JSON.stringify(entry.errors)
            : entry.errors,
        ).length > 0,
    );

    if (entriesWithErrors.length > 0) {
      console.log("\nError Details:");
      entriesWithErrors.forEach((entry) => {
        const errors = JSON.parse(
          Array.isArray(entry.errors)
            ? JSON.stringify(entry.errors)
            : entry.errors,
        );
        console.log(`\n${CYAN}${entry.migration_name}${RESET}:`);
        errors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error.message}`);
          if (error.sourceId) console.log(`     Source ID: ${error.sourceId}`);
        });
      });
    }
  } catch (error) {
    logger.error(`Error displaying migration history: ${error.message}`);
    console.log("Raw migration history:", history);
  }
}

process.on("SIGINT", () => {
  logger.info("Process interrupted");
  Logger.closeAndExit(1);
});

program
  .requiredOption(
    "-c, --config <path>",
    "Path to the configuration file",
    "config/config.yaml",
  )
  .option(
    "-m, --migrations <migrations>",
    "Comma-separated list of migrations to run",
  )
  .option(
    "-pt, --post-transforms <post-transforms>",
    "Post-transform to run for the specified migration",
  )
  .option("-d, --dry", "Dry run the migrations", false)
  .option("-v, --verbose", "Verbose logging", false)
  .option(
    "-l, --limit <number>",
    "Limit the number of entries to process (for testing)",
    Number,
  )
  .option(
    "-s, --start <number>",
    "Start from a specific record (for testing)",
    Number,
  )
  .option(
    "-f, --filters <filters>",
    "Comma-separated list of filters in format: field operator value",
  )
  .option(
    "-sqf, --subquery-filter <query>",
    "SQL subquery filter in format: field in (select ... from ...)",
  )
  .action(async (options) => {
    try {
      const config = await loadConfig(options.config);
      const source_limit = options.limit;
      const startFrom = parseInt(options.start || "0");

      // Require -m when -pt is specified
      if (options.postTransforms && !options.migrations) {
        logger.error(
          "Post-transforms (-pt) require a migration (-m) to be specified",
        );
        process.exit(1);
      }

      let migrationsToRun = options.migrations
        ? options.migrations.split(",")
        : config.default_migrations;
      migrationsToRun = migrationsToRun.map((m) => m.trim().toLowerCase());

      const postTransform = options.postTransforms?.trim().toLowerCase();

      Logger.setVerbose(options.verbose);

      if (source_limit || startFrom) {
        logger.info("Migration Range Configuration:");
        if (source_limit)
          logger.info(`  Source Limit: ${source_limit} records`);
        if (startFrom) logger.info(`  Starting From: record ${startFrom}`);
        logger.info("------------------------");
      }

      logger.info(
        `Running migrations: ${migrationsToRun.join(", ")}, \n` +
          `Source DB: ${config.source.pg_uri}, \n` +
          `Source API: ${config.source.api_uri}, \n` +
          `Destination DB: ${config.dest.pg_uri}, \n` +
          `Destination API: ${config.dest.api_uri}, \n` +
          `Dry run: ${options.dry}, \n` +
          `Verbose: ${options.verbose}`,
      );

      const destConnector = new HostConnector(
        { ...config.dest, isDryRun: options.dry },
        "dest",
      );

      try {
        await destConnector.db.create_clara_migration_table();
        const migration_history =
          await destConnector.db.get_migration_history();

        logger.info("Migration History:");
        if (migration_history && migration_history.length > 0) {
          displayMigrationHistory(migration_history);
        } else {
          logger.info("No previous migration history found.");
        }

        let question = `Are you sure you want to continue with migration${migrationsToRun.length > 1 ? "s" : ""}: ${migrationsToRun.join(", ")}`;
        if (postTransform) {
          question += `\nRunning only post-transform: ${postTransform}`;
        }
        question += " (y/n) ";

        rl.question(question, async (answer) => {
          if (answer.toLowerCase() === "y") {
            Logger.enableLogging(options.dry);
            try {
              const sourceConnector = new HostConnector(
                { ...config.source, isDryRun: options.dry },
                "source",
              );

              // Parse regular filters
              let parsedFilters = [];
              if (options.filters) {
                const filterStrings = options.filters
                  .split(",")
                  .map((f) => f.trim());
                parsedFilters = filterStrings.map((filterStr) => {
                  const parts = filterStr.match(
                    /([^\s]+)\s*([><=!]+|>=|<=|like|ilike|nlike|nilike)\s*"?([^"]+)"?/,
                  );
                  if (!parts) {
                    throw new Error(`Invalid filter format: ${filterStr}`);
                  }
                  return [parts[1], parts[2], parts[3]];
                });
              }

              // Add subquery filter if provided
              if (options.subqueryFilter) {
                const match = options.subqueryFilter.match(
                  /([^\s]+)\s+in\s*\((.*)\)/i,
                );
                if (!match) {
                  throw new Error(
                    `Invalid subquery filter format. Expected: field in (select ...)"`,
                  );
                }
                const [_, field, subquery] = match;
                parsedFilters.push([field, "in_subquery", subquery]);
              }

              logger.debug("Parsed filters:", parsedFilters);

              const executor = new MigrationExecutor(
                sourceConnector,
                destConnector,
                options.dry,
                options.limit ? parseInt(options.limit) : null,
                startFrom,
                postTransform,
                parsedFilters,
              );

              // Execute migrations and wait for completion
              await executor.executeMigrations(migrationsToRun);

              // Results are now handled within executeMigrations
              logger.success("All operations completed successfully");
            } catch (error) {
              logger.error("Error:", error);
              if (error.stack) {
                logger.error("Error stack:", error.stack);
              }
            }
            Logger.closeAndExit(0);
          } else {
            logger.warn("Operation cancelled by user");
            process.exit(0);
          }
          rl.close();
        });
      } catch (error) {
        logger.error(`Error accessing migration history: ${error.message}`);
        // Still allow the user to continue
        rl.question(
          `Continue with migrations despite history error? ${migrationsToRun.join(", ")} (y/n) `,
          async (answer) => {
            if (answer.toLowerCase() === "y") {
              Logger.enableLogging(options.dry);
              try {
                const sourceConnector = new HostConnector(
                  { ...config.source, isDryRun: options.dry },
                  "source",
                );
                const startFrom = parseInt(process.env.START_FROM || "0");
                const executor = new MigrationExecutor(
                  sourceConnector,
                  destConnector,
                  options.dry,
                  source_limit,
                  startFrom,
                );

                await executor.executeMigrations(migrationsToRun);
                logger.success("All migrations completed successfully");
              } catch (error) {
                logger.error("Migration error:", error);
                if (error.stack) {
                  logger.error("Error stack:", error.stack);
                }
              }
              Logger.closeAndExit(0);
            } else {
              logger.warn("Migration cancelled by user");
              process.exit(0);
            }
            rl.close();
          },
        );
      }
    } catch (error) {
      logger.error(`Error: ${error.message}`);
      if (error.stack) {
        logger.error("Error stack:", error.stack);
      }
      rl.close();
      Logger.closeAndExit(1);
    }
  });

program
  .command("backup-map")
  .description("Create a backup of the document map")
  .action(async () => {
    try {
      const documentMapManager = new DocumentMapManager();
      await documentMapManager.initialize();
      await documentMapManager.createBackup();
      logger.success("Backup created successfully");
      process.exit(0);
    } catch (error) {
      logger.error(`Backup failed: ${error.message}`);
      process.exit(1);
    }
  });

program
  .command("init-map")
  .description("Initialize an empty document map")
  .action(async () => {
    try {
      const documentMapManager = new DocumentMapManager();
      await documentMapManager.initialize();
      await documentMapManager.persistMap();
      logger.success("Document map initialized successfully");
      process.exit(0);
    } catch (error) {
      logger.error(`Map initialization failed: ${error.message}`);
      process.exit(1);
    }
  });

program
  .command("list-backups")
  .description("List available document map backups")
  .action(async () => {
    try {
      const documentMapManager = new DocumentMapManager();
      const backups = await documentMapManager.listBackups();
      if (backups.length === 0) {
        logger.info("No backups found");
      } else {
        logger.info("Available backups:");
        backups.forEach((b) => {
          logger.info(`  ${b.timestamp} - ${b.path}`);
        });
      }
      process.exit(0);
    } catch (error) {
      logger.error(`Failed to list backups: ${error.message}`);
      process.exit(1);
    }
  });

program.parse(process.argv);
