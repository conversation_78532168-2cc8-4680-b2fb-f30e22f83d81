[32m2024-11-15 05:22:08.124 [MAIN] INFO: Running migrations: patient_document, 
Source DB: postgresql://hb:<EMAIL>:5432/ebridge, 
Source API: https://api.source.com, 
Destination DB: postgresql://hb:<EMAIL>:15432/hb, 
Destination API: https://byoung.clararx.com, 
Dry run: true, 
Verbose: true[0m
[32m2024-11-15 05:22:08.126 [API_CONNECTOR] INFO: Initialized APIConnector for https://byoung.clararx.com[0m
[36m2024-11-15 05:22:08.174 [POSTGRES_CONNECTOR] DEBUG: Executing migration history query: 
            WITH RankedMigrations AS (
                SELECT 
                    migration_name,
                    last_run,
                    entries_changed,
                    errors,
                    summary,
                    ROW_NUMBER() OVER (
                        PARTITION BY migration_name 
                        ORDER BY last_run DESC NULLS LAST
                    ) as rn
                FROM clara_migration
            )
            SELECT 
                migration_name,
                last_run,
                entries_changed,
                errors,
                summary
            FROM RankedMigrations 
            WHERE rn = 1
            ORDER BY migration_name
        [0m
[36m2024-11-15 05:22:08.178 [POSTGRES_CONNECTOR] DEBUG: Found 12 migration history records[0m
[32m2024-11-15 05:22:08.178 [MAIN] INFO: Migration History:[0m
┌────────────────────┬─────────────────────────┬───────────────┬────────────────────┬────────────────────────────────────────┐
│ [36mMigration Name[0m     │ [36mLast Run[0m                │ [36mEntries[39m       │ [36mErrors[0m             │ [36mSummary[0m                                │
│                    │                         │ [36mChanged[0m       │                    │                                        │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ insurance          │ 11/15/2024, 4:14:56 AM  │ 0             │ None               │ Processed: 0, Successes: 0, Errors:    │
│                    │                         │               │                    │  0, Post-transform Successes: 0, Po    │
│                    │                         │               │                    │ st-transform Errors: 0, Skipped: 0,    │
│                    │                         │               │                    │  Inserts: 0, Updates: 0, Unchanged:    │
│                    │                         │               │                    │  0                                     │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ inventory_lots     │ 11/8/2024, 6:10:52 PM   │ 0             │ 1 errors           │ Processed: 92, Successes: 1, Errors    │
│                    │                         │               │                    │ : 1, Post-transform Successes: 0, P    │
│                    │                         │               │                    │ ost-transform Errors: 0, Skipped: 0    │
│                    │                         │               │                    │ , Inserts: 0, Updates: 0, Unchanged    │
│                    │                         │               │                    │ : 0                                    │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient            │ 11/15/2024, 4:16:50 AM  │ 25            │ None               │ Processed: 102, Successes: 25, Erro    │
│                    │                         │               │                    │ rs: 0, Post-transform Successes: 30    │
│                    │                         │               │                    │ 6, Post-transform Errors: 0, Skippe    │
│                    │                         │               │                    │ d: 0, Inserts: 1, Updates: 24, Unch    │
│                    │                         │               │                    │ anged: 77                              │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_allergy    │ 11/15/2024, 4:22:29 AM  │ 0             │ None               │ Processed: 2, Successes: 0, Errors:    │
│                    │                         │               │                    │  0, Post-transform Successes: 1, Po    │
│                    │                         │               │                    │ st-transform Errors: 0, Skipped: 1,    │
│                    │                         │               │                    │  Inserts: 0, Updates: 0, Unchanged:    │
│                    │                         │               │                    │  1                                     │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_contact    │ 11/15/2024, 4:22:49 AM  │ 1             │ None               │ Processed: 1015, Successes: 1, Erro    │
│                    │                         │               │                    │ rs: 0, Post-transform Successes: 0,    │
│                    │                         │               │                    │  Post-transform Errors: 0, Skipped:    │
│                    │                         │               │                    │  2, Inserts: 1, Updates: 0, Unchang    │
│                    │                         │               │                    │ ed: 1012                               │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_diagnosis  │ 11/15/2024, 4:23:24 AM  │ 2             │ None               │ Processed: 2, Successes: 2, Errors:    │
│                    │                         │               │                    │  0, Post-transform Successes: 0, Po    │
│                    │                         │               │                    │ st-transform Errors: 0, Skipped: 0,    │
│                    │                         │               │                    │  Inserts: 2, Updates: 0, Unchanged:    │
│                    │                         │               │                    │  0                                     │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_document   │ 11/11/2024, 3:30:02 AM  │ 0             │ None               │ Processed: 71504, Successes: 0, Err    │
│                    │                         │               │                    │ ors: 0, Post-transform Successes: 7    │
│                    │                         │               │                    │ 0630, Post-transform Errors: 0, Ski    │
│                    │                         │               │                    │ pped: 1740, Inserts: 0, Updates: 0,    │
│                    │                         │               │                    │  Unchanged: 70630                      │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_insurance  │ 11/15/2024, 5:19:14 AM  │ 0             │ None               │ Processed: 5, Successes: 0, Errors:    │
│                    │                         │               │                    │  0, Post-transform Successes: 0, Po    │
│                    │                         │               │                    │ st-transform Errors: 0, Skipped: 0,    │
│                    │                         │               │                    │  Inserts: 0, Updates: 0, Unchanged:    │
│                    │                         │               │                    │  5                                     │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ patient_medication │ 11/15/2024, 5:00:04 AM  │ 8157          │ None               │ Processed: 8157, Successes: 8157, E    │
│                    │                         │               │                    │ rrors: 0, Post-transform Successes:    │
│                    │                         │               │                    │  0, Post-transform Errors: 0, Skipp    │
│                    │                         │               │                    │ ed: 0, Inserts: 25, Updates: 8132,     │
│                    │                         │               │                    │ Unchanged: 0                           │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ physician          │ 11/15/2024, 4:12:25 AM  │ 933           │ None               │ Processed: 933, Successes: 933, Err    │
│                    │                         │               │                    │ ors: 0, Post-transform Successes: 0    │
│                    │                         │               │                    │ , Post-transform Errors: 0, Skipped    │
│                    │                         │               │                    │ : 0, Inserts: 0, Updates: 933, Unch    │
│                    │                         │               │                    │ anged: 0                               │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ referral_source    │ 11/15/2024, 4:15:57 AM  │ 0             │ None               │ Processed: 0, Successes: 0, Errors:    │
│                    │                         │               │                    │  0, Post-transform Successes: 0, Po    │
│                    │                         │               │                    │ st-transform Errors: 0, Skipped: 0,    │
│                    │                         │               │                    │  Inserts: 0, Updates: 0, Unchanged:    │
│                    │                         │               │                    │  0                                     │
├────────────────────┼─────────────────────────┼───────────────┼────────────────────┼────────────────────────────────────────┤
│ user               │ 11/15/2024, 4:11:58 AM  │ 0             │ None               │ Processed: 27, Successes: 0, Errors    │
│                    │                         │               │                    │ : 0, Post-transform Successes: 0, P    │
│                    │                         │               │                    │ ost-transform Errors: 0, Skipped: 0    │
│                    │                         │               │                    │ , Inserts: 0, Updates: 0, Unchanged    │
│                    │                         │               │                    │ : 26                                   │
└────────────────────┴─────────────────────────┴───────────────┴────────────────────┴────────────────────────────────────────┘

Error Details:

[36minventory_lots[0m:
  1. this.destinationConnector.api.post is not a function
     Source ID: undefined
Are you sure you want to continue with migration: patient_document (y/n) [32m2024-11-15 05:22:09.317 [API_CONNECTOR] INFO: Initialized APIConnector for https://api.source.com[0m
[32m2024-11-15 05:22:09.318 [MigrationExecutor] INFO: Parsed filters in executor: [["createdon",">=","2024-01-10"]][0m
[36m2024-11-15 05:22:09.318 [MigrationExecutor] DEBUG: Skipping transformer insurance - not in specified migrations[0m
[36m2024-11-15 05:22:09.318 [MigrationExecutor] DEBUG: Skipping transformer inventory_lots - not in specified migrations[0m
[36m2024-11-15 05:22:09.318 [MigrationExecutor] DEBUG: Skipping transformer patient - not in specified migrations[0m
[36m2024-11-15 05:22:09.318 [MigrationExecutor] DEBUG: Skipping transformer patient_allergy - not in specified migrations[0m
[36m2024-11-15 05:22:09.319 [MigrationExecutor] DEBUG: Skipping transformer patient_contact - not in specified migrations[0m
[36m2024-11-15 05:22:09.319 [MigrationExecutor] DEBUG: Skipping transformer patient_diagnosis - not in specified migrations[0m
[36m2024-11-15 05:22:09.394 [BaseTransformer] DEBUG: BaseTransformer initialized with additionalFilters: [["createdon",">=","2024-01-10"]][0m
isDryRun: true
[36m2024-11-15 05:22:09.401 [MigrationExecutor] DEBUG: Skipping transformer patient_insurance - not in specified migrations[0m
[36m2024-11-15 05:22:09.401 [MigrationExecutor] DEBUG: Skipping transformer patient_medication - not in specified migrations[0m
[36m2024-11-15 05:22:09.402 [MigrationExecutor] DEBUG: Skipping transformer physician - not in specified migrations[0m
[36m2024-11-15 05:22:09.402 [MigrationExecutor] DEBUG: Skipping transformer referral_source - not in specified migrations[0m
[36m2024-11-15 05:22:09.402 [MigrationExecutor] DEBUG: Skipping transformer user - not in specified migrations[0m
[36m2024-11-15 05:22:09.403 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "cpr_documents" WHERE "delflag" != $1 AND "createdon" >= $2 OFFSET 0 with values:[0m
[36m[ 1, '2024-01-10' ][0m
[36m2024-11-15 05:22:09.556 [PatientDocumentsTransformer] DEBUG: Loaded document map with 89280 entries[0m
[32m2024-11-15 05:22:09.810 [POSTGRES_CONNECTOR] INFO: Fetched 11412 rows from cpr_documents[0m
[32m2024-11-15 05:22:09.837 [MigrationExecutor] INFO: Fetched 11412 records starting from 0[0m
[36m2024-11-15 05:22:09.837 [MigrationExecutor] DEBUG: Processing batch of 50 records[0m
[36m2024-11-15 05:22:09.838 [BaseTransformer] DEBUG: [object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object][0m
[32m2024-11-15 05:22:09.838 [BaseTransformer] INFO: Starting batch transform of 50 records[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108178,"sig_required":false,"faxsrc":"RX","chgbyhost":"DESKTOP-4CV0NL1 # cpr_user","doc_no":1889,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":97,"keywords":null,"doc_mrn":"201926","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201926_5.PDF","web_printed":false,"createdby":"DESKTOP-4CV0NL1 # cpr_user","descrip":"FedEx Proof of Delivery","returncode":null,"user_name":"Randee Miller","createdon":"2024-01-10T08:17:28.000Z","status":"Assigned","time_":"08:17:11","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":25240,"delflag":0,"pat_name":"Gillette, Teresa","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\TG.PDF","required":0,"touchdate":"2024-01-10T08:17:28.000Z","cpk_documents":110160,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108180,"sig_required":false,"faxsrc":"RX","chgbyhost":"DESKTOP-8SOV2QI # cpr_user","doc_no":1210,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":12,"keywords":null,"doc_mrn":"200425","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\200425_1.PDF","web_printed":false,"createdby":"DESKTOP-8SOV2QI # cpr_user","descrip":"AOB (unsigned)","returncode":null,"user_name":"Meredith Wallpe","createdon":"2024-01-10T09:20:01.000Z","status":"Assigned","time_":"09:19:48","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":24499,"delflag":0,"pat_name":"O'Dell, Deborah","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\ODELLAOB.PDF","required":0,"touchdate":"2024-01-10T09:20:01.000Z","cpk_documents":110162,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108181,"sig_required":false,"faxsrc":"RX","chgbyhost":"DESKTOP-8SOV2QI # cpr_user","doc_no":1046,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":12,"keywords":null,"doc_mrn":"200254","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\200254_1.PDF","web_printed":false,"createdby":"DESKTOP-8SOV2QI # cpr_user","descrip":"AOB (unsigned)","returncode":null,"user_name":"Meredith Wallpe","createdon":"2024-01-10T09:20:26.000Z","status":"Assigned","time_":"09:20:13","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":24516,"delflag":0,"pat_name":"Root, Sherrie","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\ROOTAOB.PDF","required":0,"touchdate":"2024-01-10T09:20:26.000Z","cpk_documents":110163,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108182,"sig_required":false,"faxsrc":"RX","chgbyhost":"DESKTOP-8SOV2QI # cpr_user","doc_no":1889,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":12,"keywords":null,"doc_mrn":"201926","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201926_6.PDF","web_printed":false,"createdby":"DESKTOP-8SOV2QI # cpr_user","descrip":"AOB (unsigned)","returncode":null,"user_name":"Meredith Wallpe","createdon":"2024-01-10T09:20:50.000Z","status":"Assigned","time_":"09:20:40","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":25240,"delflag":0,"pat_name":"Gillette, Teresa","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\GILLETTEAOB.PDF","required":0,"touchdate":"2024-01-10T09:20:50.000Z","cpk_documents":110164,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108183,"sig_required":false,"faxsrc":"RX","chgbyhost":"HBLSPSPC003 # cpr_user","doc_no":1501,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":20,"keywords":null,"doc_mrn":"201614","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201614_2.PDF","web_printed":false,"createdby":"HBLSPSPC003 # cpr_user","descrip":"Medical H&P","returncode":null,"user_name":"Ka-Nor Geeston","createdon":"2024-01-10T09:23:24.000Z","status":"Assigned","time_":"09:23:01","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":19669,"delflag":0,"pat_name":"Wolter, Elizabeth","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\DOWNLOAD_94BC4236449940CBB22F246E3CE14417.PDF","required":0,"touchdate":"2024-01-10T09:23:24.000Z","cpk_documents":110165,"siteno":7,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108184,"sig_required":false,"faxsrc":"RX","chgbyhost":"HBLSPSPC003 # cpr_user","doc_no":1567,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":20,"keywords":null,"doc_mrn":"201266","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201266_9.PDF","web_printed":false,"createdby":"HBLSPSPC003 # cpr_user","descrip":"Prior Authorization (Approval)","returncode":null,"user_name":"Ka-Nor Geeston","createdon":"2024-01-10T09:24:04.000Z","status":"Assigned","time_":"09:23:48","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":24375,"delflag":0,"pat_name":"Fontenot, Craig","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\DOWNLOAD_A5B700351C8D4235B46F30398C331210.PDF","required":0,"touchdate":"2024-01-10T09:24:04.000Z","cpk_documents":110166,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108185,"sig_required":false,"faxsrc":"RX","chgbyhost":"HBLSPSPC003 # cpr_user","doc_no":1495,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":20,"keywords":null,"doc_mrn":"201616","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201616_1.PDF","web_printed":false,"createdby":"HBLSPSPC003 # cpr_user","descrip":"New Script","returncode":null,"user_name":"Ka-Nor Geeston","createdon":"2024-01-10T09:24:53.000Z","status":"Assigned","time_":"09:24:33","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":25258,"delflag":0,"pat_name":"Dukes, Susan","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\DOWNLOAD_BBDBA16C0903490DAF3EBA315CFC70F9.PDF","required":0,"touchdate":"2024-01-10T09:24:53.000Z","cpk_documents":110167,"siteno":7,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108186,"sig_required":false,"faxsrc":"RX","chgbyhost":"PC-1QC0 # cpr_user","doc_no":0,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":48,"keywords":null,"doc_mrn":"200476","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\200476_3.PDF","web_printed":false,"createdby":"PC-1QC0 # cpr_user","descrip":"Co-pay Assistance","returncode":null,"user_name":"Mike Montgomery","createdon":"2024-01-10T09:24:54.000Z","status":"Assigned","time_":"09:24:41","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":0,"delflag":0,"pat_name":"Morgan, Hunter","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\HMORGANCOPAYAPPROVAL.PDF","required":0,"touchdate":"2024-01-10T09:24:54.000Z","cpk_documents":110168,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108187,"sig_required":false,"faxsrc":"RX","chgbyhost":"DESKTOP-8SOV2QI # cpr_user","doc_no":1493,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":12,"keywords":null,"doc_mrn":"201323","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201323_1.PDF","web_printed":false,"createdby":"DESKTOP-8SOV2QI # cpr_user","descrip":"AOB (unsigned)","returncode":null,"user_name":"Meredith Wallpe","createdon":"2024-01-10T09:25:17.000Z","status":"Assigned","time_":"09:25:01","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":24355,"delflag":0,"pat_name":"Simmons, Michele","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\SIMMONSAOB.PDF","required":0,"touchdate":"2024-01-10T09:25:17.000Z","cpk_documents":110169,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.838 [PatientDocumentsTransformer] DEBUG: Transforming patient document: {"id":108188,"sig_required":false,"faxsrc":"RX","chgbyhost":"HBLSPSPC003 # cpr_user","doc_no":1228,"cfk_orgdocrec":0,"active_date":null,"linkeddocrec":false,"user_no":20,"keywords":null,"doc_mrn":"201249","findlinkedrec":false,"cfk_docname":0,"template":false,"category":null,"cfk_web_docimages":0,"cfk_approverxdocuments":0,"filename":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\FAXES\\202401\\201249_4.PDF","web_printed":false,"createdby":"HBLSPSPC003 # cpr_user","descrip":"Prior Authorization","returncode":null,"user_name":"Ka-Nor Geeston","createdon":"2024-01-10T09:25:30.000Z","status":"Assigned","time_":"09:25:18","int_type":2,"cfk_signatures":0,"doc_type":"Patient","doc_key":25092,"delflag":0,"pat_name":"Powell, Bonita","orig_file":"\\\\HB-APP01.HB.LOCAL\\CPRPLUS\\INCOMING\\LEESSUMMIT\\DOWNLOAD_79ACA383D0A64DEF8A7D456D2B42E275.PDF","required":0,"touchdate":"2024-01-10T09:25:30.000Z","cpk_documents":110170,"siteno":3,"date_due":null,"cfk_labels":0,"inactive_date":null,"cfk_tickci":0,"received":null,"linkeddt":false,"date_resent":null,"date_ret":null,"notes":null,"primaryflag":false,"inactive":false,"date_sent":"2024-01-10T00:00:00.000Z","webaccess":true,"faxsite":3,"__envoy_touch_stamp__":"2024-11-15T04:09:30.880Z"}[0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201926' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '200425' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '200254' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201926' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201614' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201266' ][0m
[36m2024-11-15 05:22:09.839 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201616' ][0m
[36m2024-11-15 05:22:09.840 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '200476' ][0m
[36m2024-11-15 05:22:09.840 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201323' ][0m
[36m2024-11-15 05:22:09.840 [POSTGRES_CONNECTOR] DEBUG: Executing query: SELECT * FROM "form_patient" WHERE "external_id" = $1 AND ("archived" IS FALSE OR "archived" IS NULL) AND ("deleted" IS FALSE OR "deleted" IS NULL) LIMIT 1 OFFSET 0 with values:[0m
[36m[ '201249' ][0m
[32m2024-11-15 05:22:09.847 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.847 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T10:53:12.689Z","change_type":null,"change_on":null,"id":1880,"change_by":null,"auto_name":"Teresa Gillette","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:48:05.305Z","pediatric":null,"phone_primary":null,"diabetic":"IDDM","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Bethany Trogstad","patient_status":null,"caregiver_phone":null,"email":null,"home_city":null,"firstname":"Teresa","lastname":"Gillette","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1962-02-18T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201926","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"026","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2023-12-12T00:00:00.000Z","referral_date":"2023-11-16T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Morphine Sulfate,Albuterol,Bactrim,Cipro,Ativan","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:48:05.349227+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1684,"referrer_id":12495,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"Goes by Terry\r\nDelivery Signature Waiver Letter Sent\r\nPaper DT's","billing_alert":"\r\nDrug billed to RX plan\r\nSupplies/RN visits billed to Ambetter Home State Health-Lisa M 12/13/23\r\n\r\nPrivigen 40gm IV Q2 weeks","primary_physician":null,"search":"'gillette':2B,4 'teresa':1A,3","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":2116,"other_allergies":"Shellfish,Seasonal Allergies"}][0m
[36m2024-11-15 05:22:09.847 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201926_5.pdf[0m
[32m2024-11-15 05:22:09.882 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.882 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T11:03:09.080Z","change_type":null,"change_on":null,"id":2626,"change_by":null,"auto_name":"Susan Dukes","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:47:42.197Z","pediatric":null,"phone_primary":null,"diabetic":"NIDDM","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Justin Brewer","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Susan","lastname":"Dukes","emergency_phone":null,"call_time":null,"ssn":null,"phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1959-09-18T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201616","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"014","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2021-11-03T00:00:00.000Z","referral_date":"2021-10-29T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"ZOLMitriptan","marital_status":"M","surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:47:42.241122+00\",)","status":null,"site_id":14,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1516,"referrer_id":12318,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"DALLAS PATIENT PROFILE\r\n\r\nOpt out on file\r\n\r\nText electronic signature links to ************","billing_alert":"Bill drug with date shipped on one claim\r\nBill per diem with same day each week on separate claim-Lisa M 8/7/24\r\n","primary_physician":null,"search":"'dukes':2B,4 'susan':1A,3","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":1670,"other_allergies":"No other allergies noted"}][0m
[36m2024-11-15 05:22:09.882 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201616_1.pdf[0m
[32m2024-11-15 05:22:09.883 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.883 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T11:03:26.937Z","change_type":null,"change_on":null,"id":2655,"change_by":null,"auto_name":"Deborah O'Dell","archived":null,"created_by":22,"updated_by":null,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":null,"pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Jake Christensen","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Deborah","lastname":"O'Dell","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1962-05-16T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"200425","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"004","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2016-09-22T00:00:00.000Z","referral_date":"2016-09-12T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Avelox,Eggs or Egg-derived Products","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 14:06:55.70388+00\",)","status":null,"site_id":3,"category_id":100000,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1309,"referrer_id":12604,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"HIZENTRA COPAY ASSISTANCE\r\nDelivery Signature Opt-Out On File\r\n-permission received to text, but no email. eff 12-3-17\r\n\r\nE-Sig via text ************\r\n\r\n\r\n","billing_alert":"HIZENTRA COPAY ASSISTANCE\r\n\r\nDrug billed to RX plan\r\nSupplies billed to Ambetter Sunflower using every other week same day each week-Lisa M 1/11/24\r\n\r\nNO CMM PORTAL REQUESTS","primary_physician":null,"search":"'deborah':1A,4 'dell':3B,6 'o':2B,5","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":164,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":296,"other_allergies":null}][0m
[36m2024-11-15 05:22:09.883 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/200425_1.pdf[0m
[32m2024-11-15 05:22:09.883 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.883 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T11:04:30.633Z","change_type":null,"change_on":null,"id":2766,"change_by":null,"auto_name":"Sherrie Root","archived":null,"created_by":22,"updated_by":null,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":null,"pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Jake Christensen","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Sherrie","lastname":"Root","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1953-12-06T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"200254","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"05","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2016-01-14T00:00:00.000Z","referral_date":"2016-01-11T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Sulfa Antibiotics","marital_status":"M","surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 14:06:33.204061+00\",)","status":null,"site_id":3,"category_id":100000,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1352,"referrer_id":12582,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"**SEND SHARPS BOX WITH EACH DELIVERY**\r\nProbably wants Tues delivery.  Infuses Thursdays\r\n\r\nE-Sig <NAME_EMAIL>","billing_alert":"SWO 07/03/24 Hizentra 7gm SQ weekly Rx5 Dr Baratham-Jessica H 9/19/24\r\n\r\nPatient owned E0779 as of 06/20/2019\r\nRural","primary_physician":null,"search":"'root':2B,4 'sherrie':1A,3","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":164,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":54,"other_allergies":null}][0m
[36m2024-11-15 05:22:09.883 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/200254_1.pdf[0m
[32m2024-11-15 05:22:09.884 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.884 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T10:56:08.841Z","change_type":null,"change_on":null,"id":1905,"change_by":null,"auto_name":"Craig Fontenot","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:45:27.560Z","pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Justin Brewer","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Craig","lastname":"Fontenot","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1968-11-20T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Male","insurance_approval_time":null,"external_id":"201266","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"014","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2020-04-15T00:00:00.000Z","referral_date":"2020-04-08T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"No Known Drug Allergy","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:45:27.603807+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1327,"referrer_id":12246,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"E-Sig via text ************\r\n\r\nElectronic signature on delivery tickets always\r\n\r\n***SWO NEEDED***\t","billing_alert":"Make sure JZ modifier on J1559 line item\r\nBill drug and supplies together.. Span date out for the drug and claim line for each date on supplies.\r\n\r\nSigned order 6/13/24 Hizentra 15gm SQ weekly D4 Rx11 Dr Sugerman\r\n\r\nhave electronically signed AOB","primary_physician":null,"search":"'craig':1A,3 'fontenot':2B,4","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":1309,"other_allergies":"Bee Sting,Other - see Med Hx"}][0m
[36m2024-11-15 05:22:09.884 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201266_9.pdf[0m
[32m2024-11-15 05:22:09.884 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.884 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T10:57:07.765Z","change_type":null,"change_on":null,"id":2009,"change_by":null,"auto_name":"Elizabeth Wolter","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:47:14.664Z","pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Justin Brewer","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Elizabeth","lastname":"Wolter","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1959-02-05T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201614","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"014","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2021-11-03T00:00:00.000Z","referral_date":"2021-10-29T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Penicillins,Corticosteroids,Morphine and Related,Antihistami","marital_status":"M","surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:47:14.707679+00\",)","status":null,"site_id":14,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1597,"referrer_id":12643,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"DALLAS Profile\r\nE-signature okay: send text to ************\r\n\r\nDelivery Signature Waiver letter sent\r\n\r\nPrevious last name- Garrett","billing_alert":"Bill drug with date of shipment on one claim\r\nBill per diem with infusion date (RN visit note) on separate claim\r\n\r\nBCBS Texas Fee Schedule BlueChoice","primary_physician":null,"search":"'elizabeth':1A,3 'wolter':2B,4","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":1668,"other_allergies":"Iodine and Iodide containing products"}][0m
[36m2024-11-15 05:22:09.884 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201614_2.pdf[0m
[32m2024-11-15 05:22:09.885 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.885 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T10:52:32.313Z","change_type":null,"change_on":null,"id":1806,"change_by":null,"auto_name":"Michele Simmons","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:45:47.577Z","pediatric":null,"phone_primary":null,"diabetic":"NIDDM","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Nick Quesenberry","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Michele","lastname":"Simmons","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1948-10-09T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201323","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"011","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2020-08-06T00:00:00.000Z","referral_date":"2020-07-28T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"No Known Drug Allergy","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:45:47.621051+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":41,"referral_source_id":1479,"referrer_id":12928,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"\r\n\r\nE-Sig <NAME_EMAIL>","billing_alert":"Need SWO 6/19/24\r\nSWO 6/19/23 Hizentra 18gms SQ Q2wks Rx 12 Dr Hanissian-Lisa M 1/11/24\r\n\r\nhave AOB\r\nE0779 pump capped","primary_physician":null,"search":"'michele':1A,3 'simmons':2B,4","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":1365,"other_allergies":"No other allergies noted"}][0m
[36m2024-11-15 05:22:09.885 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201323_1.pdf[0m
[32m2024-11-15 05:22:09.885 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.885 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T10:53:12.689Z","change_type":null,"change_on":null,"id":1880,"change_by":null,"auto_name":"Teresa Gillette","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:48:05.305Z","pediatric":null,"phone_primary":null,"diabetic":"IDDM","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Bethany Trogstad","patient_status":null,"caregiver_phone":null,"email":null,"home_city":null,"firstname":"Teresa","lastname":"Gillette","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1962-02-18T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201926","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"026","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2023-12-12T00:00:00.000Z","referral_date":"2023-11-16T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Morphine Sulfate,Albuterol,Bactrim,Cipro,Ativan","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:48:05.349227+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1684,"referrer_id":12495,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"Goes by Terry\r\nDelivery Signature Waiver Letter Sent\r\nPaper DT's","billing_alert":"\r\nDrug billed to RX plan\r\nSupplies/RN visits billed to Ambetter Home State Health-Lisa M 12/13/23\r\n\r\nPrivigen 40gm IV Q2 weeks","primary_physician":null,"search":"'gillette':2B,4 'teresa':1A,3","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":2116,"other_allergies":"Shellfish,Seasonal Allergies"}][0m
[36m2024-11-15 05:22:09.885 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201926_6.pdf[0m
[32m2024-11-15 05:22:09.886 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.886 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T11:01:35.944Z","change_type":null,"change_on":null,"id":2468,"change_by":null,"auto_name":"Hunter Morgan","archived":null,"created_by":22,"updated_by":null,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":null,"pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Chris Quesenberry","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Hunter","lastname":"Morgan","emergency_phone":null,"call_time":null,"ssn":"***********","phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP1","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1995-03-27T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Male","insurance_approval_time":null,"external_id":"200476","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"007","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2016-12-23T00:00:00.000Z","referral_date":"2016-12-21T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Aspirin","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 14:07:16.104302+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":37,"referral_source_id":null,"referrer_id":12838,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"E-Sig via text ************\r\n\r\nCall at noon","billing_alert":"Drug billed to RX plan\r\nSupplies zero billed to Ambetter Home-Lisa M 1/22/24","primary_physician":null,"search":"'hunter':1A,3 'morgan':2B,4","status_id":"4","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":373,"other_allergies":null}][0m
[36m2024-11-15 05:22:09.886 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/200476_3.pdf[0m
[32m2024-11-15 05:22:09.886 [POSTGRES_CONNECTOR] INFO: Fetched 1 rows from form_patient[0m
[36m2024-11-15 05:22:09.886 [POSTGRES_CONNECTOR] DEBUG: Fetched data: [{"reviewed_by":null,"created_on":"2024-11-07T11:02:04.544Z","change_type":null,"change_on":null,"id":2515,"change_by":null,"auto_name":"Bonita Powell","archived":null,"created_by":22,"updated_by":22,"deleted":null,"change_data":null,"reviewed_on":null,"updated_on":"2024-11-09T23:45:24.881Z","pediatric":null,"phone_primary":null,"diabetic":"No","emergency_name":null,"caregiver_name":null,"medical_history":null,"is_test":null,"home_zip":null,"portal_access":null,"emergency_relationship":null,"priority":null,"middlename":null,"home_street":null,"home_street2":null,"sales_representative":"Jake Christensen","patient_status":null,"caregiver_phone":null,"email":"<EMAIL>","home_city":null,"firstname":"Bonita","lastname":"Powell","emergency_phone":null,"call_time":null,"ssn":null,"phone_home":null,"caregiver_relationship":null,"external_alert_2":null,"external_alert_1":null,"code_status":"Other","code_status_other":"Full  EP2","death_cause":null,"death_confirm":null,"phone_work":null,"dob":"1965-09-11T00:00:00.000Z","service_area":null,"phone_cell":"************","advanced_directive":null,"gender":"Female","insurance_approval_time":null,"external_id":"201249","death_date":null,"home_state":null,"facility":null,"user_id":null,"referral_source":null,"sales_code":"004","sms_opt_in":null,"email_opt_in":null,"team":null,"medications_status":null,"care_start_date":"2020-03-26T00:00:00.000Z","referral_date":"2020-03-24T00:00:00.000Z","language":"English","home_county":null,"ship_city":null,"ship_type":null,"ship_zip":null,"ship_street2":null,"ship_street":null,"ship_state":null,"contact":null,"patient_stage":null,"height":null,"is_prn":null,"weight":null,"referrer_name":null,"allergies":"Sudafed,levoFLOXacin","marital_status":null,"surgical_history":null,"cancellation_reason":null,"living_will":null,"caregiver":null,"caregiver_contact_phone":null,"pharmacy_contact_phone":null,"emergency_contact_phone":null,"category":null,"other_contact_phone":null,"emergency_contact":null,"other_contact":null,"pharmacy":null,"power_of_attorney":null,"is_prn_set_date":null,"emergency_contact_cell_phone":null,"dx_1":null,"therapy_1":null,"agency":null,"dx_name":null,"sys_period":"[\"2024-11-09 23:45:24.924882+00\",)","status":null,"site_id":3,"category_id":null,"home_state_id":null,"ship_state_id":null,"facility_id":null,"territory_id":null,"team_id":39,"referral_source_id":1289,"referrer_id":12148,"pmp_benefits_optout":null,"mrn":null,"external_ids":null,"identify_gender":null,"ibw":null,"lbw":null,"bsa":null,"power_of_attorney_details":null,"location":null,"ship_sameas":null,"ship_to":null,"clinical_alert":"Delivery Signature Opt-out On File\r\n\r\nE-Sig via text ************\r\n","billing_alert":"Medicare BFD primary as of 2/1/24\r\nBCBS KS secondary as of 2/1/24-Lisa M 2/12/24\r\n\r\nSWO 11/1/23 Hizentra 17gms SQ every other week D2 Rx6 Dr Hobson\r\nHave AOB\r\nHave ABN w/option 1 checked, signed by pt 2/14/24\r\nE0779RRKH 2/8/24","primary_physician":null,"search":"'bonita':1A,3 'powell':2B,4","status_id":"3","primary_physician_id":null,"status_code":null,"ship_location":null,"health_declaration":null,"tab_if_test_rk":null,"tabif_":null,"stripe_id":null,"braintree_id":null,"authnet_id":null,"cust_therapy_management":null,"cust_therapy_management_date":null,"phone_other":null,"cust_phone_message":null,"cust_call_perm":null,"cust_health_perm":null,"cust_contacts":null,"cust_phone_text":null,"cust_elec_perm":null,"cust_dpoa":null,"cust_dpoa_who":null,"careteam_pharmacist_id":null,"careteam_nurse_id":null,"careteam_pt_advocate_id":null,"measurement_log":null,"embed_contacts":null,"embed_address":null,"advanced_directives_details":null,"envoy_external_id":1292,"other_allergies":"No other allergies noted"}][0m
[36m2024-11-15 05:22:09.886 [PatientDocumentsTransformer] DEBUG: Checking full local path: /mnt/cprplus/202401/201249_4.pdf[0m
