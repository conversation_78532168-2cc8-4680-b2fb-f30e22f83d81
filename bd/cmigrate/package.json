{"name": "cmigrate", "version": "1.0.0", "description": "Migration toold from existing systems to Clara.", "main": "node cmigrate.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.682.0", "@eslint/js": "^9.19.0", "axios": "^1.7.7", "cli-table3": "^0.6.5", "cmigrate": "link:", "commander": "^12.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsonc": "^2.16.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-yml": "^1.14.0", "globals": "^15.11.0", "lodash": "^4.17.21", "mime": "^4.0.4", "mime-types": "^2.1.35", "moment": "^2.30.1", "node-color-log": "^12.0.1", "pg": "^8.13.0", "uuid": "^11.0.2", "word-wrap": "^1.2.5", "yaml": "^2.6.0"}, "devDependencies": {"eslint": "^9.13.0"}}