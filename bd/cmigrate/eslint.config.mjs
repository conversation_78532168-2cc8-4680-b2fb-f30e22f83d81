// base lint rules for javascript
import eslint from "@eslint/js";
import globals from "globals";

// syntax check plugins for: json, yaml
import json from "eslint-plugin-jsonc";
import yml from "eslint-plugin-yml";

// prettier plugin for formatting
import prettier from "eslint-plugin-prettier";
import prettierRecommended from "eslint-plugin-prettier/recommended";

export default [
  {
    ignores: [
      "**/sharedState.json",
      "pnpm-lock.yaml",
      "node_modules/**",
      "dist/**",
      "coverage/**",
      "config/*.yaml",
      "config/*.yml",
      "**/fixtures/**",
    ],
  },
  // follow recommended eslint rules
  eslint.configs.recommended,
  {
    files: ["**/*.js"],
    languageOptions: {
      sourceType: "commonjs",
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
    plugins: {
      prettier: prettier,
    },
    rules: {
      "no-unused-vars": [
        "warn",
        {
          varsIgnorePattern: "^_",
          argsIgnorePattern: "^_",
        },
      ],
      "constructor-super": "warn",
      "no-console": "off",
      "no-const-assign": "error",
      "no-new-require": "error",
      "no-prototype-builtins": "off",
      "no-this-before-super": "warn",
      "no-undef": "error",
      "no-unreachable": "error",
      "no-var": "warn",
      "prefer-const": "warn",
      "prettier/prettier": "error",
      "valid-typeof": "warn",
    },
  },
  {
    files: ["**/*.json"],
    languageOptions: {
      parser: json,
    },
    rules: {
      "prettier/prettier": "error",
    },
  },
  {
    files: ["**/*.yaml", "**/*.yml"],
    plugins: {
      yml: yml,
    },
    rules: {
      ...yml.configs.recommended.rules,
      "prettier/prettier": "error",
    },
  },
  // Prettier to handle conflicts with other configs
  prettierRecommended,
];
