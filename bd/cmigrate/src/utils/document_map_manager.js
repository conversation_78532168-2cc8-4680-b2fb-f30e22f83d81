const Logger = require("./logger");
const logger = new Logger("DocumentMapManager");
const fs = require("fs").promises;
const path = require("path");

class DocumentMapManager {
  constructor(options = {}) {
    this.documentMap = new Map();
    this.processedCount = 0;
    this.persistPath =
      options.persistPath ||
      path.join(__dirname, "../fixtures/document_map.json");
    this.saveInterval = options.saveInterval || 1000;
    this.lastSave = Date.now();
    this.initialized = false;
    this.batchSize = options.batchSize || 1000;
    this.pendingWrites = new Map();
    this._persisting = null;
    this.backupInterval = options.backupInterval || 1000 * 60 * 5; // 5 minutes
    this.lastBackup = Date.now();
  }

  async initialize() {
    if (this.initialized) return;

    // Create backup before loading new map
    await this.createBackup();
    await this.loadPersistedMap();
    this.initialized = true;
  }

  async createBackup() {
    try {
      // Check if main file exists
      const exists = await fs
        .access(this.persistPath)
        .then(() => true)
        .catch(() => false);

      if (exists) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
        const backupPath = `${this.persistPath}.${timestamp}.backup`;

        // Read current map
        const data = await fs.readFile(this.persistPath, "utf8");

        // Write to backup file
        await fs.writeFile(backupPath, data, "utf8");
        logger.info(`Created document map backup at ${backupPath}`);

        // Keep only last 5 backups
        const dir = path.dirname(this.persistPath);
        const files = await fs.readdir(dir);
        const backups = files
          .filter(
            (f) =>
              f.startsWith(path.basename(this.persistPath)) &&
              f.endsWith(".backup"),
          )
          .sort()
          .reverse();

        // Remove old backups
        if (backups.length > 5) {
          for (const oldBackup of backups.slice(5)) {
            await fs
              .unlink(path.join(dir, oldBackup))
              .catch((err) =>
                logger.warn(
                  `Failed to remove old backup ${oldBackup}: ${err.message}`,
                ),
              );
          }
        }
      }
    } catch (error) {
      logger.error(`Error creating backup: ${error.message}`);
    }
  }

  async loadPersistedMap() {
    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(this.persistPath), { recursive: true });

      // Check if file exists
      const exists = await fs
        .access(this.persistPath)
        .then(() => true)
        .catch(() => false);

      if (exists) {
        try {
          const data = await fs.readFile(this.persistPath, "utf8");
          if (!data.trim()) {
            logger.info("Empty map file found, initializing new map");
            await fs.writeFile(this.persistPath, "{}", "utf8");
            return;
          }
          const parsed = JSON.parse(data);

          logger.info(`Loading document map from ${this.persistPath}`);
          logger.debug(
            `Current map size before load: ${this.documentMap.size}`,
          );

          Object.entries(parsed).forEach(([key, value]) => {
            this.documentMap.set(key, value);
          });

          logger.info(
            `Loaded ${this.documentMap.size} entries from persisted map at ${this.persistPath}`,
          );
        } catch (error) {
          if (error instanceof SyntaxError) {
            logger.warn("Invalid JSON in map file, initializing new map");
            await fs.writeFile(this.persistPath, "{}", "utf8");
          } else {
            throw error;
          }
        }
      } else {
        logger.info(
          `No existing map found at ${this.persistPath}, creating new map`,
        );
        await fs.writeFile(this.persistPath, "{}", "utf8");
      }
    } catch (error) {
      logger.error(`Error loading persisted map: ${error.message}`);
      throw error;
    }
  }

  async persistMap() {
    try {
      // Create periodic backup
      if (Date.now() - this.lastBackup > this.backupInterval) {
        await this.createBackup();
        this.lastBackup = Date.now();
      }

      // Add a lock to prevent concurrent writes
      if (this._persisting) {
        logger.debug("Persist already in progress, waiting...");
        await this._persisting;
        return;
      }

      this._persisting = (async () => {
        // Read existing map if it exists
        let existingData = {};
        try {
          const exists = await fs
            .access(this.persistPath)
            .then(() => true)
            .catch(() => false);

          if (exists) {
            const data = await fs.readFile(this.persistPath, "utf8");
            existingData = JSON.parse(data);
            logger.debug(
              `Loaded ${Object.keys(existingData).length} existing entries`,
            );
          }
        } catch (error) {
          logger.warn(`Could not read existing map: ${error.message}`);
        }

        // Get pending writes
        const pendingData = Object.fromEntries(this.pendingWrites);

        // Merge with existing data
        const mergedData = { ...existingData };

        Object.entries(pendingData).forEach(([key, newValue]) => {
          const existingValue = existingData[key];
          if (
            !existingValue ||
            JSON.stringify(existingValue) !== JSON.stringify(newValue)
          ) {
            mergedData[key] = newValue;
            logger.debug(`Adding/Updating entry for key: ${key}`);
          }
        });

        // Write merged data back to file
        await fs.writeFile(
          this.persistPath,
          JSON.stringify(mergedData, null, 2),
          "utf8",
        );

        // Clear pending writes after successful persist
        this.pendingWrites.clear();

        this.lastSave = Date.now();
        logger.info(
          `Persisted ${Object.keys(mergedData).length} entries to ${this.persistPath}`,
        );

        // Update in-memory map with merged data
        this.documentMap = new Map(Object.entries(mergedData));
      })();

      await this._persisting;
      this._persisting = null;
    } catch (error) {
      logger.error(`Error persisting map: ${error.message}`);
      throw error;
    }
  }

  async addDocument(key, data) {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }

    // Check if document already exists with same data
    const existingDoc = this.documentMap.get(key);
    if (existingDoc && JSON.stringify(existingDoc) === JSON.stringify(data)) {
      logger.debug(`Skipping duplicate document: ${key}`);
      return;
    }

    // Add to pending writes
    this.pendingWrites.set(key, data);

    // Update in-memory map immediately
    this.documentMap.set(key, data);
    this.processedCount++;

    if (this.processedCount % 1000 === 0) {
      logger.info(`Processed ${this.processedCount} documents`);
    }

    // Auto-persist if we've hit the batch size or enough time has passed
    if (
      this.pendingWrites.size >= this.batchSize ||
      Date.now() - this.lastSave > this.saveInterval
    ) {
      await this.persistMap(); // Wait for persist to complete
    }
  }

  async addDocumentBatch(entries) {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }

    let newEntries = 0;
    for (const [key, data] of entries) {
      const existingDoc = this.documentMap.get(key);
      if (
        !existingDoc ||
        JSON.stringify(existingDoc) !== JSON.stringify(data)
      ) {
        this.pendingWrites.set(key, data);
        this.documentMap.set(key, data);
        newEntries++;
      }
    }

    if (newEntries > 0) {
      logger.info(`Added ${newEntries} new entries to pending writes`);
      this.processedCount += newEntries;

      if (this.pendingWrites.size >= this.batchSize) {
        await this.persistMap();
      }
    }
  }

  getDocument(key) {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }
    return this.documentMap.get(key);
  }

  hasDocument(key) {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }
    return this.documentMap.has(key);
  }

  clear() {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }
    this.documentMap.clear();
    this.processedCount = 0;
  }

  getStats() {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }
    return {
      totalDocuments: this.documentMap.size,
      processedCount: this.processedCount,
    };
  }

  // Method to manually trigger persistence
  async forcePersist() {
    if (!this.initialized) {
      throw new Error(
        "DocumentMapManager not initialized. Call initialize() first",
      );
    }
    await this.persistMap();
  }

  // Method to get pending changes count
  getPendingCount() {
    return this.pendingWrites.size;
  }

  // Method to force persist if there are any pending changes
  async flushPending() {
    if (this.pendingWrites.size > 0) {
      await this.persistMap();
    }
  }

  // Method to restore from a specific backup
  async restoreFromBackup(backupPath) {
    try {
      const data = await fs.readFile(backupPath, "utf8");
      const parsed = JSON.parse(data);

      // Clear current map
      this.documentMap.clear();
      this.pendingWrites.clear();

      // Load from backup
      Object.entries(parsed).forEach(([key, value]) => {
        this.documentMap.set(key, value);
      });

      // Save restored data as current
      await this.persistMap();

      logger.info(`Successfully restored from backup ${backupPath}`);
      logger.info(`Restored ${this.documentMap.size} entries`);
    } catch (error) {
      logger.error(`Error restoring from backup: ${error.message}`);
      throw error;
    }
  }

  // Method to list available backups
  async listBackups() {
    try {
      const dir = path.dirname(this.persistPath);
      const files = await fs.readdir(dir);
      return files
        .filter(
          (f) =>
            f.startsWith(path.basename(this.persistPath)) &&
            f.endsWith(".backup"),
        )
        .map((f) => ({
          path: path.join(dir, f),
          timestamp: f.split(".")[1],
        }))
        .sort((a, b) => b.timestamp.localeCompare(a.timestamp));
    } catch (error) {
      logger.error(`Error listing backups: ${error.message}`);
      return [];
    }
  }
}

module.exports = DocumentMapManager;
