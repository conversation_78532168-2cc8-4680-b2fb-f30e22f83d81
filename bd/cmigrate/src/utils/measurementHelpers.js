const moment = require("moment");
const calculateIBW = (heightInCm, gender) => {
  if (!heightInCm || !gender) return null;
  const heightInInches = heightInCm / 2.54;
  return gender === "Male"
    ? 50 + 2.3 * (heightInInches - 60)
    : 45.5 + 2.3 * (heightInInches - 60);
};

const calculateLBW = (weightInKg, heightInCm, gender) => {
  if (!weightInKg || !heightInCm || !gender) return null;
  return gender === "Male"
    ? 0.407 * weightInKg + 0.267 * heightInCm - 19.2
    : 0.252 * weightInKg + 0.473 * heightInCm - 48.3;
};

const calculateBSA = (weightInKg, heightInCm) => {
  if (!weightInKg || !heightInCm) return null;
  return Math.sqrt((heightInCm * weightInKg) / 3600);
};

const calculateIBWPercent = (actualWeightKg, ibw) => {
  if (!actualWeightKg || !ibw) return null;
  return (actualWeightKg / ibw) * 100;
};

const calculateABW = (actualWeightKg, idealBodyWeight) => {
  if (!actualWeightKg || !idealBodyWeight) return null;
  return idealBodyWeight + 0.4 * (actualWeightKg - idealBodyWeight);
};

const calculateAge = (dob) => {
  if (!dob) return null;
  const parsedDate = moment(
    dob,
    ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD"],
    true,
  );

  if (!parsedDate.isValid()) {
    console.warn(`Invalid date format: ${dob}`);
    return null;
  }

  return moment().diff(parsedDate, "years");
};

module.exports = {
  calculateIBW,
  calculateLBW,
  calculateBSA,
  calculateIBWPercent,
  calculateABW,
  calculateAge,
};
