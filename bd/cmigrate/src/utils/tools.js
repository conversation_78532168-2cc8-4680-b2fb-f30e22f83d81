const Logger = require("./logger");
const logger = new Logger("tools");
const moment = require("moment");
const _ = require("lodash");
const { Maps } = require("./maps"); // Use destructuring to get Maps directly

async function map_physician_id(conn, data) {
  const npi = await conn.dst.fetchData(
    `SELECT npi FROM cpr_doctors WHERE no = '${data.ph_no}'`,
  );
  if (npi) {
    data.physician_id = npi.id;
  } else {
    logger.warn(`No physician found for ${data.ph_no}`);
  }
  return data;
}

async function fetch_or_create(db, table, data, filter) {
  const existing = await db.fetchData(`SELECT * FROM ${table} WHERE ${filter}`);
  if (existing) {
    logger.info(`${table} with ${filter} already exists, using existing id`);
    return existing[0].id;
  }
  logger.info(`${table} with ${filter} does not exist, creating new record`);
  const new_id = await db.insertData(table, data);
  return new_id;
}

function isEmptyValue(value) {
  return (
    value === null ||
    value === undefined ||
    value === "" ||
    (typeof value === "number" && isNaN(value)) ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === "object" &&
      value !== null &&
      !(value instanceof Date) &&
      Object.keys(value).length === 0) ||
    value === "{}"
  );
}

async function toDate(dateValue) {
  if (isEmptyValue(dateValue)) {
    return null;
  }
  try {
    let parsedDate;
    if (dateValue instanceof Date) {
      parsedDate = moment(dateValue);
    } else {
      parsedDate = moment(
        dateValue,
        [
          "MM/DD/YYYY",
          "MM/DD/YYYY HH:mm:ss",
          moment.ISO_8601,
          "YYYY-MM-DD",
          "YYYY-MM-DD HH:mm:ss",
        ],
        true,
      );
    }

    if (parsedDate.isValid()) {
      let formattedDate;
      if (parsedDate.hour() === 0 && parsedDate.minute() === 0) {
        formattedDate = parsedDate.format("MM/DD/YYYY");
      } else {
        formattedDate = parsedDate.format("MM/DD/YYYY HH:mm:ss");
      }
      logger.debug(`Parsed date ${dateValue} to ${formattedDate}`);
      return formattedDate;
    }
    logger.warn(`Invalid date format for value: ${dateValue}`);
    return null;
  } catch (error) {
    logger.error(`Error parsing date value: ${dateValue} ${error.stack}`);
    return null;
  }
}

function mapData(value, mapName) {
  if (isEmptyValue(value)) {
    return null;
  }
  if (!Object.keys(Maps).includes(mapName)) {
    logger.warn(`mapData called with unknown map: ${mapName}`);
    return value;
  }
  const map = Maps[mapName];
  const mappedValue = map && map[value] !== undefined ? map[value] : value;
  logger.debug(
    `mapData called with value: ${value}, map: ${mapName}, mappedValue: ${mappedValue}`,
  );
  return mappedValue;
}

function format_phone(phone) {
  if (isEmptyValue(phone)) return null;

  // Convert to string and remove all non-numeric characters
  const digits = String(phone).replace(/\D/g, "");

  // If no digits found, return null
  if (digits.length === 0) return null;

  if (digits.length === 10) {
    return digits.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  }
  if (digits.length === 7) {
    return digits.replace(/(\d{3})(\d{4})/, "$1-$2");
  }

  // If already formatted correctly, return as is
  if (/^\(\d{3}\) \d{3}-\d{4}$/.test(phone.trim())) {
    return phone.trim();
  }

  // If we get here and have digits, format what we have
  return digits.trim() || null;
}

async function cleanup_nulls(data) {
  if (typeof data !== "object" || data === null) {
    // Handle string trimming for non-object values
    return typeof data === "string" ? data.trim() : data;
  }

  const cleanData = Array.isArray(data) ? [] : {};

  for (const [key, value] of Object.entries(data)) {
    // Handle null, undefined, empty strings, and "null" strings
    if (
      value === null ||
      value === undefined ||
      value === "" ||
      value === "null"
    ) {
      continue;
    }

    if (typeof value === "object") {
      const cleanedValue = await cleanup_nulls(value);
      if (Object.keys(cleanedValue).length > 0) {
        cleanData[key] = cleanedValue;
      }
    } else if (typeof value === "string") {
      // Trim string values and convert empty strings to null
      const trimmed = value.trim();
      if (trimmed !== "") {
        cleanData[key] = trimmed;
      }
    } else {
      cleanData[key] = value;
    }
  }

  return cleanData;
}

function isEmptyOrUndefined(value) {
  return value === undefined || value === null || value === "";
}

async function has_changed(oldData, newData) {
  logger.debug(`Comparing old and new data:`);
  logger.debug(`Old: ${JSON.stringify(oldData)}`);
  logger.debug(`New: ${JSON.stringify(newData)}`);

  if (!oldData || !newData) {
    logger.debug("One of the objects is null/undefined");
    return true;
  }
  if (isEmptyValue(oldData) && isEmptyValue(newData)) {
    logger.debug("Both objects are empty/undefined, no changes detected");
    return false;
  }
  // Only compare fields that exist in newData
  const fieldsToCompare = Object.keys(newData);

  for (const key of fieldsToCompare) {
    let oldValue = oldData[key];
    let newValue = newData[key];

    // Try to parse JSON strings for comparison
    if (typeof oldValue === "string" && typeof newValue === "object") {
      try {
        oldValue = JSON.parse(oldValue);
        logger.debug(
          `Parsed oldValue from JSON string: ${JSON.stringify(oldValue)}`,
        );
      } catch (e) {
        logger.debug(`Failed to parse oldValue as JSON: ${e.message}`);
      }
    } else if (typeof newValue === "string" && typeof oldValue === "object") {
      try {
        newValue = JSON.parse(newValue);
        logger.debug(
          `Parsed newValue from JSON string: ${JSON.stringify(newValue)}`,
        );
      } catch (e) {
        logger.debug(`Failed to parse newValue as JSON: ${e.message}`);
      }
    }

    // Sort arrays of objects for comparison
    if (Array.isArray(oldValue) && Array.isArray(newValue)) {
      const sortByKey = (a, b) => {
        return (a.rank || 0) - (b.rank || 0);
      };

      oldValue = [...oldValue].sort(sortByKey);
      newValue = [...newValue].sort(sortByKey);

      logger.debug(`Comparing sorted arrays for ${key}:`);
      logger.debug(`Old (sorted): ${JSON.stringify(oldValue)}`);
      logger.debug(`New (sorted): ${JSON.stringify(newValue)}`);
    }

    // If both values are effectively empty, no change
    if (isEmptyValue(oldValue) && isEmptyValue(newValue)) {
      logger.debug(`  Both values are effectively empty - no change`);
      continue;
    }

    // If new value is empty and old value exists, preserve old value
    if (isEmptyValue(newValue) && !isEmptyValue(oldValue)) {
      logger.debug(
        `  New value is empty but old value exists - preserving old value`,
      );
      delete newData[key];
      continue;
    }

    // Skip system fields
    if (
      [
        "id",
        "created_by",
        "created_on",
        "updated_by",
        "updated_on",
        "sys_period",
        "search",
      ].includes(key)
    ) {
      logger.debug(`Skipping system field: ${key}`);
      continue;
    }

    logger.debug(`Comparing field ${key}:`);
    logger.debug(`  Old value: ${JSON.stringify(oldValue)}`);
    logger.debug(
      `  New value: ${newValue ? JSON.stringify(newValue) : "null"}`,
    );

    // If both values are empty or undefined, consider them equal
    if (isEmptyOrUndefined(oldValue) && isEmptyOrUndefined(newValue)) {
      logger.debug(`  Both values are empty/undefined - continuing`);
      continue;
    }

    // If one value is empty/undefined and the other isn't, consider it a change
    if (isEmptyOrUndefined(oldValue) !== isEmptyOrUndefined(newValue)) {
      logger.debug(
        `  One value is empty and the other isn't - change detected`,
      );
      return true;
    }

    // Special handling for dates
    if (
      isDateString(oldValue) ||
      isDateString(newValue) ||
      oldValue instanceof Date ||
      newValue instanceof Date
    ) {
      const oldMoment = moment(oldValue);
      const newMoment = moment(newValue);

      if (oldMoment.isValid() && newMoment.isValid()) {
        // Compare dates ignoring timezone
        const isSameDate = oldMoment
          .startOf("day")
          .isSame(newMoment.startOf("day"));
        logger.debug(
          `  Comparing dates: ${oldMoment.format()} vs ${newMoment.format()}`,
        );
        logger.debug(`  Dates are ${isSameDate ? "equal" : "different"}`);
        if (!isSameDate) {
          return true;
        }
        continue;
      }
    }

    // Special handling for number/string comparisons
    if (
      (typeof oldValue === "number" || typeof oldValue === "string") &&
      (typeof newValue === "number" || typeof newValue === "string")
    ) {
      const normalizedOld = String(oldValue).trim();
      const normalizedNew = String(newValue).trim();
      if (normalizedOld === normalizedNew) {
        logger.debug(`  Values are equal after normalization - continuing`);
        continue;
      }
      // Try number comparison if both can be converted to numbers
      if (!isNaN(normalizedOld) && !isNaN(normalizedNew)) {
        const numOld = Number(normalizedOld);
        const numNew = Number(normalizedNew);

        // Use small epsilon for floating point comparison
        const epsilon = 0.000001;
        if (Math.abs(numOld - numNew) < epsilon) {
          logger.debug(
            `  Values are equal after floating point comparison - continuing`,
          );
          continue;
        }

        // Try rounding to 4 decimal places for currency values
        if (
          Math.abs(Math.round(numOld * 10000) - Math.round(numNew * 10000)) <
          epsilon
        ) {
          logger.debug(
            `  Values are equal after currency rounding - continuing`,
          );
          continue;
        }
      }
    }

    // For non-empty values, use deep equality check
    if (!_.isEqual(oldValue, newValue)) {
      logger.warn(`  Values are different - change detected`);
      return true;
    }
  }

  logger.warn("No changes detected");
  return false;
}

function isDateString(value) {
  if (typeof value !== "string") return false;
  const momentDate = moment(value, DATE_FORMATS, true);
  return momentDate.isValid();
}

const DATE_FORMATS = [
  "MM/DD/YYYY hh:mm:ss A",
  "MM/DD/YYYY HH:mm:ss",
  "MM/DD/YYYY",
  "MM/DD/YY",
  "YYYY/MM/DD",
  "YYYY-MM-DDTHH:mm:ss",
  "YYYY-MM-DD HH:mm:ss",
];

async function to_lower(value) {
  return value.toLowerCase();
}

async function _invertBoolean(value) {
  return !value;
}

async function format_checkbox(data) {
  logger.debug(`format_checkbox called with data: ${JSON.stringify(data)}`);
  if (isEmptyValue(data)) {
    return null;
  }

  const items = Array.isArray(data) ? data : [data];

  const formattedItems = items
    .map((item) => item.toString().trim())
    .filter((item) => item !== "");

  if (formattedItems.length === 0) {
    return null;
  }

  // Create the literal string format
  const result = `{${formattedItems.join(",")}}`;

  logger.debug(`format_checkbox returning: ${result}`);
  return result;
}

function checkboxField(sourceField, invert = false) {
  return async (data) => {
    let value = data[sourceField];

    // Handle different input types
    if (typeof value === "string") {
      value = value.toLowerCase();
    }

    // Use Maps.YnMap for consistent mapping
    value = mapData(Maps.YnMap, value);

    if (invert) {
      value = !value;
    }

    if (isEmptyValue(value)) {
      return null;
    }

    return await format_checkbox(value ? ["Yes"] : []);
  };
}

function ynField(sourceField, invert = false) {
  logger.debug(
    `ynField called with sourceField: ${sourceField}, invert: ${invert}`,
  );
  return async (data) => {
    let value = data[sourceField];
    value = mapData(value, "ynmap");
    if (invert) {
      value = !value;
    }
    // Return "Yes" if true, null if false
    return value === true ? "Yes" : null;
  };
}

module.exports = {
  _invertBoolean,
  cleanup_nulls,
  fetch_or_create,
  format_phone,
  has_changed,
  isDateString,
  isEmptyValue,
  map_physician_id,
  mapData,
  toDate,
  to_lower,
  format_checkbox,
  checkboxField,
  ynField,
};
