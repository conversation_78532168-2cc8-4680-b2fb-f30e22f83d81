const Logger = require("./logger");
const logger = new Logger("Mapper");
const Maps = {
  ynmap: {
    1: true,
    0: false,
    t: true,
    f: false,
    y: true,
    n: false,
    yes: true,
    no: false,
    true: true,
    false: false,
  },

  YnReverseMap: {
    Yes: 1,
    No: 0,
  },

  DelFlagMap: {
    1: true,
    0: false,
  },

  SiteMap: {
    1: 2,
    2: 4,
  },
  ActivityLevelMap: {
    "Up w/ Wlkr": "Up with walker",
    "Bedrest b/t pri": "Bedrest between ???",
    "Up w/crutches o": "Up with crutches",
    "Up as tolerated": "Up as tolerated",
    "Up w/ Walker": "Up with walker",
    Sedentary: "Sedentary",
    "Up w/ assist": "Up with assistance",
    "Up Ad Lib": "Up ad lib",
    Newborn: "Newborn",
    "Up Ad Lib, whee": "Up ad lib with wheelchair",
    Wheelchair: "Wheelchair",
    "Electric Wheelc": "Wheelchair, Electric",
    "BR w/ BRPs": "Bedrest with Bathroom Privileges",
    "Bed Rest": "Bed rest",
    Infant: "Infant",
    "Max assist": "Maximum assistance",
    "Up w/ wheelchai": "Up with wheelchair",
  },

  DiabeticMap: {
    niddm: "NIDDM",
    iddm: "IDDM",
    no: "No",
  },

  TherapyTypeMap: {
    // Antibiotics
    abx: "antibiotic",
    "iv-abx": "antibiotic",
    "anti-inf": "antibiotic",

    // Hepatitis and HIV
    "hep b": "hepatitis_b",
    "hep c": "hepatitis_c",
    hiv: "hiv",

    // Neurological
    ms: "multiple_sclerosis",
    neuro: "neuro",

    // Cardiovascular
    cardio: "cardio",
    htn: "cardio",
    anticoag: "factor",

    // Dermatological
    derm: "dermatology",

    // Diabetes and Weight Management
    diabetes: "diabetes",
    "wt loss": "weight_loss",

    // Oncology
    oncology: "oncology",
    chemo: "oncology",

    // Immunoglobulins
    ivig: "ig",
    scig: "ig",

    // Biologics/MABs
    biologic: "biologic",
    mab: "mono",
    "crohn/uc": "tnf",
    rheum: "tnf",
    anemia: "biologic",
    enzyme: "biologic",
    fertilit: "biologic",
    hgh: "biologic",
    iron: "biologic",
    nephrolo: "biologic",
    ophthal: "biologic",
    osteoart: "biologic",
    vaccine: "biologic",

    // Blood factors
    factor: "factor",
    hemophil: "factor",

    // Nutrition
    "iv-tpn": "tpn",

    // Steroids
    allergy: "steroid",

    // Bone health
    osteopor: "bisphosphonates",

    // Support medications
    "pre-med": "premedication",
    anaphyla: "premedication",
    flush: "flush",
    diluent: "flush",
    supply: "flush",

    // Respiratory
    pulmonar: "asthma",
    asthma: "asthma",

    // Special cases
    vyvgart: "vyvgart",

    // Map remaining to other
    compound: "other",
    gastro: "other",
    "organ tx": "other",
    other: "other",
    urology: "other",
    narcotic: "other",
  },

  PayorMap: {
    medicare: "Medicare",
    medicaid: "Medicaid",
    "part d": "Medicare",
  },

  CodeStatusMap: {
    "n/a": "N/A",
    full: "Full",
    dnr: "DNR",
  },

  MedicationStatus: {
    active: "Active",
    "dc'd": "Discontinued",
    pending: "Pending",
  },

  PartTypeMap: {
    D: "Drug",
    C: "Compounded Drug",
    I: "Compound Ingredient",
    S: "Supply",
    R: "Equipment",
    O: "Non-Inventory Billable",
  },

  PatientStatusMap: {
    Active: "Active",
    Cancelled: "Cancelled",
    Inactive: "Inactive",
    "On Hold": "On Hold",
    Pending: "Pending",
  },

  PrimaryFlagMap: {
    P: "Primary",
  },

  TherapyPriority: {
    ivig: 7,
    tnf: 6,
    factor: 5,
    tpn: 4,
    steroid: 3,
    methyl: 2,
    antibiotic: 1,
  },

  ContactType: {
    P: "Primary",
    E: "Emergency",
    B: "Both",
  },

  MaritalStatusMap: {
    M: "Married",
    S: "Single",
    W: "Widowed",
    D: "Divorced",
  },

  DispenseTypeMap: {
    I: "Initial",
    R: "Refill",
    Initial: "I",
    Refill: "R",
  },
  InventoryUnitMap: {
    181: "mcg",
    245: "syringe",
    272: "unit",
    301: "mL",
    477: "mL",
    480: "mL",
    515: "mL",
    516: "mL",
    592: "mL",
    794: "mg",
    804: "tablet or capsule",
    887: "gram",
    888: "gram",
    889: "gram",
    890: "gram",
    915: "gram",
    916: "gram",
    917: "gram",
    919: "gram",
    920: "gram",
    934: "gram",
    937: "gram",
    938: "gram",
    947: "syringe",
    1068: "cap",
    1091: "tablet or capsule",
    1132: "gram",
    1133: "gram",
    1185: "mL",
    1231: "unit",
    1321: "mL",
    1368: "each",
    1408: "unit",
    1409: "unit",
    1492: "unit",
    1510: "unit",
    1524: "mg",
    1527: "mg",
    1528: "mg",
    1529: "mg",
    1530: "mg",
    1565: "mg",
    1580: "unit",
    1581: "unit",
    1607: "tablet or capsule",
    1612: "tablet or capsule",
    1618: "vial",
    1632: "unit",
    1660: "unit",
    1676: "mL",
    1680: "mg",
    1683: "tablet or capsule",
    1692: "unit",
    1694: "unit",
    1715: "unit",
    1727: "unit",
    1732: "vial",
    1736: "mcg",
    1756: "mg",
    1763: "tablet or capsule",
    1771: "tablet or capsule",
    1776: "unit",
    1778: "unit",
    1785: "container",
    1800: "gram",
    1801: "gram",
    1823: "tablet or capsule",
    1837: "tablet or capsule",
    1871: "unit",
    1888: "container",
    1901: "tablet or capsule",
    1920: "mg",
    1933: "mcg",
    1934: "mg",
    1954: "tablet or capsule",
    1962: "mg",
    1969: "gram",
    1985: "gram",
    2100: "mL",
    2102: "gram",
    2105: "mg",
    2124: "gram",
    2125: "mg",
    2126: "mg",
    2171: "cap",
    2188: "each",
    2194: "unit",
    2195: "unit",
    2198: "unit",
    2199: "unit",
    2201: "tablet or capsule",
    2207: "mg",
    2222: "syringe",
    2223: "syringe",
    2234: "cap",
    2278: "gram",
    2282: "mg",
    2283: "gram",
    2284: "gram",
    2287: "gram",
    2300: "gram",
    2309: "cap",
    2319: "tablet or capsule",
  },
  DispenseUnitMap: {
    181: "mcg",
    245: "syringe",
    272: "unit",
    301: "mL",
    477: "mL",
    480: "mL",
    515: "mL",
    516: "mL",
    592: "mL",
    794: "mg",
    804: "tablet or capsule",
    887: "gram",
    888: "gram",
    889: "gram",
    890: "gram",
    915: "gram",
    916: "gram",
    917: "gram",
    919: "gram",
    920: "gram",
    934: "gram",
    937: "gram",
    938: "gram",
    947: "mL",
    1068: "cap",
    1091: "tablet or capsule",
    1132: "gram",
    1133: "gram",
    1185: "mL",
    1231: "unit",
    1321: "mL",
    1368: "each",
    1408: "unit",
    1409: "unit",
    1492: "unit",
    1510: "unit",
    1524: "mg",
    1527: "mg",
    1528: "mg",
    1529: "mg",
    1530: "mg",
    1565: "mg",
    1580: "unit",
    1581: "unit",
    1607: "tablet or capsule",
    1612: "tablet or capsule",
    1618: "vial",
    1632: "unit",
    1660: "unit",
    1676: "mL",
    1680: "mg",
    1683: "tablet or capsule",
    1692: "unit",
    1694: "unit",
    1715: "unit",
    1727: "unit",
    1732: "vial",
    1736: "mcg",
    1756: "mg",
    1763: "tablet or capsule",
    1771: "tablet or capsule",
    1776: "unit",
    1778: "unit",
    1785: "container",
    1800: "gram",
    1801: "gram",
    1823: "tablet or capsule",
    1837: "tablet or capsule",
    1871: "unit",
    1888: "container",
    1901: "tablet or capsule",
    1920: "mg",
    1933: "mcg",
    1934: "mg",
    1954: "tablet or capsule",
    1962: "mg",
    1969: "gram",
    1985: "gram",
    2100: "mL",
    2102: "gram",
    2105: "mg",
    2124: "gram",
    2125: "mg",
    2126: "mg",
    2171: "cap",
    2188: "each",
    2194: "unit",
    2195: "unit",
    2198: "unit",
    2199: "unit",
    2201: "tablet or capsule",
    2207: "mg",
    2222: "mg",
    2223: "mg",
    2234: "cap",
    2278: "gram",
    2282: "mg",
    2283: "gram",
    2284: "gram",
    2287: "gram",
    2300: "gram",
    2309: "cap",
    2319: "tablet or capsule",
  },

  DoseUnitMap: {
    bottle: "container",
    capsule: "cap",
    each: "each",
    gm: "gram",
    mcg: "mcg",
    mg: "mg",
    ml: "mL",
    mL: "mL",
    syringe: "syringe",
    tab: "tablet or capsule",
    units: "unit",
    vial: "vial",
  },
};

// Helper function to get value from map with default
const getMapValue = (mapName, key, defaultValue = undefined) => {
  logger.info(`getting map value for ${mapName} with key ${key}`);
  const map = Maps[mapName];
  return key in map
    ? map[key]
    : defaultValue !== undefined
      ? defaultValue
      : null;
};

const getTherapyPriority = (old, new_) => {
  const oldPriority = getMapValue("TherapyPriority", old.toLowerCase(), 0);
  const newPriority = getMapValue("TherapyPriority", new_.toLowerCase(), 0);
  return oldPriority > newPriority ? old : new_;
};

module.exports = {
  Maps,
  getMapValue,
  getTherapyPriority,
};
