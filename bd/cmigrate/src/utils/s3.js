const {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
} = require("@aws-sdk/client-s3");
const Logger = require("./logger");
const logger = new Logger("S3Client");
const { loadConfig } = require("../core/config");
const mimeTypes = require("mime-types");
const uuid = require("uuid");
const crypto = require("crypto");

class S3Manager {
  constructor(isDryRun) {
    this.config = loadConfig();
    this.isDryRun = isDryRun;
    console.log(`isDryRun: ${isDryRun}`);
    if (
      !this.config.s3.access_key ||
      !this.config.s3.secret_key ||
      !this.config.s3.bucket
    ) {
      throw new Error(
        "AWS_SECRET_KEY, AWS_ACCESS_KEY, AWS_S3_BUCKET and AWS_S3_BASE_PATH are required.",
      );
    }

    this.bucket = this.config.s3.bucket;

    const opts = {
      credentials: {
        accessKeyId: this.config.s3.access_key,
        secretAccessKey: this.config.s3.secret_key,
      },
      region: "us-east-1",
    };
    this.client = new S3Client(opts);
  }

  async get(filehash) {
    if (!filehash) {
      throw new Error("No key passed for s3 GET.");
    }
    const slug = filehash.substring(0, 2);
    const key = `secure/${slug}/${filehash}`;
    const params = {
      Bucket: this.bucket,
      Key: key,
    };
    try {
      logger.info(`Fetching file from s3: ${key}`);
      const command = new GetObjectCommand(params);
      const response = await this.client.send(command);
      const chunks = [];
      for await (const chunk of response.Body) {
        chunks.push(chunk);
      }
      const data = Buffer.concat(chunks);

      const originalFilename = response.Metadata?.filename || key;

      return {
        body: data,
        mimetype: response.ContentType,
        filename: originalFilename,
        headers: {
          "Content-Disposition": `attachment;filename=${originalFilename}`,
        },
      };
    } catch (err) {
      logger.error("An error occurred fetching the file from s3: " + err);
      throw new Error(err.message);
    }
  }

  generateFileHash(filename, documentId) {
    const uniqueString = `${documentId}_${filename}`.toLowerCase();
    return Buffer.from(uniqueString)
      .toString("base64")
      .replace(/[+/=]/g, "")
      .substring(0, 64);
  }

  async put(file, opts = {}) {
    if (this.isDryRun) {
      logger.info(`Dry run, skipping upload of file: ${opts.filename}`);
      return { filehash: "dry-run", response: null };
    }

    const filename = opts.filename || "unknown";
    const documentId = opts.documentId;
    const mimetype =
      opts.mimetype || mimeTypes.lookup(filename) || "application/octet-stream";

    if (!file) {
      logger.error(
        `No data passed for upload to s3 with opts: ${JSON.stringify(opts)}`,
      );
      throw new Error("No Data passed for upload.");
    }

    const hash = this.generateFileHash(filename, documentId);
    const slug = hash.substring(0, 2);
    const key = `secure/${slug}/${hash}`;

    const exists = await this.fileExists(hash);
    if (exists.exists) {
      logger.info(`File with hash ${hash} already exists in S3`);
      return { filehash: hash, exists: true };
    }

    const params = {
      Bucket: this.bucket,
      Key: key,
      Body: file,
      ServerSideEncryption: "AES256",
      StorageClass: "STANDARD_IA",
      Metadata: {
        filename: filename,
        contentType: mimetype,
      },
    };

    const command = new PutObjectCommand(params);
    try {
      logger.info(
        `Uploading file to s3 with filename: ${filename} and hash ${hash}`,
      );
      const response = await this.client.send(command);
      logger.info(
        `File uploaded to s3: with hash ${hash} ${JSON.stringify(response)}`,
      );
      return { filehash: hash, response };
    } catch (err) {
      logger.error("Error uploading to S3", err);
      throw new Error(err.message);
    }
  }

  async fileExists(filehash) {
    if (!filehash) {
      throw new Error("No filehash passed for s3 fileExists check.");
    }
    const slug = filehash.substring(0, 2);
    const key = `secure/${slug}/${filehash}`;
    const params = {
      Bucket: this.bucket,
      Key: key,
    };
    const command = new HeadObjectCommand(params);

    try {
      await this.client.send(command);
      return { exists: true, error: null };
    } catch (error) {
      if (error.name === "NotFound") {
        return { exists: false, error: null };
      }
      return { exists: false, error };
    }
  }

  async getMetadata(filehash) {
    if (!filehash) {
      throw new Error("No key passed for metadata GET.");
    }
    const slug = filehash.substring(0, 2);
    const key = `secure/${slug}/${filehash}`;
    const params = {
      Bucket: this.bucket,
      Key: key,
    };
    try {
      const command = new HeadObjectCommand(params);
      const response = await this.client.send(command);
      return {
        filename: response.Metadata?.filename,
        contentType: response.Metadata?.contentType,
        size: response.ContentLength,
      };
    } catch (err) {
      logger.error("An error occurred fetching metadata from s3: " + err);
      throw new Error(err.message);
    }
  }
}

module.exports = {
  S3Manager,
};
