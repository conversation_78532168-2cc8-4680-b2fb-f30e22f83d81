const log = require("node-color-log");
const util = require("util");
const moment = require("moment");
const fs = require("fs");
const path = require("path");

class Logger {
  static instance;
  static logFileName;
  static logFile;
  static isLoggingEnabled = false;
  static isDryRun = false;

  static RED = "\x1b[31m";
  static YELLOW = "\x1b[33m";
  static RESET = "\x1b[0m";
  static BOLD = "\x1b[1m";
  static CYAN = "\x1b[36m";
  static GREEN = "\x1b[32m";

  constructor(module) {
    this.module = module;
  }

  static enableLogging(isDryRun = false) {
    Logger.isDryRun = isDryRun;
    if (!Logger.isLoggingEnabled && !isDryRun) {
      Logger.isLoggingEnabled = true;
      Logger.logFile = Logger.createLogFile();
    }
  }

  static createLogFile() {
    const timestamp = moment().format("MMDDYYHHmmss");
    const logDir = path.join(__dirname, "..", "..", "logs");
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    Logger.logFileName = `cmigrate_${timestamp}.log`;
    const logPath = path.join(logDir, Logger.logFileName);
    return fs.createWriteStream(logPath, { flags: "a" });
  }

  _formatMessage(level, message) {
    const timestamp = moment().format("YYYY-MM-DD HH:mm:ss.SSS");
    return `${timestamp} [${this.module}] ${level}: ${message}`;
  }

  _formatArg(arg) {
    if (typeof arg === "object" || Array.isArray(arg)) {
      return util.inspect(arg, { depth: null, colors: false });
    }
    return arg;
  }

  _log(level, color, message, ...args) {
    const formattedMessage = this._formatMessage(level, message);

    if (Logger.isLoggingEnabled && Logger.logFile && !Logger.isDryRun) {
      Logger.logFile.write(formattedMessage + "\n");
      args.forEach((arg) =>
        Logger.logFile.write(
          this._formatMessage(level, this._formatArg(arg)) + "\n",
        ),
      );
    }

    const shouldLogToConsole = level === "DEBUG" ? Logger.verbose : true;

    if (shouldLogToConsole) {
      switch (level) {
        case "DEBUG":
          log.color("cyan").log(formattedMessage);
          args.forEach((arg) => log.color("cyan").log(this._formatArg(arg)));
          break;

        case "ERROR":
          console.error(
            `${Logger.RED}${Logger.BOLD}${formattedMessage}${Logger.RESET}`,
          );
          args.forEach((arg) => {
            if (arg instanceof Error) {
              console.error(`${Logger.RED}${arg.message}${Logger.RESET}`);
              if (arg.stack) {
                console.error(`${Logger.RED}${arg.stack}${Logger.RESET}`);
              }
            } else {
              console.error(
                `${Logger.RED}${this._formatArg(arg)}${Logger.RESET}`,
              );
            }
          });
          break;

        case "WARN":
          console.warn(
            `${Logger.YELLOW}${Logger.BOLD}${formattedMessage}${Logger.RESET}`,
          );
          args.forEach((arg) =>
            console.warn(
              `${Logger.YELLOW}${Logger.BOLD}${this._formatArg(arg)}${Logger.RESET}`,
            ),
          );
          break;

        case "SUCCESS":
          console.log(
            `${Logger.GREEN}${Logger.BOLD}${formattedMessage}${Logger.RESET}`,
          );
          args.forEach((arg) =>
            console.log(
              `${Logger.GREEN}${Logger.BOLD}${this._formatArg(arg)}${Logger.RESET}`,
            ),
          );
          break;

        default: // INFO
          log.color("green").log(formattedMessage);
          args.forEach((arg) => log.color("green").log(this._formatArg(arg)));
          break;
      }
    }
  }

  debug(message, ...args) {
    this._log("DEBUG", "cyan", message, ...args);
  }

  info(message, ...args) {
    this._log("INFO", "green", message, ...args);
  }

  warn(message, ...args) {
    this._log("WARN", "yellow", message, ...args);
  }

  error(message, ...args) {
    if (args.length > 0) {
      const firstArg = args[0];
      if (firstArg instanceof Error) {
        this._log("ERROR", "red", message, firstArg);
        if (firstArg.cause) {
          this._log("ERROR", "red", "Caused by:", firstArg.cause);
        }
      } else {
        this._log("ERROR", "red", message, ...args);
      }
    } else {
      this._log("ERROR", "red", message);
    }
  }

  success(message, ...args) {
    this._log("SUCCESS", "green", message, ...args);
  }

  static verbose = false;

  static setVerbose(isVerbose) {
    Logger.verbose = isVerbose;
  }

  static closeAndExit(exitCode = 0) {
    if (Logger.isLoggingEnabled && Logger.logFile && !Logger.isDryRun) {
      const finalMessage = `Log file ${Logger.logFileName} contains full details of this run.`;
      if (Logger.instance) Logger.instance.info(finalMessage);
      Logger.logFile.end(() => {
        console.log(`\n${finalMessage}`);
        process.exit(exitCode);
      });
    } else {
      process.exit(exitCode);
    }
  }
}

module.exports = Logger;
