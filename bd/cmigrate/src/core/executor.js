const fs = require("fs").promises;
const path = require("path");
const Logger = require("../utils/logger");
const {
  TransformPreCheckError,
  TransformErrorIgnore,
} = require("../transformers/base");
const { isEmptyValue } = require("../utils/tools");
const logger = new Logger("MigrationExecutor");

class MigrationExecutor {
  constructor(
    sourceConnector,
    destConnector,
    isDryRun,
    source_limit,
    startFrom = 0,
    postTransform = null,
    additionalFilters = null,
  ) {
    this.sourceConnector = sourceConnector;
    const parsedFilters = this.parseFilters(additionalFilters);
    logger.info(`Parsed filters in executor: ${JSON.stringify(parsedFilters)}`);
    this.additionalFilters = parsedFilters;
    this.destConnector = destConnector;
    this.isDryRun = isDryRun;
    this.limit = source_limit;
    this.startFrom = startFrom;
    this.postTransform = postTransform;
    this.transformers = {};
    this.results = {
      summary: {
        totalRecords: 0,
        totalErrors: 0,
        totalSkipped: 0,
        totalInserts: 0,
        totalUpdates: 0,
        totalUnchanged: 0,
        totalPostTransformErrors: 0,
      },
      migrations: {},
      postTransforms: {},
    };
    this.totalProcessed = 0;
  }

  parseFilters(filterString) {
    if (!filterString) return [];

    // If already an array of filter arrays, return as is
    if (
      Array.isArray(filterString) &&
      filterString.every((f) => Array.isArray(f))
    ) {
      return filterString;
    }

    // Handle string format
    return filterString
      .split(",")
      .map((filter) => {
        const trimmed = filter.trim();

        // Handle IN clause
        if (trimmed.toLowerCase().includes(" in ")) {
          const match = trimmed.match(/([^\s]+)\s+in\s*\((.*)\)/i);
          if (!match) {
            logger.warn(`Invalid IN clause format: ${trimmed}`);
            return null;
          }
          const [_, field, valueList] = match;
          // Parse the values inside the parentheses
          const values = valueList
            .split(",")
            .map((v) => v.trim().replace(/^'|'$/g, "").trim())
            .filter(Boolean);

          return [field, "in", values];
        }

        // Handle other operators
        const match = trimmed.match(
          /([^\s]+)\s*([><=!]+|>=|<=|like|ilike|nlike|nilike)\s*"?([^"]+)"?/,
        );
        if (!match) {
          logger.warn(`Invalid filter format: ${trimmed}`);
          return null;
        }

        const [_, field, operator, value] = match;

        // Handle date values
        if (value.match(/^\d{4}-\d{2}-\d{2}/)) {
          return [field, operator, value]; // Keep date strings as is
        }

        // Handle numeric values
        if (!isNaN(value)) {
          return [field, operator, Number(value)];
        }

        return [field, operator, value];
      })
      .filter((f) => f !== null);
  }

  async loadTransformers(specificMigrations = []) {
    const transformerDir = path.join(__dirname, "..", "transformers");
    const files = await fs.readdir(transformerDir);

    for (const file of files) {
      // Skip if not a JS file or is base.js
      if (!file.endsWith(".js") || file === "base.js") continue;

      const transformerName = file.replace(".js", "").toLowerCase();

      // Skip if not in the specified migrations list
      if (
        specificMigrations.length > 0 &&
        !specificMigrations.includes(transformerName)
      ) {
        logger.debug(
          `Skipping transformer ${transformerName} - not in specified migrations`,
        );
        continue;
      }

      try {
        const TransformerModule = require(path.join(transformerDir, file));
        if (typeof TransformerModule === "function") {
          this.transformers[transformerName] = new TransformerModule(
            this.sourceConnector,
            this.destConnector,
            this.isDryRun,
          );
        } else if (
          typeof TransformerModule === "object" &&
          typeof TransformerModule.default === "function"
        ) {
          this.transformers[transformerName] = new TransformerModule.default(
            this.sourceConnector,
            this.destConnector,
            this.isDryRun,
          );
        } else {
          logger.warn(
            `Transformer ${file} does not export a constructor or class`,
          );
        }
      } catch (error) {
        logger.error(`Error loading transformer ${file}: ${error.message}`);
        logger.error(error.stack);
      }
    }
  }

  async executeMigrations(migrationNames) {
    await this.loadTransformers(migrationNames);

    for (const migrationName of migrationNames) {
      const transformer = this.transformers[migrationName];
      if (!transformer) {
        logger.error(`Transformer not found for migration: ${migrationName}`);
        continue;
      }

      if (this.postTransform) {
        // If post-transform is specified, only run that
        logger.info(
          `Running post-transform ${this.postTransform} for migration ${migrationName}`,
        );
        const sourceData = await this.fetchSourceData(transformer, this.limit);

        for (const item of sourceData) {
          try {
            const result = await transformer.runPostTransform(
              item,
              item,
              { operation: "post-transform-only" },
              { transformName: this.postTransform },
            );
            logger.info(
              `Post-transform completed for item ${item.id} in ${migrationName} with result: ${JSON.stringify(result)}`,
            );
          } catch (error) {
            logger.error(`Error in post-transform: ${error.message}`);
          }
        }
      } else {
        // Run normal migration and store results
        const migrationResult = await this.executeMigration(migrationName);
        this.results.migrations[migrationName] = migrationResult;

        // Process post-transform results
        if (migrationResult.postTransformResults?.length > 0) {
          // Initialize post-transform stats if not exists
          if (!migrationResult.postTransforms) {
            migrationResult.postTransforms = {};
          }

          migrationResult.postTransformResults.forEach((ptr) => {
            // Get function name without 'bound ' prefix
            const funcName = ptr.function.replace(/^bound\s+/, "");

            // Initialize stats for this function if not exists
            if (!migrationResult.postTransforms[funcName]) {
              migrationResult.postTransforms[funcName] = {
                records: 0,
                errors: 0,
                skipped: 0,
                inserts: 0,
                updates: 0,
                unchanged: 0,
              };
            }

            const stats = migrationResult.postTransforms[funcName];
            stats.records++;

            if (ptr.status === "error") {
              stats.errors++;
              this.results.summary.totalPostTransformErrors++;
            } else if (ptr.dbResult) {
              switch (ptr.dbResult.operation) {
                case "insert":
                  stats.inserts++;
                  migrationResult.postTransforms[funcName].inserts++;
                  this.results.summary.totalInserts++;
                  break;
                case "update":
                case "upsert":
                  stats.updates++;
                  migrationResult.postTransforms[funcName].updates++;
                  this.results.summary.totalUpdates++;
                  break;
                case "none":
                  stats.unchanged++;
                  migrationResult.postTransforms[funcName].unchanged++;
                  this.results.summary.totalUnchanged++;
                  break;
              }
            }
          });

          // Update the migration result with the post-transform stats
          Object.keys(migrationResult.postTransforms).forEach((funcName) => {
            const stats = migrationResult.postTransforms[funcName];
            this.results.migrations[migrationName].postTransforms[funcName] = {
              records: stats.records,
              errors: stats.errors,
              skipped: stats.skipped,
              inserts: stats.inserts,
              updates: stats.updates,
              unchanged: stats.unchanged,
            };
          });
        }

        // Update summary
        this.results.summary.totalRecords += migrationResult.totalProcessed;
        this.results.summary.totalErrors += migrationResult.totalErrors;
        this.results.summary.totalSkipped += migrationResult.totalSkipped;
      }
    }

    // Store and log overall results after all migrations complete
    if (!this.isDryRun) {
      await this.storeResults(this.results);
    }
    this.logOverallResults(this.results);
  }

  async storeResults(overallResults) {
    if (this.isDryRun) {
      logger.info("[DRY RUN] Would store migration results");
      return;
    }

    for (const [migrationName, result] of Object.entries(
      overallResults.migrations,
    )) {
      const transformer = this.transformers[migrationName];
      if (!transformer) {
        throw new Error(
          `Cannot store results for ${migrationName}: transformer not found`,
        );
      }

      const totalErrors = result.errors.length;
      const totalSkipped = result.totalSkipped;
      const mainInserts = result.totalInserts;
      const mainUpdates = result.totalUpdates;
      const mainUnchanged = result.totalUnchanged;

      const postTransformCounts = result.postTransformResults.reduce(
        (acc, ptr) => {
          if (ptr.status === "success") acc.successes++;
          if (ptr.status === "error") acc.errors++;
          if (ptr.operation === "insert") acc.inserts++;
          if (ptr.operation === "update") acc.updates++;
          if (ptr.operation === "none") acc.unchanged++;
          if (ptr.operation === "skipped") acc.skipped++;
          return acc;
        },
        {
          successes: 0,
          errors: 0,
          inserts: 0,
          updates: 0,
          unchanged: 0,
          skipped: 0,
        },
      );

      const summary =
        `Processed: ${result.totalProcessed}, ` +
        `Successes: ${result.totalSuccess}, ` +
        `Errors: ${totalErrors}, ` +
        `Post-transform Successes: ${postTransformCounts.successes}, ` +
        `Post-transform Errors: ${postTransformCounts.errors}, ` +
        `Skipped: ${totalSkipped}, ` +
        `Inserts: ${mainInserts}, ` +
        `Updates: ${mainUpdates}, ` +
        `Unchanged: ${mainUnchanged}`;

      // Trim down errors to essential information and limit the number stored
      const formattedErrors = result.errors.slice(0, 100).map((error) => ({
        message: error.message,
        sourceId: error.sourceId,
      }));

      // Store only essential data from source and destination
      const migrationData = {
        source_table: transformer.sourceTable,
        dest_table: transformer.destTable,
        source_data: {
          total_records: result.sourceData?.length || 0,
          sample: result.sourceData?.slice(0, 5).map((record) => ({
            id: record.id,
            external_id: record.external_id,
          })),
        },
        dest_data: {
          total_records: result.destData?.length || 0,
          sample: result.destData?.slice(0, 5).map((record) => ({
            id: record.id,
            external_id: record.external_id,
          })),
        },
        result: {
          transformResults: result.transformResults
            ?.slice(0, 100)
            .map((tr) => ({
              operation: tr.operation,
              id: tr.id,
              status: tr.status,
            })),
          postTransformResults: result.postTransformResults
            ?.slice(0, 100)
            .map((ptr) => ({
              operation: ptr.operation,
              id: ptr.id,
              status: ptr.status,
            })),
        },
        last_run: new Date(),
        migration_name: migrationName,
        errors: formattedErrors.length > 0 ? formattedErrors : null,
        entries_changed: mainInserts + mainUpdates,
        summary: summary,
      };

      await this.destConnector.db.insert_migration_run(migrationData);
    }
  }

  cleanSourceData(data) {
    if (!data || typeof data !== "object") return data;

    const cleaned = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === "string") {
        // Trim strings and convert empty strings to null
        cleaned[key] = value.trim() || null;
      } else if (Array.isArray(value)) {
        // Clean array values
        cleaned[key] = value.map((item) =>
          typeof item === "string" ? item.trim() : item,
        );
      } else {
        // Keep other types as is
        cleaned[key] = value;
      }
    }
    return cleaned;
  }

  async executeMigration(migrationName) {
    const migrationResult = {
      transformResults: [],
      postTransformResults: [],
      totalProcessed: 0,
      totalSuccess: 0,
      totalErrors: 0,
      totalSkipped: 0,
      sourceData: [],
      destData: [],
      totalInserts: 0,
      totalUpdates: 0,
      totalUnchanged: 0,
      errors: [],
      postTransforms: {},
    };

    try {
      const transformer = this.transformers[migrationName];
      if (!transformer) {
        throw new Error(`No transformer found for migration: ${migrationName}`);
      }

      // Disable triggers if specified
      if (transformer.disableTriggers?.length > 0) {
        logger.info(
          `Disabling triggers for ${migrationName}: ${transformer.disableTriggers.join(", ")}`,
        );
        for (const trigger of transformer.disableTriggers) {
          await this.destConnector.db.raw_query(
            `alter table ${transformer.destTable} disable trigger ${trigger};`,
          );
        }
      }

      try {
        // Handle bulk transformers differently
        if (transformer.transformType === "bulk") {
          logger.info(`Processing bulk transformer for ${migrationName}`);

          // Get all data at once for bulk processing
          const sourceData = await this.fetchSourceData(
            transformer,
            null, // no limit for bulk
            this.startFrom || 0,
          );

          if (!sourceData?.length) {
            logger.warn("No source data found for bulk transform");
            return migrationResult;
          }

          logger.info(
            `Fetched ${sourceData.length} records for bulk processing`,
          );

          try {
            // Use the transformer's transform method directly instead of transformBatch
            const transformResults = await transformer.transform(sourceData);

            // Handle single result or array of results
            const resultArray = Array.isArray(transformResults)
              ? transformResults
              : [transformResults];

            // Count operations from transform results
            resultArray.forEach((result) => {
              if (result.operation === "error") {
                migrationResult.totalErrors++;
                migrationResult.errors.push({
                  id: result.sourceData?.id,
                  message: result.error,
                });
              } else if (result.operation === "insert") {
                migrationResult.totalInserts++;
              } else if (result.operation === "update") {
                migrationResult.totalUpdates++;
              } else if (result.operation === "skip") {
                migrationResult.totalUnchanged++;
              }

              // Track post-transform results
              if (result.postTransformResults) {
                migrationResult.postTransformResults.push(
                  ...result.postTransformResults,
                );
              }
            });

            // Store transform results
            migrationResult.transformResults.push(...resultArray);
            migrationResult.totalProcessed = sourceData.length;
          } catch (error) {
            if (error.message?.includes("TransformErrorIgnore")) {
              logger.error(`Transform error in bulk: ${error.message}`);
              migrationResult.totalErrors++;
              migrationResult.errors.push({
                message: error.message,
              });
            } else {
              throw error;
            }
          }

          return migrationResult;
        }

        // Original batch processing logic for non-bulk transformers
        const batchSize = transformer.batchProcess ? transformer.batchSize : 50;
        let startFrom = this.startFrom || 0;
        let totalCount;

        // Get initial count based on source type
        if (transformer.sourceRawQuery) {
          // Remove any trailing semicolon and add count wrapper
          const baseQuery = transformer.sourceRawQuery.replace(/;$/, "");
          const countQuery = `SELECT COUNT(*) as total FROM (${baseQuery}) as count_query`;
          const countResult =
            await this.sourceConnector.db.raw_query(countQuery);
          totalCount = parseInt(countResult[0].total);
        } else if (transformer.sourceTable) {
          // Ensure additionalFilters is an array
          const additionalFilters = Array.isArray(this.additionalFilters)
            ? this.additionalFilters
            : [];

          // Combine transformer filters with additional filters
          const allFilters = [
            ...(transformer.sourceFilters || []),
            ...additionalFilters,
          ];

          // Use the db connector's filter translation for the count query
          const { whereClause, values } =
            transformer.sourceConnector.db._buildWhereClause(allFilters);

          let countQuery = `SELECT COUNT(*) as count FROM ${transformer.sourceTable}`;
          if (whereClause) {
            countQuery += ` ${whereClause}`;
          }

          logger.debug(`Count query: ${countQuery}`);
          const countResult = await transformer.sourceConnector.raw_query(
            countQuery,
            values,
          );
          totalCount = parseInt(countResult[0].count);
        } else {
          throw new Error("No source query or table defined for transformer");
        }

        if (isNaN(totalCount)) {
          throw new Error("Failed to get valid count of records");
        }

        logger.info(`Total records to process: ${totalCount}`);
        logger.info(`Starting from record: ${startFrom}`);

        while (startFrom < totalCount) {
          if (this.limit && migrationResult.totalProcessed >= this.limit) {
            logger.info(`Reached total limit of ${this.limit} records`);
            break;
          }

          const batchLimit = this.limit
            ? Math.min(batchSize, this.limit - migrationResult.totalProcessed)
            : Math.min(batchSize, totalCount - startFrom);

          if (batchLimit <= 0) break;

          const sourceData = await this.fetchSourceData(
            transformer,
            batchLimit,
            startFrom,
          );
          if (!sourceData?.length) break;

          // Clean the source data
          const cleanedData = sourceData.map((item) =>
            this.cleanSourceData(item),
          );
          logger.debug(
            `Cleaned data sample: ${JSON.stringify(cleanedData[0])}`,
          );

          migrationResult.totalProcessed += cleanedData.length;
          logger.info(
            `Fetched ${cleanedData.length} records (${migrationResult.totalProcessed} of ${totalCount})`,
          );

          try {
            // Use cleaned data for transformation
            const transformResults =
              await transformer.transformBatch(cleanedData);

            // Count operations from transform results
            transformResults.forEach((result) => {
              if (result.operation === "error") {
                migrationResult.totalErrors++;
                migrationResult.errors.push({
                  id: result.sourceData?.id,
                  message: result.error,
                });
              } else if (result.operation === "insert") {
                migrationResult.totalInserts++;
              } else if (result.operation === "update") {
                migrationResult.totalUpdates++;
              } else if (result.operation === "skip") {
                migrationResult.totalUnchanged++;
              }

              // Track post-transform results
              if (result.postTransformResults) {
                migrationResult.postTransformResults.push(
                  ...result.postTransformResults,
                );
              }
            });

            // Store transform results
            migrationResult.transformResults.push(...transformResults);
          } catch (error) {
            if (error.message?.includes("TransformErrorIgnore")) {
              logger.error(`Transform error in batch: ${error.message}`);
              migrationResult.totalErrors++;
              migrationResult.errors.push({
                message: error.message,
              });
              continue;
            }
            throw error;
          }

          startFrom += sourceData.length;

          if (startFrom >= totalCount) {
            break;
          }
        }

        return migrationResult;
      } finally {
        // Re-enable triggers in finally block to ensure they're re-enabled even if there's an error
        if (transformer.disableTriggers?.length > 0) {
          logger.info(
            `Re-enabling triggers for ${migrationName}: ${transformer.disableTriggers.join(", ")}`,
          );
          for (const trigger of transformer.disableTriggers) {
            await this.destConnector.db.raw_query(
              `alter table ${transformer.destTable} enable trigger ${trigger};`,
            );
          }
        }
      }
    } catch (error) {
      logger.error(`Error in migration ${migrationName}: ${error.message}`);
      migrationResult.errors.push({
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async fetchSourceData(transformer, limit = null, startFrom = 0) {
    // Ensure additionalFilters is an array
    const additionalFilters = this.additionalFilters || [];

    // Combine transformer's source filters with additional filters
    const allFilters = [
      ...(transformer.sourceFilters || []),
      ...additionalFilters,
    ];

    logger.debug(`Combined filters for query:`, allFilters);

    if (transformer.sourceRawQuery) {
      logger.debug(
        `Using raw query: ${transformer.sourceRawQuery.substring(0, 100)}...`,
      );
      let query = transformer.sourceRawQuery;

      // Only add LIMIT and OFFSET if not already present in query
      const hasLimit = query.toLowerCase().includes("limit");
      const hasOffset = query.toLowerCase().includes("offset");

      // Add LIMIT and OFFSET only if they're not already in the query
      if (limit && !hasLimit) {
        query += ` LIMIT ${limit}`;
      }
      if (startFrom > 0 && !hasOffset) {
        query += ` OFFSET ${startFrom}`;
      }

      const result = await transformer.sourceConnector.raw_query(
        query,
        [],
        allFilters, // Pass combined filters here
        null, // Don't pass limit here since we added it to the query
      );

      logger.debug(`Fetched ${result.length} records from raw query`);
      return result;
    }

    if (transformer.sourceTable) {
      const result = await transformer.sourceConnector.fetchData(
        transformer.sourceTable,
        {
          filters: allFilters,
          limit: limit,
          offset: startFrom,
        },
      );
      return result;
    }

    throw new Error("No source query or table defined");
  }

  logMigrationResults(migrationName, results) {
    logger.debug("=== logMigrationResults ===");
    logger.debug(
      `Post transform results length: ${results.postTransformResults?.length}`,
    );
    if (results.postTransformResults?.length > 0) {
      logger.debug(
        `First result: ${JSON.stringify(results.postTransformResults[0])}`,
      );
    }
    logger.info(`\nResults for ${migrationName}:`);

    // Main transform results
    logger.info("    Main Transform:");
    logger.info(
      `      Records: ${results.totalProcessed}, ` +
        `Errors: ${results.errors.length}, ` +
        `Skipped: ${results.totalSkipped}`,
    );
    logger.info(
      `      Inserts: ${results.totalInserts}, ` +
        `Updates: ${results.totalUpdates}, ` +
        `Unchanged: ${results.totalUnchanged}`,
    );

    // Post transform results
    if (results.postTransformResults?.length > 0) {
      logger.info("    Post Transforms:");

      // Add debug logging
      logger.debug(
        `Post transform results length: ${results.postTransformResults.length}`,
      );
      logger.debug(
        `Sample result: ${JSON.stringify(results.postTransformResults[0])}`,
      );

      // Group by function name
      const postTransformsByFunction = results.postTransformResults.reduce(
        (acc, result) => {
          logger.debug(`\n=== Processing Post Transform Result ===`);
          logger.debug(`Full result: ${JSON.stringify(result)}`);

          const funcName =
            result.function?.replace(/^(bound\s+)+/, "") || "unknown";

          // If there's an error, throw it immediately with full details
          if (result.status === "error" || result.error) {
            logger.error(
              `Error in post-transform ${funcName}:`,
              result.error || result.message,
            );
            throw new Error(
              `Post-transform ${funcName} failed: ${result.error?.message || result.message}\n${result.error?.stack || ""}`,
            );
          }

          if (!acc[funcName]) {
            acc[funcName] = {
              error: 0,
              processed: 0,
              inserts: 0,
              updates: 0,
              unchanged: 0,
              skipped: 0,
              processedIds: new Set(),
            };
          }

          // Always increment processed count for each result
          acc[funcName].processed++;

          // Add the source ID to processed IDs if it exists
          const recordId = result.sourceId || result.dbResult?.sourceId;
          if (recordId) {
            acc[funcName].processedIds.add(recordId);
          }

          // Update operation counts based on dbResult
          if (result.status === "error") {
            acc[funcName].error++;
          } else if (result.dbResult?.operation) {
            switch (result.dbResult.operation) {
              case "insert":
                acc[funcName].inserts++;
                break;
              case "update":
              case "upsert":
                acc[funcName].updates++;
                break;
              case "skip":
                acc[funcName].skipped++;
                break;
              case "none":
                acc[funcName].unchanged++;
                break;
            }
          }

          return acc;
        },
        {},
      );

      // Log each function's results
      Object.entries(postTransformsByFunction).forEach(([funcName, counts]) => {
        logger.info(`      ${funcName}:`);
        logger.info(
          `        Records: ${counts.processed}, ` +
            `Errors: ${counts.error}, ` +
            `Skipped: ${counts.skipped}`,
        );
        logger.info(
          `        Inserts: ${counts.inserts}, ` +
            `Updates: ${counts.updates}, ` +
            `Unchanged: ${counts.unchanged}`,
        );
      });
    }
  }

  logOverallResults(overallResults) {
    logger.debug("=== logOverallResults ===");
    for (const [migrationName, results] of Object.entries(
      overallResults.migrations,
    )) {
      logger.debug(
        `${migrationName} post transform results length: ${results.postTransformResults?.length}`,
      );
      if (results.postTransformResults?.length > 0) {
        logger.debug(
          `First result: ${JSON.stringify(results.postTransformResults[0])}`,
        );
      }
    }
    const summary = overallResults.summary;

    // Log Summary first
    logger.info("Migration Summary:");
    logger.info(`Total Records: ${summary.totalRecords}`);
    logger.info(`Total Errors: ${summary.totalErrors}`);
    logger.info(`Total Skipped: ${summary.totalSkipped}`);
    logger.info(`Total Inserts: ${summary.totalInserts}`);
    logger.info(`Total Updates: ${summary.totalUpdates}`);
    logger.info(`Total Unchanged: ${summary.totalUnchanged}`);
    logger.info(
      `Total Post-transform Errors: ${summary.totalPostTransformErrors}`,
    );
    logger.info(""); // Empty line for readability

    // Log Individual Migration Details
    logger.info("Individual Migration Results:");
    for (const [migrationName, results] of Object.entries(
      overallResults.migrations,
    )) {
      logger.info(`  ${migrationName}:`);
      logger.info(`    Main Transform:`);
      logger.info(
        `      Records: ${results.totalProcessed}, ` +
          `Errors: ${results.totalErrors}, ` +
          `Skipped: ${results.totalSkipped}`,
      );
      logger.info(
        `      Inserts: ${results.totalInserts}, ` +
          `Updates: ${results.totalUpdates}, ` +
          `Unchanged: ${results.totalUnchanged}`,
      );

      // Group and count post-transform results by function name
      const postTransformsByFunction = results.postTransformResults.reduce(
        (acc, result) => {
          const funcName =
            result.function?.replace(/^(bound\s+)+/, "") || "unknown";
          if (!acc[funcName]) {
            acc[funcName] = {
              error: 0,
              processed: 0,
              inserts: 0,
              updates: 0,
              unchanged: 0,
              skipped: 0,
              processedIds: new Set(),
            };
          }

          // Always increment processed count for each result
          acc[funcName].processed++;

          // Add the source ID to processed IDs if it exists
          const recordId = result.sourceId || result.dbResult?.sourceId;
          if (recordId) {
            acc[funcName].processedIds.add(recordId);
          }

          // Update operation counts based on dbResult
          if (result.status === "error") {
            acc[funcName].error++;
          } else if (result.dbResult?.operation) {
            switch (result.dbResult.operation) {
              case "insert":
                acc[funcName].inserts++;
                break;
              case "update":
              case "upsert":
                acc[funcName].updates++;
                break;
              case "skip":
                acc[funcName].skipped++;
                break;
              case "none":
                acc[funcName].unchanged++;
                break;
            }
          }

          return acc;
        },
        {},
      );

      // Post-transform details
      logger.info(`    Post Transforms:`);
      for (const [funcName, counts] of Object.entries(
        postTransformsByFunction,
      )) {
        logger.info(`      ${funcName}:`);
        logger.info(
          `        Records: ${counts.processed}, ` +
            `Errors: ${counts.error}, ` +
            `Skipped: ${counts.skipped}`,
        );
        logger.info(
          `        Inserts: ${counts.inserts}, ` +
            `Updates: ${counts.updates}, ` +
            `Unchanged: ${counts.unchanged}`,
        );
      }
      logger.info(""); // Empty line between migrations
    }

    if (this.isDryRun) {
      logger.info("[DRY RUN] Would store overall results");
    }
  }

  async runPostTransforms(postTransformNames) {
    await this.loadTransformers(postTransformNames);
    const results = {
      migrations: {},
      summary: {
        totalPostTransformSuccess: 0,
        totalPostTransformErrors: 0,
        totalInserts: 0,
        totalUpdates: 0,
        totalUnchanged: 0,
        totalRecords: 0,
      },
    };

    for (const migrationName of postTransformNames) {
      const transformer = this.transformers[migrationName];
      if (!transformer) {
        logger.error(
          `No transformer found for post-transform: ${migrationName}`,
        );
        continue;
      }

      try {
        // Get all records that need post-transform processing
        const sourceData = await this.fetchSourceData(transformer, this.limit);
        logger.info(
          `Running post-transforms for ${sourceData.length} records in ${migrationName}`,
        );

        const migrationResult = {
          postTransformResults: [],
          totalProcessed: sourceData.length,
          errors: [],
        };

        const processedTransforms = new Set();

        for (const item of sourceData) {
          try {
            const postTransformResults = await transformer.runPostTransform(
              item,
              item,
              { operation: "post-transform-only" },
            );
            migrationResult.postTransformResults =
              migrationResult.postTransformResults.concat(postTransformResults);
          } catch (error) {
            logger.error(
              `Error in post-transform for ${migrationName}: ${error.message}`,
            );
            migrationResult.errors.push({
              message: error.message,
              stack: error.stack,
              sourceId: item.id,
            });
          }
        }

        results.migrations[migrationName] = migrationResult;

        // Update summary counts
        const successCount = migrationResult.postTransformResults.filter(
          (r) => r.status === "success",
        ).length;
        const errorCount = migrationResult.postTransformResults.filter(
          (r) => r.status === "error",
        ).length;

        results.summary.totalPostTransformSuccess += successCount;
        results.summary.totalPostTransformErrors += errorCount;
        results.summary.totalRecords += migrationResult.totalProcessed;

        // Count operations
        migrationResult.postTransformResults.forEach((ptr) => {
          if (ptr.operation === "insert") results.summary.totalInserts++;
          else if (ptr.operation === "upsert" || ptr.operation === "update")
            results.summary.totalUpdates++;
          else if (ptr.operation === "none") results.summary.totalUnchanged++;
        });
      } catch (error) {
        logger.error(
          `Error executing post-transform ${migrationName}: ${error.message}`,
        );
        if (error.stack) logger.error(error.stack);
      }
    }

    if (!this.isDryRun) {
      await this.storeResults(results);
    }

    this.logPostTransformResults(results);
    return results;
  }

  logPostTransformResults(results) {
    logger.info("Post-Transform Results Summary:");
    logger.info(`Total Records Processed: ${results.summary.totalRecords}`);
    logger.info(
      `Successful Post-Transforms: ${results.summary.totalPostTransformSuccess}`,
    );
    logger.info(
      `Failed Post-Transforms: ${results.summary.totalPostTransformErrors}`,
    );
    logger.info(`Total Inserts: ${results.summary.totalInserts}`);
    logger.info(`Total Updates: ${results.summary.totalUpdates}`);
    logger.info(`Total Unchanged: ${results.summary.totalUnchanged}`);

    // Log details for each migration
    Object.entries(results.migrations).forEach(([migrationName, result]) => {
      logger.info(`\nResults for ${migrationName}:`);
      logger.info(`Records Processed: ${result.totalProcessed}`);

      if (result.errors.length > 0) {
        logger.error(`Errors in ${migrationName}:`);
        result.errors.forEach((error, index) => {
          logger.error(`  ${index + 1}. ${error.message}`);
          if (error.sourceId) logger.error(`     Source ID: ${error.sourceId}`);
        });
      }
    });
  }

  async preloadDocumentMap(transformer) {
    logger.info("Preloading document map...");
    // Logic to preload document map from database or cache
    // This could be implemented based on your specific needs
  }
}

module.exports = MigrationExecutor;
