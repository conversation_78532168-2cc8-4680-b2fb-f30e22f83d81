const PostgresConnector = require("./db");
const ApiConnector = require("./api");
const Logger = require("../utils/logger");
const logger = new Logger("HostConnector");

class HostConnector {
  constructor(config, type) {
    this.config = config;
    this.type = type; // 'source' or 'dest'
    this.db = new PostgresConnector(config);
    this.api = new ApiConnector(config);
    this.isDryRun = config.isDryRun || false;
  }

  async connect() {
    await this.db.connect();
    await this.api.connect();
  }

  async disconnect() {
    await this.db.disconnect();
    await this.api.disconnect();
  }

  async fetchData(table, options = {}) {
    if (
      this.type === "dest" &&
      options.filters?.length > 0 &&
      !this.skipDefaultFilters
    ) {
      const existingFields = options.filters.map((f) =>
        Array.isArray(f) ? f[0] : Object.keys(f)[0],
      );

      if (!existingFields.includes("archived")) {
        options.filters.push(["archived", "OR_NULL", false]);
      }

      if (!existingFields.includes("deleted")) {
        options.filters.push(["deleted", "OR_NULL", false]);
      }
    }

    return this.db.fetchData(table, options);
  }

  async insertData(table, data) {
    return this.db.insertData(table, data);
  }

  async insert_gerund(gerund, source_id, dest_id) {
    if (this.isDryRun) {
      logger.info(
        `Dry run: insert_gerund(${gerund}, ${source_id}, ${dest_id})`,
      );
      return;
    }
    return this.db.insert_gerund(gerund, source_id, dest_id);
  }
  async insert_subform(subform, source_id, dest_id) {
    if (this.isDryRun) {
      logger.info(
        `Dry run: insert_subform(${subform}, ${source_id}, ${dest_id})`,
      );
      return;
    }
    return this.db.insert_subform(subform, source_id, dest_id);
  }
  async raw_query(query, values = [], additionalFilters = [], limit = null) {
    logger.debug(`Running Raw query in HostConnector: ${query}`);
    logger.debug(`With values: ${JSON.stringify(values)}`);
    logger.debug(
      `With additional filters: ${JSON.stringify(additionalFilters)}`,
    );
    logger.debug(`With limit: ${limit}`);

    // Pass all parameters to the DB connector's raw_query
    return this.db.raw_query(query, values, additionalFilters, limit);
  }
  async fetchOrCreate(table, data, filter) {
    return this.db.fetchOrCreate(table, data, filter);
  }

  async transaction(callback) {
    return this.db.transaction(callback);
  }

  async updateData(table, data, options = {}) {
    return this.db.updateData(table, data, options);
  }

  async upsertData(table, data, options = {}, sourceData = {}) {
    const { qualifiers = [], customEndpoints = null } = options;

    // Process qualifiers based on the operation type
    if (customEndpoints) {
      logger.info("Using API upsertData");
      return this.api.upsertData(table, data, options);
    } else {
      logger.info("Using DB upsertData");
      // Process qualifiers for DB operations
      const processedQualifiers = await this._processDBQualifiers(
        qualifiers,
        sourceData,
      );
      return this.db.upsertData(
        table,
        data,
        {
          ...options,
          qualifiers: processedQualifiers,
        },
        sourceData,
      );
    }
  }

  async _processDBQualifiers(qualifiers, sourceData) {
    if (!qualifiers || qualifiers.length === 0) {
      return [];
    }

    const processedQualifiers = [];
    logger.debug(`Processing qualifiers: ${JSON.stringify(qualifiers)}`);
    for (const qualifier of qualifiers) {
      // Handle array format qualifiers [field, operator, value]
      if (Array.isArray(qualifier)) {
        processedQualifiers.push(qualifier);
        continue;
      }

      // Handle object format qualifiers {field, source, lookup}
      const { field, source, lookup } = qualifier;

      if (lookup) {
        // Handle lookup qualifiers
        const lookupValue = await this.db.lookupForeignKey(
          lookup.table,
          lookup.sourceField,
          sourceData[source],
          lookup.table,
          lookup.returnField,
        );
        if (lookupValue) {
          processedQualifiers.push([field, "=", lookupValue]);
        }
      } else {
        // Handle direct qualifiers
        const value = sourceData[source] || qualifier.value;
        if (value !== undefined) {
          processedQualifiers.push([field, "=", value]);
        }
      }
    }

    logger.debug(
      `Processed qualifiers: ${JSON.stringify(processedQualifiers)}`,
    );
    return processedQualifiers;
  }

  async lookupForeignKey(
    sourceTable,
    sourceField,
    sourceValue,
    destTable,
    destField,
  ) {
    return this.db.lookupForeignKey(
      sourceTable,
      sourceField,
      sourceValue,
      destTable,
      destField,
    );
  }

  async lookupSourceValue(sourceTable, sourceField, sourceValue, selectField) {
    return this.db.lookupSourceValue(
      sourceTable,
      sourceField,
      sourceValue,
      selectField,
    );
  }

  async fetch_source_id(table, field, value) {
    return this.db.fetch_source_id(table, field, value);
  }

  async apiGet(endpoint, params = {}) {
    return this.api.client.get(endpoint, { params });
  }

  async apiPost(endpoint, data = {}) {
    try {
      logger.debug(`Making API POST request to ${endpoint}`);
      logger.debug(`Request payload: ${JSON.stringify(data, null, 2)}`);

      const response = await this.api.client.post(endpoint, data);
      const responseData = await response.data;

      logger.debug(`API Response: ${JSON.stringify(responseData, null, 2)}`);
      return responseData;
    } catch (error) {
      logger.error(`API POST Error:
        Endpoint: ${endpoint}
        Status: ${error.response?.status}
        Message: ${error.message}
        Response Data: ${JSON.stringify(error.response?.data, null, 2)}
        Request Data: ${JSON.stringify(data, null, 2)}
      `);
      throw error;
    }
  }

  async apiPut(endpoint, data = {}) {
    return this.api.client.put(endpoint, data);
  }

  async apiDelete(endpoint) {
    return this.api.client.delete(endpoint);
  }
}

module.exports = HostConnector;
