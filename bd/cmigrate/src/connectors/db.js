const { Pool } = require("pg");
const Logger = require("../utils/logger");
const logger = new Logger("POSTGRES_CONNECTOR");
const { cleanup_nulls, has_changed, isEmptyValue } = require("../utils/tools");

class PostgresConnector {
  constructor(config) {
    this.config = config;
    const isLocalhost =
      this.config.pg_uri.includes("localhost") ||
      this.config.pg_uri.includes("127.0.0.1");

    const poolConfig = {
      connectionString: this.config.pg_uri,
      // Only enable SSL for non-localhost connections
      ...(isLocalhost
        ? {}
        : {
            ssl: {
              rejectUnauthorized: false,
              mode: "require",
            },
          }),
    };

    this.pool = new Pool(poolConfig);
    this.isDryRun = config.isDryRun || false;
  }
  async connect() {
    try {
      logger.debug(`Attempting to connect with URI: ${this.config.pg_uri}`);
      await this.pool.query("SELECT 1"); // Test the connection
      logger.success(`Connected to Postgres ${this.config.pg_uri}`);
    } catch (error) {
      logger.error(`Failed to connect to Postgres: ${error.message}`);
      throw error;
    }
  }
  async create_clara_migration_table() {
    const query = `
            CREATE TABLE IF NOT EXISTS clara_migration (
                id SERIAL PRIMARY KEY,
                created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                source_table TEXT,
                dest_table TEXT,
                source_data JSONB,
                dest_data JSONB,
                result JSONB,
                last_run TIMESTAMP,
                migration_name TEXT,
                errors JSONB,
                entries_changed INTEGER,
                summary TEXT
            )
        `;
    await this.pool.query(query);
  }

  async get_migration_history() {
    try {
      const query = `
            WITH RankedMigrations AS (
                SELECT 
                    migration_name,
                    last_run,
                    entries_changed,
                    errors,
                    summary,
                    ROW_NUMBER() OVER (
                        PARTITION BY migration_name 
                        ORDER BY last_run DESC NULLS LAST
                    ) as rn
                FROM clara_migration
            )
            SELECT 
                migration_name,
                last_run,
                entries_changed,
                errors,
                summary
            FROM RankedMigrations 
            WHERE rn = 1
            ORDER BY migration_name
        `;

      logger.debug(`Executing migration history query: ${query}`);
      const result = await this.pool.query(query);
      logger.debug(`Found ${result.rows.length} migration history records`);

      return result.rows;
    } catch (error) {
      logger.error(`Error fetching migration history: ${error.message}`);
      logger.error(error.stack);
      throw error;
    }
  }

  async insert_migration_run(data) {
    try {
      const query = `
            INSERT INTO clara_migration 
            (source_table, dest_table, source_data, dest_data, result, last_run, migration_name, errors, entries_changed, summary) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id
        `;

      // Ensure JSON fields are properly stringified
      const sourceData =
        typeof data.source_data === "string"
          ? data.source_data
          : JSON.stringify(data.source_data);
      const destData =
        typeof data.dest_data === "string"
          ? data.dest_data
          : JSON.stringify(data.dest_data);
      const result =
        typeof data.result === "string"
          ? data.result
          : JSON.stringify(data.result);
      const errors = data.errors ? JSON.stringify(data.errors) : null;

      logger.debug(`Inserting migration run with query: ${query}`);

      const queryResult = await this.pool.query(query, [
        data.source_table,
        data.dest_table,
        sourceData,
        destData,
        result,
        data.last_run,
        data.migration_name,
        errors,
        data.entries_changed,
        data.summary,
      ]);

      logger.info(
        `Successfully stored migration run with ID: ${queryResult.rows[0]?.id}`,
      );
      return queryResult;
    } catch (error) {
      logger.error(`Error storing migration run: ${error.message}`);
      logger.error(`Error details: ${error.stack}`);
      throw error;
    }
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      logger.info(`Disconnected from Postgres ${this.config.pg_uri}`);
    }
  }

  _escapeIdentifier(identifier) {
    if (typeof identifier !== "string") {
      logger.warn(
        `Invalid identifier type: ${typeof identifier}, value: ${identifier}`,
      );
      return identifier;
    }
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  _buildWhereClause(filters, startParamCount = 0) {
    if (!filters || filters.length === 0)
      return { whereClause: "", values: [] };

    const conditions = [];
    const values = [];
    let paramCount = startParamCount;

    logger.debug("Building where clause with filters:", filters);

    filters.forEach(([field, operator, value]) => {
      const escapedField = this._escapeIdentifier(field);
      logger.debug(
        `Processing filter: field=${field}, operator=${operator}, value=${value}`,
      );

      switch (operator.toLowerCase()) {
        case "not_null":
          logger.debug("Handling not_null operator");
          conditions.push(
            `${escapedField} IS NOT NULL AND ${escapedField} != ''`,
          );
          break;

        case "in": {
          if (!Array.isArray(value)) {
            throw new Error(
              `IN operator expects an array of values for field: ${field}`,
            );
          }
          const placeholders = value
            .map(() => {
              paramCount++;
              return `$${paramCount}`;
            })
            .join(", ");
          conditions.push(`${escapedField} IN (${placeholders})`);
          values.push(...value);
          break;
        }

        case "like":
        case "ilike":
        case "nlike":
        case "nilike": {
          paramCount++;
          const notOp = operator.startsWith("n") ? "NOT " : "";
          const likeOp = operator.includes("ilike") ? "ILIKE" : "LIKE";
          const searchValue = `%${value}%`;
          conditions.push(`${escapedField} ${notOp}${likeOp} $${paramCount}`);
          values.push(searchValue);
          break;
        }

        case "=":
        case "!=":
        case ">":
        case "<":
        case ">=":
        case "<=": {
          paramCount++;
          conditions.push(`${escapedField} ${operator} $${paramCount}`);
          values.push(value);
          break;
        }

        case "in_subquery": {
          // Handle subquery - value contains the raw subquery
          // Cast only the field we're comparing against
          conditions.push(
            `CAST(${escapedField} AS TEXT) IN (${value.replace(/CAST\((.*?)\)/i, "$1")})`,
          );
          break;
        }

        default: {
          throw new Error(`Unsupported operator: ${operator}`);
        }
      }
    });

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";
    logger.debug("Final where clause:", whereClause);

    return { whereClause, values };
  }

  async fetchData(table, options = {}) {
    const {
      filters = [],
      limit = null,
      offset = 0,
      lowercase = false,
      orderBy = [],
    } = options;

    let query = `SELECT * FROM ${this._escapeIdentifier(table)}`;
    const values = [];

    // Process filters including default archived/deleted filters
    if (filters.length > 0) {
      const whereConditions = filters.map((filter, _index) => {
        if (Array.isArray(filter)) {
          const [field, operator, value] = filter;
          const escapedField = this._escapeIdentifier(field);
          const wrappedField = lowercase
            ? `LOWER(${escapedField})`
            : escapedField;
          let notOp, likeOp, placeholders;

          switch (operator.toLowerCase()) {
            case "or_null":
              if (value === false) {
                return `(${escapedField} IS FALSE OR ${escapedField} IS NULL)`;
              } else if (value === true) {
                return `(${escapedField} IS TRUE OR ${escapedField} IS NULL)`;
              }
              break;

            case "not_null":
              return `${escapedField} IS NOT NULL AND ${escapedField} != ''`;

            case "is":
              if (value === null) {
                return `${escapedField} IS NULL`;
              } else if (value === false) {
                return `${escapedField} IS FALSE`;
              } else if (value === true) {
                return `${escapedField} IS TRUE`;
              }
              break;

            case "in":
              if (!Array.isArray(value)) {
                throw new Error(
                  `IN operator expects an array of values for field: ${field}`,
                );
              }
              placeholders = value
                .map((_, i) => {
                  const placeholder = `$${values.length + i + 1}`;
                  return lowercase ? `LOWER(${placeholder})` : placeholder;
                })
                .join(", ");
              values.push(
                ...(lowercase
                  ? value.map((v) => v?.toString().toLowerCase())
                  : value),
              );
              return `${wrappedField} IN (${placeholders})`;

            case "like":
            case "ilike":
            case "nlike":
            case "nilike":
              values.push(`%${value}%`);
              notOp = operator.startsWith("n") ? "NOT " : "";
              likeOp = operator.includes("ilike") ? "ILIKE" : "LIKE";
              return `${wrappedField} ${notOp}${likeOp} $${values.length}`;

            case "=":
            case "!=":
            case ">":
            case "<":
            case ">=":
            case "<=":
              values.push(lowercase ? value?.toString().toLowerCase() : value);
              return `${wrappedField} ${operator} $${values.length}`;

            case "in_subquery": {
              // Handle subquery with proper casting
              return `CAST(${wrappedField} AS TEXT) IN (${value})`;
            }

            default:
              throw new Error(`Unsupported operator: ${operator}`);
          }
        } else {
          // Handle object-style filters {field: value}
          const field = Object.keys(filter)[0];
          const value = filter[field];
          const escapedField = this._escapeIdentifier(field);
          const wrappedField = lowercase
            ? `LOWER(${escapedField})`
            : escapedField;
          values.push(lowercase ? value?.toString().toLowerCase() : value);
          return `${wrappedField} = $${values.length}`;
        }
      });
      query += ` WHERE ${whereConditions.join(" AND ")}`;
    }

    if (orderBy.length > 0) {
      const orderClauses = orderBy.map(([field, direction = "ASC"]) => {
        const escapedField = this._escapeIdentifier(field);
        const sanitizedDirection =
          direction?.toUpperCase() === "DESC" ? "DESC" : "ASC";
        return `${escapedField} ${sanitizedDirection}`;
      });
      query += ` ORDER BY ${orderClauses.join(", ")}`;
    }

    if (limit !== null) {
      query += ` LIMIT ${limit}`;
    }

    query += ` OFFSET ${offset}`;

    logger.debug(`Executing query: ${query} with values:`, values);

    try {
      const result = await this.pool.query(query, values);
      logger.info(
        `Final query: ${query} with values: ${JSON.stringify(values)}`,
      );
      logger.info(`Fetched ${result.rows.length} rows from ${table}`);

      // Parse TEXT array fields in the results, but only for known JSONB fields
      const parsedRows = result.rows.map((row) => {
        Object.keys(row).forEach((key) => {
          // Only attempt to parse if it's a known JSONB field from options
          if (
            options?.jsonFields?.includes(key) &&
            typeof row[key] === "string" &&
            row[key].startsWith("{") &&
            row[key].endsWith("}")
          ) {
            try {
              // Convert TEXT array to real array and parse JSON elements
              const arrayString = row[key].slice(1, -1);
              if (arrayString) {
                const elements = arrayString.match(
                  /(".*?"|[^,]+)(?=\s*,|\s*$)/g,
                );
                row[key] = elements.map((element) => {
                  const cleaned = element.trim().replace(/^"|"$/g, "");
                  return JSON.parse(cleaned);
                });
              } else {
                row[key] = [];
              }
            } catch (e) {
              // Only log if debug logging is enabled
              if (logger.isDebugEnabled()) {
                logger.warn(`Failed to parse JSON field ${key}`);
              }
            }
          }
        });
        return row;
      });

      return parsedRows;
    } catch (error) {
      logger.error(`Error fetching data from ${table}: ${error.message}`);
      throw error;
    }
  }

  async fetch_source_id(table, field, value) {
    const query = `SELECT id FROM ${table} WHERE ${field} = $1`;
    const result = await this.pool.query(query, [value]);
    return result.rows[0]?.id;
  }
  async autoname_to_fk(src_field, dest_form, dest_field, data, fuzzy = false) {
    // given a source_field get the source_field_auto_name and use it to look up the destination_field fk ,
    // then insert the destination_field fk into the data for the destination_form
    const src_field_auto_name = `${src_field}_auto_name`;
    const op = fuzzy ? "ILIKE" : "=";
    if (data.src_field_auto_name) {
      const value = fuzzy
        ? `%${data[src_field_auto_name]}%`
        : data[src_field_auto_name];
      const query = `SELECT id FROM ${dest_form} WHERE ${src_field_auto_name} ${op} ${value}`;
      const [result] = await this.pool.query(query);
      return result.id;
    }
    return null;
  }

  // Modify the base formatting method
  _formatJSONBField(value) {
    if (!value) return null;

    if (typeof value === "string") {
      try {
        value = JSON.parse(value);
      } catch (e) {
        return value;
      }
    }

    // Just stringify the value - no PostgreSQL array syntax
    return JSON.stringify(value);
  }

  // Update insertData method
  async insertData(table, data, options = {}) {
    let query;
    try {
      // Process JSONB fields
      const processedData = { ...data };
      if (options.jsonFields) {
        options.jsonFields.forEach((field) => {
          if (processedData[field]) {
            processedData[field] = this._formatJSONBField(processedData[field]);
          }
        });
      }

      const uniqueConstraints = await this.getTableUniqueConstraints(table);
      logger.debug(`Database constraints for ${table}:`, uniqueConstraints);
      logger.debug(`Incoming data:`, processedData);

      const fields = Object.keys(processedData);
      const values = Object.values(processedData);
      const placeholders = values.map((_, i) => `$${i + 1}`);

      // Get non-constraint qualifier fields
      const qualifierFields = options.qualifiers?.map((q) => q.field) || [];
      const allConstraintColumns = uniqueConstraints.flatMap((c) => c.columns);
      const additionalFields = qualifierFields.filter(
        (field) => !allConstraintColumns.includes(field),
      );

      logger.debug(`Additional qualifier fields to check: ${additionalFields}`);

      if (uniqueConstraints.length > 0) {
        // Try multi-column constraint first
        const multiColumnConstraint = uniqueConstraints.find(
          (c) => c.columns.length > 1,
        );
        const singleColumnConstraint = uniqueConstraints.find(
          (c) => c.columns.length === 1,
        );

        // Choose which constraint to use
        const usableConstraint =
          multiColumnConstraint &&
          multiColumnConstraint.columns.every(
            (col) => processedData[col] !== undefined,
          )
            ? multiColumnConstraint
            : singleColumnConstraint &&
                processedData[singleColumnConstraint.columns[0]] !== undefined
              ? singleColumnConstraint
              : null;

        logger.debug(`Selected constraint:`, usableConstraint);

        if (usableConstraint) {
          const constraintClause = usableConstraint.columns
            .map((col) => `"${col}"`)
            .join(", ");

          // Build WHERE clause
          const whereClause =
            additionalFields.length > 0
              ? `WHERE ${additionalFields
                  .map((field) => `target."${field}" = EXCLUDED."${field}"`)
                  .join(" AND ")}`
              : "";

          query = `
            INSERT INTO "${table}" as target
            (${fields.map((f) => `"${f}"`).join(", ")})
            VALUES (${placeholders})
            ON CONFLICT (${constraintClause})
            DO UPDATE SET
              ${fields
                .filter(
                  (field) =>
                    !["id", "created_on", "created_by"].includes(field),
                )
                .map((field) => `"${field}" = EXCLUDED."${field}"`)
                .join(",\n            ")},
              updated_on = CURRENT_TIMESTAMP,
              updated_by = ${await this.get_eadmin_id()}
            ${whereClause}
            RETURNING *, 
              CASE 
                WHEN xmax = 0 THEN true
                ELSE false
              END as inserted
          `;
        } else {
          // Fall back to simple insert if no usable constraints
          logger.warn(
            `Missing some constraint columns in data, falling back to insert`,
          );
          query = `
            INSERT INTO "${table}"
            (${fields.map((f) => `"${f}"`).join(", ")})
            VALUES (${placeholders})
            RETURNING *
          `;
        }
      } else {
        query = `
          INSERT INTO "${table}"
          (${fields.map((f) => `"${f}"`).join(", ")})
          VALUES (${placeholders})
          RETURNING *
        `;
      }

      logger.debug("Insert/Upsert query:", query);
      logger.debug("Insert/Upsert values:", values);

      if (this.isDryRun) {
        logger.info(`[DRY RUN] Would execute: ${query}`);
        return {
          id: -1,
          operation: "dryrun",
          data: processedData,
        };
      }

      const result = await this.pool.query(query, values);

      // Add null check and error handling
      if (!result || !result.rows || result.rows.length === 0) {
        logger.error(`Insert/upsert returned no rows for table ${table}`);
        throw new Error(`Insert/upsert failed - no rows returned`);
      }

      // Fix operation determination
      const operation = result.rows[0]?.inserted ? "insert" : "update";

      logger.debug(
        `Insert result - operation: ${operation}, inserted: ${result.rows[0]?.inserted}`,
      );

      return {
        id: result.rows[0]?.id,
        operation: operation,
        data: {
          ...result.rows[0],
          inserted: result.rows[0]?.inserted,
        },
      };
    } catch (error) {
      logger.error(`Error in insert/upsert for ${table}:`, error);
      if (query) {
        logger.error(`Failed query:`, query);
      }
      throw new Error(`Insert/upsert failed: ${error.message}`);
    }
  }
  async raw_query(query, values = [], filters = null, limit = null) {
    try {
      if (query.toLowerCase().includes("select")) {
        if (filters && Array.isArray(filters) && filters.length > 0) {
          logger.debug(
            "Adding command-line filters to raw query:",
            JSON.stringify(filters),
          );

          const filterClauses = filters
            .map((filter) => {
              if (Array.isArray(filter)) {
                const [field, operator, value] = filter;
                if (operator.toLowerCase() === "in") {
                  if (Array.isArray(value)) {
                    const quotedValues = value.map((v) => `'${v}'`).join(", ");
                    return `${field} ${operator} (${quotedValues})`;
                  }
                }
                return `${field} ${operator} '${value}'`;
              }
              return null;
            })
            .filter(Boolean)
            .join(" AND ");

          if (filterClauses) {
            const hasWhere = query.toLowerCase().includes("where");
            if (hasWhere) {
              const lowerQuery = query.toLowerCase();
              const insertPosition = lowerQuery.indexOf("where");
              // Find the position of the last condition in the WHERE clause
              const orderByPos = lowerQuery.indexOf("order by");
              const groupByPos = lowerQuery.indexOf("group by");
              const limitPos = lowerQuery.indexOf("limit");

              // Find the end of the WHERE clause
              let endPosition = query.length;
              [orderByPos, groupByPos, limitPos].forEach((pos) => {
                if (pos !== -1 && pos < endPosition) {
                  endPosition = pos;
                }
              });

              // Insert the new filter at the end of existing WHERE conditions
              query =
                query.slice(0, endPosition).trim() +
                ` AND (${filterClauses})` +
                (endPosition < query.length
                  ? " " + query.slice(endPosition)
                  : "");
            } else {
              query = `${query} WHERE ${filterClauses}`;
            }
          }
        }

        if (limit !== null) {
          query += ` LIMIT ${limit}`;
        }

        logger.debug(`Executing raw query: ${query}`);
        const result = await this.pool.query(query, values || []);
        return result.rows;
      }

      // Original filter processing for non-raw queries
      const queryValues = [...values];
      if (filters && Array.isArray(filters) && filters.length > 0) {
        logger.debug("Processing filters:", JSON.stringify(filters));

        const filterClauses = filters
          .map((filter) => {
            if (Array.isArray(filter)) {
              const [field, operator, value] = filter;
              queryValues.push(value);
              return `${field} ${operator} $${queryValues.length}`;
            }
            return null;
          })
          .filter(Boolean)
          .join(" AND ");

        if (filterClauses) {
          const hasWhere = query.toLowerCase().includes("where");
          if (hasWhere) {
            query = query.replace(/where/i, `WHERE ${filterClauses} AND`);
          } else {
            query = `${query} WHERE ${filterClauses}`;
          }
        }
      }

      if (limit !== null) {
        query += ` LIMIT ${limit}`;
      }

      logger.debug(`Executing query: ${query}`);
      logger.debug(`With values: ${JSON.stringify(queryValues)}`);

      const result = await this.pool.query(query, queryValues);
      return result.rows;
    } catch (error) {
      logger.error(`Error executing raw query: ${error.message}`);
      logger.error(`Query was: ${query}`);
      throw error;
    }
  }
  async upsertData(table, data, options = {}, sourceData = {}) {
    data = await cleanup_nulls(data);

    // Process array and JSONB fields
    Object.keys(data).forEach((key) => {
      if (options.jsonFields && options.jsonFields.includes(key)) {
        data[key] = this._formatJSONBField(data[key]);
      } else if (Array.isArray(data[key])) {
        data[key] = this._formatArrayForPostgres(data[key]);
      }
    });

    if (!data.created_on) data.created_on = new Date();
    if (!data.created_by) data.created_by = await this.get_eadmin_id();

    // Process qualifiers to build filters
    const validFilters = [];
    if (options.qualifiers) {
      logger.debug(
        `Processing qualifiers: ${JSON.stringify(options.qualifiers)}`,
      );

      for (const qualifier of options.qualifiers) {
        if (Array.isArray(qualifier)) {
          const [field, operator, value] = qualifier;
          if (!isEmptyValue(value)) {
            validFilters.push([field, operator, value]);
          }
        } else if (qualifier.field) {
          let value;
          if (qualifier.lookup) {
            // Perform the lookup using sourceData
            const lookupValue = sourceData[qualifier.lookup.sourceField];
            const query = `
              SELECT ${qualifier.lookup.returnField} 
              FROM ${qualifier.lookup.table} 
              WHERE ${qualifier.lookup.destinationField} = $1
            `;
            const result = await this.pool.query(query, [lookupValue]);
            value = result.rows[0]?.[qualifier.lookup.returnField];

            logger.debug(
              `Lookup result for ${qualifier.field}: ${value} (from ${lookupValue})`,
            );
          } else {
            value = data[qualifier.field];
          }

          if (!isEmptyValue(value)) {
            validFilters.push([qualifier.field, "=", value]);
          }
        }
      }

      logger.debug(
        `Processed qualifiers into filters: ${JSON.stringify(validFilters, null, 2)}`,
      );
    }

    // Add default archive/delete filters unless explicitly disabled
    if (!options.skipDefaultFilters) {
      validFilters.push(["archived", "or_null", false]); // Will generate: archived IS FALSE OR archived IS NULL
      validFilters.push(["deleted", "or_null", false]); // Will generate: deleted IS FALSE OR deleted IS NULL
    }

    // Check if record exists using validFilters
    const existingRecord =
      validFilters.length > 0
        ? await this.fetchData(table, { filters: validFilters, limit: 1 })
        : [];

    logger.debug(
      `Fetched existing record: ${JSON.stringify(existingRecord, null, 2)}`,
    );

    if (existingRecord && existingRecord.length > 0) {
      const existing = existingRecord[0];
      const hasChanged = await has_changed(existing, data);

      if (!hasChanged) {
        logger.info(
          `No changes detected for ${table} record ${existing.id} - skipping`,
        );
        return {
          id: existing.id,
          operation: this.isDryRun ? "dryrun" : "skip",
          would_be_operation: "skip",
          message: "No changes detected",
        };
      }

      const id = existing.id;
      // Only proceed with update if changes were detected
      if (this.isDryRun) {
        logger.info(`[DRY RUN] Would update ${table}: ${JSON.stringify(data)}`);
        return {
          id: id,
          operation: "dryrun",
          would_be_operation: "update",
          data: data,
        };
      }

      logger.info(`Updating ${table} record ${id}`);
      return await this.updateData(table, data, {
        filters: [["id", "=", id]],
      });
    } else {
      logger.info(
        `${this.isDryRun ? "[DRY RUN] Would insert" : "Inserting"} new ${table} record`,
      );

      if (this.isDryRun) {
        logger.info(`[DRY RUN] Would insert ${table}: ${JSON.stringify(data)}`);
        return {
          operation: "dryrun",
          would_be_operation: "insert",
          data: data,
        };
      }

      return await this.insertData(table, data);
    }
  }

  // Update updateData method
  async updateData(table, data, options = {}) {
    // Process array fields consistently
    let processedData = { ...data };

    // Always ensure updated_on and updated_by are set
    const now = new Date();
    processedData.updated_on = now;
    processedData.updated_by = await this.get_eadmin_id();

    // Rest of preprocessing...
    Object.keys(processedData).forEach((key) => {
      if (options.jsonFields && options.jsonFields.includes(key)) {
        processedData[key] = this._formatJSONBField(processedData[key]);
      } else if (Array.isArray(processedData[key])) {
        processedData[key] = this._formatArrayForPostgres(processedData[key]);
      }
    });

    // Remove sys_period from the data if it exists
    delete processedData.sys_period; // Never update sys_period directly

    // Clean up nulls but preserve updated_on and updated_by
    const cleanedData = await cleanup_nulls(processedData);
    processedData = {
      ...cleanedData,
      updated_on: now,
      updated_by: processedData.updated_by,
    };

    if (Object.keys(processedData).length === 0) {
      logger.info(`No data to update for ${table} after cleanup`);
      return {
        rowCount: 0,
        operation: "skip",
        data: processedData,
      };
    }

    // Get table constraints to check for temporal versioning
    const uniqueConstraints = await this.getTableUniqueConstraints(table);
    logger.debug(`Database constraints for ${table}:`, uniqueConstraints);
    logger.debug(`Incoming data:`, processedData);

    const client = await this.pool.connect();
    try {
      await client.query("BEGIN");

      // First fetch the record to update
      const filters = options.filters || options.qualifiers || [];
      logger.debug(`Fetching with filters:`, filters);
      const existingRecord = await this.fetchData(table, { filters, limit: 1 });
      logger.debug(`Found existing record:`, existingRecord);

      if (!existingRecord?.length) {
        throw new Error(
          `Record not found in ${table} with filters ${JSON.stringify(filters)}`,
        );
      }

      // Build values array
      const values = Object.values(processedData).filter(
        (_, index) =>
          !["id", "created_on", "created_by", "sys_period"].includes(
            Object.keys(processedData)[index],
          ),
      );
      let paramIndex = values.length + 1;
      logger.debug(`Values array:`, values);

      // Build SET clause
      const setClause = Object.keys(processedData)
        .filter(
          (key) =>
            !["id", "created_on", "created_by", "sys_period"].includes(key),
        )
        .map((key, index) => `${this._escapeIdentifier(key)} = $${index + 1}`)
        .join(", ");
      logger.debug(`SET clause:`, setClause);

      let query = `
        UPDATE ${this._escapeIdentifier(table)} 
        SET ${setClause}`;

      // Add WHERE clause
      if (filters.length > 0) {
        const whereClause = filters
          .map(([field, op, value]) => {
            values.push(value);
            return `${this._escapeIdentifier(field)} ${op} $${paramIndex++}`;
          })
          .join(" AND ");
        query += ` WHERE ${whereClause}`;
      }
      logger.debug(`Final WHERE clause:`, query);

      query += ` RETURNING id`;

      logger.debug(`Update query: ${query}`);
      logger.debug(`Update values: ${JSON.stringify(values)}`);

      if (this.isDryRun) {
        logger.info(
          `[DRY RUN] Would update ${table}: ${JSON.stringify(processedData)}`,
        );
        return {
          operation: "dryrun",
          data: processedData,
        };
      }

      const result = await client.query(query, values);
      await client.query("COMMIT");

      logger.debug(`Updated ${result?.rowCount} rows in ${table}`);
      return {
        id: result.rows[0]?.id,
        rowCount: result?.rowCount,
        operation: "update",
        data: processedData,
      };
    } catch (error) {
      await client.query("ROLLBACK");
      logger.error(`Error updating ${table}: ${error.message}`);
      throw error;
    } finally {
      client.release();
    }
  }

  async lookupForeignKey(
    sourceTable,
    sourceField,
    sourceValue,
    destTable,
    destField,
  ) {
    const query = `SELECT id FROM ${destTable} WHERE ${destField} = $1`;
    logger.debug(`Executing query: ${query} with value: ${sourceValue}`);
    const client = await this.pool.connect();
    try {
      const result = await client.query(query, [sourceValue]);
      if (result.rows.length > 0) {
        return result.rows[0].id;
      }
      logger.warn(
        `No matching ${destTable} found for ${sourceField} = ${sourceValue}`,
      );
      return null;
    } finally {
      client.release();
    }
  }

  async lookupSourceValue(sourceTable, sourceField, sourceValue, selectField) {
    const query = `SELECT ${selectField} FROM ${sourceTable} WHERE ${sourceField} = $1`;
    logger.info(`Executing query: ${query} with value: ${sourceValue}`);
    try {
      const result = await this.pool.query(query, [sourceValue]);
      if (result.rows.length > 0) {
        logger.debug(`Found value: ${result.rows[0][selectField]}`);
        return result.rows[0][selectField];
      }
      logger.warn(
        `No matching ${sourceTable} found for ${sourceField} = ${sourceValue}`,
      );
      return null;
    } catch (error) {
      logger.error(`Error in lookupSourceValue: ${error.message}`);
      throw error;
    }
  }

  async get_eadmin_id() {
    const query = `SELECT id FROM form_user WHERE username = 'eadmin'`;
    const result = await this.pool.query(query);
    if (result.rows.length > 0) {
      return result.rows[0].id;
    }
    logger.warn(
      `No matching form_user found for username 'eadmin' defaulting to 22`,
    );
    return 22;
  }

  async fetchOrCreate(table, data, filter) {
    if (!filter || !Array.isArray(filter)) {
      logger.warn(
        `Invalid filter passed to fetchOrCreate for ${table}: ${JSON.stringify(filter)}`,
      );
      filter = [];
    }

    const existing = await this.fetchData(table, { filters: filter, limit: 1 });
    if (existing && existing.length > 0) {
      logger.info(
        `Record in ${table} with filters ${JSON.stringify(filter)} already exists, using existing id ${existing[0].id}`,
      );
      return existing[0].id;
    }

    logger.info(
      `Record in ${table} with filters ${JSON.stringify(filter)} does not exist, creating new record`,
    );

    if (!data.created_on) data.created_on = new Date();
    if (!data.created_by) data.created_by = await this.get_eadmin_id();

    const result = await this.insertData(table, data);
    logger.info(
      `Created new record in ${table} with result ${JSON.stringify(result)}`,
    );
    return result?.id;
  }

  async transaction(callback) {
    const client = await this.pool.connect();
    try {
      await client.query("BEGIN");
      const result = await callback(client);
      await client.query("COMMIT");
      return result;
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  }

  async check_table_exists(table) {
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      )
    `;
    const tableExists = await this.pool.query(tableExistsQuery, [table]);
    return tableExists.rows[0].exists;
  }
  async insert_gerund(gerund, source_id, dest_id) {
    const gerundTableName = `gr_${gerund}`;

    const tableExists = await this.check_table_exists(gerundTableName);
    if (!tableExists) {
      logger.warn(`Table ${gerundTableName} does not exist`);
      return null;
    }

    // Get the column names that start with form_ ordered by ordinal position
    const columnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = $1 
      AND column_name LIKE 'form_%' 
      ORDER BY ordinal_position
    `;
    const columnResult = await this.pool.query(columnQuery, [gerundTableName]);

    if (columnResult.rows.length < 2) {
      logger.error(
        `Expected at least 2 form_* columns in ${gerundTableName}, found ${columnResult.rows.length}`,
      );
      return null;
    }

    const [sourceFkColumn, destFkColumn] = columnResult.rows.map(
      (row) => row.column_name,
    );

    logger.debug(
      `Using columns: ${sourceFkColumn}, ${destFkColumn} for gerund ${gerundTableName}`,
    );

    // Check for existing record
    const existingQuery = `
      SELECT id FROM ${gerundTableName}
      WHERE ${sourceFkColumn} = $1 AND ${destFkColumn} = $2
    `;
    const existingResult = await this.pool.query(existingQuery, [
      source_id,
      dest_id,
    ]);

    if (existingResult.rows.length > 0) {
      logger.info(`Gerund record already exists in ${gerundTableName}`);
      return {
        id: existingResult.rows[0].id,
        operation: "none",
        status: "success",
      };
    }

    // Insert new record
    const insertQuery = `
      INSERT INTO ${gerundTableName} (${sourceFkColumn}, ${destFkColumn})
      VALUES ($1, $2)
      RETURNING id
    `;

    try {
      const insertResult = await this.pool.query(insertQuery, [
        source_id,
        dest_id,
      ]);
      logger.info(`Inserted new gerund record in ${gerundTableName}`);
      return {
        id: insertResult.rows[0].id,
        operation: "insert",
        status: "success",
      };
    } catch (error) {
      logger.error(`Error inserting gerund record: ${error.message}`);
      return {
        operation: "error",
        status: "error",
        message: error.message,
      };
    }
  }

  async insert_subform(subform, source_id, dest_id) {
    const subformTableName = `sf_${subform}`;

    // Check if table exists
    const tableExists = await this.check_table_exists(subformTableName);
    if (!tableExists) {
      logger.warn(`Table ${subformTableName} does not exist`);
      return null;
    }

    // Get the column names that end with _fk ordered by ordinal position
    const columnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = $1 
      AND column_name LIKE '%_fk' 
      AND column_name NOT LIKE 'sys_%'
      ORDER BY ordinal_position
    `;
    const columnResult = await this.pool.query(columnQuery, [subformTableName]);

    if (columnResult.rows.length < 2) {
      logger.error(
        `Expected at least 2 *_fk columns in ${subformTableName}, found ${columnResult.rows.length}`,
      );
      return null;
    }

    const [sourceFkColumn, destFkColumn] = columnResult.rows.map(
      (row) => row.column_name,
    );

    logger.debug(
      `Using columns: ${sourceFkColumn}, ${destFkColumn} for subform ${subformTableName}`,
    );

    // Check for existing record
    const existingQuery = `
      SELECT id FROM ${subformTableName}
      WHERE ${sourceFkColumn} = $1 
      AND ${destFkColumn} = $2 
      AND delete IS NOT TRUE 
      AND archive IS NOT TRUE
    `;
    const existingResult = await this.pool.query(existingQuery, [
      source_id,
      dest_id,
    ]);

    if (existingResult.rows.length > 0) {
      logger.info(`Subform record already exists in ${subformTableName}`);
      return {
        id: existingResult.rows[0].id,
        operation: "none",
        status: "success",
      };
    }

    // Insert new record
    const insertQuery = `
      INSERT INTO ${subformTableName} (${sourceFkColumn}, ${destFkColumn}, delete, archive)
      VALUES ($1, $2, FALSE, FALSE)
      RETURNING id
    `;

    try {
      const insertResult = await this.pool.query(insertQuery, [
        source_id,
        dest_id,
      ]);
      logger.info(`Inserted new subform record in ${subformTableName}`);
      return {
        id: insertResult.rows[0].id,
        operation: "insert",
        status: "success",
      };
    } catch (error) {
      logger.error(`Error inserting subform record: ${error.message}`);
      throw error;
    }
  }

  async getTableUniqueConstraints(table) {
    const query = `
      SELECT 
        i.indexrelid::regclass as index_name,
        ARRAY_AGG(a.attname ORDER BY array_position(i.indkey, a.attnum)) as columns
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      WHERE i.indrelid = '${table}'::regclass
      AND i.indisunique = true
      AND NOT i.indisprimary
      GROUP BY i.indexrelid;
    `;

    try {
      const result = await this.pool.query(query);
      logger.debug(`Raw constraint results for ${table}:`, result.rows);

      // Convert Postgres array strings to regular arrays
      const constraints = result.rows.map((row) => ({
        name: row.index_name,
        columns: row.columns.replace(/[{}]/g, "").split(","),
      }));

      logger.debug(`Processed constraints for ${table}:`, constraints);
      return constraints;
    } catch (error) {
      logger.error(`Error getting unique constraints for ${table}:`, error);
      return [];
    }
  }

  // Remove or update _formatArrayForPostgres since we're handling JSONB differently
  _formatArrayForPostgres(array) {
    if (!Array.isArray(array)) return array;
    if (typeof array === "string") {
      try {
        array = JSON.parse(array);
      } catch (e) {
        return array;
      }
    }
    return JSON.stringify(array);
  }
}

module.exports = PostgresConnector;
