const axios = require("axios");
const querystring = require("querystring");
const Logger = require("../utils/logger");
const logger = new Logger("API_CONNECTOR");

class APIConnector {
  constructor(config) {
    this.config = config;
    this.isDryRun = config.isDryRun || false;

    const uri = this.config.api_uri;
    const user = this.config.api_user;
    const password = this.config.api_password;

    if (!uri) {
      throw new Error(`No API URI configured for ${uri} connection`);
    }

    this.baseURL = uri + "/api";
    this.client = axios.create({
      baseURL: uri,
      auth: { username: user, password: password },
      timeout: 30000,
      validateStatus: function (status) {
        return status >= 200 && status < 500;
      },
    });

    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          logger.error(`API Error Response:
            Status: ${error.response.status}
            Data: ${JSON.stringify(error.response.data, null, 2)}
            Headers: ${JSON.stringify(error.response.headers, null, 2)}
          `);
        } else if (error.request) {
          logger.error(`API No Response Error: ${error.request}`);
        } else {
          logger.error(`API Request Setup Error: ${error.message}`);
        }
        return Promise.reject(error);
      },
    );

    logger.info(`Initialized APIConnector for ${uri}`);
  }

  _buildFilterString(qualifiers) {
    if (!qualifiers || qualifiers.length === 0) {
      logger.warn("No qualifiers provided");
      return "";
    }

    logger.info(`Building filter string from: ${JSON.stringify(qualifiers)}`);
    const filterString = qualifiers
      .map((q) => `${q.field}:${q.value}`)
      .join(",");
    logger.info(`Generated filter: ${filterString}`);
    return filterString;
  }

  async upsertData(form, data, options = {}) {
    const { qualifiers = [], customEndpoints = null } = options;

    if (customEndpoints) {
      logger.info(
        `API customUpsert - Endpoints: ${JSON.stringify(customEndpoints)}`,
      );
      logger.info(
        `API customUpsert - Qualifiers: ${JSON.stringify(qualifiers)}`,
      );

      // Build filter string directly here instead of using PostgreSQL connector
      const filterString = qualifiers
        .map((q) => `${q.field}:${q.value}`)
        .join(",");

      logger.info(`API customUpsert - Filter string: ${filterString}`);

      return this.customUpsert(data, {
        ...customEndpoints,
        filterString,
      });
    }

    logger.debug(`upsertData called with form: ${form}`);
    logger.debug(`Data: ${JSON.stringify(data)}`);
    logger.debug(`Options: ${JSON.stringify(options)}`);

    if (!this.client) {
      throw new Error("API client not initialized");
    }

    try {
      // First, try to find existing record
      const filterString = this._buildFilterString(qualifiers);
      if (!filterString) {
        logger.warn("No valid filter string generated from qualifiers");
        return { id: null, operation: "none" };
      }

      const searchUrl = `${this.baseURL}/view/${form}/?filter=${filterString}`;
      logger.debug(`Searching for existing record: ${searchUrl}`);

      const searchResponse = await this.client.get(searchUrl);
      logger.debug(`Search response: ${JSON.stringify(searchResponse.data)}`);

      if (searchResponse.data && searchResponse.data.length > 0) {
        // Record exists, perform PUT
        const existingRecord = searchResponse.data[0];
        logger.info(
          `Found existing record with ID: ${existingRecord.id}, updating...`,
        );

        const updateUrl = `${this.baseURL}/form/${form}/${existingRecord.id}`;
        const updateResponse = await this.client.put(updateUrl, data, {
          headers: { "Content-Type": "application/json" },
        });

        return {
          operation: "update",
          id: existingRecord.id,
          data: updateResponse.data,
        };
      } else {
        // Record doesn't exist, perform POST
        logger.info(`No existing record found, creating new...`);
        const createUrl = `${this.baseURL}/form/${form}`;
        const createResponse = await this.client.post(createUrl, data, {
          headers: { "Content-Type": "application/json" },
        });

        return {
          operation: "insert",
          id: createResponse.data.id,
          data: createResponse.data,
        };
      }
    } catch (error) {
      logger.error(`Error in upsertData: ${error.message}`);
      logger.error(`Stack: ${error.stack}`);
      throw error;
    }
  }

  async customUpsert(data, endpoints) {
    const {
      check: checkEndpoint,
      create: createEndpoint,
      update: updateEndpoint,
      checkField = "id",
      qualifiers = [],
    } = endpoints;

    try {
      // Build check URL with qualifiers
      const queryParams = qualifiers
        .map((q) => `${q.field}=${encodeURIComponent(q.value)}`)
        .join("&");
      const checkUrl = `${this.baseURL}/${checkEndpoint}?${queryParams}`;

      logger.info(`API Request - GET ${checkUrl}`);
      const checkResponse = await this.client.get(checkUrl);

      const exists = checkResponse.data && checkResponse.data.length > 0;

      if (exists) {
        const existingRecord = checkResponse.data[0];
        const updateUrl = `${this.baseURL}/${updateEndpoint.replace(":id", existingRecord[checkField])}`;

        logger.info(`API Request - PUT ${updateUrl}`);
        const updateResponse = await this.client.put(updateUrl, data);
        logger.info(`API Response - ${updateResponse.status}`);

        return {
          operation: "update",
          id: existingRecord[checkField],
          data: updateResponse.data,
        };
      } else {
        const createUrl = `${this.baseURL}/${createEndpoint}`;
        logger.info(`API Request - POST ${createUrl}`);

        const createResponse = await this.client.post(createUrl, data);
        logger.info(`API Response - ${createResponse.status}`);

        return {
          operation: "insert",
          id: createResponse.data[checkField],
          data: createResponse.data,
        };
      }
    } catch (error) {
      logger.error(`API Error: ${error.message}`);
      if (error.response) {
        logger.error(`Status: ${error.response.status}`);
        logger.error(`Response: ${JSON.stringify(error.response.data)}`);
      }
      throw error;
    }
  }

  async fetchData(form, options = {}) {
    const { filters = [], qualifiers = [], limit = null } = options;

    // Build URL with filters and qualifiers
    const queryParams = {};

    if (limit) {
      queryParams.limit = limit;
    }

    // Handle traditional filters
    if (filters.length > 0) {
      queryParams.filter = this._buildFilterString(filters);
    }

    // Handle qualifiers (same format as filters)
    if (qualifiers.length > 0) {
      const qualifierFilters = this._buildFilterString(qualifiers);
      queryParams.filter = queryParams.filter
        ? `${queryParams.filter},${qualifierFilters}`
        : qualifierFilters;
    }

    const url = `api/view/${form}/?${querystring.stringify(queryParams)}`;

    try {
      logger.debug(`Fetching data from: ${url}`);
      const response = await this.client.get(url);
      return response.data;
    } catch (error) {
      logger.error(`Error fetching data from API: ${error.message}`);
      throw error;
    }
  }

  async post(endpoint, data) {
    const headers = {
      "Content-Type": "application/json",
    };
    const response = await this.client.post(endpoint, data, { headers });
    return response.data;
  }

  async updateData(form, id, data) {
    const baseUrl = this.config.api_url;
    const url = `${baseUrl}/api/form/${form}/${id}`;
    const headers = {
      "Content-Type": "application/json",
    };
    const auth = {
      username: this.config.api_user,
      password: this.config.api_password,
    };
    const response = await this.client.put(url, data, { headers, auth });
    return response.data;
  }

  async connect() {
    logger.info("API connector ready");
  }
}

module.exports = APIConnector;
