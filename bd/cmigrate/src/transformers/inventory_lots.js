const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("InventoryLotsTransformer");
const { isEmptyValue, cleanup_nulls } = require("../utils/tools");
const { getMapValue } = require("../utils/maps");
const fs = require("fs");
const moment = require("moment");
const path = require("path");

module.exports = class InventoryLots extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.transformType = "bulk";
    this.sourceTable = "cpr_lots";
    this.sourceRawQuery = fs.readFileSync(
      __dirname + "/fixtures/inventory_all.sql",
      "utf8",
    );
    this.destTable = "form_po";
    this.baseurl = this.config.dest.api_uri;
    this.customEndpoints = {
      check: `${this.baseurl}/api/view/form_po`,
      create: `${this.baseurl}/api/inventory/receive`,
      update: `${this.baseurl}/api/inventory/receive/:id`,
      checkField: "po_id",
    };
  }

  async lookupSiteId(data) {
    const siteno = getMapValue("SiteMap", data, 2);
    return siteno;
  }

  async _getSupplier(record) {
    let supplier = record.supplier;
    if (isEmptyValue(supplier)) {
      supplier = record.manufacturer;
    }
    if (isEmptyValue(supplier)) {
      logger.warn(`No supplier found for record: ${JSON.stringify(record)}`);
      return null;
    }
    const supplierId = await this.db.dst.fetchOrCreate(
      "form_list_supplier",
      {
        name: supplier,
        external_id: record.supplier_id,
      },
      [
        ["name", "ilike", supplier],
        ["external_id", "=", record.supplier_id],
      ],
    );

    logger.debug(`Supplier ID: ${supplierId}`);
    return supplierId;
  }

  async lookupInventory(record) {
    try {
      const existingRecord = await this.db.dst.fetchData("form_inventory", {
        filters: [["external_id", "=", record.external_id]],
        limit: 1,
      });

      if (existingRecord && existingRecord.length > 0) {
        logger.debug(
          `Found inventory ID ${existingRecord[0].id} for external_id ${record.external_id}`,
        );
        return existingRecord[0].id;
      }

      logger.warn(
        `No matching inventory found for external_id: ${record.external_id}`,
      );
      return null;
    } catch (error) {
      logger.error(
        `Error looking up inventory for external_id ${record.external_id}: ${error.message}`,
      );
      throw error;
    }
  }

  async transform(sourceData) {
    try {
      let recordsBySiteAndSupplier = {};

      for (const record of sourceData) {
        const supplier_id = await this._getSupplier(record);
        const inventory_id = await this.lookupInventory(record);
        if (!inventory_id) {
          logger.warn(
            `No inventory ID found for record: ${JSON.stringify(record)}`,
          );
          continue;
        }
        const site_id = await this.lookupSiteId(record.siteno);

        // Create composite key for site+supplier
        const key = `${site_id}_${supplier_id}`;

        if (!recordsBySiteAndSupplier[key]) {
          recordsBySiteAndSupplier[key] = {
            receipt_date: moment().format("MM/DD/YYYY"),
            site_id,
            supplier_id,
            subform_lots: {},
          };
        }

        // Create composite key for inventory within the group
        const inventoryKey = `${inventory_id}`;

        if (!recordsBySiteAndSupplier[key].subform_lots[inventoryKey]) {
          recordsBySiteAndSupplier[key].subform_lots[inventoryKey] = {
            inventory_id,
            supplier_id,
            site_id,
            quantity: 0,
            cost_each: record.site_inventory_last_cost || record.lot_cost,
            lot_no: String(record.lot_number)
              ?.replace(" ", "")
              ?.trim()
              ?.toUpperCase(),
            expiration: record.expiration_date,
          };
        }

        // Add quantities together
        recordsBySiteAndSupplier[key].subform_lots[inventoryKey].quantity +=
          parseFloat(record.quantity) || 0;
      }

      // Convert subform_lots back to array
      for (const key in recordsBySiteAndSupplier) {
        recordsBySiteAndSupplier[key].subform_lots = Object.values(
          recordsBySiteAndSupplier[key].subform_lots,
        );
      }

      recordsBySiteAndSupplier = await cleanup_nulls(recordsBySiteAndSupplier);
      logger.info(`DRY RUN: would post to ${this.customEndpoints.create}`);
      logger.debug(
        `Records by site and supplier: ${JSON.stringify(recordsBySiteAndSupplier)}`,
      );

      if (!this.isDryRun) {
        const results = [];

        for (const key in recordsBySiteAndSupplier) {
          const poData = recordsBySiteAndSupplier[key];
          try {
            logger.debug(
              `Processing site ${poData.site_id} supplier ${poData.supplier_id} with ${poData.subform_lots.length} lots`,
            );
            logger.debug(`Request payload: ${JSON.stringify(poData)}`);

            const result = await this.destinationConnector.apiPost(
              this.customEndpoints.create,
              poData,
            );

            results.push({
              site_id: poData.site_id,
              supplier_id: poData.supplier_id,
              status: "success",
              record_count: poData.subform_lots.length,
              receipt_id: result?.receipt_id,
            });
          } catch (error) {
            logger.error(
              `Error processing site ${poData.site_id} supplier ${poData.supplier_id}:
              Status: ${error.response?.status}
              Message: ${error.message}
              Response Data: ${JSON.stringify(error.response?.data, null, 2)}
              Request Data: ${JSON.stringify(poData)}`,
            );

            results.push({
              site_id: poData.site_id,
              supplier_id: poData.supplier_id,
              status: "error",
              error: error.message,
              error_details: error.response?.data,
              request_data: poData,
              record_count: poData.subform_lots.length,
            });
          }
        }

        return results;
      } else {
        return sourceData.map((record) => ({
          status: "would_create",
          supplier_id: record.supplier_id,
          record_count: 1,
          data: {
            ndc: record.ndc_code,
            lot_no: record.lot_number,
          },
        }));
      }
    } catch (error) {
      logger.error(`Error in transform: ${error.message}`);
      throw error;
    }
  }

  async validateTransformedBatch(batch) {
    if (!batch.transformedData.supplier_id) {
      throw new Error("Missing supplier_id in transformed data");
    }
    if (!batch.transformedData.site_id) {
      throw new Error("Missing site_id in transformed data");
    }
    if (!batch.transformedData.subform_items?.length) {
      throw new Error("No valid items in transformed data");
    }
  }
};
