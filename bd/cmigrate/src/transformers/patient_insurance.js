const { BaseTransformer, TransformErrorIgnore } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientInsuranceTransformer");
const fs = require("fs");
const path = require("path");
const {
  isEmptyValue,
  format_phone,
  toDate,
  ynField,
} = require("../utils/tools");
const _head = require("lodash/head");

class PatientInsuranceTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_patins";
    this.destTable = "form_patient_insurance";
    this.sourceRawQuery = fs.readFileSync(
      path.join(__dirname, "fixtures/patient_insurance.sql"),
      "utf8",
    );
    if (sourceConnector.additionalFilters?.length > 0) {
      this.addFiltersToRawQuery(sourceConnector.additionalFilters);
    }
    this.disableTriggers = ["trg_update_rank_insurance"];
    this.qualifiers = [
      { field: "external_id", source: "external_id" },
      {
        field: "patient_id",
        source: "patient_mrn",
        lookup: {
          table: "form_patient",
          sourceField: "patient_mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
    ];
    this.enableBatchProcessing({
      batchSize: 50,
      batchConcurrency: 10,
    });
    this.field_map = {
      patient_mrn: this.warp(this.mrn, "patient_id"),
      external_id: "external_id",
      payer_external_id: this.warp(this.insno, "payer_id"),
      rank: "rank",
      payor: this.warp(this.payor, "type_id"),
      active: this.warp(this.active, "active"),
      effective_date: this.mapField("effective_date", "effective_date", toDate),
      payer_level: "payer_level",
      payer_level_other: "payer_level_other",
      billing_method_id: "billing_method_id",
      group_number: "group_number",
      cardholder_id: "cardholder_id",
      medicare_number: "medicare_number",
      medicaid_number: "medicaid_number",
      person_code: "person_code",
      medical_relationship_id: "medical_relationship_id",
      pharmacy_relationship_id: "pharmacy_relationship_id",
      beneficiary_lname: "beneficiary_lname",
      beneficiary_fname: "beneficiary_fname",
      beneficiary_gender: "beneficiary_gender",
      beneficiary_state_id: "beneficiary_state_id",
      beneficiary_city: "beneficiary_city",
      beneficiary_address1: "beneficiary_address1",
      beneficiary_postal_code: "beneficiary_postal_code",
      beneficiary_dob: this.mapField(
        "beneficiary_dob",
        "beneficiary_dob",
        toDate,
      ),
      contact_phone: this.mapField(
        "contact_phone",
        "contact_phone",
        format_phone,
      ),
      contact_name: "contact_name",
      patient_id_qualifier: "patient_id_qualifier",
      patient_claim_id: "patient_claim_id",
      bin: "bin",
      pcn: "pcn",
      bill_for_denial: this.warp(ynField("bill_for_denial"), "bill_for_denial"),
      partd_facility: this.warp(ynField("partd_facility"), "partd_facility"),
    };
  }
  async transform(data) {
    logger.debug(`Transforming patient insurance: ${JSON.stringify(data)}`);
    try {
      const result = await super.transform(data);
      logger.debug(`Transform result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      logger.error(`Transform error for ${data.external_id}: ${error.message}`);
      if (error instanceof TransformErrorIgnore) {
        throw error;
      }
      // Log the full error for non-ignored errors
      logger.error(error);
      throw error;
    }
  }
  async active(data) {
    if (isEmptyValue(data.active)) return null;
    if (data.inactive === 1) {
      return "No";
    }
    if (!isEmptyValue(data.ic_inactive) && data.ic_inactive === 1) {
      return "No";
    }
    return data.active;
  }
  async mrn(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.patient_mrn]],
      limit: 1,
    });
    const patient_data = _head(patient);
    if (isEmptyValue(patient_data?.id)) {
      throw new TransformErrorIgnore(
        `Patient ID not found for MRN: ${data.patient_mrn}`,
      );
    }
    const update = {};
    update.patient_id = patient_data.id;
    update.mrn = patient_data.mrn;
    const careplan = await this.db.dst.fetchData("form_careplan", {
      filters: [
        ["patient_id", "=", patient_data.id],
        ["status_id", "=", "3"],
      ],
      limit: 1,
      order: [["created_on", "desc"]],
    });
    const careplan_id = _head(careplan)?.id;
    if (!isEmptyValue(careplan_id)) {
      update.careplan_id = careplan_id;
    }
    return update;
  }
  async insno(data) {
    const insurance = await this.db.dst.fetchData("form_payer", {
      filters: [["external_id", "=", data.payer_external_id]],
      limit: 1,
    });
    const insurance_record = _head(insurance);
    const update = {};
    if (!isEmptyValue(insurance_record?.id)) {
      update.payer_id = insurance_record.id;
    }
    // need some other info from the plan if we have it
    update.pcn = insurance_record?.pcn;
    update.bin = insurance_record?.bin;
    update.plan_name = insurance_record?.organization;
    update.billing_method_id = insurance_record?.billing_method_id;

    return update;
  }
  async payor(data) {
    logger.debug(
      `Processing payor: ${data.payor} for record ${data.external_id}`,
    );
    if (isEmptyValue(data.payor)) return null;
    const normalizedPayor = data.payor.trim().toUpperCase();
    const typeMap = {
      MEDICARE: "MCRB",
      MGDMEDICARE: "MCRD",
      "MEDICARE PART D": "MCRD",
      "MED ADV PART D": "MCRD",
      "MED PART C": "MCRC",
      MEDICAID: "MEDI",
      MGDMEDICAID: "MEDI",
      COMMERCIAL: "CMMED",
      "MEDICARE SUPP": "CMMED",
      "MED SUPPLEMENT": "CMMED",
      PRIVATE: "CMMED",
      BCBS: "CMMED",
      PHARMACY: "CMPBM",
      PAP: "PAP",
      "MFG PROGRAM": "PAP",
      "COPAY ASSIST": "COPAY",
      "COPAY ASSISTANCE": "COPAY",
      "MFG COPAY": "COPAY",
      REBATE: "COPAY",
      FOUNDATION: "FOUND",
      SELF: "SELF",
      "SELF PAY": "SELF",
      HARDSHIP: "HARD",
      "WORKERS COMP": "OTHER",
      NURSING: "OTHER",
      OTHER: "OTHER",
      "": "OTHER",
    };

    const mapped = typeMap[normalizedPayor];

    logger.debug(`Mapped ${data.payor} (${normalizedPayor}) -> ${mapped}`);

    // Look up the type code in the destination table
    const typeId = await this.db.dst.lookupSourceValue(
      "form_list_payer_type",
      "code",
      mapped,
      "code",
    );

    logger.debug(`Type ID lookup result: ${typeId}`);

    if (isEmptyValue(typeId)) {
      logger.warn(`No matching type_id found for mapped code: ${mapped}`);
      return null;
    }

    const update = {
      type_id: typeId,
    };
    return update;
  }
}

module.exports = PatientInsuranceTransformer;
