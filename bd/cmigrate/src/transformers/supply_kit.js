const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("SupplyKitTransformer");
const { isEmptyValue } = require("../utils/tools");
const { getMapValue } = require("../utils/maps");
const _head = require("lodash/head");
class SupplyKitTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_kits";
    this.destTable = "form_inventory_supply_kit";
    this.qualifiers = [
      { field: "external_id", source: "external_id" },
      { field: "name", source: "name" },
    ];
    this.sourceRawQuery = `
      SELECT 
        kt.name_ as name,
        MIN(cpk_kits) as external_id,
        MIN(siteno) as external_site_id,
        json_agg(json_build_object(
          'inventory_external_id', ki.no,
          'dispense_quantity', defquant,
          'dispense_unit_id', 'each',
          'one_time_only', CASE 
            WHEN onetimeonly THEN 'Yes' 
            ELSE NULL 
          END
        )) as subform_items
      FROM cpr_kits kt
      INNER JOIN cpr_skt ki ON ki.knum = kt.cpk_kits 
        AND ki.delflag = 0
      WHERE kt.delflag = 0 
        AND kt.name_ NOT LIKE '%Test%'
      GROUP BY kt.name_`;
    this.field_map = {
      name: "name",
      external_id: "external_id",
    };
    this.addPostTransform(this.populateGerunds);
    this.addPostTransform(this.populateSubform);
  }

  async transform(data) {
    const result = await super.transform(data);
    return result;
  }
  async populateGerunds(transformedData, sourceData, upsertResult) {
    const { id: supplyKitId } = upsertResult;
    if (isEmptyValue(supplyKitId)) {
      logger.warn("No supply kit ID available for gerund relationship");
      return;
    }

    const siteId = getMapValue("SiteMap", sourceData.external_site_id, 2);
    logger.debug(
      `Creating gerund relationship between supply kit ID ${supplyKitId} and site ID ${siteId}`,
    );
    await this.db.dst.insert_gerund(
      "form_inventory_supply_kit_site_id_to_site_id",
      supplyKitId, // form_inventory_supply_kit_fk
      siteId, // form_site_fk
    );
  }

  async populateSubform(transformedData, sourceData, upsertResult) {
    const { id: supplyKitId } = upsertResult;
    const kitItems = sourceData.subform_items;
    logger.debug(
      `Found ${kitItems.length} kit items for supply kit ${supplyKitId}`,
    );
    if (isEmptyValue(kitItems)) {
      logger.warn("No kit items found for supply kit");
      return;
    }
    for (const item of kitItems) {
      const inventoryResult = await this.db.dst.fetchData("form_inventory", {
        filters: [["external_id", "=", item.inventory_external_id]],
        limit: 1,
      });
      const inventoryId = _head(inventoryResult)?.id;

      if (!inventoryId) {
        logger.warn(
          `No matching inventory found for external_id: ${item.inventory_external_id}`,
        );
        continue;
      }

      logger.debug(
        `Found inventory ID ${inventoryId} for external_id ${item.inventory_external_id}`,
      );

      const itemResult = await this.db.dst.upsertData(
        "form_inventory_sk_item",
        {
          inventory_id: inventoryId,
          external_id: item.inventory_external_id,
          dispense_quantity: item.dispense_quantity,
          dispense_unit_id: item.dispense_unit_id,
          one_time_only: item.one_time_only,
          part_of_kit: "Yes",
        },
        {
          qualifiers: [{ field: "inventory_id", source: "inventory_id" }],
        },
      );

      const subformItemId = itemResult.id;
      logger.debug(
        `Created subform item ${subformItemId} for supply kit ${supplyKitId}`,
      );

      // Create the subform link
      await this.db.dst.insert_subform(
        "form_inventory_supply_kit_to_inventory_sk_item",
        supplyKitId, // form_inventory_supply_kit_fk
        subformItemId, // inventory_sk_item_fk
      );
    }
  }
}

module.exports = SupplyKitTransformer;
