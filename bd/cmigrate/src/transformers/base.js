const Logger = require("../utils/logger");
const logger = new Logger("BaseTransformer");
const { isEmptyValue, cleanup_nulls } = require("../utils/tools");
const { loadConfig } = require("../core/config");
const { has_changed } = require("../utils/tools");

class TransformError extends Error {
  constructor(message) {
    super(message);
    this.name = "TransformError";
  }
}
class TransformPreCheckError extends TransformError {
  constructor(message) {
    super(message);
    this.name = "TransformPreCheckError";
  }
}
class TransformErrorIgnore extends TransformError {
  constructor(message) {
    super(message);
    this.name = "TransformErrorIgnore";
  }
}
class BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    this.sourceConnector = sourceConnector;
    logger.debug(
      `BaseTransformer initialized with additionalFilters: ${JSON.stringify(sourceConnector.additionalFilters)}`,
    );
    this.destinationConnector = destinationConnector;
    this.db = {
      src: sourceConnector,
      dst: destinationConnector,
    };
    this.api = {
      src: sourceConnector,
      dst: destinationConnector,
    };
    this.isDryRun = isDryRun;
    this.sourceTable = null;
    this.destTable = null;
    this.field_map = {};
    this.sourceQuery = null;
    this.sourceRawQuery = null;
    this.disableTriggers = [];

    // Initialize sourceFilters with both base filters and additional filters
    this.sourceFilters = [];

    // Add additional filters from sourceConnector if they exist
    if (Array.isArray(sourceConnector.additionalFilters)) {
      logger.debug(
        "Adding additional filters to sourceFilters:",
        sourceConnector.additionalFilters,
      );
      this.sourceFilters.push(...sourceConnector.additionalFilters);
    }

    this.qualifiers = [];
    this.postTransformFunctions = [];
    this.config = loadConfig();

    // Default batch settings
    this.batchProcess = false;
    this.batchSize = 50;
    this.batchDestination = "db";
    this.batchConcurrency = 5;

    this.customEndpoints = null;
    this.skipDefaultFilters = false;

    // Add transform type with default
    this.transformType = "standard"; // 'standard' | 'batch' | 'bulk'
  }

  addFiltersToRawQuery(filters) {
    if (!this.sourceRawQuery || !filters?.length) {
      logger.debug(`Skipping filter addition - no raw query or filters`);
      return;
    }

    const filterClauses = filters
      .map(([field, operator, value]) => {
        if (field.includes(".")) {
          // If field already has table reference, use it as is
          return `${field} ${operator} '${value}'`;
        } else {
          // Default to pti. prefix for patient_insurance
          return `pti.${field} ${operator} '${value}'`;
        }
      })
      .join(" AND ");

    // Check if query already has WHERE clause
    const hasWhere = this.sourceRawQuery.toLowerCase().includes("where");

    if (hasWhere) {
      // Add to existing WHERE clause
      this.sourceRawQuery = this.sourceRawQuery.replace(
        /where\s+(.*?)(?=(?:order by|group by|limit|$))/i,
        `WHERE ${filterClauses} AND $1`,
      );
    } else {
      // Add new WHERE clause
      const lowerQuery = this.sourceRawQuery.toLowerCase();
      let insertPosition = this.sourceRawQuery.length;

      ["group by", "order by", "limit"].forEach((clause) => {
        const pos = lowerQuery.indexOf(clause);
        if (pos !== -1 && pos < insertPosition) {
          insertPosition = pos;
        }
      });

      this.sourceRawQuery =
        this.sourceRawQuery.slice(0, insertPosition).trim() +
        ` WHERE ${filterClauses} ` +
        this.sourceRawQuery.slice(insertPosition);
    }

    logger.debug(`Modified raw query: ${this.sourceRawQuery}`);
  }

  // Helper method to enable batch processing with standard settings
  enableBatchProcessing(options = {}) {
    this.batchProcess = true;
    this.batchSize = options.batchSize || 50;
    this.batchDestination = options.batchDestination || "db";
    this.batchConcurrency = options.batchConcurrency || 10;
    return this;
  }

  warp(method, destField) {
    return async (data) => {
      const result =
        typeof method === "function"
          ? await method.call(this, data)
          : await method(data);

      if (result === null) {
        return {}; // Return an empty object if the result is null
      }
      if (typeof result === "object" && !Array.isArray(result)) {
        return result; // Return the object as is if it's already an object
      }
      return { [destField]: result }; // Wrap the result in an object with the destField
    };
  }

  lookupId(lookupTable, lookupField, destField) {
    const lookupFunction = async (data) => {
      // Find the source field by comparing the function reference
      const sourceField = Object.keys(this.field_map).find(
        (key) => this.field_map[key] === lookupFunction,
      );
      const lookupValue = data[sourceField];

      if (!lookupValue) {
        logger.debug(`No lookup value found for ${sourceField}`);
        return {};
      }

      const result = await this.destinationConnector.fetch_source_id(
        lookupTable,
        lookupField,
        lookupValue,
      );

      logger.debug(`Lookup result for ${destField}: ${result}`);
      return result ? { [destField]: result } : {};
    };

    return lookupFunction;
  }

  mapField(sourceField, destField, transformer = null) {
    logger.debug(
      `Mapping ${sourceField} to ${destField} with ${transformer ? transformer.name : "no transformer"}`,
    );
    return async (data) => {
      let value = data[sourceField];
      if (isEmptyValue(value)) {
        return {};
      }
      if (transformer) {
        try {
          value = await transformer(value);
          logger.debug(`Transformed value for ${sourceField}: ${value}`);
        } catch (error) {
          logger.error(
            `Error transforming ${sourceField}: ${error.message} ${error.stack}`,
          );
          return {};
        }
      }
      logger.debug(`Returning mapped value for ${destField}: ${value}`);
      return { [destField]: value };
    };
  }

  async precheck(_data) {
    return;
  }

  async transform(data) {
    if (typeof this.precheck === "function") {
      try {
        await this.precheck(data);
      } catch (error) {
        if (
          error instanceof TransformPreCheckError ||
          error instanceof TransformErrorIgnore
        ) {
          logger.error(`Transform error: ${error.message}`);
          return {
            sourceData: data,
            operation: "error",
            error: error.message,
          };
        }
        throw error;
      }
    }

    try {
      const mappedData = {};
      for (const [sourceField, transformation] of Object.entries(
        this.field_map,
      )) {
        let value;
        try {
          if (typeof transformation === "function") {
            value = await transformation(data);
          } else if (typeof transformation === "string") {
            value = { [transformation]: data[sourceField] };
          } else {
            logger.warn(
              `Unexpected transformation type for ${sourceField}:`,
              typeof transformation,
            );
            continue;
          }

          if (
            typeof value === "object" &&
            value !== null &&
            Object.keys(value).length > 0
          ) {
            Object.assign(mappedData, value);
          }
        } catch (error) {
          if (
            error instanceof TransformPreCheckError ||
            error instanceof TransformErrorIgnore
          ) {
            logger.error(`Transform error: ${error.message}`);
            return {
              sourceData: data,
              operation: "error",
              error: error.message,
            };
          }
          throw error;
        }
      }

      const result = await this.processTransform(data, mappedData);
      return result;
    } catch (error) {
      if (
        error instanceof TransformPreCheckError ||
        error instanceof TransformErrorIgnore
      ) {
        logger.error(`Transform error: ${error.message}`);
        return {
          sourceData: data,
          operation: "error",
          error: error.message,
        };
      }
      throw error;
    }
  }

  addPostTransform(func) {
    if (typeof func === "string") {
      if (typeof this[func] === "function") {
        // Store the function reference and name without binding
        this.postTransformFunctions.push({
          func: this[func],
          name: func,
        });
      } else {
        throw new Error(`Method ${func} not found in transformer`);
      }
    } else if (typeof func === "function") {
      this.postTransformFunctions.push({
        func: func,
        name: func.name || "anonymous",
      });
    } else {
      throw new Error("Invalid post-transform function");
    }
  }

  async runPostTransform(
    transformedData,
    sourceData,
    upsertResult,
    options = {},
  ) {
    if (Object.keys(transformedData).length === 0) {
      return [];
    }

    const results = [];
    let postTransformFunctions = this.postTransformFunctions;

    // If a specific transform is requested, filter for it
    if (options.transformName) {
      postTransformFunctions = postTransformFunctions.filter(({ name }) => {
        const requestedName = options.transformName
          .replace(/([A-Z])/g, "_$1")
          .toLowerCase()
          .replace(/^_/, "");
        const actualName = name
          .replace(/([A-Z])/g, "_$1")
          .toLowerCase()
          .replace(/^_/, "");

        logger.debug(`Comparing ${actualName} with ${requestedName}`);
        return actualName === requestedName;
      });

      if (postTransformFunctions.length === 0) {
        logger.warn(
          `No post-transform function found matching name: ${options.transformName}`,
        );
        return [];
      }
    }

    for (const { func, name } of postTransformFunctions) {
      logger.info(`Running post-transform function: ${name}`);
      try {
        // Bind the function only when executing
        const boundFunc = func.bind(this);
        const postTransformResult = await boundFunc(
          transformedData,
          sourceData,
          upsertResult,
          options,
        );

        logger.debug(
          `Raw post-transform result: ${JSON.stringify(postTransformResult)}`,
        );

        const result = {
          function: name,
          status: "success",
          dbResult: postTransformResult,
        };

        results.push(result);
      } catch (error) {
        logger.error(
          `Error running post-transform function ${name}: ${error.message} ${error.stack}`,
        );
        results.push({
          function: name,
          status: "error",
          error: error.message,
          operation: "error",
        });
      }
    }
    return results;
  }

  liveTransform(method) {
    return async (data) => {
      const result = await method.call(this, data);
      return result || {}; // Return the result if it exists, otherwise an empty object
    };
  }

  async transformBatch(sourceData) {
    const results = [];
    const chunks = [];

    // Split into chunks for concurrent processing
    for (let i = 0; i < sourceData.length; i += this.batchSize) {
      chunks.push(sourceData.slice(i, i + this.batchSize));
    }

    // Process each chunk
    for (const chunk of chunks) {
      const concurrentChunks = [];
      for (let i = 0; i < chunk.length; i += this.batchConcurrency) {
        concurrentChunks.push(chunk.slice(i, i + this.batchConcurrency));
      }

      // Process chunks concurrently
      for (const concurrentChunk of concurrentChunks) {
        const transformPromises = concurrentChunk.map(async (data) => {
          try {
            const result = await this.transform(data);
            return result;
          } catch (error) {
            if (error instanceof TransformErrorIgnore) {
              logger.error(
                `Transform error for ${data.id || "unknown"}: ${error.message}`,
              );
              return {
                sourceData: data,
                operation: "error",
                error: error.message,
              };
            }
            throw error;
          }
        });

        const chunkResults = await Promise.all(transformPromises);
        results.push(...chunkResults.filter((r) => r !== null));
      }
    }

    return results;
  }

  // Helper method to process a single transform
  async processTransform(data, transformedData) {
    const existingRecord = await this.destinationConnector.fetchData(
      this.destTable,
      {
        filters: await Promise.all(
          this.qualifiers.map(async (q) => {
            if (typeof q === "object" && q.field) {
              let value;
              if (q.lookup) {
                // Use the lookup to get the actual ID
                const lookupResult = await this.destinationConnector.fetchData(
                  q.lookup.table,
                  {
                    filters: [
                      [
                        q.lookup.destinationField,
                        "=",
                        data[q.lookup.sourceField],
                      ],
                    ],
                    limit: 1,
                  },
                );
                value = lookupResult[0]?.[q.lookup.returnField];
              } else {
                value = data[q.source];
              }
              return [q.field, "=", value];
            }
            return q;
          }),
        ),
        limit: 1,
      },
    );

    let operation = "insert";
    let transformed_id;

    if (existingRecord?.length > 0) {
      const hasChanged = await has_changed(existingRecord[0], transformedData);
      if (!hasChanged) {
        operation = "skip";
        transformed_id = existingRecord[0].id;
      } else {
        operation = "update";
      }
    }

    let upsertResult;
    if (operation !== "skip") {
      upsertResult = await this.destinationConnector.upsertData(
        this.destTable,
        transformedData,
        {
          qualifiers: await Promise.all(
            this.qualifiers.map(async (q) => {
              if (typeof q === "object" && q.field) {
                let value;
                if (q.lookup) {
                  // Use the lookup to get the actual ID
                  const lookupResult =
                    await this.destinationConnector.fetchData(q.lookup.table, {
                      filters: [
                        [
                          q.lookup.destinationField,
                          "=",
                          data[q.lookup.sourceField],
                        ],
                      ],
                      limit: 1,
                    });
                  value = lookupResult[0]?.[q.lookup.returnField];
                } else {
                  value = data[q.source];
                }
                return [q.field, "=", value];
              }
              return q;
            }),
          ),
        },
      );
    } else {
      upsertResult = { operation, id: transformed_id };
    }

    // Track post-transform results
    const postTransformResults = [];

    // Always run post-transforms, even for unchanged records
    if (this.postTransformFunctions.length > 0) {
      for (const postTransform of this.postTransformFunctions) {
        const result = await postTransform.func.call(
          this,
          transformedData,
          data,
          upsertResult,
        );
        postTransformResults.push({
          function: postTransform.name,
          status: "success",
          dbResult: result,
        });
      }
    }

    return {
      transformedData,
      sourceData: data,
      operation: upsertResult.operation,
      transformed_id: upsertResult.id,
      postTransformResults, // Include post-transform results
    };
  }

  disableDefaultFilters() {
    this.skipDefaultFilters = true;
    return this;
  }

  async transformSingle(sourceData) {
    logger.info(
      `Starting single transform of ${sourceData.length} source records`,
    );

    try {
      // Run precheck if exists
      if (typeof this.precheck === "function") {
        await this.precheck(sourceData);
      }

      // Run the main transform
      const results = await this.transform(sourceData);

      if (!results || results.length === 0) {
        logger.warn("No results produced by transform");
        return {
          success: false,
          message: "No results produced",
          processed: sourceData.length,
          transformed: 0,
        };
      }

      return {
        success: true,
        results,
        processed: sourceData.length,
        transformed: results.length,
      };
    } catch (error) {
      if (
        error instanceof TransformPreCheckError ||
        error instanceof TransformErrorIgnore
      ) {
        logger.warn(`Transform stopped: ${error.message}`);
        return {
          success: false,
          message: error.message,
          processed: sourceData.length,
          transformed: 0,
        };
      }
      throw error;
    }
  }

  // Add new bulk transform method
  async transformBulk(sourceData) {
    logger.info(`Starting bulk transform of ${sourceData.length} records`);

    try {
      // Run precheck if exists
      if (typeof this.precheck === "function") {
        await this.precheck(sourceData);
      }

      // Run the main transform which will handle its own destination posting
      const results = await this.transform(sourceData);

      if (!results || results.length === 0) {
        logger.warn("No results produced by bulk transform");
        return {
          success: false,
          message: "No results produced",
          processed: sourceData.length,
          transformed: 0,
        };
      }

      return {
        success: true,
        results,
        processed: sourceData.length,
        transformed: Array.isArray(results) ? results.length : 1,
      };
    } catch (error) {
      if (
        error instanceof TransformPreCheckError ||
        error instanceof TransformErrorIgnore
      ) {
        logger.warn(`Bulk transform stopped: ${error.message}`);
        return {
          success: false,
          message: error.message,
          processed: sourceData.length,
          transformed: 0,
        };
      }
      throw error;
    }
  }
}

module.exports = {
  BaseTransformer,
  TransformPreCheckError,
  TransformErrorIgnore,
};
