const { BaseTransformer } = require("./base");
const fs = require("fs");
const { toDate } = require("../utils/tools");
const { getMapValue } = require("../utils/maps");
const _head = require("lodash/head");
class InventoryRentalsTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_rentals";
    this.destTable = "form_inventory_rental_log";
    this.sourceRawQuery = fs.readFileSync(
      __dirname + "/fixtures/inventory_rentals.sql",
      "utf8",
    );
    this.qualifiers = [
      {
        field: "inventory_id",
        source: "inventory_external_id",
        lookup: {
          table: "form_inventory",
          sourceField: "inventory_external_id",
          destinationField: "external_id",
          returnField: "id",
        },
      },
      { field: "external_id", source: "rental_external_id" },
    ];
    this.field_map = {
      patient_mrn: this.warp(this.lookupPatientId, "patient_id"),
      inventory_external_id: this.warp(this.lookupInventoryId, "inventory_id"),
      rental_external_id: "external_id",
      serial_no: "serial_no",
      active: "active",
      in_stock: "in_stock",
      status: "status",
      in_service_datetime: this.mapField(
        "in_service_datetime",
        "in_service_datetime",
        toDate,
      ),
      last_checked_date: this.mapField(
        "last_checked_date",
        "last_checked_date",
        toDate,
      ),
      next_check_date: this.mapField(
        "next_check_date",
        "next_check_date",
        toDate,
      ),
      next_pm_date: this.mapField("next_pm_date", "next_pm_date", toDate),
      last_pm_date: this.mapField("last_pm_date", "last_pm_date", toDate),
      checked_out_date: this.mapField(
        "checked_out_date",
        "checked_out_date",
        toDate,
      ),
      external_site_id: this.warp(this.lookupSiteId, "site_id"),
    };
  }

  async lookupSiteId(data) {
    const siteno = getMapValue("SiteMap", data, 2);
    return siteno;
  }
  async lookupPatientId(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.patient_mrn]],
      limit: 1,
    });
    const patient_data = _head(patient);
    return patient_data?.id;
  }
  async lookupInventoryId(data) {
    const inventory = await this.db.dst.fetchData("form_inventory", {
      filters: [["external_id", "=", data.inventory_external_id]],
      limit: 1,
    });
    return _head(inventory)?.id;
  }
}
module.exports = InventoryRentalsTransformer;
