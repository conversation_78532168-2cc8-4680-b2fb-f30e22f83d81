const { BaseTransformer } = require("./base");
const { format_phone, isEmptyValue } = require("../utils/tools");
const _head = require("lodash/head");
const Logger = require("../utils/logger");
const logger = new Logger("PhysicianTransformer");
const { ynField } = require("../utils/tools");
class PhysicianTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_doctors";
    this.destTable = "form_physician";
    this.qualifiers = [
      { field: "external_id", source: "no" },
      { field: "npi", source: "ph_npi" },
    ];
    this.sourceFilters = [
      ["delflag", "!=", 1],
      ["ph_npi", "not_null"],
      ["ph_npi", "!=", "**********               **********"],
    ];

    // Enable batch processing with default settings
    this.enableBatchProcessing();

    this.field_map = {
      no: "external_id",
      ph_first: "first",
      ph_last: "last",
      profdesig: this.warp(
        (data) => data.profdesig?.trim().substring(0, 10) || null,
        "title",
      ),
      ph_npi: this.warp(this.physicianNpi, "npi"),
      ph_dea: "dea",
      ph_mcd: "mcd",
      ph_rrmcrid: "mcr_ptan_id",
      ph_lic: "state_license",
      taxonomy: "taxonomy_id",
      pecos: this.warp(ynField("pecos"), "pecos_enrolled"),
      ph_phone: this.mapField("ph_phone", "primary_phone", format_phone),
      ph_fax: this.mapField("ph_fax", "primary_fax", format_phone),
      ph_email: "email",
      notes: "notes",
    };
  }

  async transform(data) {
    const result = await super.transform(data);
    logger.info(`Transformed physician data:`, result);
    result.npi_number = result.npi;
    return result;
  }

  async physicianNpi(data) {
    if (isEmptyValue(data.ph_npi)) return null;
    return data.ph_npi.trim().substring(0, 10);
  }

  async _specialty(data) {
    if (isEmptyValue(data.ph_spec)) {
      return null;
    }
    const specialty = await this.db.dst
      .fetchData("form_list_nucc_taxonomy", {
        filters: [["name", "ilike", specialty]],
        limit: 1,
      })
      .then((results) => _head(results)?.id || null);
    return specialty;
  }
}

module.exports = PhysicianTransformer;
