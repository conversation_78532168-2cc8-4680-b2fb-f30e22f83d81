const { BaseTransformer } = require("./base");

class BillingUnitTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_parts";
    this.destTable = "form_list_billing_code";
    this.qualifiers = [
      { field: "code", source: "code" },
      { field: "name", source: "name" },
    ];
    this.sourceRawQuery = `WITH code_counts AS (
      SELECT hcpc, COUNT(DISTINCT TRIM(name_)) as name_count
      FROM cpr_parts
      WHERE parttype IN ('S', 'O', 'R') 
        AND length(hcpc) > 0 
        AND length(TRIM(name_)) > 0
      GROUP BY hcpc
    )
    SELECT 
      'HCPC' as code_type,
      pt.hcpc as code,
      CASE 
        WHEN cc.name_count > 1 THEN pt.hcpc || ' - Infusion Supplies'
        ELSE TRIM(pt.name_)
      END as name
    FROM cpr_parts pt 
    JOIN code_counts cc ON cc.hcpc = pt.hcpc
    WHERE parttype IN ('S', 'O', 'R') 
      AND length(pt.hcpc) > 0 
      AND length(TRIM(pt.name_)) > 0`;
    this.field_map = {
      code_type: "code_type",
      code: "code",
      name: "name",
    };
  }

  async transform(data) {
    if (data.name) data.name = data.name.trim();
    if (data.code) data.code = data.code.trim();
    if (data.code_type) data.code_type = data.code_type.trim();
    const result = await super.transform(data);
    return result;
  }
}

module.exports = BillingUnitTransformer;
