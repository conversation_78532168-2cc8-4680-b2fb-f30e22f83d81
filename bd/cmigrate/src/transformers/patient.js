const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientTransformer");
const _head = require("lodash/head");
const { isEmpty } = require("lodash");
const {
  addPatientAddress,
  createMeasurementLog,
  addPatientPrescriber,
} = require("./post-transforms/patient");
const { format_phone, toDate, isEmptyValue } = require("../utils/tools");
const { getMapValue } = require("../utils/maps");

class PatientTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_hr";
    this.destTable = "form_patient";
    this.qualifiers = [{ field: "external_id", source: "mrn" }];
    this.sourceFilters = [
      ["delflag", "!=", 1],
      ["mrn", "not_null"],
    ];
    this.db = {
      src: this.sourceConnector,
      dst: this.destinationConnector,
    };
    this.enableBatchProcessing();

    this.addPostTransform(addPatientAddress);
    this.addPostTransform(addPatientPrescriber);
    this.addPostTransform(createMeasurementLog);

    this.field_map = {
      first_name: "firstname",
      last_name: "lastname",
      phone: this.mapField("phone", "phone_cell", format_phone),
      workphone: this.mapField("workphone", "phone_work", format_phone),
      cellphone: this.mapField("cellphone", "phone_cell", format_phone),
      soc: this.mapField("soc", "care_start_date", toDate),
      email: "email",
      allergies: "allergies",
      dob: this.mapField("dob", "dob", toDate),
      ssn: "ssn",
      sex: this.warp(this._gender, "gender"),
      language: "language",
      ref_date: this.mapField("ref_date", "referral_date", toDate),
      cfk_refsourc: this.warp(this.referral, "referral_source_id"),
      salesrep: "sales_representative",
      salescode: "sales_code",
      mrn: "external_id",
      marital: "marital_status",
      pat_cat: this.warp(this._category, "category_id"),
      pat_stat: this.warp(this._patientStatus, "status_id"),
      alert_c: "clinical_alert",
      alert_b: "billing_alert",
      codestat: this.warp(this._codeStatus),
      advdirect: this.warp(
        this.advancedDirective,
        "advanced_directives_details",
      ),
      diabetic: "diabetic",
      siteno: this.warp(this.siteno, "site_id"),
      ph_no: this.warp(this.refPhysician, "referrer_id"),
      cfk_popupdata_entteams: this.warp(this._team, "team_id"),
      cm: this.warp(this.cmId, "careteam_nurse_id"),
    };
  }

  async transform(data) {
    const result = await super.transform(data);

    if (!result.transformedData) {
      return result;
    }

    // Check if patient exists in destination
    const existingPatient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", result.transformedData.external_id]],
      limit: 1,
    });

    // Only generate MRN for new patients
    if (!this.isDryRun && !existingPatient?.[0]) {
      const new_mrn = await this.db.dst.raw_query(
        "select crx_next_number('MRN')",
      );
      const mrn = _head(new_mrn)?.crx_next_number || null;

      result.transformedData.mrn = mrn;
      logger.info(
        `Generated new MRN ${mrn} for new patient with external_id: ${result.transformedData.external_id}`,
      );
    } else {
      logger.debug(
        `Skipping MRN generation - patient exists: ${!!existingPatient?.[0]}`,
      );
    }

    return result;
  }

  async _category(data) {
    const category = data.pat_cat;
    if (isEmptyValue(category)) return null;
    const d = {};
    d.allow_sync = "Yes";
    d.active = "Yes";
    d.name = category;
    return this.db.dst.fetchOrCreate("form_list_category", d, [
      ["name", "ilike", category],
    ]);
  }

  async _gender(data) {
    return data.sex === "M" ? "Male" : "Female";
  }

  async _codeStatus(data) {
    if (isEmptyValue(data.codestat)) return null;
    const codestatLower = data.codestat.toLowerCase();
    if (["full", "dnr", "n/a"].includes(codestatLower)) {
      return { code_status: data.codestat };
    } else {
      return { code_status: "Other", code_status_other: data.codestat };
    }
  }

  async _team(data) {
    const cfkpopupdata = data.cfk_popupdata_entteams;
    logger.debug(`_team method called with cfkpopupdata: ${cfkpopupdata}`);

    if (isEmptyValue(cfkpopupdata)) {
      logger.debug("cfkpopupdata is empty, returning null");
      return null;
    }

    try {
      logger.debug(`Looking up team value for cfkpopupdata: ${cfkpopupdata}`);
      const teamData = await this.db.src.fetchData("cpr_popupdata", {
        filters: [["cpk_popupdata", "=", cfkpopupdata]],
        limit: 1,
      });

      const teamName = teamData[0]?.text_?.trim().toLowerCase();

      if (teamName) {
        logger.debug(`Found team name: ${teamName}`);

        // Look up or create the team in the destination database
        const teamId = await this.db.dst.fetchOrCreate(
          "form_list_team",
          { name: teamName },
          [["name", "=", teamName]],
        );

        logger.debug(`Team ID for ${teamName}: ${teamId}`);
        return teamId;
      } else {
        logger.warn(`No team name found for cfkpopupdata: ${cfkpopupdata}`);
      }
    } catch (error) {
      throw new Error(`Error in _team lookup: ${error.message}`);
    }

    return null;
  }

  async advancedDirective(data) {
    if (isEmptyValue(data.ADVDIRECT)) return null;
    return data.ADVDIRECT.toLowerCase();
  }

  async siteno(data) {
    const siteno = getMapValue("SiteMap", data.siteno, 2);
    return siteno;
  }

  async refPhysician(data) {
    const ph_no = data.ph_no;
    if (isEmptyValue(ph_no)) return null;
    const phys = await this.db.dst.fetchData("form_physician", {
      filters: [["external_id", "=", ph_no]],
      limit: 1,
    });
    if (phys && phys.length > 0) {
      return phys[0].id;
    } else {
      logger.warn(`No matching physician found for ph_no: ${ph_no}`);
    }
    return null;
  }

  async cmId(data) {
    if (isEmptyValue(data.cm)) return null;
    const [firstName, lastName] = data.cm.split(" ", 2);
    return this.db.dst
      .fetchData("form_user", {
        filters: [
          ["firstname", "like", firstName],
          ["lastname", "like", lastName],
        ],
        limit: 1,
      })
      .then((results) => _head(results)?.id || null);
  }

  async referral(data) {
    const cfkrefsourc = data.cfk_refsourc;
    logger.debug(`Referral method called with cfkrefsourc: ${cfkrefsourc}`);
    if (isEmptyValue(cfkrefsourc)) {
      return null;
    }

    try {
      let org = await this.db.src.lookupSourceValue(
        "cpr_roster",
        "cpk_roster",
        cfkrefsourc,
        "org",
      );
      if (isEmpty(org)) {
        return null;
      }
      org = org.trim();
      if (org) {
        logger.debug(`Looking up foreign key for org: ${org}`);
        const salesAccountId = await this.db.dst.lookupForeignKey(
          "cpr_roster",
          "org",
          org,
          "form_sales_account",
          "name",
        );
        logger.debug(`Foreign key lookup result: ${salesAccountId}`);
        return salesAccountId;
      }
    } catch (error) {
      logger.error(`Error in referral lookup: ${error.message}`);
    }

    return null;
  }

  async _patientStatus(data) {
    if (isEmptyValue(data.pat_stat)) return null;
    const patientStatus = data.pat_stat.trim();
    return (
      this.db.dst.lookupSourceValue(
        "form_list_patient_status",
        "name",
        patientStatus,
        "code",
      ) || null
    );
  }
}

module.exports = PatientTransformer;
