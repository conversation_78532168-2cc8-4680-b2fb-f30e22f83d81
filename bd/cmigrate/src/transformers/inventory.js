const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("InventoryTransformer");
const fs = require("fs");
const _head = require("lodash/head");
const { format_checkbox, isEmptyValue } = require("../utils/tools");
const { getMapValue } = require("../utils/maps");
class InventoryTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_parts";
    this.destTable = "form_inventory";
    this.qualifiers = [
      { field: "external_id", source: "external_id" },
      { field: "fdb_id", source: "fdb_id" },
    ];
    this.sourceRawQuery = fs.readFileSync(
      __dirname + "/fixtures/inventory_base.sql",
      "utf8",
    );
    this.enableBatchProcessing();
    this.field_map = {
      external_id: "external_id",
      active: "active",
      type: "type",
      fdb_id: this.warp(this.lookupFdbId, "fdb_id"),
      name: "name",
      billable_code_type: this.warp(
        this.addCodeTypeFilter,
        "billable_code_type",
      ),
      billable_code_id: "billable_code_id",
      specialty_flag: "specialty_flag",
      revenue_code_id: "revenue_code_id",
      last_cost: "last_cost",
      price_code_id: "price_code_id",
      lot_tracking: "lot_tracking",
      serial_tracking: "serial_tracking",
      therapy_id: "therapy_id",
      requires_nursing: "requires_nursing",
      min_qty: async (data) => {
        data.min_qty = data.min_qty || 1;
        return data.min_qty;
      },
      max_qty: async (data) => {
        data.max_qty = data.max_qty || 1;
        return data.max_qty;
      },
      type_id: "type_id",
      warning_label: "warning_label",
      generic_name: "generic_name",
      primary_supplier_id_external_id: this.lookupId(
        "form_list_supplier",
        "external_id",
        "primary_supplier_id",
      ),
      last_supplier_id_external_id: this.lookupId(
        "form_list_supplier",
        "external_id",
        "last_supplier_id",
      ),
      quantity_per_package: "quantity_per_package",
      dispense_quantity_unit: "dispense_quantity_unit",
    };
    this.addPostTransform(this.addDosingUnitId.bind(this));
    this.fdb_dose_map = {
      1: "each",
      2: "mL",
      3: "gram",
    };
  }
  async addCodeTypeFilter(data) {
    const update_data = {};
    update_data.billable_code_type = data.billable_code_type;
    if (update_data?.billable_code_type === "HCPC") {
      update_data.code_type_filter = await format_checkbox(["HCPC"]);
    }
    return update_data;
  }
  async transform(data) {
    const result = await super.transform(data);

    return result;
  }

  async lookupFdbId(data) {
    if (!data.fdb_id) return {};

    const update_data = {
      fdb_id: data.fdb_id?.trim(),
    };
    if (data.type === "Drug" && !isEmptyValue(data.external_id)) {
      // Get inventory units data using new function
      const query = `SELECT * FROM get_ndc_unit_ids($1)`;
      const unitResults = await this.db.dst.raw_query(query, [
        data.external_id,
      ]);

      let unitData = unitResults?.[0];
      // check by ndc if available
      if (data.fdb_id && isEmptyValue(unitData)) {
        const query = `SELECT * FROM get_ndc_unit_ids($1)`;
        const unitResults = await this.db.dst.raw_query(query, [data.fdb_id]);
        unitData = unitResults?.[0];
      }

      if (unitData) {
        data.concentration = unitData.unit_string;
        data.dosing_unit_id = unitData.default_dosing_unit_id;
        data.default_dosing_unit_id = unitData.default_dosing_unit_id;
        data.report_unit_id = unitData.report_unit_id;
        data.dispense_unit_id = unitData.dispense_unit_id;
      }
    }
    // Get basic FDB data
    const fdb = await this.db.dst.fetchData("form_list_fdb_ndc", {
      filters: [["code", "=", data.fdb_id]],
      limit: 1,
    });
    const fdb_data = _head(fdb);
    logger.warn(`fdb_data: ${JSON.stringify(fdb_data)}`);
    if (!fdb_data) return update_data;

    // Basic FDB fields
    update_data.ndc = fdb_data.ndc?.trim();
    update_data.fdb_id = fdb_data.ndc?.trim();
    update_data.formatted_ndc = fdb_data.formatted_ndc?.trim();
    update_data.dea_schedule_id = fdb_data.dea;
    update_data.gcn_seqno = fdb_data.gcn_seqno;
    update_data.label_name = fdb_data.ln;
    update_data.brand_name_id = fdb_data.bn;
    update_data.manufacturer_id = fdb_data.lblrid;
    if (isEmptyValue(update_data.billing_unit_id))
      update_data.billing_unit_id = getMapValue(
        "DoseUnitMap",
        this.fdb_dose_map[fdb_data.df],
      );
    if (
      isEmptyValue(update_data.report_unit_id) &&
      !isEmptyValue(update_data.billing_unit_id)
    )
      update_data.report_unit_id = update_data.billing_unit_id;
    if (
      isEmptyValue(update_data.default_dosing_unit_id) &&
      !isEmptyValue(update_data.billing_unit_id)
    )
      update_data.default_dosing_unit_id = update_data.billing_unit_id;
    if (isEmptyValue(update_data.quantity_each))
      update_data.quantity_each = fdb_data.ps;

    // Get MedID
    update_data.medid = await this.getMedId(data.fdb_id);

    // Get caution label
    update_data.caution_label = await this.lookupCautionLabel(fdb_data);

    // Get NDC pricing data
    const pricing_query = `
      SELECT json_build_object(
        'md_price', (SELECT json_agg(row_to_json(md_price)) FROM (SELECT hcpc_pbp1 as asp_price FROM form_list_fdb_medicare_price WHERE hcpc = $1 ORDER BY hcpc_pbc1 DESC) md_price),
        'hcpc_lmt', (SELECT json_agg(row_to_json(hcpc_lmt)) FROM (SELECT payment_limit as mcr_asp_limit FROM form_list_cms_ndc_hcpc WHERE hcpc = $1) hcpc_lmt),
        'hcpc_cw', (SELECT json_agg(row_to_json(hcpc_cw)) FROM (SELECT bill_units as hcpc_quantity, hcpcs_dosage as hcpc_unit FROM form_list_cms_ndc_hcpc_cw WHERE ndc = $2) hcpc_cw)
      ) as result`;

    const pricing_result = await this.db.dst.raw_query(pricing_query, [
      update_data.hcpc_id,
      data.fdb_id,
    ]);
    if (pricing_result?.[0]?.result) {
      const pricing = pricing_result[0].result;
      update_data.asp_price = pricing.md_price?.[0]?.asp_price;
      update_data.mcr_asp_limit = pricing.hcpc_lmt?.[0]?.mcr_asp_limit;
      update_data.hcpc_quantity = pricing.hcpc_cw?.[0]?.hcpc_quantity;
      update_data.hcpc_unit = pricing.hcpc_cw?.[0]?.hcpc_unit;
    }

    // Get detailed NDC data
    const details_query = `
      WITH ndc_param AS (SELECT $1::text as ndc)
      SELECT json_build_object(
        'ndc_price_pkg', (SELECT json_agg(row_to_json(ndc_price_pkg)) FROM (SELECT price FROM form_list_fdb_ndc_price WHERE ndc = (SELECT ndc FROM ndc_param) AND price_type = '10' ORDER BY price_effective_dt DESC) ndc_price_pkg),
        'ndc_price_each', (SELECT json_agg(row_to_json(ndc_price_each)) FROM (SELECT price FROM form_list_fdb_ndc_price WHERE ndc = (SELECT ndc FROM ndc_param) AND price_type = '09' ORDER BY price_effective_dt DESC) ndc_price_each),
        'ful_price', (SELECT json_agg(row_to_json(ful_price)) FROM (SELECT price as ful FROM form_list_fdb_ndc_price WHERE ndc = (SELECT ndc FROM ndc_param) AND price_type = '23' ORDER BY price_effective_dt DESC) ful_price),
        'brand_price', (SELECT json_agg(row_to_json(brand_price)) FROM (SELECT price as avg_acq_cost_brand FROM form_list_fdb_ndc_price WHERE ndc = (SELECT ndc FROM ndc_param) AND price_type = '24' ORDER BY price_effective_dt DESC) brand_price),
        'gen_price', (SELECT json_agg(row_to_json(gen_price)) FROM (SELECT price as avg_acq_cost_gen FROM form_list_fdb_ndc_price WHERE ndc = (SELECT ndc FROM ndc_param) AND price_type = '25' ORDER BY price_effective_dt DESC) gen_price),
        'ndc_medicare_mstr', (SELECT json_agg(row_to_json(ndc_medicare_mstr)) FROM (
          SELECT mddesc.code as hcpc_id, mddesc.mcr_bcdesc as cmn_description, mddesc.mcr_ref as hcpc_code
          FROM form_list_fdb_medicare_mstr mdmstr 
          INNER JOIN (
            SELECT mcr_ref, MAX(mcr_refsn) as max_refsn 
            FROM form_list_fdb_medicare_desc 
            GROUP BY mcr_ref
          ) latest ON mdmstr.mcr_ref = latest.mcr_ref
          INNER JOIN form_list_fdb_medicare_desc mddesc ON mddesc.mcr_ref = mdmstr.mcr_ref 
            AND mddesc.mcr_refsn = latest.max_refsn
          WHERE ndc = (SELECT ndc FROM ndc_param)
        ) ndc_medicare_mstr),
        'ndc_route', (SELECT json_agg(row_to_json(ndc_route)) FROM (SELECT rtcl.route_id FROM form_list_fdb_ndc_to_route ndcrt INNER JOIN form_list_fdb_route_to_clara rtcl ON ndcrt.clinical_rt_id = rtcl.rt_id WHERE ndc = (SELECT ndc FROM ndc_param)) ndc_route),
        'ndc_therapy', (SELECT json_agg(row_to_json(ndc_therapy)) FROM (SELECT ft.etc_name as therapy_class FROM form_list_fdb_ndc_to_therapy ndct INNER JOIN form_list_fdb_therapy ft ON ndct.etc_id = ft.etc_id WHERE ndc = (SELECT ndc FROM ndc_param)) ndc_therapy),
        'ndc_attribute', (SELECT json_agg(row_to_json(ndc_attribute)) FROM (SELECT fdbst.storage_id as storage_id FROM form_list_fdb_ndc_attribute ndcattr INNER JOIN form_list_fdb_storage_to_clara fdbst ON ndcattr.ndc_attribute_value = fdbst.attribute_val WHERE ndc = (SELECT ndc FROM ndc_param) AND ndc_attribute_type_cd = '55' AND ndc_attribute_value IS NOT NULL) ndc_attribute),
        'ndc_tallman', (SELECT json_agg(row_to_json(ndc_tallman)) FROM (SELECT tm_alt_ndc_desc as tallman_name FROM form_list_fdb_tm_ndc WHERE ndc = (SELECT ndc FROM ndc_param) AND tm_name_type_id = '2') ndc_tallman)
      ) as result`;

    const details_result = await this.db.dst.raw_query(details_query, [
      data.fdb_id,
    ]);
    if (details_result?.[0]?.result) {
      const details = details_result[0].result;
      // always wac * 1.2
      update_data.awp_price_pkg = details.ndc_price_pkg?.[0]?.price * 1.2;
      update_data.awp_price = details.ndc_price_each?.[0]?.price * 1.2;
      update_data.ful = details.ful_price?.[0]?.ful;
      update_data.avg_acq_cost_brand =
        details.brand_price?.[0]?.avg_acq_cost_brand;
      update_data.avg_acq_cost_gen = details.gen_price?.[0]?.avg_acq_cost_gen;
      update_data.hcpc_id = details.ndc_medicare_mstr?.[0]?.hcpc_id;
      update_data.hcpc_code = details.ndc_medicare_mstr?.[0]?.hcpc_code;
      update_data.cmn_description =
        details.ndc_medicare_mstr?.[0]?.cmn_description;
      update_data.route_id = details.ndc_route?.[0]?.route_id;
      update_data.therapy_class = details.ndc_therapy?.[0]?.therapy_class;
      update_data.storage_id = details.ndc_attribute?.[0]?.storage_id;
      update_data.tallman_name = details.ndc_tallman?.[0]?.tallman_name;
    }
    // calculate list price based on the price code
    const price_code = await this.db.dst.fetchData(
      "form_site_price_code_item",
      {
        filters: [["price_code_id", "=", data.price_code_id]],
        limit: 1,
      },
    );
    const pc = _head(price_code);
    const mult = pc?.multiplier;
    const awp_price = update_data.awp_price
      ? update_data.awp_price
      : data.awp_price;
    if (pc && mult && !isEmptyValue(awp_price)) {
      if (
        pc.price_formula_id.includes("A") ||
        (pc.price_formula_id.includes("L") && pc.multiplier > 0)
      ) {
        update_data.list_price = awp_price * mult;
      }
      if (pc.multiplier < 0) {
        update_data.list_price = awp_price;
      }
    } else {
      // if no price from fdb or inventory, set to 0 for rentals ..etc
      update_data.list_price = 0;
    }
    return update_data;
  }

  async getMedId(ndc) {
    const med = await this.db.dst.fetchData("form_list_fdb_ndc_to_medid", {
      filters: [["ndc", "=", ndc]],
      limit: 1,
    });
    const med_data = _head(med);
    return med_data.medid;
  }

  async lookupCautionLabel(data) {
    const gcn_seqno = data.gcn_seqno;
    if (!gcn_seqno) return {};

    // Get label links with priority
    const labelLinks = await this.db.dst.fetchData(
      "form_list_fdb_gcnseq_label_link",
      {
        filters: [["gcn_seqno", "=", gcn_seqno]],
        orderBy: [["lbl_prty", "ASC"]],
      },
    );

    const warningLines = [];
    let totalLines = 0;

    // Process each label link in priority order
    for (const link of labelLinks) {
      // Stop if we've already collected 5 lines
      if (totalLines >= 5) break;

      const warnings = await this.db.dst.fetchData(
        "form_list_fdb_warning_label_info",
        {
          filters: [["lbl_warn", "=", link.lbl_warn]],
          orderBy: [["lbl_textsn", "ASC"]],
        },
      );

      // Check if adding these warnings would exceed 5 lines
      if (totalLines + warnings.length <= 5) {
        warnings.forEach((warning) => {
          if (warning.lbl_desc?.trim()) {
            warningLines.push(warning.lbl_desc.trim());
          }
        });
        totalLines += warnings.length;
      }
    }

    const cautionLabel = warningLines.filter((line) => line).join("\n");
    return cautionLabel;
  }

  async addDosingUnitId(transformedData, sourceData, upsertResult) {
    // Skip if no valid ID from upsert
    if (!upsertResult?.id) {
      return {
        operation: "skip",
        status: "success",
        sourceId: sourceData.id,
      };
    }

    const { id: mainOrderId } = upsertResult;
    const doseUnitId = transformedData.default_dosing_unit_id;
    const dispenseUnitId = transformedData.dispense_unit_id;
    let insertCount = 0;
    let unchangedCount = 0;

    // Only proceed if we have both a valid mainOrderId and unit IDs
    if (!this.isDryRun && mainOrderId && !isEmptyValue(doseUnitId)) {
      const result1 = await this.db.dst.insert_gerund(
        "form_inventory_flt_dose_unit_to_list_unit_id",
        mainOrderId,
        doseUnitId,
      );
      const result2 = await this.db.dst.insert_gerund(
        "form_inventory_dosing_unit_id_to_list_unit_id",
        mainOrderId,
        doseUnitId,
      );

      if (result1.operation === "insert") insertCount++;
      else unchangedCount++;

      if (result2.operation === "insert") insertCount++;
      else unchangedCount++;
    }

    if (!this.isDryRun && mainOrderId && !isEmptyValue(dispenseUnitId)) {
      const result3 = await this.db.dst.insert_gerund(
        "form_inventory_flt_disp_unit_to_list_unit_id",
        mainOrderId,
        dispenseUnitId,
      );
      if (result3.operation === "insert") insertCount++;
      else unchangedCount++;
    }

    return {
      operation: insertCount > 0 ? "insert" : "none",
      status: "success",
      insertCount,
      unchangedCount,
      sourceId: mainOrderId,
    };
  }
}

module.exports = InventoryTransformer;
