const { BaseTransformer, TransformPreCheckError } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientMedicationTransformer");
const { isEmptyValue, toDate, format_checkbox } = require("../utils/tools");
const _head = require("lodash/head");

class PatientMedicationTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_ot";
    this.destTable = "form_patient_medication";
    this.qualifiers = [
      {
        field: "patient_id",
        source: "mrn",
        lookup: {
          table: "form_patient",
          sourceField: "mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
      { field: "external_id", source: "no" },
    ];
    this.sourceRawQuery = `
      SELECT 
        l.rx_type,
        translate(p.ndc, '-', '') as ndc,
        l.scriptext,
        o.createdon,
        o.touchdate,
        o.start,
        o.stop,
        o.freq,
        o.mrn,
        o.ph_no,
        o.rank,
        o.status,
        o.comments,
        o.descrip,
        o.no,
        h.siteno
      FROM cpr_ot o
      LEFT JOIN cpr_lablog l ON l.orderno = o.no AND o.mrn = l.link 
      join cpr_parts p on p.no = o.invno and p.parttype = 'D'
      join cpr_hr h on h.mrn = o.mrn and h.delflag = 0 and h.pat_stat in ('Active', 'Pending')
      WHERE o.delflag != 1 and o.status in ('Active', 'Pending')
    `;
    if (sourceConnector.additionalFilters?.length > 0) {
      this.addFiltersToRawQuery(sourceConnector.additionalFilters);
    }
    this.field_map = {
      no: "external_id",
      mrn: this.warp(this.patient_lookup, "patient_id"),
      ndc: this.warp(async (data) => {
        if (isEmptyValue(data.ndc)) return null;
        return data.ndc.replace(/-/g, "");
      }, "fdb_id"),
      start: this.mapField("start", "start_date", toDate),
      stop: this.mapField("stop", "end_date", toDate),
      descrip: "medication_dose",
      freq: "medication_frequency",
      comments: "note",
      scriptext: "rx_no",
      status: "status",
    };
    this.status_map = {
      active: "Active",
      pending: "Pending",
      "dc'd": "Discontinued",
    };
  }
  async transform(data) {
    logger.debug(`Transforming patient diagnosis: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    return result;
  }
  async precheck(data) {
    if (isEmptyValue(data.mrn)) {
      throw new TransformPreCheckError("Patient ID is empty");
    }
    const status = data.status?.trim()?.toLowerCase();
    if (!["active", "pending", "dc'd"].includes(status)) {
      throw new TransformPreCheckError(
        `Invalid status for order: ${data.status}`,
      );
    }
  }

  async rank(data) {
    return parseInt(data.rank);
  }
  async patient_lookup(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.mrn]],
      limit: 1,
    });
    const patient_data = _head(patient);
    const updated_data = {};
    if (data.rank < 1) {
      updated_data.reported_by = "Patient Reported";
    } else {
      updated_data.reported_by = "Physician";
    }
    if (patient_data) {
      updated_data.patient_id = patient_data.id;
      const careplan = await this.db.dst.fetchData("form_careplan", {
        filters: [
          ["patient_id", "=", patient_data.id],
          ["status_id", "=", "3"],
        ],
        order: [["created_on", "desc"]],
        limit: 1,
      });
      const careplan_data = _head(careplan);
      if (careplan_data) {
        updated_data.careplan_id = careplan_data.id;
      }
      const prescribed_by_id = await this.prescriber_lookup(
        patient_data.id,
        data.ph_no,
        updated_data.careplan_id,
      );
      if (prescribed_by_id) {
        updated_data.prescribed_by_id = prescribed_by_id;
        updated_data.prescribed_by = "Physician";
      }
      return updated_data;
    } else {
      throw new TransformPreCheckError(
        `Patient not found for MRN: ${data.mrn}`,
      );
    }
  }

  async prescriber_lookup(pid, ph_no, careplan_id) {
    if (isEmptyValue(ph_no)) return null;
    const physician = await this.db.dst.fetchData("form_physician", {
      filters: [["external_id", "=", ph_no]],
      limit: 1,
    });
    const physician_data = _head(physician);
    if (isEmptyValue(physician_data?.id)) return null;
    const prescriber = await this.db.dst.fetchOrCreate(
      "form_patient_prescriber",
      {
        physician_id: physician_data.id,
        patient_id: pid,
        ordering: "Yes",
        careplan_id,
      },
      [
        ["physician_id", "=", physician_data.id],
        ["patient_id", "=", pid],
      ],
    );
    return prescriber;
  }
}

module.exports = PatientMedicationTransformer;
