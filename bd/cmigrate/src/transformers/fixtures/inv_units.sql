CREATE OR REPLACE FUNCTION get_inventory_units(p_ndc text DEFAULT NULL, p_inventory_external_id text DEFAULT NULL)
RETURNS TABLE (
    ndc text,
    inventory_external_id text,
    ln text,
    external_name text,
    external_dispensing_unit text,
    dispense_unit_id text,
    default_dosing_unit_id text,
    dosing_unit_id text[],
    quantity_each numeric,
    billing_unit_id text,
    concentration text,
    fractional_units text,
    report_unit_id text
) AS $$
BEGIN
    RETURN QUERY
    WITH base_data AS (
        SELECT 
            fdb2mid.ndc::text,
            pt.no::text as inventory_external_id,
            fdbndc.ln::text,
            pt.name_::text as external_name,
            pt.dispensingunit::text as external_dispensing_unit,
            CASE WHEN fdb2mid.ndc IN ('50242021501', '50242021401') THEN 'each'::text 
                 ELSE convert_to_fdb_unit(pt.dispensingunit)::text 
            END as dispense_unit_id,
            convert_to_fdb_unit(ot.dose_unit)::text as default_dosing_unit_id,
            ARRAY[convert_to_fdb_unit(ot.dose_unit)::text] as dosing_unit_id,
            fdbndc.ps::numeric as quantity_each,
            CASE 
                WHEN fdbndc.df = '1' THEN 'each'::text
                WHEN fdbndc.df = '2' THEN 'mL'::text
                WHEN fdbndc.df = '3' THEN 'gram'::text 
                ELSE NULL::text 
            END as billing_unit_id
        FROM form_list_fdb_ndc_to_medid fdb2mid
        JOIN form_list_fdb_ndc fdbndc ON fdbndc.ndc = fdb2mid.ndc
        JOIN form_list_fdb_med_table fdblm ON fdblm.medid = fdb2mid.medid
        JOIN form_list_fdb_gcnseqno_mstr gcnm ON gcnm.gcn_seqno = fdblm.gcn_seqno
        LEFT JOIN form_list_fdb_ndc_clin_qual_map cqm ON cqm.ndc = fdb2mid.ndc
        JOIN form_list_fdb_gen_df_mstr_link gdf ON gdf.gcdf = gcnm.gcdf
        JOIN form_list_fdb_dosage_master dm ON dm.dosage_form_id = gdf.dosage_form_id
        LEFT JOIN form_list_fdb_unit_master um ON um.uom_mstr_id = dm.uom_mstr_id
        inner join cpr_parts pt on fdbndc.ndc = REPLACE(pt.ndc,'-','') 
            AND pt.inactive = '0' AND pt.delflag = 0
        LEFT JOIN LATERAL (
            SELECT ot.no, lbl.dose, 
                REGEXP_REPLACE(lbl.dose, '^\d+(-\d+)?(\.\d+)?\s*', '') as dose_unit 
            FROM cpr_ot ot 
            INNER JOIN cpr_labels lbl on lbl.orderno = ot.no
            WHERE ot.INVNO = pt.NO
            AND ot.STATUS IN ('Pending', 'Active') AND ot.delflag = 0
            LIMIT 1
        ) ot ON TRUE
        WHERE OT.NO IS NOT NULL
        AND (p_ndc IS NULL OR fdb2mid.ndc = p_ndc)
        AND (p_inventory_external_id IS NULL OR pt.no::text = p_inventory_external_id)
    )
    SELECT 
        base_data.*,
        format_concentration(
            base_data.ndc,
            base_data.ln,
            base_data.external_name,
            base_data.quantity_each,
            base_data.external_dispensing_unit,
            base_data.billing_unit_id
        )::text as concentration,
        CASE WHEN base_data.billing_unit_id = 'each' AND base_data.quantity_each > 1 
             THEN 'Yes'::text ELSE NULL::text 
        END as fractional_units,
        CASE WHEN base_data.default_dosing_unit_id = 'gram' 
             THEN 'gram'::text ELSE base_data.billing_unit_id::text 
        END as report_unit_id
    FROM base_data;
END;
$$ LANGUAGE plpgsql;