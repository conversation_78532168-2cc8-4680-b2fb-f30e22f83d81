WITH active_orders AS (
  SELECT o.*, 
         COALESCE(p.wac, p.wac_each, 0) as drug_price,
         COALESCE(l.rx_type, '') as rx_type,
         COALESCE(translate(p.ndc, '-', ''), '') as ndc,
         COALESCE(l.scriptext, '') as scriptext,
         COALESCE(lb.nextcomp, o.start) as nextcomp,
         COALESCE(lb.end_rx, o.start) as end_rx,
         COALESCE(lb.freq, o.freq) as freq,
         lb.dose,
         lb.dosesleft,
         lb.dosesallow,
         lb.dosesday,
         lb.dosesprep,
         lb.bagsdisp,
         lb.contdoses,
         lb.rawquant,
         lb.refills,
         lb.days,
         lb.rxdays,
         lb.expdat,
         lb.rxorigin,
         lb.daw,
         lb.refillnum,
         lb.directns,
         lb.line1,
         lb.line5,
         lb.line6,
         lb.line7,
         lb.line8,
         lb.line9,
         lb.line11,
         lb.line12,
         lb.line13,
         lb.line14,
         lb.qtyperdose,
         h.siteno as facility_siteno,
         TRIM(CONCAT_WS(' ', 
           NULLIF(TRIM(lb.posig1), ''),
           NULLIF(TRIM(lb.posig2), ''),
           NULLIF(TRIM(lb.posig3), '')
         )) as po_sig,
         COALESCE(p.bname, '') as bname,
         COALESCE(p.manufact, '') as manufact,
         COALESCE(p.parttype, '') as parttype,
         COALESCE(p.eachuom, '') as eachuom,
         COALESCE(p.no, 0) as partno,
         p.dispensinguniteach,
         t.knum as supply_kit_id,
         ARRAY_REMOVE(ARRAY[
           NULLIF(TRIM(o.dxcode), ''),
           NULLIF(TRIM(o.dxcode2), ''),
           NULLIF(TRIM(o.dxcode3), ''),
           NULLIF(TRIM(o.dxcode4), '')
         ], NULL) as dx_codes,
         o.siteno
  FROM cpr_ot o
  INNER JOIN cpr_hr h ON h.mrn = o.mrn 
    AND h.delflag != 1 
    AND h.pat_stat IN ('Active', 'Pending')
  INNER JOIN cpr_parts p ON p.no = o.invno 
    AND p.delflag != 1
    AND p.parttype = 'D'
  LEFT JOIN cpr_lablog l ON l.orderno = o.no 
    AND o.mrn = l.link 
    AND l.delflag != 1
  LEFT JOIN cpr_labels lb ON lb.orderno = o.no 
    AND lb.delflag != 1
  LEFT JOIN LATERAL (
    SELECT knum 
    FROM cpr_tickets t 
    WHERE t.otno = o.no 
    AND t.mrn = o.mrn
    AND t.delflag != 1 
    AND t.knum != 0 
    AND t.parttype in ('S', 'O')
    LIMIT 1
  ) t ON true
  WHERE o.delflag != 1
  AND o.status IN ('Active', 'Pending')
  AND o.createdon >= now() - interval '14 months'
--  AND o.primaryflg = 'P'
)
SELECT 
  m.*,
  (
    SELECT json_agg(items ORDER BY items.sort_order, items.drug_price DESC, items.ordered DESC)
    FROM (
      SELECT DISTINCT ON (o.no) 
        o.*,
        CASE 
          WHEN o.no = m.no THEN 0
          WHEN o.status = 'Active' THEN 1
          WHEN o.status = 'Pending' THEN 2
          ELSE 3
        END as sort_order
      FROM active_orders o
      WHERE o.mrn = m.mrn
        AND o.rank >= 1
    ) items
  ) as subform_order_items
FROM (
  SELECT DISTINCT ON (mrn) *
  FROM active_orders
  WHERE facility_siteno = 2
  --where mrn = '241515'
  --where mrn in ('253708', '201658')
  -- where mrn in ('253708', '201658', '213679', '235389', '252589', '248218', '244510', '256902', '232956', '240666', '250757', '216881', '236278', '220416', '239287', '245886', '231887', '249960', '248594', '259171', '250557', '212308', '249092', '215554')
  -- where mrn in ('258427','260671', '261229')
  ORDER BY mrn, 
    CASE WHEN status = 'Active' THEN 0 ELSE 1 END,
    drug_price DESC,
    ordered DESC
) m
ORDER BY 
  CASE WHEN m.status = 'Active' THEN 0 ELSE 1 END,
  m.drug_price DESC,
  m.mrn,
  m.no;