WITH unit_map AS (
  SELECT 
    'bottle' as input, 'container' as output UNION ALL
    SELECT 'capsule', 'cap' UNION ALL
    SELECT 'each', 'each' UNION ALL
    SELECT 'Each', 'each' UNION ALL
    SELECT 'gm', 'gram' UNION ALL
    SELECT 'Gm', 'gram' UNION ALL
    SELECT 'NCSPAmg', 'mg' UNION ALL
    SELECT 'mcg', 'mcg' UNION ALL
    SELECT 'mg', 'mg' UNION ALL
    SELECT 'MU', 'mu' UNION ALL
    SELECT 'ml', 'mL' UNION ALL
    SELECT 'syringe', 'syringe' UNION ALL
    SELECT 'tab', 'tablet or capsule' UNION ALL
    SELECT 'units', 'unit' UNION ALL
    SELECT 'Units', 'unit' UNION ALL
    SELECT 'vial', 'vial'
)
SELECT DISTINCT ON (fi.fdb_id)
  fi.formatted_ndc,
  fndc.df,
  fi.auto_name as clara_name,
  name_ as parts_name,
  ot.dose_unit,
  COALESCE((SELECT output FROM unit_map WHERE input = ot.dose_unit), ot.dose_unit) as default_dosing_unit_id,
  fndc.ps,
  COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit) as dispensingunit,
  CASE CAST(fndc.df AS INTEGER)
    WHEN 1 THEN 'each'
    WHEN 2 THEN 'mL'
    WHEN 3 THEN 'gram'
  END as billing_unit_id,
  CASE CAST(fndc.df AS INTEGER)
    WHEN 1 THEN 'each'
    WHEN 2 THEN 'mL'
    WHEN 3 THEN 'gram'
  END as reporting_unit_id,
  parts.dispensinguniteach,
  CONCAT_WS(' ',
    -- First part: Default dispense unit (each/vial)
    NULLIF(
      CASE
        WHEN PARTS.supplied = 'Vials' AND (
          NULLIF(PARTS.dispensinguniteach, 0) IS NOT NULL OR
          (PARTS.DOSEFORM = 2 AND NULLIF(PARTS.solvolume, 0) IS NOT NULL) OR
          (LOWER(CASE CAST(fndc.df AS INTEGER)
             WHEN 1 THEN 'each'
             WHEN 2 THEN 'mL'
             WHEN 3 THEN 'gram'
           END) NOT IN (LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)), 'ml'))
        )
        THEN '1 vial'
        WHEN LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)) != 'each'
        OR (PARTS.DOSEFORM = 2 AND NULLIF(PARTS.solvolume, 0) IS NOT NULL)
        OR (LOWER(CASE CAST(fndc.df AS INTEGER)
             WHEN 1 THEN 'each'
             WHEN 2 THEN 'mL'
             WHEN 3 THEN 'gram'
           END) NOT IN (LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)), 'ml'))
        THEN '1 each'
        ELSE NULL
      END,
      ''
    ),
    -- Only add slash if first part exists
    CASE WHEN NULLIF(
      CASE
        WHEN PARTS.supplied = 'Vials' AND (
          NULLIF(PARTS.dispensinguniteach, 0) IS NOT NULL OR
          (PARTS.DOSEFORM = 2 AND NULLIF(PARTS.solvolume, 0) IS NOT NULL) OR
          (LOWER(CASE CAST(fndc.df AS INTEGER)
             WHEN 1 THEN 'each'
             WHEN 2 THEN 'mL'
             WHEN 3 THEN 'gram'
           END) NOT IN (LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)), 'ml'))
        )
        THEN '1 vial'
        WHEN LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)) != 'each'
        OR NULLIF(PARTS.dispensinguniteach, 0) IS NOT NULL
        OR (PARTS.DOSEFORM = 2 AND NULLIF(PARTS.solvolume, 0) IS NOT NULL)
        OR (LOWER(CASE CAST(fndc.df AS INTEGER)
             WHEN 1 THEN 'each'
             WHEN 2 THEN 'mL'
             WHEN 3 THEN 'gram'
           END) NOT IN (LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)), 'ml'))
        THEN '1 each'
        ELSE NULL
      END,
      ''
    ) IS NOT NULL THEN '/' ELSE NULL END,
    -- For items where dispensing unit equals billing unit, just show ps with unit
    CASE 
      WHEN LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)) = 
           LOWER(CASE CAST(fndc.df AS INTEGER)
             WHEN 1 THEN 'each'
             WHEN 2 THEN 'mL'
             WHEN 3 THEN 'gram'
           END)
           AND NULLIF(ps, 0) IS NOT NULL
      THEN CONCAT(REGEXP_REPLACE(TRIM(TO_CHAR(ps::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', ''), ' ', 
           COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit))
      ELSE
        -- Otherwise show dispensing units and solution volume
        CONCAT(
          CASE WHEN NULLIF(PARTS.dispensinguniteach, 0) IS NOT NULL
          THEN CONCAT(
            REGEXP_REPLACE(TRIM(TO_CHAR(PARTS.dispensinguniteach::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', ''), ' ',
            COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)
          )
          END,
          CASE
            WHEN PARTS.DOSEFORM = 2 AND NULLIF(PARTS.solvolume, 0) IS NOT NULL
            THEN CONCAT(' / ', REGEXP_REPLACE(TRIM(TO_CHAR(PARTS.solvolume::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', ''), ' mL')
            ELSE NULL
          END,
          -- Only add billing units if they differ from dispensing units (case-insensitive) and mL and aren't already shown
          CASE
            WHEN LOWER(CASE CAST(fndc.df AS INTEGER)
                   WHEN 1 THEN 'each'
                   WHEN 2 THEN 'mL'
                   WHEN 3 THEN 'gram'
                 END) NOT IN (LOWER(COALESCE((SELECT output FROM unit_map WHERE input = parts.dispensingunit), parts.dispensingunit)), 'ml')
                   AND NULLIF(fi.quantity_each, 0) IS NOT NULL
                   AND REGEXP_REPLACE(TRIM(TO_CHAR(fi.quantity_each::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', '') != 
                       REGEXP_REPLACE(TRIM(TO_CHAR(PARTS.dispensinguniteach::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', '')
            THEN CONCAT(' / ', REGEXP_REPLACE(TRIM(TO_CHAR(fi.quantity_each::numeric, 'FM999990.999')), '\.?0*$|\.0+(?=[^0-9])', ''), ' ',
                 CASE CAST(fndc.df AS INTEGER)
                   WHEN 1 THEN 'each'
                   WHEN 2 THEN 'mL'
                   WHEN 3 THEN 'gram'
                 END)
            ELSE NULL
          END
        )
    END
  ) as formatted_string
FROM cpr_parts PARTS
JOIN form_inventory fi 
  ON CAST(fi.external_id AS INTEGER) = PARTS.no 
  AND PARTS.parttype = 'D' 
  AND parts.delflag = 0
JOIN form_list_fdb_ndc fndc 
  ON fndc.ndc = fi.fdb_id
        LEFT JOIN LATERAL (
            SELECT ot.no, lbl.dose, 
                REGEXP_REPLACE(lbl.dose, '^\d+(-\d+)?(\.\d+)?\s*', '') as dose_unit 
            FROM cpr_ot ot 
            INNER JOIN cpr_labels lbl on lbl.orderno = ot.no
            WHERE ot.INVNO = PARTS.NO
            AND ot.STATUS IN ('Pending', 'Active') AND ot.delflag = 0
            LIMIT 1
        ) ot ON TRUE
        WHERE OT.NO IS NOT NULL;