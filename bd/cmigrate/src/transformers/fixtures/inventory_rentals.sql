SELECT
    rt.no as inventory_external_id,
    siteno as external_site_id,
    mrn as patient_mrn,
    sn as serial_no,
    'Yes' as active,
    rt.cpk_rentals as rental_external_id,
    CASE
        WHEN rt.instock='Y' THEN 'Yes'
        ELSE NULL
    END as in_stock,
    CASE
        WHEN status='Training' THEN 'TRN'
        WHEN status='Cleaning' THEN 'CLN'
        WHEN status='Available' THEN 'AVL'
        WHEN status IN ('Sold', 'Sold / Srvc', 'Sold / Rtrn') THEN 'SLD'
        WHEN status='Broken/Retired' THEN 'BRK'
        WHEN status='Quarantine' THEN 'QTN'
        WHEN status='Returned to IMS' THEN 'RTN'
        WHEN status IN ('Dispensed', 'Patient') THEN 'DSP'
        WHEN status='Lost' THEN 'LST'
        WHEN status='Pending' THEN 'PND'
        ELSE NULL
    END as status,
    to_char (inservice, 'MM/DD/YYYY HH:mm:ss') as in_service_datetime,
    to_char (lastcheck, 'MM/DD/YYYY') as last_checked_date,
    to_char (nextcheck, 'MM/DD/YYYY') as next_check_date,
    to_char (lastpm, 'MM/DD/YYYY') as last_pm_date,
    to_char (nextpm, 'MM/DD/YYYY') as next_pm_date,
    to_char (wentout, 'MM/DD/YYYY') as checked_out_date
FROM
    cpr_rentals rt
    inner join cpr_parts pt on pt.no=rt.no
where
    pt.inactive=0
    and pt.delflag=0
    and rt.delflag=0