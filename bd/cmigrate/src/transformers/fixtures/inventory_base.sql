WITH distinct_parts AS (
    SELECT DISTINCT
        pt.NO,
        pt.manufact,
        pt.parttype,
        pt.ndc,
        pt.name_,
        pt.cattext,
        pt.gname,
        pt.hcpc,
        pt.notes,
        pt.partnum,
        pt.salesprice,
        pt.rentpurchcost,
        pt.cost1,
        pt.reorder,
        pt.specprcode,
        pt.minquant,
        pt.maxquant,
        pt.primaryflg,
        pt.line1,
        pt.cfk_supplier,
        pt.rxtype,
        pt.eachpercontainer,
        CASE 
            WHEN pt.parttype = 'D' THEN EXISTS (
                SELECT 1 FROM cpr_ot ot 
                WHERE ot.INVNO = pt.NO 
                AND ot.STATUS IN ('Pending', 'Active') 
                AND ot.delflag != 1
            )
            ELSE true
        END as has_active_order
    FROM cpr_parts pt
    WHERE pt.inactive = 0
    AND pt.delflag != 1
)
SELECT
    pt.NO as external_id,
    pt.manufact as manufacturer,
    CASE
        WHEN pt.parttype = 'D' THEN 'Drug'
        WHEN pt.parttype = 'S' THEN 'Supply'
        WHEN pt.parttype = 'O' THEN 'Billable'
        WHEN pt.parttype = 'R' THEN 'Equipment Rental'
    END as "type",
    'Yes' as active,
    REPLACE(pt.ndc, '-', '') as fdb_id,
    pt.name_ as name,
    pt.cattext as supply_category_id,
    pt.gname as generic_name,
    CASE
        WHEN pt.parttype IN ('S', 'O', 'R') THEN 'HCPC'
        ELSE NULL
    END as billable_code_type,
    CASE
        WHEN pt.parttype IN ('S', 'O', 'R') THEN pt.hcpc
        ELSE NULL
    END as billable_code_id,
    pt.notes as notes,
    CASE
        WHEN pt.parttype IN ('S') THEN pt.partnum
        ELSE NULL
    END as sku,
    CASE
        WHEN pt.parttype IN ('R') THEN pt.salesprice
        ELSE NULL
    END as purchase_cost,
    CASE
        WHEN pt.parttype IN ('R') THEN pt.rentpurchcost
        ELSE NULL
    END as purchase_list_price,
    sp.cpk_supplier as primary_supplier_id_external_id,
    sp.cpk_supplier as last_supplier_id_external_id,
    CASE
        WHEN pt.parttype IN ('D') THEN 'Yes'
        ELSE NULL
    END as lot_tracking,
    CASE
        WHEN pt.parttype IN ('R') THEN 'Yes'
        ELSE NULL
    END as serial_tracking,
    CASE
        WHEN pt.parttype IN ('D') THEN 'DRG'
        WHEN pt.parttype IN ('S') THEN 'SUP'
        ELSE NULL
    END as revenue_code_id,
    CASE
        WHEN pt.rxtype IN ('FACTOR') THEN 'factor'
        WHEN pt.rxtype IN ('IVIG', 'SQIG') THEN 'ig'
        WHEN pt.rxtype IN ('CHEMO') THEN 'antibiotic'
        WHEN pt.rxtype IN ('TPN') THEN 'tpn'
        WHEN pt.rxtype IN ('TNF') THEN 'tnf'
        WHEN pt.rxtype IN ('CHEMO') THEN 'chemotherapy'
        WHEN pt.rxtype IN ('Steroid') THEN 'steroid'
        WHEN pt.rxtype IN ('AAT') THEN 'aat'
        ELSE NULL
    END as therapy_id,
    CASE
        WHEN pt.rxtype IN ('IVIG', 'SQIG') THEN 'Yes'
        ELSE NULL
    END as requires_nursing,
    CASE
        WHEN pt.rxtype IN ('IVIG', 'SQIG', 'AAT', 'TNF', 'FACTOR') THEN 'Yes'
        ELSE NULL
    END as speciality_flag,
    pt.cost1 as last_cost,
    pt.reorder as reorder_no,
    pt.specprcode as price_code_id,
    pt.minquant as min_qty,
    pt.maxquant as max_qty,
    pt.hcpc as hcpc,
    CASE
        WHEN pt.primaryflg IN ('P') THEN 'Primary'
        WHEN pt.primaryflg IN ('A') THEN 'Premedication'
        ELSE NULL
    END as type_id,
    pt.line1 as warning_label,
    COALESCE(caution1, '') || ' ' || COALESCE(caution2, '') || ' ' || COALESCE(caution3, '') || ' ' || COALESCE(caution4, '') || ' ' || COALESCE(caution5, '') as caution_label,
    CASE WHEN pt.parttype IN ('S') THEN pt.eachpercontainer ELSE NULL END as quantity_per_package,
    CASE WHEN pt.parttype IN ('S') THEN 'each' ELSE NULL END as dispense_quantity_unit
FROM distinct_parts pt
LEFT JOIN cpr_supplier sp ON pt.cfk_supplier = sp.cpk_supplier 
LEFT JOIN LATERAL (
    SELECT
        caution1,
        caution2,
        caution3,
        caution4,
        caution5
    FROM
        cpr_partslabelinfo li
    WHERE
        li.cfk_parts = pt.NO
) LABELS on true
WHERE pt.has_active_order = true