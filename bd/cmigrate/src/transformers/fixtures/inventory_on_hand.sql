WITH LastMonthRaws AS (
    SELECT SUM(qty) as Quantity,
        translate(INVENTORY.NDC, '- ', '') as NDC,
        lot,
        MAX(purdate) as purdate,
        MAX(supplierpk) as supplierpk,
        MAX(exp) as exp,
        MAX(cost) as LotCost,
        MAX(CPR_RAWS.CREATEDON)::timestamp as CREATEDON,
        MAX(unitsvial) as unitsvial,
        SITENO
    FROM CPR_RAWS
    INNER JOIN CPR_PARTS INVENTORY ON INVENTORY.NO = CPR_RAWS.NO 
        AND INVENTORY.DELFLAG = 0
    WHERE CPR_RAWS.DELFLAG = 0
        AND CPR_RAWS.qty <> 0
        AND CPR_RAWS.CREATEDON <= CURRENT_DATE
    GROUP BY NDC, lot, SITENO
),
BaseInventory AS (
    SELECT DISTINCT ON (
        CASE WHEN INV.LOT IS NULL OR INV.LOT = '' THEN 1 ELSE 0 END,
        INV.LOT,
        INV.SITENO,
        INV.NDC
    )
        TO_CHAR((DATE_TRUNC('MONTH', CURRENT_DATE) - INTERVAL '1 day')::date, 'YYYYMMDD') as "date",
        INV.NDC as "ndc_code",
        CPR_SITES.sitename as "site_name",
        SITE.SITENO as "siteno",
        INVENTORY.Name_ AS "product_description",
        INVENTORY.manufact as "manufacturer",
        INVENTORY.parttype as "inventory_type",
        SUPPLIER.suppliername as "supplier",
        TO_CHAR(INV.purdate, 'YYYY-MM-DD') as "purchase_date",
        TO_CHAR(INV.exp, 'YYYY-MM-DD') as "expiration_date",
        ROUND(INV.Quantity, 0) as "beginning_balance_quantity",
        COALESCE(DISPENSES.Quantity, 0) as "quantity_shipped",
        CASE
            WHEN COALESCE(DISPENSES.Quantity, 0) > (
                COALESCE(ROUND(INV.Quantity, 0), 0) + COALESCE(ROUND(RAWS_ADDED.qtyadded, 0), 0)
            ) THEN ROUND(COALESCE(DISPENSES.Quantity, 0) - INV.Quantity, 0)
            WHEN RAWS_ADDED.qtyadded IS NULL THEN 0
            ELSE ROUND(RAWS_ADDED.qtyadded, 0)
        END as "quantity_received",
        ROUND(
            COALESCE(INV.Quantity, 0) + COALESCE(RAWS_ADDED.qtyadded, 0) - COALESCE(DISPENSES.Quantity, 0),
            0
        ) as "quantity_adjusted",
        ROUND(INV.Quantity, 0) as "quantity",
        INVENTORY.cost1 as "inventory_last_cost",
        COALESCE(PARTSMS.cost1, 0) as "site_inventory_last_cost",
        COALESCE(INV.LotCost, 0) as "lot_cost",
        'VIALS' as "unit_of_measure",
        INVENTORY.strength as "potency",
        INV.lot as "lot_number",
        SITE.seivtype as "site_type"
    FROM CPR_CLIENT SITE
    INNER JOIN CPR_SITES ON SITE.siteno = CPR_SITES.siteno
    INNER JOIN LastMonthRaws INV ON INV.SITENO = SITE.SITENO
    INNER JOIN CPR_PARTS INVENTORY ON INV.NDC = translate(INVENTORY.NDC, '- ', '')
    LEFT JOIN CPR_SUPPLIER SUPPLIER ON SUPPLIER.cpk_supplier = INV.supplierpk
    LEFT JOIN CPR_PARTSMS PARTSMS ON PARTSMS.delflag = 0
        AND PARTSMS.hideit = 0
        AND INVENTORY.NO = PARTSMS.no
        AND SITE.SITENO = PARTSMS.siteno
    LEFT JOIN LATERAL (
        SELECT RXLOG.SITENO,
            translate(INVENTORY.NDC, '- ', '') as NDC,
            LOTLOG.LOT,
            SUM(
                CASE 
                    WHEN LOTLOG.unitsvial > 0 THEN LOTLOG.vials * LOTLOG.unitsvial
                    ELSE LOTLOG.vials 
                END
            ) as Quantity
        FROM CPR_LABLOG RXLOG
        INNER JOIN CPR_HR ON RXLOG.LINK = CPR_HR.MRN
        LEFT OUTER JOIN CPR_LOTLOG LOTLOG ON LOTLOG.DELFLAG = 0
            AND RXLOG.CPK_LABLOG = LOTLOG.LABLOGNO
            AND LOTLOG.LOT = INV.LOT
        LEFT OUTER JOIN CPR_PARTS INVENTORY ON INVENTORY.DELFLAG = 0
            AND LOTLOG.NO = INVENTORY.NO
        WHERE (
            RXLOG.CURDATE >= date_trunc('month', current_date - interval '1' month)
            AND RXLOG.CURDATE < date_trunc('month', current_date)
        )
        AND RXLOG.siteno = SITE.SITENO
        AND LOWER(LOTLOG.DRUG) NOT LIKE '%void%'
        AND RXLOG.DELFLAG = 0
        AND translate(INVENTORY.NDC, '- ', '') = INV.NDC
        GROUP BY RXLOG.SITENO, LOTLOG.LOT, translate(INVENTORY.NDC, '- ', '')
    ) DISPENSES ON TRUE
    LEFT JOIN LATERAL (
        SELECT CPR_RAWS.SITENO,
            translate(INVENTORY.NDC, '- ', '') as NDC,
            CPR_RAWS.LOT,
            SUM(qty) as qtyadded
        FROM CPR_RAWS
        INNER JOIN CPR_PARTS INVENTORY ON INVENTORY.NO = CPR_RAWS.NO
            AND INVENTORY.DELFLAG = 0
        WHERE CPR_RAWS.SITENO = SITE.SITENO
            AND CPR_RAWS.DELFLAG = 0
            AND CPR_RAWS.CREATEDON >= date_trunc('month', current_date - interval '1' month)
            AND CPR_RAWS.CREATEDON < date_trunc('month', current_date)
            AND translate(INVENTORY.NDC, '- ', '') = INV.NDC
            AND CPR_RAWS.LOT = INV.LOT
        GROUP BY CPR_RAWS.SITENO, translate(INVENTORY.NDC, '- ', ''), CPR_RAWS.LOT
    ) RAWS_ADDED ON TRUE
    WHERE INV.exp >= DATE_TRUNC('month', CURRENT_DATE - interval '1' month) - INTERVAL '1 day'
    AND (
        COALESCE(DISPENSES.Quantity, 0) > 0
        OR ROUND(INV.Quantity, 0) > 0
    )
    ORDER BY 
        CASE WHEN INV.LOT IS NULL OR INV.LOT = '' THEN 1 ELSE 0 END,
        INV.LOT,
        INV.SITENO,
        INV.NDC,
        ROUND(INV.Quantity, 0) DESC,
        INV.CREATEDON
)
SELECT 
    now()::date as "todays_date",
    "date" as "inventory_since_date",
    "lot_number",
    "ndc_code",
    "site_name",
    "siteno",
    "product_description",
    "manufacturer",
    "supplier",
    "inventory_type",
    "purchase_date",
    "expiration_date",
    "quantity",
    "quantity_received",
    "site_inventory_last_cost",
    "lot_cost",
    "unit_of_measure",
    "potency",
    "site_type"
FROM BaseInventory
ORDER BY 
    product_description