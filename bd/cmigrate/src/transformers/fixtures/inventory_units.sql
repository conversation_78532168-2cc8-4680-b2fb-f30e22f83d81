SELECT tmp.*,
	format_concentration(ndc, ln, external_name, quantity_each, external_dispensing_unit, billing_unit_id) as concentration,
	CASE WHEN billing_unit_id = 'each' AND quantity_each > 1 THEN 'Yes' ELSE NULL END as fractional_units,
	CASE WHEN default_dosing_unit_id = 'gram' THEN 'gram' ELSE billing_unit_id END as report_unit_id
FROM
(SELECT 
    fdb2mid.ndc,
    pt.no as inventory_external_id,
    fdbndc.ln,
    pt.name_ as external_name,
    pt.dispensingunit as external_dispensing_unit,
	CASE WHEN fdb2mid.ndc IN ('50242021501', '50242021401') THEN 'each' ELSE convert_to_fdb_unit(pt.dispensingunit) END as dispense_unit_id,
	convert_to_fdb_unit(ot.dose_unit) as default_dosing_unit_id,
	ARRAY[convert_to_fdb_unit(ot.dose_unit)] as dosing_unit_id,
	fdbndc.ps as quantity_each,
    CASE 
        WHEN fdbndc.df = '1' THEN 'each'
        WHEN fdbndc.df = '2' THEN 'mL'
        WHEN fdbndc.df = '3' THEN 'gram' 
        ELSE NULL 
    END as billing_unit_id
FROM form_list_fdb_ndc_to_medid fdb2mid
JOIN form_list_fdb_ndc fdbndc ON fdbndc.ndc = fdb2mid.ndc
JOIN form_list_fdb_med_table fdblm ON fdblm.medid = fdb2mid.medid
JOIN form_list_fdb_gcnseqno_mstr gcnm ON gcnm.gcn_seqno = fdblm.gcn_seqno
LEFT JOIN form_list_fdb_ndc_clin_qual_map cqm ON cqm.ndc = fdb2mid.ndc
JOIN form_list_fdb_gen_df_mstr_link gdf ON gdf.gcdf = gcnm.gcdf
JOIN form_list_fdb_dosage_master dm ON dm.dosage_form_id = gdf.dosage_form_id
LEFT JOIN form_list_fdb_unit_master um ON um.uom_mstr_id = dm.uom_mstr_id
inner join cpr_parts pt on fdbndc.ndc = REPLACE(pt.ndc,'-','') AND pt.inactive = '0' AND pt.delflag = 0
LEFT JOIN LATERAL (SELECT ot.no, lbl.dose, REGEXP_REPLACE(lbl.dose, '^\d+(-\d+)?(\.\d+)?\s*', '') as dose_unit FROM cpr_ot ot 
INNER JOIN cpr_labels lbl on lbl.orderno = ot.no
WHERE ot.INVNO = pt.NO
AND ot.STATUS IN ('Pending', 'Active') AND ot.delflag = 0
LIMIT 1) ot ON TRUE

WHERE OT.NO IS NOT NULL) tmp