WITH PartsLocCTE AS (
    SELECT 
        Cfk_Parts,
        Cfk_Locations,
        PartsMS.siteno AS Cfk_Sites,
        PartsLoc.MinQuant
    FROM CPR_PartsLoc PartsLoc
    INNER JOIN CPR_Parts Parts 
        ON Parts.Delflag = 0 
        AND Parts.no = PartsLoc.cfk_parts 
        AND Parts.qtybyloc = 'Y'
    LEFT JOIN CPR_PartsMS PartsMS 
        ON PartsMS.delflag = 0 
        AND PartsMS.hideit = 0
        AND Parts.NO = PartsMS.no
    WHERE PartsLoc.DelFlag = 0
    
    UNION
    
    SELECT
        Parts.No AS Cfk_Parts,
        0 AS Cfk_Locations,
        PartsMS.siteno AS Cfk_Sites,
        0 AS MinQuant
    FROM CPR_Parts Parts
    LEFT JOIN CPR_PartsMS PartsMS 
        ON PartsMS.delflag = 0 
        AND PartsMS.hideit = 0
        AND Parts.NO = PartsMS.no
    WHERE Parts.DelFlag = 0
    AND (
        Parts.qtybyloc <> 'Y' 
        OR (
            Parts.no IN (
                SELECT no FROM CPR_RAWS WHERE delflag = 0 AND locno = 0
                UNION 
                SELECT no FROM CPR_RENTALS WHERE delflag = 0 AND cfk_locations = 0
            )
        )
    )
), 
QuantityData AS (
    -- Raw materials and drugs inventory
    SELECT 
        cfk_parts,
        siteno,
        cfk_locations,
        '' AS status,
        SUM(qty) AS quantity,
        lot,
        exp,
        cost,
        SUM(ext_cost) AS ext_cost,
        MAX(purdate) as purchase_date,
        MAX(supplierpk) as supplier_pk
    FROM (
        SELECT 
            RAWS.NO AS cfk_parts,
            RAWS.siteno,
            CASE 
                WHEN CPR_Parts.qtybyloc = 'Y' THEN RAWS.locno 
                ELSE 0 
            END AS cfk_locations,
            CASE 
                WHEN CPR_Parts.lottrack = 1 THEN COALESCE(RAWS.Lot, '') 
                ELSE '' 
            END AS Lot,
            RAWS.exp,
            RAWS.Qty,
            RAWS.Cost,
            RAWS.purdate,
            RAWS.supplierpk,
            CASE 
                WHEN RAWS.Qty > 0 THEN RAWS.Qty * RAWS.Cost 
                ELSE 0 
            END AS Ext_Cost
        FROM CPR_RAWS RAWS
        INNER JOIN CPR_Parts 
            ON CPR_Parts.DELFLAG = 0 
            AND CPR_Parts.no = RAWS.no
        LEFT JOIN CPR_PartsMS PartsMS 
            ON PartsMS.delflag = 0 
            AND PartsMS.hideit = 0
            AND CPR_Parts.NO = PartsMS.no
            AND RAWS.siteno = PartsMS.siteno
        WHERE RAWS.Delflag = 0 
        AND RAWS.qty <> 0
        AND (PartsMS.no IS NOT NULL OR CPR_Parts.PartType = 'O')
    ) AS RawsData
    GROUP BY cfk_parts, siteno, cfk_locations, Lot, Exp, Cost

    UNION ALL

    -- Rentals inventory
    SELECT 
        cfk_Parts,
        siteno,
        cfk_locations,
        status,
        COUNT(*) AS quantity,
        '' AS lot,
        NULL AS exp,
        CAST(COALESCE(SUM(VALUE), 0) / NULLIF(COUNT(*), 0) AS NUMERIC(15,2)) AS cost,
        SUM(VALUE) AS Ext_Cost,
        MAX(CREATEDON) as purchase_date,
        NULL as supplier_pk
    FROM (
        SELECT 
            rentals.no AS cfk_parts,
            rentals.siteno,
            CASE 
                WHEN CPR_Parts.qtybyloc = 'Y' THEN CFK_LOCATIONS 
                ELSE 0 
            END AS cfk_locations,
            rentals.status,
            RENTALS.VALUE,
            rentals.CREATEDON
        FROM CPR_Rentals rentals
        INNER JOIN CPR_Parts 
            ON CPR_Parts.DELFLAG = 0 
            AND CPR_Parts.no = rentals.no
        LEFT JOIN CPR_PartsMS PartsMS 
            ON PartsMS.delflag = 0 
            AND PartsMS.hideit = 0
            AND CPR_Parts.NO = PartsMS.no
            AND rentals.siteno = PartsMS.siteno
        WHERE rentals.delflag = 0 
        AND outservice IS NULL 
        AND status NOT IN ('Patient', 'Sold')
        AND (PartsMS.no IS NOT NULL OR CPR_Parts.PartType = 'O')
    ) rentalsdata
    GROUP BY cfk_Parts, siteno, cfk_locations, status
)
SELECT * FROM (SELECT 
    now()::date as "todays_date",
    Parts.No as "external_id",
    Parts.NDC as "ndc_code",
    Sites.sitename as "site_name",
    QD.siteno as "siteno",
    Parts.Name_ AS "product_description",
    Parts.manufact as "manufacturer",
    Parts.parttype as "inventory_type",
    SUPPLIER.suppliername as "supplier",
    SUPPLIER.cpk_supplier as "supplier_id",
    TO_CHAR(QD.purchase_date, 'YYYY-MM-DD') as "purchase_date",
    TO_CHAR(QD.exp, 'YYYY-MM-DD') as "expiration_date",
    COALESCE(QD.Quantity, 0) AS "quantity",
    CASE 
        WHEN Parts.PartType IN ('D','S','R') THEN COALESCE(QD.Cost, 0)
        ELSE Parts.Cost1 
    END AS "lot_cost",
    CASE 
        WHEN Parts.PartType = 'D' THEN 'VIALS'
        ELSE 'each'
    END as "unit_of_measure",
    Parts.strength as "potency",
    QD.lot as "lot_number",
    COALESCE(Locations.text_, '') AS "location_name",
    Parts.CatText as "category",
    Parts.QtyByLoc as "quantity_by_location",
    Parts.LotTrack as "lot_tracking_enabled",
    Parts.Inactive as "inactive_flag",
    COALESCE(PartsMS.cost1, Parts.cost1) as "site_inventory_last_cost",
    (SELECT COUNT(DISTINCT(raws.lot)) 
     FROM CPR_RAWS raws 
     WHERE parts.no = raws.no 
     AND raws.delflag = 0) AS "lot_count"
FROM CPR_Parts Parts
FULL OUTER JOIN PartsLocCTE PL 
    ON PL.Cfk_parts = Parts.No
LEFT JOIN QuantityData QD 
    ON QD.Cfk_Parts = Parts.No 
    AND QD.cfk_locations = PL.Cfk_Locations
    AND QD.siteno = PL.Cfk_Sites
LEFT JOIN CPR_Locations Locations 
    ON Locations.delflag = 0 
    AND Locations.no = PL.Cfk_Locations
LEFT JOIN CPR_SITES Sites
    ON Sites.siteno = QD.siteno
LEFT JOIN CPR_SUPPLIER SUPPLIER 
    ON SUPPLIER.cpk_supplier = QD.supplier_pk
LEFT JOIN CPR_PartsMS PartsMS 
    ON PartsMS.delflag = 0 
    AND PartsMS.hideit = 0
    AND Parts.NO = PartsMS.no
    AND QD.siteno = PartsMS.siteno
WHERE Parts.DelFlag = 0
AND Parts.No > 0
AND COALESCE(Parts.Name_, '') <> ''
AND (Parts.PartType IN ('D', 'S', 'R', 'O'))  -- Include all part types
AND (Parts.PartType = 'O' OR QD.Quantity IS NOT NULL)  -- Only include items with quantity unless they're non-inventory
ORDER BY 
    Parts.CatText,
    Parts.Name_,
    Parts.No,
    Sites.sitename,
    Locations.text_,
    QD.status,
    QD.Cost) TMP WHERE quantity > 0