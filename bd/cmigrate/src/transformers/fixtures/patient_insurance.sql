SELECT
    pti.mrn as patient_mrn,
    pti.cpk_patins as external_id,
    pti.insno as payer_external_id,
    pti.payor,
    CASE
        WHEN pti.rank = 1.0 THEN 'Primary'
        WHEN pti.rank = 2.0 THEN 'Secondary'
        WHEN pti.rank = 3.0 THEN 'Teritary'
        ELSE 'Other'
    END as payer_level,
    CASE
        WHEN pti.rank = 4.0 THEN '4'
        WHEN pti.rank = 5.0 THEN '5'
        WHEN pti.rank = 6.0 THEN '6'
        WHEN pti.rank = 7.0 THEN '7'
        WHEN pti.rank = 8.0 THEN '8'
        ELSE NULL
    END as payer_level_other,
    CAST(pti.rank as int) as rank,
    CASE
        WHEN pti.status = 'Inactive' THEN 'No'
        WHEN pti.status is null THEN 'No'
        ELSE 'Yes'
    END as active,
    CASE
        WHEN ic.invoicetyp IN ('1', 'E') THEN 'mm'
        WHEN ic.invoicetyp = 'M' THEN 'ncpdp'
        WHEN ic.invoicetyp = 'G' THEN 'generic'
        WHEN ic.invoicetyp = 'F' THEN 'cms1500'
    END as billing_method_id,
    pti.pcode as person_code,
    pti.group_num as group_number,
    pti.policy as cardholder_id,
    'Brandon use your payer type map' as type_id,
    CASE
        WHEN pti.partdfacility THEN 'Y'
        ELSE NULL
    END as partd_facility,
    CASE
        WHEN ic.invoicetyp = 'M' THEN 'EA'
        ELSE NULL
    END as patient_id_qualifier,
    CASE
        WHEN ic.invoicetyp = 'M' THEN pti.mrn
        ELSE NULL
    END as patient_claim_id,
    CASE
        WHEN ic.invoicetyp IN ('1', 'E')
        AND pc.relationcode = '1' THEN '01'
        WHEN ic.invoicetyp IN ('1', 'E')
        AND pc.relationcode = '19' THEN '19'
        WHEN ic.invoicetyp IN ('1', 'E') THEN '18'
        ELSE NULL
    END as medical_relationship_id,
    CASE
        WHEN ic.invoicetyp IN ('M')
        AND pc.relationcode = '1' THEN '2'
        WHEN ic.invoicetyp IN ('M')
        AND pc.relationcode = '19' THEN '3'
        WHEN ic.invoicetyp IN ('M') THEN '1'
        ELSE NULL
    END as pharmacy_relationship_id,
    CASE
        WHEN length (pc.insname) > 0 THEN TRIM(SPLIT_PART (pc.insname, ',', 1))
        ELSE NULL
    END AS beneficiary_lname,
    CASE
        WHEN length (pc.insname) > 0 THEN TRIM(SPLIT_PART (pc.insname, ',', 2))
        ELSE NULL
    END AS beneficiary_fname,
    CASE
        WHEN pc.isex = 'M' THEN 'M'
        WHEN pc.isex = 'F' THEN 'F'
        ELSE NULL
    END as beneficiary_gender,
    pc.istate as beneficiary_state_id,
    pc.icity as beneficiary_city,
    pc.iaddr as beneficiary_address1,
    pc.izip as beneficiary_postal_code,
    TO_CHAR (pc.idob, 'MM/DD/YYYY') as beneficiary_dob,
    REGEXP_REPLACE (
        pti.inscphone,
        '(\d{3})-(\d{3})-(\d{4})',
        '(\1) \2-\3'
    ) as contact_phone,
    inscontac as contact_name,
    CASE
        WHEN pti.payor = 'MEDICARE' THEN policy
        ELSE NULL
    END as medicare_number,
    CASE
        WHEN pti.payor = 'MEDICAID' THEN policy
        ELSE NULL
    END as medicaid_number,
    CASE
        WHEN pti.payor = 'MEDICAID'
        AND pc.relationcode = '1' THEN '2'
        WHEN pti.payor = 'MEDICAID'
        AND pc.relationcode = '19' THEN '3'
        WHEN pti.payor = 'MEDICAID' THEN '1'
    END as medicaid_relationship_id,
    ic.binno as bin,
    ic.pcn as pcn,
    ic.inactive as ic_inactive,
    CASE
        WHEN billasdenial THEN 'Yes'
        ELSE NULL
    END as bill_for_denial,
    TO_CHAR (pti.start, 'MM/DD/YYYY') as effective_date
FROM
    cpr_patins pti
    INNER JOIN cpr_inscomp ic ON ic.no = pti.insno
    inner join cpr_hr hr on hr.mrn = pti.mrn
    LEFT JOIN cpr_policy pc ON pc.pi_no = pti.cpk_patins
    AND pc.delflag != '1'
    AND hr.delflag != '1'
where
    pti.delflag != '1'
    and hr.delflag != '1'
    and (
        stop IS NULL
        OR stop >= CURRENT_TIMESTAMP
    )
order by pti.cpk_patins asc