SELECT TMP.*,
hcpc,
to_json(array_remove(array[pa_type_1, pa_type_2, pa_type_3, pa_type_4], NULL))::text AS pa_type,
CASE WHEN pa_type_1 = 'Supplies' THEN to_char(astart, 'MM/DD/YYYY') ELSE NULL END as supplies_approval_date,
CASE WHEN pa_type_1 = 'Supplies' THEN to_char(start, 'MM/DD/YYYY')  ELSE NULL END as supplies_effective_date,
CASE WHEN pa_type_2 = 'Drug' THEN to_char(astart, 'MM/DD/YYYY') ELSE NULL END as drug_approval_date,
CASE WHEN pa_type_2 = 'Drug' THEN to_char(start, 'MM/DD/YYYY')  ELSE NULL END as drug_effective_date,
CASE WHEN pa_type_3 = 'DME' THEN to_char(astart, 'MM/DD/YYYY') ELSE NULL END as dme_approval_date,
CASE WHEN pa_type_3 = 'DME' THEN to_char(start, 'MM/DD/YYYY')  ELSE NULL END as dme_effective_date,
CASE WHEN pa_type_4 = 'Nursing' THEN to_char(astart, 'MM/DD/YYYY') ELSE NULL END as nurse_approval_date,
CASE WHEN pa_type_4 = 'Nursing' THEN to_char(start, 'MM/DD/YYYY')  ELSE NULL END as nurse_effective_date,
CASE WHEN pa_type_2 = 'Drug' AND businesstype = 1 THEN 'Day'
WHEN pa_type_2 = 'Drug' AND businesstype = 2 THEN 'Week'
WHEN pa_type_2 = 'Drug' AND businesstype = 3 THEN 'Month'
WHEN pa_type_2 = 'Drug' AND businesstype = 4 THEN 'Quarter'
WHEN pa_type_2 = 'Drug' AND businesstype = 5 THEN 'Bi-Annual'
WHEN pa_type_2 = 'Drug' AND businesstype = 6 THEN 'Annual' END as limit_freq,
CASE WHEN pa_type_2 = 'Drug' AND pa.limit > 0 AND businesstype > 0 THEN 'Billable Units/Frequency'
WHEN pa_type_2 = 'Drug' AND pa.limit > 0 THEN 'Total Billable Units' ELSE NULL END as limits,
CASE WHEN pa_type_2 = 'Drug' AND pa.limit > 0 THEN pa.limit ELSE NULL END as unit_limit,
CASE WHEN pa_type_4 = 'Nursing' AND businesstype = 1 THEN 'Day'
WHEN pa_type_4 = 'Nursing' AND businesstype = 2 THEN 'Week'
WHEN pa_type_4 = 'Nursing' AND businesstype = 3 THEN 'Month'
WHEN pa_type_4 = 'Nursing' AND businesstype = 4 THEN 'Quarter'
WHEN pa_type_4 = 'Nursing' AND businesstype = 5 THEN 'Bi-Annual'
WHEN pa_type_4 = 'Nursing' AND businesstype = 6 THEN 'Annual' END as visit_limit_freq,
CASE WHEN pa_type_2 = 'Nursing' AND pa.limit > 0 AND businesstype > 0 THEN 'Number of Visits/Frequency'
WHEN pa_type_2 = 'Nursing' AND pa.limit > 0 THEN 'Number of Visits' ELSE NULL END as nursing_limits,
CASE WHEN pa_type_2 = 'Nursing' AND pa.limit > 0 THEN pa.limit ELSE NULL END as visit_limit,
specpr as expected_price,
CASE WHEN pa_type_1 = 'Supplies' THEN to_json(array[hcpc])::text ELSE NULL END AS sc_id,
CASE WHEN pa_type_2 = 'Drug' THEN to_json(array[hcpc])::text ELSE NULL END AS hc_id,
CASE WHEN pa_type_4 = 'Nursing' THEN to_json(array[hcpc])::text ELSE NULL END AS nc_id,
CASE WHEN authtype = 'I' THEN 'Initial' ELSE 'Renewal' END as auth_type
FROM 
(SELECT 
ptins.cpk_patins as patient_insurance_external_id,
otno as order_item_external_id,
cpk_insveri as external_id,
pa.mrn as patient_mrn,
'5' as status_id,
NULL as order_no,
NULL as order_id,
NULL as patient_id,
CASE WHEN py.invoicetyp IN ('1', 'E') THEN 'mm'
WHEN py.invoicetyp = 'M' THEN 'ncpdp'
WHEN py.invoicetyp = 'G' THEN 'generic'
WHEN py.invoicetyp = 'F' THEN 'cms1500' END as billing_method_id,
CASE WHEN hcpc LIKE 'S%' OR hcpc LIKE 'A%' OR item LIKE '%Per Diem%' THEN 'Supplies' ELSE NULL END as pa_type_1,
CASE WHEN hcpc LIKE 'J%' OR hcpc LIKE 'G%' OR hcpc LIKE 'B%' THEN 'Drug' ELSE NULL END as pa_type_2,
CASE WHEN hcpc LIKE 'E%' OR hcpc LIKE 'K%' THEN 'DME' ELSE NULL END as pa_type_3,
CASE WHEN item LIKE '%Nursing%' THEN 'Nursing' END as pa_type_4,
refnum as request_id,
authnum as number,
instructions as comment
FROM cpr_insveri pa 
LEFT JOIN LATERAL (SELECT * FROM cpr_patins pti
WHERE pti.insno = pa.insno AND pti.delflag = 0 AND pti.status = 'Active' ORDER BY rank desc LIMIT 1) ptins on true
INNER JOIN cpr_inscomp py ON py.no = ptins.insno
where pa.delflag != 1 AND dcauth = 0) TMP
INNER JOIN cpr_insveri pa ON pa.cpk_insveri = TMP.external_id
WHERE pa.astop IS NULL OR pa.astop > CURRENT_TIMESTAMP
order by pa.mrn, tmp.order_item_external_id