const { BaseTransformer } = require("./base");
const { format_phone } = require("../utils/tools");
const Logger = require("../utils/logger");
const logger = new Logger("UsersTransformer");

class UsersTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector) {
    super(sourceConnector, destinationConnector);
    this.sourceTable = "cpr_pnnames";
    this.destTable = "form_user";
    this.sourceFilters = [
      ["delflag", "!=", 1],
      ["acctlock", "!=", 1],
      ["cpruser", "=", "Y"],
    ];
    this.db = {
      src: this.sourceConnector,
      dst: this.destinationConnector,
    };
    this.enableBatchProcessing({
      batchSize: 50,
      batchConcurrency: 5,
    });
    this.field_map = {
      no: "external_id",
      username: "username",
      fname: "firstname",
      lname: "lastname",
      email: this.warp(this.email, "email"),
      phone1: this.mapField("phone1", "phone_cell", format_phone),
      authentication_type: () => "password",
      timezone: () => "America/Pacific",
      cfk_usergroups: this.warp(this._role, "role"),
      category: () => async (data) => {
        return data.cpruser === "Y" ? "Internal" : "External";
      },
    };
    this.qualifiers = [
      {
        field: "email",
        source: "email",
      },
      {
        field: "external_authentication_id",
        source: "email",
      },
      {
        field: "external_id",
        source: "no",
      },
    ];
  }

  async _role(data) {
    const usergroup = data.cfk_usergroups;
    logger.warn(`usergroup: ${usergroup}`);
    const retdata = {};
    if (!usergroup && !data.rn) {
      return retdata;
    }

    if (data.rn === "Y") {
      retdata.role = "nurse";
    }
    if (
      usergroup &&
      (usergroup === "2" || usergroup === "3" || usergroup === "20")
    ) {
      retdata.role = "admin";
      retdata.is_admin = true;
    }
    if (usergroup === "9") {
      retdata.role = "pharm";
    }
    return retdata;
  }
  async email(data) {
    const updated_data = {
      email: data.email,
      external_authentication_id: data.email,
    };
    return updated_data;
  }
}

module.exports = UsersTransformer;
