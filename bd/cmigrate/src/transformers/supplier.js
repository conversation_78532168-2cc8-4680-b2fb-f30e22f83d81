const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("SupplierTransformer");
const { getMapValue } = require("../utils/maps");
const { format_phone, ynField, isEmptyValue } = require("../utils/tools");
class SupplierTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_supplier";
    this.destTable = "form_list_supplier";
    this.qualifiers = [{ field: "external_id", source: "cpk_supplier" }];
    this.sourceFilters = [["delflag", "!=", 1]];
    this.field_map = {
      cpk_supplier: "external_id",
      acctno: "account_no",
      suppliername: "name",
      address: "street",
      city: "city",
      state: "state",
      zip_: "zip",
      terms: "terms",
      phone: this.mapField("phone", "phone", format_phone),
      fax: this.mapField("fax", "fax", format_phone),
      contact: "contact_name",
      returninfo: "return_info",
      specialins: "notes",
      directship_msd: this.warp(ynField("directship_msd"), "msd_enabled"),
      edi: this.warp(ynField("edi"), "edi_enabled"),
    };
    this.addPostTransform(this.populateSiteId);
  }

  async transform(data) {
    const result = await super.transform(data);
    return result;
  }
  async siteno(data) {
    const siteno = getMapValue("SiteMap", data.siteno, 2);
    return siteno;
  }
  async populateSiteId(transformedData, sourceData, upsertResult) {
    const { id: supplierId } = upsertResult;
    if (isEmptyValue(supplierId)) {
      logger.warn("No supplier ID available for site ID relationship");
      return;
    }

    const siteId = getMapValue("SiteMap", sourceData.siteno, 1);
    logger.debug(
      `Creating site ID relationship between supplier ID ${supplierId} and site ID ${siteId}`,
    );
    await this.db.dst.insert_gerund(
      "form_list_supplier_site_id_to_site_id",
      supplierId, // form_list_supplier_fk
      siteId, // form_site_id
    );
  }
}

module.exports = SupplierTransformer;
