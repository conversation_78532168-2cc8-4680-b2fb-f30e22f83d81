const {
  BaseTransformer,
  TransformErrorIgnore,
  TransformPreCheckError,
} = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientDiagnosisTransformer");
const { isEmptyValue, toDate } = require("../utils/tools");
const _head = require("lodash/head");

class PatientDiagnosisTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector) {
    super(sourceConnector, destinationConnector);
    this.sourceTable = "cpr_icdpatient";
    this.destTable = "form_patient_diagnosis";
    this.disableTriggers = ["trg_update_rank_diagnosis"];
    this.qualifiers = [
      {
        field: "patient_id",
        source: "cfk_hr",
        lookup: {
          table: "form_patient",
          sourceField: "cfk_hr",
          destinationField: "external_id",
          returnField: "id",
        },
      },
      { field: "external_id", source: "cpk_icdpatient" },
    ];
    this.enableBatchProcessing({
      batchSize: 50,
      batchConcurrency: 10,
    });
    this.sourceFilters = [["delflag", "!=", 1]];
    this.field_map = {
      cpk_icdpatient: "external_id",
      cfk_hr: this.warp(this.patient_lookup, "patient_id"),
      cfk_icdmasterlist_icd10: this.warp(this.dx_id, "dx_id"),
      rank: this.warp(this.rank, "rank"),
      dateassigned: this.mapField(
        "dateassigned",
        "diagnosis_start_date",
        toDate,
      ),
      dateinactivated: this.mapField(
        "dateinactivated",
        "diagnosis_stop_date",
        toDate,
      ),
    };
  }
  async transform(data) {
    logger.debug(`Transforming patient diagnosis: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    return result;
  }
  async precheck(data) {
    if (isEmptyValue(data.cfk_hr)) {
      throw new TransformPreCheckError("Patient ID is empty");
    }
    if (isEmptyValue(data.cfk_icdmasterlist_icd10)) {
      throw new TransformPreCheckError("Diagnosis ID is empty");
    }
  }
  async patient_lookup(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.cfk_hr]],
      limit: 1,
    });
    const patient_data = _head(patient);
    const updated_data = {};
    if (patient_data) {
      updated_data.patient_id = patient_data.id;
      const careplan = await this.db.dst.fetchData("form_careplan", {
        filters: [
          ["patient_id", "=", patient_data.id],
          ["status_id", "=", "3"],
        ],
        order: [["created_on", "desc"]],
        limit: 1,
      });
      const careplan_data = _head(careplan);
      if (careplan_data) {
        updated_data.careplan_id = careplan_data.id;
      }
      return updated_data;
    } else {
      logger.warn(`Patient not found for MRN: ${data.cfk_hr}`);
      return null;
    }
  }
  async rank(data) {
    if (isEmptyValue(data.rank)) return null;
    const updated_data = {};
    updated_data.rank = parseInt(data.rank);
    if (updated_data.rank === 1) {
      updated_data.active = "Yes";
    } else {
      updated_data.active = "No";
    }
    return updated_data;
  }
  async dx_id(data) {
    if (isEmptyValue(data.cfk_icdmasterlist_icd10)) return null;
    const icdfk = data.cfk_icdmasterlist_icd10;
    const icd_Code = await this.db.src.fetchData("cpr_icdmasterlist", {
      filters: [
        ["cpk_icdmasterlist", "=", icdfk],
        ["delflag", "=", 0],
      ],
      limit: 1,
    });
    const icd_code_data = _head(icd_Code);
    if (isEmptyValue(icd_code_data) || isEmptyValue(icd_code_data.code)) {
      logger.warn(`ICD code not found for icdfk in masterlist: ${icdfk}`);
      return null;
    }
    const code = icd_code_data.code;
    const diagnosis = await this.db.dst.fetchData("form_list_diagnosis", {
      filters: [["code", "=", code]],
      limit: 1,
    });
    const diagnosis_data = _head(diagnosis);
    if (isEmptyValue(diagnosis_data)) {
      logger.warn(`Diagnosis not found for code: ${code}`);
      throw new TransformErrorIgnore(
        `Diagnosis not found for code: ${code} in list_diagnosis and patient: ${data.cfk_hr}`,
      );
    }
    return diagnosis_data.code;
  }
}

module.exports = PatientDiagnosisTransformer;
