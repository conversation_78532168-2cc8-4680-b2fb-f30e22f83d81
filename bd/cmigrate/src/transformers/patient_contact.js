const {
  BaseTransformer,
  TransformErrorIgnore,
  TransformPreCheckError,
} = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientContactTransformer");
const { isEmptyValue } = require("../utils/tools");
const _head = require("lodash/head");

class PatientContactTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_roster";
    this.destTable = "form_patient_contact";
    this.qualifiers = [
      {
        field: "patient_id",
        source: "mrn",
        lookup: {
          table: "form_patient",
          sourceField: "mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
      { field: "type", source: "contact_type" },
      { field: "name", source: "fullname" },
      { field: "home", source: "home" },
    ];
    this.enableBatchProcessing({
      batchSize: 50,
      batchConcurrency: 10,
    });
    this.sourceRawQuery = `
      SELECT 
        CASE 
          WHEN r.linktype = 'P' THEN 'Primary'
          WHEN r.linktype = 'E' THEN 'Emergency'
          WHEN r.linktype = 'B' THEN 'Both'
          ELSE 'Other'
        END as contact_type,
        r.parentkey as mrn,
        ro.fname,
        ro.lname,
        h.siteno,
        CONCAT(ro.fname, ' ', ro.lname) as fullname,
        ro.phone1 as home,
        ro.phone2 as work,
        ro.phone3 as cell,
        ro.title
      FROM cpr_recordlinks r
      JOIN cpr_roster ro ON ro.cpk_roster = r.childkey
      JOIN cpr_hr h ON h.mrn = r.parentkey::text
      WHERE r.parenttable = 'HR'
        AND r.childtable = 'ROSTER'
        AND r.delflag != 1
        AND ro.patientcontact = 1
        AND ro.delflag != 1
        AND r.parentkey IS NOT NULL
    `;
    this.field_map = {
      mrn: this.warp(this.patient_lookup, "patient_id"),
      contact_type: "type",
      fullname: "name",
      home: "home",
      work: "work",
      cell: "cell",
      title: this.warp(this.relationship_lookup, "relation_id"),
    };
  }

  async transform(data) {
    logger.debug(`Transforming patient contact: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    return result;
  }
  async precheck(data) {
    if (isEmptyValue(data.mrn)) {
      throw new TransformPreCheckError("Patient ID is empty");
    }
  }
  async patient_lookup(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.mrn]],
      limit: 1,
    });
    const patient_data = _head(patient);
    if (isEmptyValue(patient_data)) {
      throw new TransformErrorIgnore(`Patient not found: ${data.mrn}`);
    }
    return patient_data?.id;
  }
  async relationship_lookup(data) {
    if (isEmptyValue(data.title)) {
      return null;
    }
    const relationid = await this.db.dst.fetchOrCreate(
      "form_list_relationship",
      {
        name: data.title,
        active: "Yes",
        allow_sync: "Yes",
      },
      [["name", "=", data.title]],
    );
    return relationid;
  }
}

module.exports = PatientContactTransformer;
