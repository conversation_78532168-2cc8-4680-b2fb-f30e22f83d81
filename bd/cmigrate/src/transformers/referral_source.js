const { BaseTransformer, TransformPreCheckError } = require("./base");
const { format_phone, isEmptyValue, has_changed } = require("../utils/tools");
const Logger = require("../utils/logger");
const logger = new Logger("ReferralSourceTransformer");
const _head = require("lodash/head");
const _ = require("lodash");

class ReferralSourceTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector) {
    super(sourceConnector, destinationConnector);
    this.sourceTable = "cpr_roster";
    this.destTable = "form_sales_account";
    this.qualifiers = [
      { field: "name", source: "org" },
      { field: "type", value: "Organization" },
      { field: "phone", source: "phone1" },
    ];
    this.sourceFilters = [
      ["delflag", "!=", 1],
      ["refsrc", "!=", 0],
      ["patientcontact", "!=", 1],
    ];
    this.db = {
      src: this.sourceConnector,
      dst: this.destinationConnector,
    };
    this.field_map = {
      org: this.warp(this._facility, "facility_name"),
      name: (data) => data.org?.trim(), // Add explicit name mapping
      title: "title",
      address: "street",
      city: "city",
      st: "state",
      zip_: "zip",
      phone1: this.mapField("phone1", "phone", format_phone),
      phone2: this.mapField("phone2", "fax", format_phone),
      email: "email",
      type: "type",
      status: "status",
    };
  }

  async _facility(data) {
    try {
      if (isEmptyValue(data.org)) {
        return null;
      }

      const facilityData = {
        name: data.org.trim(),
        type: "Other",
        address: data.address,
        city: data.city,
        state_id: data.st,
        zip: data.zip ? data.zip : data.zip_,
        phone: data.phone1,
        fax: data.phone2,
      };

      // Improve facility matching criteria
      const existingFacility = await this.db.dst.fetchData("form_facility", {
        filters: [
          ["name", "=", data.org.trim()],
          ["address", "=", data.address],
          ["city", "=", data.city],
        ],
        limit: 1,
      });
      const facility = _head(existingFacility);

      // Parse existing contacts
      let contacts = [];
      if (facility?.contact) {
        try {
          // If it's already an array, use it
          if (Array.isArray(facility.contact)) {
            contacts = facility.contact;
          }
          // If it's a string, try to parse it
          else if (typeof facility.contact === "string") {
            contacts = JSON.parse(facility.contact);
          }
        } catch (e) {
          logger.warn(`Failed to parse contacts: ${e.message}`);
          contacts = [];
        }
      }

      // Ensure contacts is an array
      contacts = Array.isArray(contacts) ? contacts : [];

      logger.debug(`Existing contacts: ${JSON.stringify(contacts)}`);

      // Create new contact object and remove null/undefined values
      const newContact = Object.fromEntries(
        Object.entries({
          name: data.lname
            ? `${data.fname} ${data.lname}`.trim()
            : data.fname?.trim(),
          role: data.title,
          work: data.phone1,
          email: data.email,
        }).filter(([_, value]) => value != null),
      );

      // Only add if name exists
      if (newContact.name) {
        const existingIndex = contacts.findIndex(
          (c) => c.name === newContact.name,
        );
        if (existingIndex === -1) {
          contacts.push(newContact);
        } else {
          contacts[existingIndex] = newContact;
        }
      }

      // Store as JSON array
      facilityData.contact = JSON.stringify(contacts);

      let facilityId;
      if (!facility) {
        const newFacility = await this.db.dst.fetchOrCreate(
          "form_facility",
          facilityData,
          [
            ["name", "=", data.org.trim()],
            ["address", "=", data.address],
            ["city", "=", data.city],
          ],
        );
        facilityId = newFacility;
      } else {
        facilityId = facility.id;
        if (has_changed(facility, facilityData)) {
          await this.db.dst.updateData("form_facility", facilityData, {
            filters: [["id", "=", facility.id]],
          });
        }
      }

      return { facility_name: facilityId, name: data.org.trim() };
    } catch (error) {
      if (
        error.message?.includes(
          "duplicate key value violates unique constraint",
        )
      ) {
        const existingFacility = await this.db.dst.fetchData("form_facility", {
          filters: [
            ["name", "=", data.org.trim()],
            ["address", "=", data.address],
            ["city", "=", data.city],
          ],
          limit: 1,
        });

        if (existingFacility?.[0]) {
          return {
            facility_name: existingFacility[0].id,
            name: data.org.trim(),
          };
        }
      }
      throw error;
    }
  }

  async precheck(data) {
    if (isEmptyValue(data.org)) {
      throw new TransformPreCheckError("Organization name is empty");
    }
  }

  async transform(data) {
    if (!data.type) {
      data.type = "Organization";
    }
    if (!data.status) {
      data.status = "Active";
    }
    return await super.transform(data);
  }
}

module.exports = ReferralSourceTransformer;
