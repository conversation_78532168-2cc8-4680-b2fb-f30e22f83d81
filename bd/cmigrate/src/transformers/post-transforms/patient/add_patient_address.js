const { has_changed } = require("../../../utils/tools");
const Logger = require("../../../utils/logger");
const logger = new Logger("PatientAddressTransform");
const _head = require("lodash/head");

async function addPatientAddress(transformedData, sourceData, _upsertResult) {
  logger.debug(
    `addPatientAddress method called with transformedData: ${JSON.stringify(transformedData)}`,
  );
  const patient = await this.db.dst.fetchData("form_patient", {
    filters: [["external_id", "=", sourceData.mrn]],
    limit: 1,
  });
  const patient_id = _head(patient)?.id;
  if (patient_id) {
    logger.debug(`Patient ID found: ${patient_id}`);
    const address_data = {};
    address_data.street = sourceData.address;
    address_data.street2 = sourceData.address2;
    address_data.city = sourceData.city;
    address_data.state_id = sourceData.state;
    address_data.zip = sourceData.zip;
    address_data.address_type = "Home";
    address_data.patient_id = patient_id;
    const patient_address = _head(
      await this.db.dst.fetchData("form_patient_address", {
        filters: [
          ["patient_id", "=", patient_id],
          ["address_type", "=", "Home"],
        ],
        limit: 1,
      }),
    );
    const careplan = await this.db.dst.fetchOrCreate(
      "form_careplan",
      {
        patient_id: patient_id,
        status_id: "3",
      },
      [
        ["patient_id", "=", patient_id],
        ["status_id", "=", "3"],
      ],
    );
    if (careplan) {
      logger.debug(`Careplan ID found: ${careplan}`);
    }
    if (await has_changed(patient_address, address_data)) {
      logger.debug(
        `Patient address changed, updating: ${JSON.stringify(address_data)}`,
      );
      const result = await this.db.dst.upsertData(
        "form_patient_address",
        address_data,
        {
          qualifiers: [
            ["patient_id", "=", patient_id],
            ["address_type", "=", "Home"],
          ],
        },
        address_data,
      );
      logger.debug(`Patient address upsert result: ${JSON.stringify(result)}`);
      return {
        operation: result.operation || "none",
        id: result.id,
        sourceId: sourceData.id || transformedData.external_id,
      };
    } else {
      logger.debug(`Patient address has not changed, skipping upsert`);
      return {
        operation: "skip",
        sourceId: sourceData.id || transformedData.external_id,
      };
    }
  } else {
    logger.warn(`No patient found for mrn: ${sourceData.mrn}`);
  }

  return {
    operation: "none",
    sourceId: sourceData.id || transformedData.external_id,
  };
}

module.exports = addPatientAddress;
