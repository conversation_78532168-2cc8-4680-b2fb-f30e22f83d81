const { isEmptyValue, has_changed } = require("../../../utils/tools");
const Logger = require("../../../utils/logger");
const logger = new Logger("PatientPrescriberTransform");
const _head = require("lodash/head");

async function addPatientPrescriber(
  transformedData,
  sourceData,
  _upsertResult,
) {
  logger.debug(
    `addPatientPrescriber method called with transformedData: ${JSON.stringify(transformedData)}`,
  );
  let patient_id = transformedData.patient_id;
  if (isEmptyValue(patient_id)) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", sourceData.mrn]],
      limit: 1,
    });
    patient_id = _head(patient)?.id;
  }
  const mrn = transformedData.external_id ?? sourceData.mrn;
  const phys = _head(
    await this.db.src.fetchData("cpr_recordlinks", {
      filters: [
        ["parenttable", "=", "HR"],
        ["childtable", "=", "DOCTORS"],
        ["parentkey", "=", mrn],
        ["delflag", "!=", 1],
        ["rank", "=", "1.0"],
      ],
      limit: 1,
    }),
  );
  if (!isEmptyValue(phys) && phys.childkey) {
    const clara_physician = _head(
      await this.db.dst.fetchData("form_physician", {
        filters: [["external_id", "=", phys.childkey]],
        limit: 1,
      }),
    );

    if (clara_physician) {
      logger.debug(
        `Physician ${phys.childkey} found for patient ${patient_id}`,
      );
      const careplan = await this.db.dst.fetchData("form_careplan", {
        filters: [
          ["patient_id", "=", patient_id],
          ["status_id", "=", "3"],
        ],
        limit: 1,
      });
      const careplan_id = _head(careplan)?.id;
      const prescriber_data = {
        patient_id: patient_id,
        physician_id: clara_physician.id,
        primary: "Yes",
        careplan_id: careplan_id,
      };

      const qualifiers = [
        ["patient_id", "=", patient_id],
        ["physician_id", "=", clara_physician.id],
        ["primary", "=", "Yes"],
      ];
      if (careplan_id) {
        qualifiers.push(["careplan_id", "=", careplan_id]);
      }
      const existing_prescriber = _head(
        await this.db.dst.fetchData("form_patient_prescriber", {
          filters: qualifiers,
          limit: 1,
        }),
      );

      if (
        !isEmptyValue(existing_prescriber) &&
        !(await has_changed(existing_prescriber, prescriber_data))
      ) {
        logger.debug(`Prescriber data has not changed, skipping upsert`);
        return {
          operation: "skip",
          sourceId: sourceData.id || transformedData.external_id,
        };
      }

      const result = await this.db.dst.upsertData(
        "form_patient_prescriber",
        prescriber_data,
        { qualifiers },
        prescriber_data,
      );

      logger.debug(
        `Patient prescriber upsert result: ${JSON.stringify(result)}`,
      );
      return {
        operation: result.operation || "none",
        id: result.id,
        sourceId: sourceData.id || transformedData.external_id,
      };
    }
  }
}

module.exports = addPatientPrescriber;
