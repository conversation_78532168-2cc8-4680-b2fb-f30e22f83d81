const { isEmptyValue, has_changed, toDate } = require("../../../utils/tools");
const Logger = require("../../../utils/logger");
const logger = new Logger("PatientMeasurementTransform");
const _head = require("lodash/head");
const moment = require("moment");
const {
  calculateIBW,
  calculateLBW,
  calculateBSA,
  calculateIBWPercent,
  calculateABW,
  calculateAge,
} = require("../../../utils/measurementHelpers");

async function createMeasurementLog(
  transformedData,
  sourceData,
  _upsertResult,
) {
  let patient_id = transformedData.id;
  if (isEmptyValue(patient_id)) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", sourceData.mrn]],
      limit: 1,
    });
    patient_id = _head(patient)?.id;
  }
  if (isEmptyValue(patient_id)) {
    logger.error(`No patient found for mrn: ${sourceData.mrn}`);
    return;
  }
  if (!sourceData.weightkg && !sourceData.heightcm) {
    logger.debug(`No weight or height data found for patient ${patient_id}`);
    return;
  }
  const weight = parseFloat(sourceData.weightkg);
  const height = parseFloat(sourceData.heightcm);
  const gender = transformedData.sex === "M" ? "Male" : "Female";
  const ibw = calculateIBW(height, gender);
  const lbw = calculateLBW(weight, height, gender);
  const bsa = calculateBSA(weight, height);
  const ibwPercentage = calculateIBWPercent(weight, ibw);
  const abw = calculateABW(weight, ibw);
  let weightdate = sourceData.weightdate
    ? toDate(sourceData.weightdate)
    : moment().format("MM/DD/YYYY");
  if (isEmptyValue(weightdate)) {
    weightdate = moment().format("MM/DD/YYYY");
  }
  const age = transformedData.dob ? calculateAge(transformedData.dob) : null;

  const measurementData = {
    patient_id: patient_id,
    weight: weight,
    height: height,
    ibw: ibw,
    ibw_percentage: ibwPercentage,
    abw: abw,
    lbw: lbw,
    bsa: bsa,
    pediatric: age < 18 ? "Yes" : "No",
    date: weightdate,
  };

  // Round decimal values to 2 decimal places
  for (const key in measurementData) {
    if (typeof measurementData[key] === "number") {
      measurementData[key] = Number(measurementData[key].toFixed(2));
    }
  }
  const qualifiers = [
    ["patient_id", "=", patient_id],
    ["weight", "=", measurementData.weight],
    ["height", "=", measurementData.height],
    ["date", "=", weightdate],
  ];
  const existingMeasurement = _head(
    await this.db.dst.fetchData("form_patient_measurement_log", {
      filters: qualifiers,
      limit: 1,
    }),
  );

  if (
    isEmptyValue(existingMeasurement) ||
    (await has_changed(existingMeasurement, measurementData))
  ) {
    const result = await this.db.dst.upsertData(
      "form_patient_measurement_log",
      measurementData,
      { qualifiers },
      measurementData,
    );
    logger.debug(`Created new measurement log for patient ${patient_id}`);
    return {
      operation: result.operation || "none",
      id: result.id,
      sourceId: sourceData.id || transformedData.external_id,
    };
  } else {
    logger.debug(
      `Measurement log for patient ${patient_id} on ${weightdate} already exists and hasn't changed. Skipping insertion.`,
    );
    return {
      operation: "skip",
      sourceId: sourceData.id || transformedData.external_id,
    };
  }
}

module.exports = createMeasurementLog;
