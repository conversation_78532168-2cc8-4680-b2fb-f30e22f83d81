const Logger = require("../../../utils/logger");
const logger = new Logger("PatientAttachmentTransform");
const { has_changed, isEmptyValue } = require("../../../utils/tools");
const { TransformPreCheckError } = require("../../base");
const _head = require("lodash/head");

async function addPatientAttachment(transformedData, sourceData, upsertResult) {
  logger.debug(
    `addPatientAttachment method called with transformedData: ${JSON.stringify(transformedData)}, upsertResult: ${JSON.stringify(upsertResult)}`,
  );

  let patient_id = transformedData.patient_id;
  if (isEmptyValue(patient_id) && sourceData.doc_mrn) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", sourceData.doc_mrn]],
      limit: 1,
    });
    patient_id = _head(patient)?.id;
  }

  let doc_id = transformedData.transformed_id || upsertResult?.id;
  if (isEmptyValue(doc_id) && transformedData.external_id) {
    const documents = await this.db.dst.fetchData("form_document", {
      filters: [["external_id", "=", transformedData.external_id]],
      limit: 1,
    });
    doc_id = _head(documents)?.id;
  }

  logger.debug(`Using document ID: ${doc_id} for patient: ${patient_id}`);
  if (isEmptyValue(patient_id)) {
    throw new TransformPreCheckError(
      `Patient ID is empty for document ${transformedData.external_id}`,
    );
  }

  if (isEmptyValue(doc_id)) {
    throw new TransformPreCheckError(
      `Document ID is empty for document ${transformedData.external_id}`,
    );
  }

  const attachment_data = {
    patient_id: patient_id,
    document_id: doc_id,
    patient_file: transformedData.file_path,
    comments: transformedData.comments,
  };

  // Strict validation of attachment data
  if (isEmptyValue(attachment_data.patient_file)) {
    throw new TransformPreCheckError(
      `Patient file is empty for document ${doc_id}`,
    );
  }

  const qualifiers = [
    ["patient_id", "=", patient_id],
    ["document_id", "=", doc_id],
  ];

  const document = await this.db.dst.fetchData("form_patient_attachment", {
    filters: [...qualifiers, ["archived", "is", null], ["deleted", "is", null]],
    limit: 1,
  });

  const existing_doc = _head(document);

  if (!isEmptyValue(existing_doc)) {
    const hasChanged = await has_changed(
      {
        patient_file: existing_doc.patient_file,
        comments: existing_doc.comments,
      },
      {
        patient_file: attachment_data.patient_file,
        comments: attachment_data.comments,
      },
    );

    if (!hasChanged) {
      logger.debug(`Patient Attachment has not changed, skipping upsert`);
      return {
        operation: "skip",
        id: existing_doc.id,
        data: existing_doc,
      };
    }
  }

  logger.debug(`Upserting Patient Attachment`);

  const result = await this.db.dst.upsertData(
    "form_patient_attachment",
    attachment_data,
    {
      qualifiers,
      jsonFields: ["patient_file"],
      requireQualifierMatch: true,
    },
    attachment_data,
  );

  return result;
}

module.exports = addPatientAttachment;
