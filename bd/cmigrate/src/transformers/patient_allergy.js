const {
  BaseTransformer,
  TransformPreCheckError,
  TransformErrorIgnore,
} = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientAllergyTransformer");
const { isEmptyValue, toDate } = require("../utils/tools");
const _head = require("lodash/head");
const { capitalize } = require("lodash");

class PatientAllergyTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector) {
    super(sourceConnector, destinationConnector);
    this.sourceTable = "cpr_drugallr";
    this.destTable = "form_patient_allergy";
    this.qualifiers = [
      { field: "external_id", source: "cpk_drugallr" },
      {
        field: "patient_id",
        source: "mrn",
        lookup: {
          table: "form_patient",
          sourceField: "mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
    ];
    this.enableBatchProcessing();
    this.sourceFilters = [["delflag", "!=", 1]];
    this.field_map = {
      mrn: this.warp(this.patient_lookup, "patient_id"),
      createdon: this.mapField("createdon", "start_date", toDate),
      name_: this.warp(this.allergy_name, "allergen_id"),
      userdesc: "note",
      cpk_drugallr: "external_id",
    };
    this.addPostTransform(this.reactions_gerund);
  }
  async precheck(data) {
    if (isEmptyValue(data.mrn?.trim())) {
      throw new TransformPreCheckError("MRN is empty");
    }
    if (isEmptyValue(data.name_)) {
      throw new TransformPreCheckError("Allergen is empty");
    }
  }
  async transform(data) {
    logger.debug(`Transforming allergy data:
        mrn: ${data.mrn}
        createdon: ${data.createdon}
        name_: ${data.name_}
        allergen_id: ${data.allergen_id}
        start_date: ${data.start_date}
    `);
    try {
      const result = await super.transform(data);
      logger.debug(`Transform result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      logger.error(
        `Transform error for allergy ${data.cpk_drugallr}: ${error.message}`,
      );
      throw error;
    }
  }

  async patient_lookup(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.mrn?.trim()]],
      limit: 1,
    });
    const patient_data = _head(patient);

    if (isEmptyValue(patient_data)) {
      throw new TransformErrorIgnore(
        `Patient not found: ${data.mrn?.trim()}`,
        data,
      );
    }

    // Fetch HR allergies
    const hr_allergies = await this.db.src.fetchData("cpr_hr", {
      filters: [["mrn", "=", data.mrn?.trim()]],
      limit: 1,
    });
    const hr_allergies_data = _head(hr_allergies);
    const hr_allergies_other_allergies = hr_allergies_data?.othallergy;

    if (!isEmptyValue(hr_allergies_other_allergies)) {
      const updated = await this.db.dst.updateData(
        "form_patient",
        { other_allergies: hr_allergies_other_allergies?.trim() },
        {
          qualifiers: [["id", "=", patient_data?.id]],
        },
      );
      logger.info(
        `Updated patient other allergies: ${JSON.stringify(updated)}`,
      );
    }
    return patient_data?.id;
  }
  async allergy_name(data) {
    if (isEmptyValue(data.name_)) return null;
    const allergen = data.name_.toLowerCase();
    logger.debug(`Allergen: ${allergen}`);
    let active = "Yes";
    const return_data = {};

    if (allergen.includes("no known")) return { allergen_id: "900590-1" };

    const fdballergen = await this.db.dst.fetchData(
      "form_list_fdb_alrgn_mstr",
      {
        filters: [["dam_concept_id_desc", "ilike", allergen]],
        limit: 1,
        lowercase: true,
      },
    );
    const allergen_data = _head(fdballergen);
    if (isEmptyValue(allergen_data)) {
      active = "No";
      return_data.unmapped_allergen = allergen;
    } else {
      return_data.allergen_id = allergen_data.code;
    }

    return return_data;
  }
  async reactions_gerund(transformedData, sourceData, upsertResult) {
    const form_reactions = ["rash", "shock", "asthma", "nausea", "anemia"];
    const foundReactions = form_reactions.filter(
      (reaction) => sourceData[reaction] === "Y",
    );

    if (foundReactions.length === 0) return;
    logger.info(`Found reactions: ${foundReactions}`);
    const { id: allergyId } = upsertResult;
    if (isEmptyValue(allergyId)) {
      logger.warn("No allergy ID available for gerund relationship");
      return;
    }

    for (const reaction of foundReactions) {
      const reactionId = await this.db.dst.fetchOrCreate(
        "form_list_reaction",
        {
          name: capitalize(reaction),
          active: "Yes",
        },
        [
          ["name", "=", capitalize(reaction)],
          ["active", "=", "Yes"],
        ],
      );
      if (reactionId) {
        logger.info(`Inserting gerund relationship for ${reaction}`);
        const result = await this.db.dst.insert_gerund(
          "form_patient_allergy_reaction_id_to_list_reaction_id",
          allergyId, // form_patient_allergy_fk
          reactionId, // form_list_reaction_fk
        );
        logger.info(`Gerund relationship result: ${JSON.stringify(result)}`);
        return result;
      }
    }
  }
}

module.exports = PatientAllergyTransformer;
