const {
  BaseTransformer,
  TransformErrorIgnore,
  TransformPreCheckError,
} = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PayerMatrixTransformer");
const { isEmptyValue, has_changed } = require("../utils/tools");
const _head = require("lodash/head");

class PayerMatrixTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_matrix";
    this.destTable = "form_payer_price_matrix";
    this.qualifiers = [
      { field: "external_id", source: "contractno" },
      { field: "name", source: "org" },
    ];
    this.sourceRawQuery = `
      SELECT DISTINCT on (i.contractno,m.insno) i.no,
        i.contractno,
        i.org
      FROM cpr_inscomp i
      JOIN cpr_matrix m ON m.insno = i.contractno
      WHERE i.delflag = 0 and i.inactive=0 and m.delflag = 0
    `;
    this.field_map = {
      contractno: "external_id",
      org: "name",
    };
    const boundAddPayerMatrixItem = this.AddPayerMatrixItem.bind(this);
    this.addPostTransform(boundAddPayerMatrixItem);
  }

  async transform(data) {
    logger.debug(`Transforming payer matrix: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    return result;
  }

  async AddPayerMatrixItem(transformedData, sourceData, upsertResult) {
    logger.debug(
      `Adding payer matrix items for ${transformedData.external_id} with upsert result: ${JSON.stringify(upsertResult)}`,
    );
    const matrix_id = upsertResult?.id;
    const org = transformedData.name;
    const results = [];
    if (isEmptyValue(matrix_id) || this.isDryRun) {
      logger.debug(
        `Skipping payer matrix items for ${matrix_id} because it is empty or dry run`,
      );
      return;
    }
    const matrix = await this.db.dst.fetchData("form_payer_price_matrix", {
      filters: [["id", "=", matrix_id]],
      limit: 1,
    });
    const matrix_data = _head(matrix);
    if (isEmptyValue(matrix_data)) {
      throw new TransformPreCheckError(`Matrix not found: ${matrix_id}`);
    }
    let inv;
    const matrix_items = await this.db.src.raw_query(`
      SELECT
        m.insno,
        i.org,
        i.sborg,
        translate(p.ndc, '- ', '') as ndc,
        p.name_,
        m.*
      FROM cpr_parts p 
      JOIN cpr_matrix m on m.invno = p.no
      JOIN cpr_inscomp i on i.contractno = m.insno
      WHERE i.delflag = 0 
      AND p.delflag = 0
      AND (
        translate(p.ndc, '- ', '') is not null
        AND p.ndc != ''
      )
      AND m.price > 0
      AND m.contractno = ${matrix_data.external_id}
      AND m.delflag = 0
    `);
    for (const item of matrix_items) {
      const ext_pk = `${matrix_id}_${item.cpk_matrix}`;
      logger.debug(`Processing item: ${JSON.stringify(item)}`);
      const inv_data = await this.db.dst.fetchData("form_inventory", {
        filters: [["external_id", "=", item.invno]],
        limit: 1,
      });
      inv = _head(inv_data);
      if (isEmptyValue(inv)) {
        logger.debug(`Inventory not found: ${item.invno}`);
        continue;
      }
      try {
        const unique_external_id = `${item.insno}_${item.invno}_${item.cpk_matrix}`;

        const item_data = {
          inventory_id: inv?.id,
          external_id: unique_external_id,
          type: inv?.type,
          last_cost: inv?.last_cost,
          awp_price: inv?.awp_price,
          wac_price: inv?.wac_price,
          payer_price_matrix_id: matrix_id,
          list_price: inv?.list_price,
          special_price: item.price,
          expected_price_multiplier: item.emultiplier,
          expected_price_formula_id: item.eformula_,
          special_price_multiplier: item.multiplier,
          special_price_formula_id: item.formula_,
          billable:
            item.billable == "Y" ? "Yes" : item.billable == "N" ? "No" : null,
          price_code_id: inv?.type
            ? await this.mapInventoryTypeToCode(inv.type)
            : null,
        };

        const result = await this.db.dst.upsertData(
          "form_payer_price_matrix_item",
          item_data,
          {
            qualifiers: [
              { field: "payer_price_matrix_id", value: matrix_id },
              { field: "inventory_id", value: inv?.id },
              { field: "external_id", value: unique_external_id },
            ],
          },
        );

        if (result?.id) {
          logger.debug(
            `Matrix item ${result.operation} for matrix ${matrix_id}, inventory ${inv?.id}, external_id ${unique_external_id}`,
          );
          logger.debug(`Operation details: ${JSON.stringify(result)}`);
        }
      } catch (error) {
        logger.error(`Error in upsert operation: ${error.message}`);
      }
    }
    // check if the matrix is associated with payer record
    const payer_ = await this.db.dst.fetchData("form_payer", {
      filters: [["assigned_matrix_id", "=", matrix_id]],
      limit: 1,
    });
    const payer_data = _head(payer_);
    if (isEmptyValue(payer_data)) {
      logger.debug(
        `Payer matrix not associated with payer record, associating it now if payer exists : ${matrix_data.external_id}`,
      );
      const payer_data = await this.db.dst.fetchData("form_payer", {
        filters: [["organization", "=", org]],
        limit: 1,
      });
      const payer = _head(payer_data);
      if (!isEmptyValue(payer)) {
        logger.debug(
          `Payer found: ${payer.external_id} updating assigned_matrix_id to ${matrix_id} for payer id: ${payer.id}`,
        );
        const res = await this.db.dst.raw_query(
          `UPDATE form_payer SET assigned_matrix_id = $1 WHERE id = $2 RETURNING id`,
          [matrix_id, payer.id],
        );
        if (res) {
          logger.debug(
            `Payer matrix associated with payer record: ${payer.external_id} result: ${JSON.stringify(res)}`,
          );
        }
      }
    }
    return results;
  }

  async mapInventoryTypeToCode(invType) {
    const typeMap = {
      Drug: "01",
      Supply: "02",
      "Equipment Rental": "03",
      Billable: "09", // Mapping to Misc as default for Billable
    };
    const code = typeMap[invType];
    return code;
  }
}

module.exports = PayerMatrixTransformer;
