const {
  BaseTransformer,
  TransformPreCheckError,
  TransformErrorIgnore,
} = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientDocumentsTransformer");
const path = require("path");
const fs = require("fs").promises;

const { S3Manager } = require("../utils/s3");
const _head = require("lodash/head");
const { isEmptyValue } = require("../utils/tools");
const mimeTypes = require("mime-types");
const { addPatientAttachment } = require("./post-transforms/document");

class PatientDocuments extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_documents";
    this.destTable = "form_document";
    this.qualifiers = [
      { field: "external_id", source: "cpk_documents" },
      {
        field: "patient_id",
        source: "doc_mrn",
        lookup: {
          table: "form_patient",
          sourceField: "doc_mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
    ];
    this.s3Manager = new S3Manager(this.isDryRun);
    this.sourceFilters = [
      ["delflag", "!=", 1],
      ["filename", "not_null"],
      ["filename", "nilike", "z:"],
    ];
    this.enableBatchProcessing({
      batchSize: 1000,
      batchConcurrency: 50,
    });

    this.field_map = {
      cpk_documents: "external_id",
      doc_mrn: this.warp(this.doc_mrn, "patient_id"),
      descrip: "comments",
      filename: this.warp(this.filename, "file_path"),
    };

    this.local_path = this.config.s3.local_path;
    this.addPostTransform(addPatientAttachment);
  }

  async transform(data) {
    logger.debug(`Starting transform for document ${data.cpk_documents}`);
    try {
      const result = await super.transform(data);
      logger.debug(`Transform result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      logger.error(
        `Transform error for ${data.cpk_documents}: ${error.message}`,
      );
      throw error;
    }
  }
  async precheck(data) {
    logger.debug(`Running precheck for document ${data.cpk_documents}`);

    if (isEmptyValue(data.cpk_documents)) {
      logger.debug(`Document ID is empty for record`);
      throw new TransformPreCheckError(
        `Document ID is empty for document ${data.cpk_documents}`,
      );
    }

    if (isEmptyValue(data.doc_mrn)) {
      logger.debug(`Patient ID is empty for document ${data.cpk_documents}`);
      throw new TransformPreCheckError(
        `Patient ID is empty for document ${data.cpk_documents}`,
      );
    }

    try {
      await fs.access(this.local_path, fs.constants.R_OK);
    } catch (error) {
      logger.debug(`Local path access error: ${error.message}`);
      throw new TransformPreCheckError(
        `Local path ${this.local_path} does not exist or is not accessible: ${error.message}`,
      );
    }

    if (isEmptyValue(data.filename)) {
      logger.debug(`Filename is empty for document ${data.cpk_documents}`);
      throw new TransformPreCheckError(
        `Filename is empty for document ${data.cpk_documents}`,
      );
    }

    logger.debug(`Precheck passed for document ${data.cpk_documents}`);
  }
  async doc_mrn(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.doc_mrn]],
      limit: 1,
    });
    const patient_id = _head(patient)?.id;
    logger.debug(
      `Patient ID for document ${data.cpk_documents}: ${patient_id}`,
    );

    if (isEmptyValue(patient_id)) {
      throw new TransformPreCheckError(
        `No patient found with external_id ${data.doc_mrn} for document ${data.cpk_documents}`,
      );
    }

    return patient_id;
  }
  async filename(data) {
    const filepath = path.basename(data.filename).toLowerCase();
    const normalizedPath = filepath.replace(/\\/g, "/").toLowerCase();
    const fname = normalizedPath.split("/").pop();

    // Get patient_id
    const patient_id = await this.doc_mrn(data);

    // Check if document exists in DB first
    const existingDoc = await this.db.dst.fetchData("form_document", {
      filters: [
        ["external_id", "=", data.cpk_documents],
        ["patient_id", "=", patient_id],
      ],
      limit: 1,
    });

    // If document exists and has a filehash, check S3
    if (existingDoc?.length && existingDoc[0].file_path?.filehash) {
      const s3check = await this.s3Manager.fileExists(
        existingDoc[0].file_path.filehash,
      );
      if (!this.isDryRun && s3check.exists) {
        logger.success(
          `Using existing file from DB: ${existingDoc[0].file_path.filehash}`,
        );
        return existingDoc[0].file_path;
      }
    }

    // If we get here, we need to upload the file
    try {
      if (!normalizedPath.includes("faxes")) {
        logger.warn(`Invalid path format: ${normalizedPath}`);
        throw new TransformErrorIgnore(
          `Invalid path format: ${normalizedPath}. Please ensure file is in faxes directory.`,
        );
      }

      const split_path = normalizedPath.split("faxes")[1];
      const dir = path.dirname(path.join(this.local_path, split_path));
      const baseFileName = path
        .basename(path.join(this.local_path, split_path))
        .toLowerCase();

      // Read directory and find file with case-insensitive match
      const files = await fs.readdir(dir);
      const actualFile = files.find(
        (file) => file.toLowerCase() === baseFileName,
      );

      if (!actualFile) {
        logger.error(`File not found: ${baseFileName} in directory ${dir}`);
        throw new TransformErrorIgnore(`File not found: ${baseFileName}`);
      }

      const fullPath = path.join(dir, actualFile);
      const file = await this.readFile(fullPath);
      const filesize = await this.getFileSize(fullPath);
      const mimetype =
        mimeTypes.lookup(actualFile) || "application/octet-stream";

      const s3_upload = await this.s3Manager.put(file, {
        filename: fname,
        documentId: data.cpk_documents,
        filesize,
        mimetype,
      });
      logger.success(`File uploaded to s3 with hash: ${s3_upload.filehash}`);

      return {
        file_path: {
          filehash: s3_upload.filehash,
          filename: fname,
          filesize,
          mimetype,
        },
        fax_id: data.cpk_documents,
      };
    } catch (err) {
      logger.error(`Error uploading file to s3: ${err.stack || err}`);
      throw new TransformErrorIgnore(
        `Error uploading file to s3: ${err.message}`,
      );
    }
  }
  async readFile(filename) {
    const file = await fs.readFile(filename);
    return file;
  }
  async getFileSize(filename) {
    return (await fs.stat(filename)).size;
  }
}

module.exports = PatientDocuments;
