const { BaseTransformer } = require("./base");
const fs = require("fs");
const { toDate, isEmptyValue, format_checkbox } = require("../utils/tools");
const Logger = require("../utils/logger");
const logger = new Logger("PriorAuthTransformer");
const _head = require("lodash/head");

class PriorAuthTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_insveri";
    this.destTable = "form_patient_prior_auth";
    this.qualifiers = [
      { field: "external_id", source: "external_id" },
      {
        field: "patient_id",
        lookup: {
          table: "form_patient",
          sourceField: "patient_mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
    ];
    this.sourceRawQuery = fs.readFileSync(
      __dirname + "/fixtures/prior_auth.sql",
      "utf8",
    );
    this.field_map = {
      external_id: "external_id",
      patient_id: this.warp(this._mapPatientId, "patient_id"),
      status_id: "status_id",
      billing_method_id: "billing_method_id",
      order_item_external_id: this.warp(this._mapOrderItems, "order_item_id"),
      pa_type: this.warp(this._mapPaType, "pa_type"),
      request_id: "request_id",
      number: "number",
      comment: "comment",
      drug_approval_date: this.mapField(
        "drug_approval_date",
        "drug_approval_date",
        toDate,
      ),
      drug_effective_date: this.mapField(
        "drug_effective_date",
        "drug_effective_date",
        toDate,
      ),
      nurse_approval_date: this.mapField(
        "nurse_approval_date",
        "nurse_approval_date",
        toDate,
      ),
      nurse_effective_date: this.mapField(
        "nurse_effective_date",
        "nurse_effective_date",
        toDate,
      ),
      dme_approval_date: this.mapField(
        "dme_approval_date",
        "dme_approval_date",
        toDate,
      ),
      dme_effective_date: this.mapField(
        "dme_effective_date",
        "dme_effective_date",
        toDate,
      ),
      supplies_approval_date: this.mapField(
        "supplies_approval_date",
        "supplies_approval_date",
        toDate,
      ),
      supplies_effective_date: this.mapField(
        "supplies_effective_date",
        "supplies_effective_date",
        toDate,
      ),
      limit_freq: "limit_freq",
      limits: "limits",
      unit_limit: "unit_limit",
      visit_limit_freq: "visit_limit_freq",
      nursing_limits: "nursing_limits",
      visit_limit: "visit_limit",
      expected_price: "expected_price",
      auth_type: "auth_type",
      patient_insurance_external_id: this.lookupId(
        "form_patient_insurance",
        "external_id",
        "insurance_id",
      ),
    };
    const boundPostUpdateIds = this.postUpdateIds.bind(this);
    this.addPostTransform(boundPostUpdateIds);
  }

  async transform(data) {
    const result = await super.transform(data);
    return result;
  }

  async _mapPatientId(data) {
    if (isEmptyValue(data.patient_mrn)) return null;
    const patient = _head(
      await this.db.dst.fetchData("form_patient", {
        filters: [["external_id", "=", data.patient_mrn]],
        limit: 1,
      }),
    );
    return patient?.id;
  }
  async _mapOrderItems(data) {
    if (isEmptyValue(data.order_item_external_id)) return null;
    const updated_data = {};
    const order = _head(
      await this.db.dst.fetchData("form_careplan_order", {
        filters: [["external_id", "=", data.order_item_external_id]],
        limit: 1,
      }),
    );
    updated_data.order_id = order?.id;
    updated_data.order_no = order?.order_no;
    const orderItem = _head(
      await this.db.dst.fetchData("form_careplan_order_item", {
        filters: [["external_id", "=", data.order_item_external_id]],
        limit: 1,
      }),
    );
    if (!isEmptyValue(data.pa_type) && !isEmptyValue(orderItem)) {
      if (data.pa_type.includes("Drug")) {
        updated_data.require_order_item_id = "Yes";
        updated_data.order_item_status = "Pending";
        updated_data.pending_order_item_id = orderItem?.id;
        updated_data.rx_no = orderItem?.rx_no;
      }
      if (!isEmptyValue(data.limits)) {
        updated_data.limits = data.limits;
        updated_data.unit_limit = data.unit_limit;
        updated_data.limit_freq = data.limit_freq;
        updated_data.refills_limit = data.refills_limit;
      }
    }
    return updated_data;
  }
  async _mapPaType(data) {
    if (isEmptyValue(data.pa_type)) return null;
    try {
      // The source data comes as a JSON string, so we parse it
      const paTypes = JSON.parse(data.pa_type);
      return format_checkbox(paTypes);
    } catch (error) {
      logger.error(`Error parsing pa_type: ${error.message}`);
      return null;
    }
  }

  async _mapHcId(data) {
    if (isEmptyValue(data.hc_id)) return null;
    try {
      // The source data comes as a JSON string, so we parse it
      return JSON.parse(data.hc_id)?.[0];
    } catch (error) {
      logger.error(`Error parsing hc_id: ${error.message}`);
      return null;
    }
  }

  async _mapScId(data) {
    if (isEmptyValue(data.sc_id)) return null;
    try {
      return JSON.parse(data.sc_id);
    } catch (error) {
      logger.error(`Error parsing sc_id: ${error.message}`);
      return null;
    }
  }

  async _mapNcId(data) {
    if (isEmptyValue(data.nc_id)) return null;
    try {
      return JSON.parse(data.nc_id);
    } catch (error) {
      logger.error(`Error parsing nc_id: ${error.message}`);
      return null;
    }
  }

  async postUpdateIds(transformedData, sourceData, upsertResult) {
    const { id: mainOrderId } = upsertResult;
    if (!isEmptyValue(sourceData.hc_id)) {
      const hcIds = JSON.parse(sourceData.hc_id);
      for (const hc_id of hcIds) {
        await this.db.dst.insert_gerund(
          "form_patient_prior_auth_hc_id_to_list_fdb_medicare_desc_id",
          mainOrderId,
          hc_id,
        );
      }
    }
    if (!isEmptyValue(sourceData.sc_id)) {
      const scIds = JSON.parse(sourceData.sc_id);
      for (const sc_id of scIds) {
        await this.db.dst.insert_gerund(
          "form_patient_prior_auth_sc_id_to_list_billing_code_id",
          mainOrderId,
          sc_id,
        );
      }
    }
    if (!isEmptyValue(sourceData.nc_id)) {
      const ncIds = JSON.parse(sourceData.nc_id);
      for (const nc_id of ncIds) {
        await this.db.dst.insert_gerund(
          "form_patient_prior_auth_nc_id_to_list_billing_code_id",
          mainOrderId,
          nc_id,
        );
      }
    }
    if (!isEmptyValue(transformedData.insurance_id)) {
      await this.db.dst.insert_gerund(
        "form_patient_prior_auth_ins_filter_to_patient_insurance_id",
        mainOrderId,
        transformedData.insurance_id,
      );
    }
  }
}

module.exports = PriorAuthTransformer;
