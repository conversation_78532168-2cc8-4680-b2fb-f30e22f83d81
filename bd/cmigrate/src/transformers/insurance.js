const { BaseTransformer } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("InsuranceTransformer");
const fs = require("fs");
const path = require("path");
const {
  isEmptyValue,
  format_checkbox,
  mapData,
  format_phone,
  checkboxField,
  ynField,
} = require("../utils/tools");
const { getMapValue } = require("../utils/maps");

class InsuranceTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector) {
    super(sourceConnector, destinationConnector);
    this.sourceTable = "cpr_inscomp";
    this.destTable = "form_payer";
    this.qualifiers = [{ field: "external_id", source: "no" }];

    // Add debug logging
    const sqlPath = path.join(__dirname, "fixtures/base_insurance.sql");
    logger.debug(`Loading SQL file from: ${sqlPath}`);
    try {
      this.sourceRawQuery = fs.readFileSync(sqlPath, "utf8");
      logger.debug(
        `Loaded SQL query: ${this.sourceRawQuery.substring(0, 100)}...`,
      );
    } catch (error) {
      logger.error(`Failed to load SQL file: ${error.message}`);
      // Optionally fall back to sourceTable
      this.sourceRawQuery = null;
    }

    if (sourceConnector.additionalFilters?.length > 0) {
      this.addFiltersToRawQuery(sourceConnector.additionalFilters);
    }
    this.enableBatchProcessing();
    this.db = {
      src: sourceConnector,
      dst: destinationConnector,
    };
    this.field_map = {
      // Basic Information
      no: "external_id", // #INSCOMP.NO
      org: "organization", // #INSCOMP.ORG
      id_: this.warp(this.id_, "short_code"), // #INSCOMP.ID
      inactive: this.warp(ynField("inactive", true), "active"), // #INSCOMP.Inactive

      // Contact Information
      phone: this.mapField("phone", "phone", format_phone), // #INSCOMP.PHONE
      fax: this.mapField("fax", "fax", format_phone), // #INSCOMP.FAX
      defcustloc: this.warp(async (data) => {
        if (isEmptyValue(data.defcustloc)) return null;
        return data.defcustloc.padStart(2, "0");
      }, "default_service_place_id"), // #INSCOMP.DEFCUSTLOC
      defaultplaceresidence: this.warp(async (data) => {
        if (isEmptyValue(data.defaultplaceresidence)) return null;
        return data.defaultplaceresidence.trim();
      }, "default_place_of_res_id"), // #INSCOMP.defaultplaceresidenceE
      // Primary Address
      address: "address1", // #INSCOMP.ADDRESS
      addr2: "address2", // #INSCOMP.ADDR2
      city: "city", // #INSCOMP.CITY
      st: "state_id", // #INSCOMP.ST
      zip_: "zip", // #INSCOMP.ZIP

      // Secondary/Billing Address
      sbaddress: "baddress1", // #INSCOMP.SBADDRESS
      sbcity: "bcity", // #INSCOMP.SBCITY
      sbst: "bstate_id", // #INSCOMP.SBST
      sbzip: "bzip", // #INSCOMP.SBZIP

      // Provider Information
      hpid: this.warp(
        async (data) => (/^\d+$/.test(data.hpid) ? data.hpid : null),
        "hp_id",
      ),
      naicno: this.warp(
        async (data) => (/^\d+$/.test(data.naicno) ? data.naicno : null),
        "naic_id",
      ), // #INSCOMP.NAICNO
      // Billing Method and Type
      billmeth: this.warp(this.billingMethodId, "billing_method_id"), // #INSCOMP.BILLMETH
      typecode: this.warp(this.payerTypeId, "type_id"), // #INSCOMP.TYPECODE
      timelyfiling: "days_timely_filing", // #INSCOMP.TIMELYFILING

      // CMS Settings
      use24j: this.warp(ynField("use24j"), "box_24j"), // #INSCOMP.USE24J
      box33bqualifier: "cms_12_qualifier", // #INSCOMP.BOX33BQUALIFIER
      group33: "cms_12_id", // #INSCOMP.GROUP33
      cms1500_printbox17qual: this.warp(
        ynField("cms1500_printbox17qual"),
        "cms_13",
      ), // #INSCOMP.CMS1500_PRINTBOX17QUAL
      cms1500_printdecimal_diagcode: this.warp(
        ynField("cms1500_printdecimal_diagcode"),
        "cms_14",
      ), // #INSCOMP.CMS1500_PRINTDECIMAL_DIAGCODE

      // Authorization and Requirements
      reqauth: this.warp(this.authorizationSetting, "req_auth"), // #INSCOMP.REQAUTH
      cmnrequired: this.warp(ynField("cmnrequired"), "req_signed_cmn"), // #INSCOMP.CMNREQUIRED

      // Pharmacy Settings
      binno: "bin", // #INSCOMP.BINNO
      pcn: "pcn", // #INSCOMP.PCN
      payer340b: this.warp(ynField("payer340b"), "payer_340b"), // #INSCOMP.PAYER340B
      sendunitofmeasure: this.warp(ynField("sendunitofmeasure"), "send_uom"), // #INSCOMP.SENDUNITOFMEASURE

      // Claim Processing Settings
      claimbycalendarmonth: this.warp(
        ynField("claimbycalendarmonth"),
        "claims_calendar_month",
      ), // #INSCOMP.CLAIMBYCALENDARMONTH
      alwaysbillasdenial: this.warp(
        ynField("alwaysbillasdenial"),
        "always_bill_for_denial",
      ), // #INSCOMP.ALWAYSBILLASDENIAL
      blankpersoncode: this.warp(
        ynField("blankpersoncode"),
        "send_blank_person_code",
      ), // #INSCOMP.BLANKPERSONCODE

      // Complex Transformations
      autotransfer: this.warp(this.autoTransfer, "auto_transfer_write_off"),
      splitbyauth: this.warp(this.splitByAuth, "split_pa"),
      cms2: this.liveTransform(this.populateCms2),

      // Other Fields
      notes: "notes", // #INSCOMP.NOTES
      collector: this.warp(this.collectorTeamId, "collector_id"), // #INSCOMP.COLLECTOR
      // new maps 01/15/2025
      defdfee: "default_dispense_fee",
      calcing: this.warp(async (data) => {
        if (isEmptyValue(data.calcing)) return null;
        const calcing = data.calcing.trim().toUpperCase();
        switch (calcing) {
          case "A":
            return "01";
          case "L":
            return "07";
          case "C":
            return "04";
          case "E":
            return "00";
          case "W":
            return "12";
          default:
            return "00";
        }
      }, "base_ing_cost_on_id"),
      servicecode: this.warp(async (data) => {
        if (isEmptyValue(data.servicecode)) return null;
        return data.servicecode?.replace(/^0+/, "");
      }, "default_los_id"),
      revgroup: this.warp(ynField("revgroup"), "transmit_grp_no_reversal"),
      d9secondary: this.warp(async (data) => {
        if (isEmptyValue(data.d9secondary)) return null;
        const d9secondary = data.d9secondary || "2";
        switch (d9secondary) {
          case "1":
            return "Send Zeros";
          case "2":
            return "Send Primary Claim Amount";
          case "3":
            return "Send Co-pay Amount";
          default:
            return "Send Primary Claim Amount";
        }
      }, "ncpdp_sec_claims_ingred_cost"),
      servicetype: this.warp(async (data) => {
        if (isEmptyValue(data.servicetype)) return null;
        try {
          const servicetype = parseFloat(
            data.servicetype?.toString()?.trim() || "1",
          );
          return servicetype;
        } catch (error) {
          logger.error(`Error parsing servicetype: ${error.message}`);
          return "1";
        }
      }, "default_service_id"),
    };
    this.postTransformGerunds = this.populateGerunds.bind(this);
    this.addPostTransform(this.postTransformGerunds);
  }

  async transform(data) {
    logger.debug(`Transforming insurance: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    return result;
  }
  async autoTransfer(data) {
    if (isEmptyValue(data.autotransfer)) return null;

    const auto_transfer =
      mapData(data.autotransfer, "ynmap") === true ? "Yes" : null;
    const auto_transfer_margin = data.transferthreshold
      ? data.transferthreshold
      : null;
    const update = {};
    if (auto_transfer) update.auto_transfer_write_off = auto_transfer;
    if (auto_transfer_margin)
      update.auto_transfer_margin = auto_transfer_margin;
    return update;
  }
  async splitByAuth(data) {
    if (isEmptyValue(data.splitbyauth)) return null;
    return mapData(data.splitbyauth, "ynmap") === true ? "Yes" : null;
  }

  async id_(data) {
    const short_code = data.id_?.trim();
    return short_code;
  }
  async billingMethodId(data) {
    const instype = data.invoicetyp;
    if (isEmptyValue(instype)) return null;
    const methodMap = {
      1: "mm",
      E: "mm",
      M: "ncpdp",
      G: "generic",
      F: "cms_1500",
    };
    const method = methodMap[instype] || null;
    const update = {};
    update.software_vendor_id = "D012500028"; // This is our CHC caremark vendor id
    update.billing_method_id = method;
    if (method && method == "mm" && !isEmptyValue(data.neicpid)) {
      update.mm_payer_id = data.neicpid;
      const chcorg = await this.db.dst.lookupSourceValue(
        "form_list_chc_payer",
        "imn_payer_id",
        data.neicpid,
        "payer_name",
      );
      update.mm_payer_name = chcorg;
    }
    return update;
  }

  async payerTypeId(data) {
    if (isEmptyValue(data.typecode) && isEmptyValue(data.payor)) return null;
    const typecode = data.typecode?.trim();
    const payor = data.payor?.trim();
    const typeMap = {
      MB: {
        MEDICARE: "MCRB",
      },
      C1: {
        COMMERCIAL: "CMMED",
        MGDMEDICARE: "MCRD",
        "MEDICARE SUPP": "CMMED",
        "CoPay Assist": "COPAY",
        PRIVATE: "CMMED",
        OTHER: "OTHER",
        MGDMEDICAID: "MEDI",
      },
      MC: {
        Pharmacy: "CMPBM",
        MGDMEDICARE: "MCRD",
        MEDICAID: "MEDI",
        MGDMEDICAID: "MEDI",
      },
      OT: {
        COMMERCIAL: "CMMED",
      },
      "": {
        "WORKERS COMP": "OTHER",
        COMMERCIAL: "CMMED",
        "MEDICARE SUPP": "CMMED",
        "MED SUPPLEMENT": "CMMED",
        MEDICARE: "MCRB",
        MEDICAID: "MEDI",
        "MEDICARE PART D": "MCRD",
        "MED ADV PART D": "MCRD",
        Pharmacy: "CMPBM",
        PRIVATE: "CMMED",
        MGDMEDICARE: "MCRD",
        MGDMEDICAID: "MEDI",
        "COPAY ASSIST": "COPAY",
        "MFG COPAY": "COPAY",
        "MFG PROGRAM": "PAP",
        HARDSHIP: "HARD",
        "MED PART C": "MCRC",
        FOUNDATION: "FOUND",
        NURSING: "OTHER",
        REBATE: "COPAY",
        SELF: "SELF",
        "": "OTHER",
      },
    };
    let mapped = typeMap[typecode]?.[payor] || typeMap[""][payor];

    if (isEmptyValue(mapped)) {
      logger.warn(
        `No mapping found for typecode: ${typecode}, payor: ${payor}`,
      );
      mapped = "OTHER";
    }

    let ptype = await this.db.dst.lookupSourceValue(
      "form_list_payer_type",
      "code",
      mapped,
      "code",
    );

    if (isEmptyValue(ptype)) {
      logger.warn(`No payer type found for mapped value: ${mapped}`);
      ptype = "OTHER";
    }

    return ptype;
  }

  async authorizationSetting(data) {
    const authMap = {
      always: "Always",
      never: "Never",
      byorder: "By Order",
      byhcpc: "By Hcpc",
    };
    return authMap[data.authrequirement?.toLowerCase()] || null;
  }

  async cmnRequired(data) {
    return data.cmnrequired === "t" ? "Yes" : null;
  }

  async collectorTeamId(data) {
    if (isEmptyValue(data.collector)) return null;
    return this.db.dst.lookupSourceValue(
      "form_wf_queue_team",
      "code",
      "Collector",
      "code",
    );
  }

  async populateCms2(data) {
    logger.debug("populateCms2 called with data:", JSON.stringify(data));
    if (!data) {
      logger.warn("populateCms2 called with undefined data");
      return { cms_2: null };
    }

    const cms2Options = [];

    if (mapData(data.usendc, "ynmap") === true) {
      cms2Options.push("NDC");
    }
    if (mapData(data.usendcqual, "ynmap") === true) {
      cms2Options.push("NDC Qualifier");
    }
    if (mapData(data.sendndcunitqual, "ynmap") === true) {
      cms2Options.push("NDC Unit Qual/Units");
    }
    if (mapData(data.useunitprice, "ynmap") === true) {
      cms2Options.push("NDC Unit Price");
    }

    if (cms2Options.length > 0) {
      const cms_2 = await format_checkbox(cms2Options);
      logger.debug(`cms_2: ${JSON.stringify({ cms_2 })}`);
      return { cms_2 };
    }

    logger.debug("No cms_2 options selected");
    return { cms_2: null };
  }

  async populateGerunds(transformedData, sourceData, upsertResult) {
    const { id: insuranceId } = upsertResult;
    if (isEmptyValue(insuranceId)) {
      logger.warn("No insurance ID available for gerund relationship");
      return;
    }

    const siteId = getMapValue("SiteMap", sourceData.siteno, 2);
    logger.debug(
      `Creating gerund relationship between insurance ID ${insuranceId} and site ID ${siteId}`,
    );
    await this.db.dst.insert_gerund(
      "form_payer_inn_site_id_to_site_id",
      insuranceId, // form_payer_fk
      siteId, // form_site_fk
    );
  }
}

module.exports = InsuranceTransformer;
