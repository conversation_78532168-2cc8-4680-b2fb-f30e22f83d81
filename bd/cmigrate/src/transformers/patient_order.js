const { BaseTransformer, TransformPreCheckError } = require("./base");
const Logger = require("../utils/logger");
const logger = new Logger("PatientOrderTransformer");
const { isEmptyValue, toDate, format_checkbox } = require("../utils/tools");
const _head = require("lodash/head");
const moment = require("moment");
const { getMapValue } = require("../utils/maps");
const fs = require("fs");
const path = require("path");

class PatientOrderTransformer extends BaseTransformer {
  constructor(sourceConnector, destinationConnector, isDryRun) {
    super(sourceConnector, destinationConnector, isDryRun);
    this.sourceTable = "cpr_ot";
    this.destTable = "form_careplan_order";
    this.qualifiers = [
      {
        field: "patient_id",
        source: "mrn",
        lookup: {
          table: "form_patient",
          sourceField: "mrn",
          destinationField: "external_id",
          returnField: "id",
        },
      },
      { field: "external_id", source: "no" },
    ];
    this.status_map = {
      active: "Active",
      pending: "Pending",
      "dc'd": "Discontinued",
    };
    this.sourceRawQuery = fs.readFileSync(
      path.join(__dirname, "/fixtures/patient_order.sql"),
      "utf8",
    );
    if (sourceConnector.additionalFilters?.length > 0) {
      this.addFiltersToRawQuery(sourceConnector.additionalFilters);
    }
    this.field_map = {
      no: "external_id",
      mrn: this.warp(this.patient_lookup, "patient_id"),
      start: this.mapField("start", "start_date", toDate),
      stop_date: async (data) => {
        return await toDate(data.stop || data.end_rx);
      },
      ordered: this.mapField("ordered", "written_date", toDate),
      nextcomp: this.mapField("nextcomp", "next_fill_date", toDate),
      end_rx: this.mapField("end_rx", "expiration_date", toDate),
      descrip: "summary",
      //      days: "day_supply",
      rxorigin: "rx_origin_id",
      dxcode: this.warp(this.dx_id, "dx_ids"),
      refillnum: async (data) => {
        const updated_data = {};
        if (isEmptyValue(data.refillnum) || data.refillnum === "0") {
          updated_data.next_fill_number = 1;
        }
        if (data.refillnum > 1) {
          updated_data.next_fill_number = parseInt(data.refillnum) + 1;
        }
        return updated_data;
      },
      siteno: this.warp(this.lookupSiteId, "site_id"),
      createdon: this.mapField(this.recdate, "date_received"),
      dose: this.warp(this.dose_lookup, "dose_id"),
      rxtype: this.warp(this.rx_type, "therapy_id"),
      perdiemno: this.warp(this.perdiem_lookup, "supply_billable_id"),
      supply_kit_id: this.warp(this.supply_kit_lookup, "supply_kit_id"),
    };
    const postTransformGerunds = this.postTransformGerunds.bind(this);
    this.addPostTransform(postTransformGerunds);
    const post_transform_order_items =
      this.post_transform_order_items.bind(this);
    this.addPostTransform(post_transform_order_items);
    const post_transform_supply_kit = this.post_transform_supply_kit.bind(this);
    this.addPostTransform(post_transform_supply_kit);
    const post_transform_rentals = this.post_transform_rentals.bind(this);
    this.addPostTransform(post_transform_rentals);
    this.insurance_map = {};
  }

  async transform(data) {
    logger.debug(`Transforming patient order: ${JSON.stringify(data)}`);
    const result = await super.transform(data);
    if (result.operation === "error") {
      return result;
    }
    // update insurance map
    await this.otinsno(data);
    return result;
  }
  async supply_kit_lookup(data) {
    if (isEmptyValue(data.supply_kit_id)) return null;
    const kit = await this.db.dst.fetchData("form_inventory_supply_kit", {
      filters: [["external_id", "=", data.supply_kit_id]],
      limit: 1,
    });
    return _head(kit)?.id;
  }
  async perdiem_lookup(data) {
    if (isEmptyValue(data.perdiemno)) return null;
    const perdiem = await this.db.dst.fetchData("form_inventory", {
      filters: [["external_id", "=", data.perdiemno]],
      limit: 1,
    });
    const inv = _head(perdiem);
    const updated_data = {};
    updated_data.supply_billable_id = inv?.id;
    updated_data.bill_supplies = "Yes";
    return updated_data;
  }
  async rx_type(data, greg = false) {
    const rxtype = data.rxtype?.toLowerCase().trim();
    const therapy_id = getMapValue("TherapyTypeMap", rxtype);
    if (greg) {
      return therapy_id || null;
    } else {
      return therapy_id ? `{${therapy_id}}` : null;
    }
  }
  async getMedicalPayers(patient_id) {
    const payers = await this.db.dst.fetchData("form_patient_insurance", {
      filters: [
        ["patient_id", "=", patient_id],
        ["active", "=", "Yes"],
        ["billing_method_id", "in", ["mm", "cms1500"]],
      ],
      order: [["rank", "asc"]],
    });

    // Transform payers similar to dx_codes with rank handling
    return payers.map((p, index) => ({
      type_id: p.billing_method_id,
      rank: index + 1, // Assign sequential ranks starting from 1
      payer_id: p.id,
      id: p.id,
      active: p.active,
      assistantance_status: p.assistantance_status,
      bill_for_denial: p.bill_for_denial,
    }));
  }

  async getPharmacyPayers(patient_id) {
    const payers = await this.db.dst.fetchData("form_patient_insurance", {
      filters: [
        ["patient_id", "=", patient_id],
        ["active", "=", "Yes"],
        ["billing_method_id", "=", "ncpdp"],
      ],
      order: [["rank", "asc"]],
    });

    // Transform payers similar to dx_codes with rank handling
    return payers.map((p, index) => ({
      type_id: p.billing_method_id,
      rank: index + 1, // Assign sequential ranks starting from 1
      payer_id: p.id,
      id: p.id,
      active: p.active,
      assistantance_status: p.assistantance_status,
      bill_for_denial: p.bill_for_denial,
    }));
  }
  async dx_id(data) {
    if (!data.dx_codes || !data.dx_codes.length) {
      logger.warn(`No dx codes found for order: ${data.no}`);
      return null;
    }

    const results = [];

    for (let i = 0; i < data.dx_codes.length; i++) {
      let new_rank;
      let dxcode = data.dx_codes[i];
      dxcode = dxcode.trim().replace(/[^A-Za-z0-9]/g, "");
      if (!dxcode) continue;

      try {
        // First check if diagnosis exists in form_list_diagnosis
        const diagnosis = await this.db.dst.fetchData("form_list_diagnosis", {
          filters: [["code", "=", dxcode]],
          limit: 1,
        });

        if (!diagnosis.length) {
          logger.warn(`Diagnosis not found for code: ${dxcode}`);
          continue;
        }

        const patient_data = await this.patient_lookup(data);
        const patient_id = patient_data.patient_id;
        // just get the rank from a query
        const rank_query = await this.db.src.raw_query(
          `select icdp.cpk_icdpatient, icdp.rank, icdp.dateinactivated from cpr_icdpatient icdp join cpr_icdmasterlist icd on icdp.cfk_icdmasterlist_icd10 = icd.cpk_icdmasterlist 
          where icdp.cfk_hr = ${data.mrn} and icd.code = '${dxcode}' and icdp.delflag = 0`,
        );
        const rank_data = _head(rank_query);
        logger.debug(`Rank data: ${JSON.stringify(rank_data)}`);
        let dx_active = "Yes";
        new_rank = rank_data?.rank;
        if (rank_data?.dateinactivated) {
          dx_active = "No";
        }
        // Then fetch or create the patient diagnosis record
        const patientDiagnosis = await this.db.dst.upsertData(
          "form_patient_diagnosis",
          {
            patient_id: patient_id,
            dx_id: dxcode,
            external_id: rank_data?.cpk_icdpatient || data.no,
            rank: new_rank,
            active: dx_active,
            diagnosis_start_date: data.start,
            diagnosis_stop_date: data.stop || null,
            careplan_id: data.careplan_id,
          },
          {
            qualifiers: [
              { field: "patient_id", value: patient_id },
              { field: "dx_id", value: dxcode },
            ],
          },
        );
        logger.debug(
          `Patient diagnosis created/found: ${JSON.stringify(patientDiagnosis)}`,
        );

        results.push({
          dx_id: dxcode,
          id: patientDiagnosis.id,
          rank: new_rank,
          active: dx_active,
        });
      } catch (error) {
        logger.error(
          `Error processing diagnosis code ${dxcode}: ${error.message}`,
        );
        continue;
      }
    }

    return results.length > 0 ? results : null;
  }
  async otinsno(data) {
    const insurance = await this.db.src.raw_query(
      `select cpk_patins from cpr_patins pi join cpr_inscomp i on i.no = pi.insno 
      where pi.insno='${data.insno}' and mrn = '${data.mrn}'`,
    );
    const insid = insurance[0]?.cpk_patins;
    const insurance_data = await this.db.dst.fetchData(
      "form_patient_insurance",
      {
        filters: [["external_id", "=", insid]],
        limit: 1,
      },
    );
    const patins = _head(insurance_data);
    const otype = data.primaryflg === "P" ? "Primary" : "Ancillary";
    this.insurance_map[otype] = patins;
    return patins?.id;
  }

  async dose_lookup(data) {
    const dallowed = data.dosesallow || 0;
    const dleft = data.dosesleft || 0;
    const updated_data = {};
    const dspent = dallowed - dleft;
    updated_data.primary_doses_dispensed = dspent;
    updated_data.primary_doses_remaining = dleft;

    return updated_data;
  }
  async precheck(data) {
    if (isEmptyValue(data.mrn)) {
      throw new TransformPreCheckError("Patient ID is empty");
    }
    const status = data.status?.trim()?.toLowerCase();
    if (!["active", "pending"].includes(status)) {
      throw new TransformPreCheckError(
        `Invalid status for order: ${data.status}`,
      );
    }
  }

  async recdate(data) {
    const updated_data = {};
    const dt = moment(data.createdon);
    updated_data.date_received = dt.format("YYYY-MM-DD");
    updated_data.time_received = dt.format("HH:mm:ss");
    // set other defaults since we know createdon is not null
    updated_data.branded_unbranded = "Branded";
    return updated_data;
  }
  async lookupSiteId(data) {
    const siteno = getMapValue("SiteMap", data.siteno, 2);
    return siteno;
  }
  async patient_lookup(data) {
    const patient = await this.db.dst.fetchData("form_patient", {
      filters: [["external_id", "=", data.mrn]],
      limit: 1,
    });
    const patient_data = _head(patient);
    const updated_data = {};
    updated_data.status_id = "1";
    updated_data.fltr_status = format_checkbox("1");
    updated_data.intake_substatus_id = "H";
    if (patient_data) {
      updated_data.patient_id = patient_data.id;
      const careplan = await this.db.dst.fetchData("form_careplan", {
        filters: [
          ["patient_id", "=", patient_data.id],
          ["status_id", "=", "3"],
        ],
        order: [["created_on", "desc"]],
        limit: 1,
      });
      const careplan_data = _head(careplan);
      if (careplan_data) {
        updated_data.careplan_id = careplan_data.id;
      }
      updated_data.physician_id = patient_data.referrer_id;
      updated_data.prescriber_id = await this.prescriber_lookup(
        data,
        updated_data,
      );
      updated_data.therapy_id = await this.rx_type(data);

      // Store the raw arrays directly without JSON.stringify
      const medicalPayers = await this.getMedicalPayers(patient_data.id);
      const pharmacyPayers = await this.getPharmacyPayers(patient_data.id);

      // Assign directly without any transformation
      updated_data.medical_payer_ids = medicalPayers;
      updated_data.pharmacy_payer_ids = pharmacyPayers;

      return updated_data;
    } else {
      throw new TransformPreCheckError(
        `Patient not found for MRN: ${data.mrn}`,
      );
    }
  }

  async prescriber_lookup(data, updated_data) {
    if (isEmptyValue(data.ph_no)) return null;
    const physician = await this.db.dst.fetchData("form_physician", {
      filters: [["external_id", "=", data.ph_no]],
      limit: 1,
    });
    const physician_data = _head(physician);
    if (isEmptyValue(physician_data?.id)) return null;
    const prescriber = await this.db.dst.fetchData("form_patient_prescriber", {
      filters: [
        ["physician_id", "=", physician_data.id],
        ["patient_id", "=", updated_data.patient_id],
      ],
      limit: 1,
    });
    return _head(prescriber)?.id;
  }

  async postTransformGerunds(transformedData, sourceData, upsertResult) {
    const { id: mainOrderId } = upsertResult;
    const therapy_id = transformedData.therapy_id?.replace(/[{}]/g, "") || null;

    if (therapy_id) {
      await this.db.dst.insert_gerund(
        "form_careplan_order_therapy_id_to_list_therapy_id",
        mainOrderId,
        therapy_id,
      );
    }
  }
  async post_transform_supply_kit(transformedData, sourceData, upsertResult) {
    const { id: mainOrderId } = upsertResult;
    if (isEmptyValue(sourceData.no)) {
      return;
    }

    const supply_kit_data = await this.db.src.raw_query(
      `select 
        t.uomtext,
        SUM(t.defquant) as defquant,
        SUM(t.rawquant) as rawquant,
        t.name_,
        t.mrn,
        MAX(t.knum) as knum,
        MAX(t.cpk_tickets) as cpk_tickets,
        i.no as inventory_id,
        MAX(t.tobill) as tobill
       from cpr_tickets t 
       join cpr_parts i on i.no = t.no 
       where t.otno = ${sourceData.no}
       and t.delflag != 1 
       and i.delflag != 1 
       and i.parttype = 'S' 
       and t.defquant > 0 
       group by t.uomtext, t.name_, t.mrn, i.no
       order by t.name_`,
    );

    for (const item of supply_kit_data) {
      const supply_external_id = `${item.cpk_tickets}`;
      const item_inventory_id = await this.db.dst.fetchData("form_inventory", {
        filters: [["external_id", "=", item.inventory_id]],
        limit: 1,
      });
      const inventory = _head(item_inventory_id);
      const inventory_id = inventory?.id;
      const quantity_per_package = inventory?.quantity_per_package;
      if (!inventory_id) {
        logger.warn(
          `Inventory ID not found for supply item: ${item.inventory_id}`,
        );
        continue;
      }
      const supplyOrderData = {
        external_id: supply_external_id,
        patient_id: transformedData.patient_id,
        careplan_id: transformedData.careplan_id,
        inventory_id: inventory_id,
        quantity_to_pull: Math.round(parseFloat(item.defquant)),
        dispense_quantity: Math.round(parseFloat(item.defquant)),
        dispense_unit_id:
          item.uomtext.toLowerCase() === "bx" ? "package" : "each",
        quantity_per_package: quantity_per_package,
        part_of_kit: item.knum !== 0 ? "Yes" : null,
        supply_billable: item.tobill === "X" ? "Yes" : null,
      };

      const supplyOrderResult = await this.db.dst.upsertData(
        "form_careplan_order_supply",
        supplyOrderData,
        {
          qualifiers: [
            { field: "external_id", value: supply_external_id },
            { field: "patient_id", value: transformedData.patient_id },
          ],
        },
      );

      if (supplyOrderResult?.id) {
        await this.db.dst.insert_subform(
          "form_careplan_order_to_careplan_order_supply",
          mainOrderId,
          supplyOrderResult.id,
        );
      }
    }
  }
  async post_transform_order_items(transformedData, sourceData, upsertResult) {
    const { id: mainOrderId } = upsertResult;
    const items = sourceData.subform_order_items || [];
    logger.warn(`Insurance map: ${JSON.stringify(this.insurance_map || {})}`);
    logger.debug(
      `Processing ${items.length} order items for order ${mainOrderId}`,
    );

    for (const item of items) {
      // Get drug info from FDB lookup
      const overridesArray = ["Dates", "Billing"];
      if (transformedData.dx_ids?.length > 0) {
        overridesArray.push("Diagnosis");
      }
      const drug_info = await this.db.dst.fetchData("form_list_fdb_ndc", {
        filters: [["ndc", "=", item.ndc]],
        limit: 1,
      });
      const drug_info_data = _head(drug_info);
      //update insurance map
      await this.otinsno(item);
      // Map the subform item fields according to careplan_order_item schema
      const itemData = {
        site_id: transformedData.site_id,
        patient_id: transformedData.patient_id,
        careplan_id: transformedData.careplan_id,
        rx_no: item.scriptext,
        start_date: await toDate(item.start),
        stop_date: await toDate(item.stop || item.end_rx),
        overrides: await format_checkbox(overridesArray),
        written_date: await toDate(item.ordered),
        expiration_date: await toDate(item.expdat),
        comments: item.comments,
        //        day_supply: item.days,

        dose: item.dose ? item.dose.replace(/[^\d.]/g, "") : null,
        frequency_id: item.freq,
        summary: item.descrip,
        type_id:
          item.primaryflg === "P"
            ? "Primary"
            : item.freq?.toLowerCase().includes("pre-")
              ? "Premedication"
              : item.freq?.toLowerCase().includes("post-")
                ? "Postmedication"
                : "Ancillary",
        daw_code: item.daw,
        external_id: item.no,
        sig: item.line9,
        admin_directions: [
          item.directns,
          item.line11,
          item.line12,
          item.line13,
          item.line14,
        ]
          .filter(Boolean)
          .join(" "),
        medical_payer_ids: !isEmptyValue(transformedData.medical_payer_ids)
          ? Array.isArray(transformedData.medical_payer_ids)
            ? transformedData.medical_payer_ids
            : JSON.parse(transformedData.medical_payer_ids)
          : [],
        pharmacy_payer_ids: !isEmptyValue(transformedData.pharmacy_payer_ids)
          ? Array.isArray(transformedData.pharmacy_payer_ids)
            ? transformedData.pharmacy_payer_ids
            : JSON.parse(transformedData.pharmacy_payer_ids)
          : [],
        dx_ids: !isEmptyValue(transformedData.dx_ids)
          ? Array.isArray(transformedData.dx_ids) &&
            transformedData.dx_ids.length > 0
            ? transformedData.dx_ids
            : JSON.parse(transformedData.dx_ids)
          : [],
        subform_ingred: drug_info_data?.cmp_ingredients
          ? JSON.stringify(
              drug_info_data.cmp_ingredients.map((ing) => ({
                inventory_id: ing.inventory_id,
                percent: ing.percent,
              })),
            )
          : null,

        label_quantity: item.bagsdisp,
        // Doses
        doses_allowed: item.dosesallow,
        doses_to_prep: item.dosesprep,
        max_doses_per_fill: item.dosesprep,
        doses_per_container: item.contdoses,
        doses_remaining: item.dosesleft,
        containers_to_prep: item.bagsdisp,
        type_filter:
          item.parttype === "D"
            ? "Drug"
            : item.parttype === "S"
              ? "Supply"
              : item.parttype === "O"
                ? "Billable"
                : item.parttype === "R"
                  ? "Equipment Rental"
                  : item.parttype === "B"
                    ? "Billable"
                    : null,
        //ndc: item.ndc,
        refills: item.refills,
        template_type:
          item.rxtype?.toLowerCase() === "factor" ? "Factor" : "Default",
        rx_template_id:
          item.rxtype?.toLowerCase() === "factor" ? "Factor" : "Default",
        prescription_provided_in:
          item.rxtype?.toLowerCase() === "factor" ? "Variance" : null,
        allowed_variance:
          item.rxtype?.toLowerCase() === "factor" ? item.factorvariance : null,
        frequency_type:
          item.rxtype?.toLowerCase() === "factor" ? "Other" : null,
        therapy_id: await this.rx_type(item, true),
        refill_tracking: "Refills",
        rx_origin_id: item.rxorigin ? item.rxorigin : "0",
        next_fill_number:
          isEmptyValue(item.refillnum) || item.refillnum === "0"
            ? 1
            : parseInt(item.refillnum) > 0
              ? parseInt(item.refillnum) + 1
              : 1,
        next_fill_date: await toDate(item.nextcomp),
        // Add infusion fields
        infuse_for: item.infusefor > 0 ? item.infusefor : null,
        infuse_time: item.infusefor > 0 ? "minutes" : null,
        infuse_length:
          item.cont_or_int === "C"
            ? "continuously"
            : item.cont_or_int === "I"
              ? "intermittently"
              : null,
      };
      if (!isEmptyValue(item.po_sig)) {
        if (!isEmptyValue(itemData.comments)) {
          itemData.comments = `${itemData.comments} \n ${item.po_sig}`;
        } else {
          itemData.comments = item.po_sig;
        }
      }

      // set the billing method:
      const billingMethodId =
        this.insurance_map.Primary?.billing_method_id ||
        this.insurance_map.Ancillary?.billing_method_id ||
        null;

      itemData.billing_method =
        billingMethodId === "ncpdp"
          ? "Pharmacy"
          : billingMethodId === "mm"
            ? "Medical"
            : null;

      itemData.override_billing_method =
        this.insurance_map.Primary?.billing_method_id === "ncpdp"
          ? "Pharmacy"
          : this.insurance_map.Primary?.billing_method_id === "mm"
            ? "Medical"
            : null;
      itemData.item_name = item.descrip;
      // Get inventory ID if available
      const inventoryResult = await this.db.dst.fetchData("form_inventory", {
        filters: [["external_id", "=", item.partno]],
        limit: 1,
      });
      let inventoryData = _head(inventoryResult);
      if (isEmptyValue(inventoryData)) {
        logger.warn(`No inventory data found for NDC: ${item.ndc}`);
        // try with fdb_id
        const fdbResult = await this.db.dst.fetchData("form_inventory", {
          filters: [["fdb_id", "=", item.ndc?.trim()]],
          limit: 1,
        });
        inventoryData = _head(fdbResult);
      }
      if (inventoryData) {
        // Set units directly from inventory
        itemData.dispense_unit_id = inventoryData.dispense_unit_id;
        itemData.dose_unit_id = inventoryData.default_dosing_unit_id;

        // Basic inventory fields
        itemData.inventory_id = inventoryData.id;
        itemData.item_name = item.descrip;

        // Route and storage
        itemData.route_id = inventoryData.route_id;
        itemData.storage_id = inventoryData.storage_id;

        // Add route-specific directions and cautions
        if (["IV", "SQ", "IVP"].includes(inventoryData.route_id)) {
          // IV directions
          itemData.iv_cautions = [item.line5, item.line6]
            .filter(Boolean)
            .join(" ");
          itemData.iv_directions = [
            item.line7,
            item.line8,
            item.line9,
            item.line11,
            item.line12,
            item.line13,
            item.line14,
          ]
            .filter(Boolean)
            .join(" ");
          // Add nursing requirements
          itemData.requires_nursing = "Yes";
          itemData.nursing_instructions = itemData.admin_directions;
        } else {
          // PO directions
          itemData.po_cautions = [item.line5, item.line6]
            .filter(Boolean)
            .join(" ");
          itemData.po_directions = [
            item.line7,
            item.line8,
            item.line9,
            item.line11,
            item.line12,
            item.line13,
            item.line14,
          ]
            .filter(Boolean)
            .join(" ");
        }

        // Drug identifiers
        //        itemData.brand_name_id = inventoryData.brand_name_id;
        itemData.medid = inventoryData.medid;
        itemData.dea_schedule_id = inventoryData.dea_schedule_id;

        // Labels and warnings
        itemData.label_header = item.line1;

        // Type and nursing requirements
        itemData.type = inventoryData.type;
        itemData.inv_requires_nursing = inventoryData.requires_nursing;

        // Compound information
        itemData.compound_type = inventoryData.compound_type;
        if (inventoryData.cmp_ingredients) {
          itemData.subform_ingred = JSON.stringify(
            inventoryData.cmp_ingredients.map((ing) => ({
              inventory_id: ing.inventory_id,
              percent: ing.percent,
            })),
          );
        }
      }
      if (item.status === "Active") {
        itemData.prescription_complete = "Yes";
      }

      logger.debug(`Item data: ${JSON.stringify(itemData, null, 2)}`);
      logger.debug(`Inventory data: ${JSON.stringify(inventoryData || {})}`);
      // return for now

      // Upsert the order item
      const itemResult = await this.db.dst.upsertData(
        "form_careplan_order_item",
        itemData,
        {
          qualifiers: [
            { field: "patient_id", value: transformedData.patient_id },
            { field: "external_id", value: item.no },
          ],
          jsonFields: [
            "medical_payer_ids",
            "pharmacy_payer_ids",
            "dx_ids",
            "subform_ingred",
          ],
        },
      );

      if (itemResult?.id) {
        logger.debug(
          `Created order item ${itemResult.id} for order ${mainOrderId}`,
        );

        // Link the item to the main order
        await this.db.dst.insert_subform(
          "form_careplan_order_to_careplan_order_item",
          mainOrderId, // form_careplan_order_fk
          itemResult.id, // form_careplan_order_item_fk
        );
        // link the dose_unit_filter_id
        if (itemData.dose_unit_id) {
          const result = await this.db.dst.insert_gerund(
            "form_careplan_order_item_dose_unit_filter_id_to_list_unit_id",
            itemResult.id, // form_careplan_order_item_fk
            itemData.dose_unit_id, // form_list_unit_fk
          );
          if (result) {
            logger.info(
              `Gerund relationship result: ${JSON.stringify(result)}`,
            );
          }
        }
      }
    }
  }
  async post_transform_rentals(transformedData, sourceData, upsertResult) {
    logger.debug(`Transforming rentals for order ${upsertResult?.id}`);
    const orderId = upsertResult?.id;
    if (!orderId) {
      logger.debug(`No order ID found for rentals`);
      return;
    }
    const rentals = await this.db.dst.fetchData("form_inventory_rental_log", {
      filters: [["patient_id", "=", transformedData.patient_id]],
    });
    logger.debug(`Rentals: ${JSON.stringify(rentals, null, 2)}`);
    if (rentals.length === 0) {
      logger.debug(
        `No Rentals found for patient ${transformedData.patient_id}`,
      );
      return;
    }
    for (const rental of rentals) {
      const invResult = await this.db.dst.fetchData("form_inventory", {
        filters: [["id", "=", rental.inventory_id]],
        limit: 1,
      });
      const invData = _head(invResult);
      logger.debug(`Linking rental ${rental.id} to order ${orderId}`);
      const rentalData = {
        careplan_id: transformedData?.careplan_id,
        patient_id: transformedData?.patient_id,
        last_dispense_date: await toDate(rental.checked_out_date),
        serial_no: rental.serial_no,
        last_billed: await toDate(rental.last_pm_date),
        inventory_id: rental.inventory_id,
        rental_type: rental.status === "SLD" ? "Purchase" : "Rental",
        max_rental_claims:
          rental.status === "SLD"
            ? null // no limit
            : 0,
        billable_code_id: invData?.billable_code_id,
        frequency_code: 4, // 4 = Monthly
      };
      const rentalResult = await this.db.dst.upsertData(
        "form_careplan_order_rental",
        rentalData,
        {
          qualifiers: [
            { field: "patient_id", value: transformedData.patient_id },
            { field: "inventory_id", value: rental.inventory_id },
          ],
        },
      );
      logger.debug(`Rental result: ${JSON.stringify(rentalResult)}`);
      if (rentalResult?.id) {
        logger.debug(`Created rental ${rentalResult.id} for order ${orderId}`);
        // link the rental to the order
        await this.db.dst.insert_subform(
          "form_careplan_order_to_careplan_order_rental",
          orderId,
          rentalResult.id,
        );
      }
    }
  }
}

module.exports = PatientOrderTransformer;
