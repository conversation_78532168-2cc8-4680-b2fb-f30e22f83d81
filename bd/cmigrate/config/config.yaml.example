
source:
  pg_uri: 'postgres://localhost:5432/source_db'
  api_uri: 'https://api.source.com'
  api_user: 'source_user'
  api_password: 'source_password'

dest:
  pg_uri: 'postgres://localhost:5432/destination_db'
  api_uri: 'https://api.destination.com'
  api_user: 'destination_user'
  api_password: 'destination_password'

default_migrations:
  - 'user'
  - 'physician'
  - 'referral_source'
  - 'patient'

s3:
  bucket: 'd-crx-nes'
  access_key: 'your-access-key'
  secret_key: 'your-secret-key'
  # You will need to mount this directory or have the source files downloaded to this location
  # You can mount locally with sshfs like: sshfs herphb004.envoymobile.net:/mnt/cprplus /tmp/cprplus -oauto_cache,reconnect,defer_permissions
  local_path: '/tmp/cprplus/'
