app = "d-csp-nes"
kill_signal = "SIGINT"
kill_timeout = "5s"
primary_region = "lax"

[http_service]
auto_start_machines = true
auto_stop_machines = false
force_https = true
internal_port = 8_000
min_machines_running = 1
processes = [ "app" ]

  [[http_service.checks]]
  grace_period = "30s"
  interval = "15s"
  method = "get"
  path = "/login"
  protocol = "http"
  timeout = "4s"

  [http_service.concurrency]
  hard_limit = 1_500
  soft_limit = 1_000
  type = "requests"

[[vm]]
cpu_kind = "performance"
cpus = 4
memory = "8gb"
