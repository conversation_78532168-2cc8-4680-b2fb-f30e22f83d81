import type { Response, NextFunction } from "npm:express";
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import type { Context } from "../types/base.d.ts";

export const validate = (schema: AnyZodObject) => {
  z.setErrorMap(customErrorMap)
  return async (ctx: Context, res: Response, next: NextFunction) => {
    try {
      await schema.parseAsync({
        body: ctx.body,
        query: ctx.query,
        params: ctx.params
      })
      return next();
    } catch (error) {
      return res.status(400).json(error)
    }
  }
}

export const customErrorMap: z.ZodErrorMap = (issue, ctx) => {
  if (issue.code === z.ZodIssueCode.invalid_type) {
      if (issue.expected === "string") {
          return { message: ctx.defaultError }
      }
  }
  if (issue.code === z.ZodIssueCode.custom) {
      return { message: `less-than-${(issue.params || {}).minimum}` }
  }
  return { message: ctx.defaultError }
}
