FROM denoland/deno:ubuntu

# The port that your application listens to.
EXPOSE 4040 

WORKDIR /app

# Prefer not to run as root.
USER deno

# Ideally cache deps.ts will download and compile _all_ external files used in main.ts.
COPY deps.ts .
RUN deno cache deps.ts

# These steps will be re-run upon each file change in your working directory:
ADD . .
# Compile the main app so that it doesn't need to be compiled each startup/entry.
RUN deno cache index.ts

CMD ["run", "--allow-all", "index.ts"]
