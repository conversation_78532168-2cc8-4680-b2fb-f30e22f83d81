import type { Request } from "npm:express"
export interface Context extends Request {
    shared: Shared
    body: Object
}

export interface DBConnection {
    execute: Function
    exists: Function
    fetch: Function
}

export type DBBuildEvent = 'cloneDBEvent' | 'checkSnapshotStatus' | 'checkDbCreateStatus' | 'checkSnapshotStatus' | 'checkDbCreateStatus';

export interface AWS {
    createNewBaseDb: Function
    createDBFromSnapshot: Function
    createDBSnapshot: Function
    cloneDb: Function
}

export interface ExecuteOptions {
    dump: boolean,
}

export interface DBFilter {
    //field, cond, val
    where: Array<[string, string, any]>,
    limit?: number
}

export interface Logger {
    info: Function,
    debug: Function,
    warn: Function,
    error: Function
}

export interface Shared {
    db?: DBConnection,
    logger?: Logger,
    aws?: AWS
}

export interface Payload {
    DBInstanceIdentifier?: String;
    DBSnapshotIdentifier?: String;
}