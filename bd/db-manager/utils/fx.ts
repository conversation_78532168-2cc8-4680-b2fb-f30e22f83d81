import * as uuid from "https://deno.land/std@0.175.0/uuid/mod.ts";

const REQUIRED_CONFIGS = ['CLARA_AUTH_TOKEN', 'ADMIN_URL']
export const missingConfig = () => {
  let missing = []
  REQUIRED_CONFIGS.forEach((k) => {
    if (!Deno.env.get(k)) {
      missing.push(k)
    }
  })
  return missing
}

export const getType = (val: any): string => {
  if (val === null) {
    return 'null'
  }
  if (typeof val === 'boolean') {
    return 'boolean'
  }
  if (typeof val === 'string') {
    return 'string'
  }
  if (typeof val === 'number') {
    return 'number'
  }
  if ((typeof val === 'object') && (val instanceof Array)) {
    return 'array'
  }
  if ((typeof val === 'object') && !(val instanceof Array)) {
    return 'object'
  }
  return 'undefined'
}

export const uuid = (): String => {
  return crypto.randomUUID();
}
export const isFalsy = (val: any): boolean => {
  const tp: string = getType(val)
  let falsy = false
  if (['null', 'boolean', 'string', 'number', 'undefined'].includes(tp)) {
    falsy = !val
  } else {
    if (tp == 'array' && val.length == 0) {
      falsy = true
    } else if (tp == 'object' && Object.keys(val).length == 0) {
      falsy = true
    }
  }
  return falsy
}

export const equalIgnoreCase = (...args: Array<string>) => {
  return new Set(args.map(v => v.toLowerCase())).size == 1
}

export const sendRequest = async (method, server, path, body) => {
  let res = await fetch(`${Deno.env.get(server)}${path}`, {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "clara-api-key": Deno.env.get('CLARA_AUTH_TOKEN'),
    },
    body: JSON.stringify(body),
  })
  return res;
}

export const generatePassword = () => {
  let password = '';
  const possibleCharacters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  for (let i = 0; i < 16; i++) {
    password += possibleCharacters.charAt(Math.floor(Math.random() * possibleCharacters.length));
  }

  return password;
}