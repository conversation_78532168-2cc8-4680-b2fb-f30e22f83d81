import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import type { Response } from "npm:express"
import type { Context } from "../../types/base.js"
import { validate } from "../../middleware/validator.ts"
import { AWS } from "../../helper/aws-sdk.ts";
import { generatePassword } from "../../utils/fx.ts";

const getValidateSchema: AnyZodObject = z.object({
    body: z.object({
        event: z.enum(['cloneDB', 'createDBFromSnapshot','modifyDbInstance']) // 'createDB', 'createSnapshot'
    }),
})
export const post = [
    validate(getValidateSchema),
    async (ctx: Context, resp: Response) => {
        let { event } = ctx.body;
        let DBInstanceIdentifier = ctx.body.db_id;
        let DBSnapshotIdentifier = ctx.body.snap_id;
        if(ctx.body.customer_id == 7)
            return
        let aws = new AWS({body: ctx.body})
        /**
         * Auth using secret header token
         * get parameters from request coming in
         * eventType (createDB | createSnapshot | cloneDB | createFromSnapshot | createFromS3)
         */
        try {
            switch (event) {
                case 'cloneDB':
                    aws.cloneDb(DBInstanceIdentifier);
                    break;
                case 'createDBFromSnapshot':
                    aws.createDBFromSnapshot(DBSnapshotIdentifier,DBInstanceIdentifier);
                    break;
                case 'modifyDbInstance':
                    aws.modifyDbInstance(DBInstanceIdentifier,{ MasterUserPassword : generatePassword() });
                    break;
                case 'createSnapshot':
                    aws.createNewBaseDb(DBInstanceIdentifier);
                    break;
                default:
                    // send response eventtype not found 404
                    break;
            }
            resp.status(200).send({ status: 200, message: 'In Progress' });
        } catch (err) {
            console.error(err)
            ctx.shared.logger?.error(err)
            resp.status(500).send({ status: 500, message: 'Unexpected Error' });
        }

    }
]