import { Client } from "https://deno.land/x/postgres@v0.17.0/client.ts";
import { getType } from "../utils/fx.ts";
import { SQLError } from "../utils/exceptions.ts";
import { ExecuteOptions, DBFilter } from "../types/base.d.ts";

export class DB {
    static _instance: DB;
    static _pool: any;
    constructor() {
        if (!DB._instance) {
            DB._instance = this;
        }
        return DB._instance;
    }
    async init() {
        DB._pool = new Client({
            user: Deno.env.get('DB_USER'),
            database: Deno.env.get('DB_NAME'),
            hostname: Deno.env.get('DB_HOST'),
            port: Deno.env.get('DB_PORT'),
            password: Deno.env.get('DB_PASSWORD'),
            tls: {
                enforce: false,
                enabled: false,
            }
        })
        await DB._pool.connect();
        return this
    }

    async execute(q: string, params?: Array<String | Number>, opts?: ExecuteOptions) {
        if(!params){
            params = []
        }
        const result = await DB._pool.queryObject(q, params)
        if (opts?.dump) {
            return result
        }
        return result.rows
    }

    getSQLSafeValue(val: any, type: string): string {
        switch (type) {
            case 'null':
                return 'null';
            case 'boolean':
                return val
            case 'string':
                return `'${val}'`
            case 'number':
                return val
            case 'array':
                if (val.length && getType(val[0]) == 'string') {
                    return `('${val.join("', '")}')`
                }
                return `(${val.join(", ")})`
            default:
                throw new SQLError(`Unsupported sql data compare type ${type}`)
        }
    }

    isValidWhereClause(cond: any, type: string): boolean {
        if (type == 'array') {
            return ['in'].includes(cond)
        } else if (type == 'number') {
            return ['eq', 'ne', 'lte', 'gt', 'gte'].includes(cond)
        } else if (['boolean', 'null'].includes(type)) {
            return ['eq', 'ne', 'is', 'isnt'].includes(cond)
        } else if (type == 'string') {
            return ['eq', 'ne', 'like', 'ilike'].includes(cond)
        }
        return false
    }


    getSQLSafe(cond: string, val: any): string {
        const type: string = getType(val)
        if (!['array', 'string', 'boolean', 'number', 'null'].includes(type)) {
            throw new SQLError(`Unsupported data type in SQL where clause ${type}`)
        }
        const sv: string = this.getSQLSafeValue(val, type)
        if (!this.isValidWhereClause(cond, type)) {
            throw new SQLError(`In valid where clause, can not use datatype ${type} with condition ${cond}`)
        }
        /*
        Todo: code is venerable to sql injection, change direct value input to value placement by deno-postgres
        */
        switch (cond) {
            case 'eq':
                return ` = ${sv} `
            case 'ne':
                return ` != ${sv} `
            case 'lt':
                return ` < ${sv} `
            case 'lte':
                return ` <= ${sv} `
            case 'gt':
                return ` > ${sv} `
            case 'gte':
                return ` >= ${sv} `
            case 'in':
                return ` in ${sv} `
            case 'is':
                return ` is ${sv} `
            case 'isnt':
                return ` is not ${sv} `
            case 'like':
                return ` like ${sv} `
            case 'ilike':
                return ` ilike ${sv} `
            default:
                throw new SQLError(`Unsupported SQL condition ${cond[0]}`)
        }
    }

    async exists(table: string, flt: DBFilter) {
        let r = await this.fetch(table, flt)
        return r.length != 0
    }

    async fetch(table: string, flt: DBFilter, mutate?: Function) {
        let q = `select * from ${table} where 1=1 `
        if (flt.where && flt.where.length > 0) {
            flt.where.forEach((wl) => {
                if (!wl[0]) {
                    throw new SQLError(`Falsy field name in where clause.`)
                }
                q = q + ` and ${wl[0]} ${this.getSQLSafe(wl[1], wl[2])} `
            })
        }
        if (flt.limit) {
            q = q + ` limit ${flt.limit} `
        }
        let r = await this.execute(q)
        if (mutate) {
            r = mutate(r)
        }
        if (flt.limit == 1 && r.length) {
            r = r[0]
        }
        return r
    }

    static getInstance(): DB {
        return this._instance;
    }

}