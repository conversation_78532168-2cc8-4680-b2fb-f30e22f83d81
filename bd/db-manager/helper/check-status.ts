import { RDSClient, DescribePendingMaintenanceActionsCommand, DescribeDBInstancesCommand, DescribeDBSnapshotsCommand } from "npm:@aws-sdk/client-rds"
var REGION = 'us-east-1';
let intervalId;

let funcs = {checkDbCreateStatus, checkSnapshotStatus, checkPendingModifyStatus};
async function checkSnapshotStatus() {
    console.log("Getting DB Snapshot Status" + new Date())
    const { DBSnapshotIdentifier } = arguments[0];
    let describeSnapshot = new DescribeDBSnapshotsCommand({ DBSnapshotIdentifier: DBSnapshotIdentifier });
    let describeResp = await client.send(describeSnapshot);
    // if(describeResp.DBSnapshots[0].Status == 'creating')
    //     postMessage({ snapshotId: DBSnapshotIdentifier, status: 'Creating Snapshot' });
    if(describeResp.DBSnapshots[0].Status == 'available'){
        postMessage({ snapshotId: DBSnapshotIdentifier, status: 'available' });
        clearInterval(intervalId);
        intervalId = null;
        self.close();
    }
}
async function checkDbCreateStatus() {
    console.log("Getting DB Status" + new Date())
    const {DBInstanceIdentifier} = arguments[0];
    let describeDb = new DescribeDBInstancesCommand({DBInstanceIdentifier:DBInstanceIdentifier});
    try {
        let describeResp = await client.send(describeDb);
        if(describeResp.DBInstances?.length > 0 && describeResp.DBInstances.length == 1){
            if(describeResp.DBInstances[0].DBInstanceStatus == 'available'){
                postMessage({ dbId: DBInstanceIdentifier, status: 'available', connectionString: describeResp.DBInstances[0].Endpoint.Address});
                clearInterval(intervalId);
                intervalId = null;
                self.close();
            }
        }
    } catch (error) {
        if(error.Code != 'DBInstanceNotFound')
            console.log(error.Code)
    }
    // let respsonse = describeDb.DBInstances.map((o)=> {return {'identifier' : o.DBInstanceIdentifier, 'status':o.DBInstanceStatus, "endpoint":o.Endpoint}})
}

async function checkPendingModifyStatus(){
    const {DBInstanceIdentifier} = arguments[0];
    let describeDb = new DescribePendingMaintenanceActionsCommand({})
    let describeResp = await client.send(describeDb);
    console.log(describeResp)
}
self.onmessage = ( async (e) => {
    let { payload, event } = e.data;
    let eventHandler;
    if(event == 'cloneDBEvent'){
        eventHandler = 'checkSnapshotStatus';
    }else if(event == 'modifyDbStatus' || event == 'createDBFromSnapshot'){
        eventHandler = 'checkDbCreateStatus'
    }else{
        eventHandler = event;
    }
    intervalId = setInterval(funcs[eventHandler], 3000, payload);
    // self.close();
}).bind(funcs);

const client = new RDSClient({ 
    region: REGION,
 });
