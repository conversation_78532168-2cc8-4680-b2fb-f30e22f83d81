import { RDSClient, CreateDBInstanceCommand, ModifyDBInstanceCommand, CreateDBSnapshotCommand, RestoreDBInstanceFromDBSnapshotCommand } from "npm:@aws-sdk/client-rds"
import { Payload, DBBuildEvent } from "../types/base.d.ts";
import { cryptoRandomString } from "https://deno.land/x/crypto_random_string@1.0.0/mod.ts"
import { sendRequest, generatePassword } from "../utils/fx.ts";

export class AWS {
    private client: any;
    static REGION: string = 'us-east-1';
    body: Object = {};
    MASTERUSERNAME: string = 'hb';
    MASTERPASSWORD: string;
    constructor({ body }) {
        this.body = body || {}
        this.client = new RDSClient({
            region: AWS.REGION
        });
    }
    /**
     * CloneDb from Customer machine to target machine
     * @param DBInstanceIdentifier | String req (UID of the DB to be created)
     */
    async cloneDb(DBInstanceIdentifier: String) {
        let event: DBBuildEvent = 'cloneDBEvent';
        this.createDBSnapshot(DBInstanceIdentifier, event);
    }

    /**
     * not yet required
     * @param DBInstanceIdentifier 
     * @param DBSnapshotIdentifier 
     * @param ApplyImmediately 
     */
    async modifyDbInstance(DBInstanceIdentifier: String, NewDBInstanceObject: Object, ApplyImmediately: boolean = true) {
        let event: DBBuildEvent = 'modifyDbStatus';
        this.MASTERPASSWORD = NewDBInstanceObject.MasterUserPassword;
        this.MASTERUSERNAME = NewDBInstanceObject.MasterUsername || 'hb';
        let payload = { DBInstanceIdentifier: DBInstanceIdentifier };
        let awsCommand = new ModifyDBInstanceCommand({
            ...NewDBInstanceObject,
            ApplyImmediately: ApplyImmediately,
            DBInstanceIdentifier: DBInstanceIdentifier,
            VpcSecurityGroupIds: ['sg-0011336ac2b73cf6e']
        });
        this.sendAwsCommand(awsCommand, event, payload);
    }

    /**
     * Create New Fresh DB
     * @param DBInstanceIdentifier | String req (UID of the DB to be created)
     * @param DBInstanceClass | String optional (default db.t3.medium)
     * @param allocatedSpace | Number optional (Size of DB in GBs)
     */
    async createNewBaseDb(DBInstanceIdentifier: String, DBInstanceClass: String = 'db.t3.medium', allocatedSpace: Number = 20) {
        let event: DBBuildEvent = 'checkDbCreateStatus';
        let payload = { DBInstanceIdentifier };
        let awsCommand = new CreateDBInstanceCommand({
            AllocatedStorage: allocatedSpace,
            DBInstanceIdentifier: DBInstanceIdentifier, // get this from appsmith
            DBInstanceClass: DBInstanceClass, // get this from appsmith
            Engine: 'postgres', // static
            DBName: 'hb',
            MasterUsername: 'hb',
            MasterUserPassword: generatePassword()
        });
        this.sendAwsCommand(awsCommand, event, payload);
    }

    /**
     * Create DB from the provided Snapshot
     * @param DBInstanceIdentifier | String opt (UID of the DB to be created if not given uuid will be generated)
     * @param DBSnapshotIdentifier | String req (UID of the snapshot FROM which DB will be created)
     * @param allocatedSpace | Number optional (Size of DB in GBs)
     * @param DBInstanceClass | String optional (default db.t3.medium)
     */
    async createDBFromSnapshot(DBSnapshotIdentifier: String, DBInstanceIdentifier: String = cryptoRandomString({ length: 3, characters: 'alphanumeric' }) + cryptoRandomString({ length: 6, type: 'alphanumeric' }), allocatedSpace: Number = 50, DBInstanceClass: String = 'db.t3.medium') {
        let event: DBBuildEvent = 'createDBFromSnapshot';
        let payload = { DBInstanceIdentifier };
        let awsCommand = new RestoreDBInstanceFromDBSnapshotCommand({
            AllocatedStorage: allocatedSpace,
            DBInstanceIdentifier: DBInstanceIdentifier, // get this from appsmith
            DBInstanceClass: DBInstanceClass, // get this from appsmith
            Engine: 'postgres', // static
            DBSnapshotIdentifier: DBSnapshotIdentifier
        });
        this.sendAwsCommand(awsCommand, event, payload);
    }


    /**
     * Create Snapshot of the DB using DBInstanceIdentifier
     * @param DBInstanceIdentifier | String req (unique Identifier of the DB to create Snapshot OF)
     * @param DBSnapshotIdentifier | String optional (unique Identifier of the DB Snapshot that will be created)
     */
    async createDBSnapshot(DBInstanceIdentifier: String, event: DBBuildEvent = 'checkSnapshotStatus', DBSnapshotIdentifier: String = cryptoRandomString({ length: 3, characters: 'alphanumeric' }) + cryptoRandomString({ length: 6, type: 'alphanumeric' })) {
        let payload = { DBSnapshotIdentifier };
        let awsCommand = new CreateDBSnapshotCommand({
            DBInstanceIdentifier: DBInstanceIdentifier,
            DBSnapshotIdentifier: DBSnapshotIdentifier
        });
        this.sendAwsCommand(awsCommand, event, payload);
    }


    /**
     * Send aws Command and poll the status of command in threaded file
     * @param awsCommand | any
     * @param event | String
     * @param payload | Payload
     */
    async sendAwsCommand(awsCommand: any, event: DBBuildEvent, payload: Payload) {
        let worker;
        let start: number = Date.now();
        try {
            console.log(`Sending Command ${event}`);
            await this.client.send(awsCommand)
            worker = new Worker(new URL("./check-status.ts", import.meta.url).href, { type: "module" });
            // return res with status 200
        } catch (error) {
            if (error.name == 'DBInstanceAlreadyExistsFault') {
                console.log(`Command ${event} Failed with DBInstanceAlreadyExists checking db`);
                worker = new Worker(new URL("./check-status.ts", import.meta.url).href, { type: "module" });
            } else {

                if (error.name == 'InvalidDBInstanceStateFault')
                    return; // return error to admin console server
                console.log("***************************");
                console.log(`${JSON.stringify(error)}`);
                console.log("***************************");
                // console.log(`Checking for DB status`);
                // only allow for passable errors otherwise send back result 500
            }
        }
        worker.postMessage({ payload: payload, event: event });
        this.eventHandler(event, worker, start)
    }

    async eventHandler(event: DBBuildEvent, worker: Worker, start: number) {
        worker.onmessage = async (evt) => {
            // send post req to admin-console and update db there with new db connections string and creds
            if (evt.data.status == 'available') {
                const end: number = Date.now();
                console.log(`Execution time: ${Math.round((end - start) / 1000)} s`);
                if (event == 'cloneDBEvent') {
                    this.createDBFromSnapshot(evt.data.snapshotId);
                } else if (event == 'createDBFromSnapshot') {
                    // send post req as status update
                    console.log("Modifying DB MASTER PASSWORD")
                    this.modifyDbInstance(evt.data.dbId, { MasterUserPassword: generatePassword() })
                } else if (event == 'modifyDbStatus') {
                    this.body.event = 'dbmDeploy';
                    this.body.secrets = {
                        "DATABASE_URL": `postgresql://${this.MASTERUSERNAME}:${this.MASTERPASSWORD}@${evt.data.connectionString}:5432/hb`
                    }
                    let r = await sendRequest("POST", "ADMIN_URL", '/fly/deploy/app', this.body)
                }
            } else
                console.log(evt.data.status); // Send Status updated to admin console
        };
    }
}
