import winston from 'npm:winston';


export class ELog {
    logger = null;
    constructor() {
        this.logger = this.init()
    }
    init() {
        const logger = winston.createLogger({
            level: 'silly',
            format: winston.format.json(),
            defaultMeta: { service: 'deno-api' },
            transports: [
                new winston.transports.Console(),
                new winston.transports.File({ filename: 'stderr.log', level: 'error' }),
                new winston.transports.File({ filename: 'stdout.log' }),
            ],
        });
        return logger
    }

    _log(level: string, msg: any) {
        if (!this.logger) {
            this.logger = this.init()
        }
        this.logger.log({
            level: level,
            message: msg
        });
    }

    debug(msg: any) {
        this._log('debug', msg)
    }

    info(msg: any) {
        this._log('info', msg)
    }

    warn(msg: any) {
        this._log('warn', msg)
    }

    error(msg: any) {
        this._log('error', msg)
    }
}