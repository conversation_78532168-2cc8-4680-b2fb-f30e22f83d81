# Clara Monorepo .gitignore
# Global patterns use **/ prefix to apply throughout the repository
# Manually update the exclude list to handle same cases as:
#   bd/.dockerignore
#   bd/apps/cli/src/commands/fly/watch.ts
#   bd/packages/containers/nes/exec/nes.watcher

# OS Files
**/._*
**/.DS_Store
**/*.*~
**/*.lock
**/*.swp
**/*~
**/Thumbs.db

# Logs & Runtime Data
**/.*history
**/*.log
**/*.pid
**/*.seed
**/*debug.log*
**/*error.log*
**/logs
**/pids
**/std*.log

# Temporary Files
**/.*cache
**/.tmp
**/[Aa]rchive[ds]
**/tmp

# Package Management
**/.grunt*
**/.npm
**/.pnp.*
**/.pnpm*
**/.yarn
**/*-lock.json
**/jspm_packages
**/node_modules
**/yarn.lock

# Build & Scripts
**/__pycache__
**/*.egg-info
**/*.pyc
**/*.save
**/*.sh
!**/scripts/*.sh
**/*.tar.gz
**/*.tgz
**/*.tsbuildinfo
**/build
**/dist

# Environment & Configuration
**/.*.local
**/.docusaurus
**/.env
**/.env.*.local
**/.env.keys*
**/.env.local
**/.local.*.pem
**/*.local
**/*.local.*
**/fly.toml
**/google-creds.json

# Editor & AI Tools
**/.aider*
**/.augment-*
**/.idea
**/.vscode*
**/.vscode/*
**/*code-workspace
**/*.suo
**/*.ntvs*
**/*.njsproj
**/*.sln
**/*.sw?

# Python & Virtual Environments
**/.Python
**/.venv
**/[Bb]in
**/[Ii]nclude
**/[Ll]ib
**/[Ll]ib64
**/[Ll]ocal
**/[Mm]an
**/[Ss]hare
**/pip-selfcheck.json
**/pyvenv.cfg

# Terraform
**/.terraform/*
**/*.tfstate
**/*.tfstate.*

# Project-Specific
**/packages/dsl/src/base*
**/tshomebase/public
