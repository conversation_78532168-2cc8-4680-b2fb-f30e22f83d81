packages:
  - nes
  - apps/*
  - packages/*
  - '!**/test/**'
  - '!**/tests/**'
  - '!**/dist/**'
  - '!**/build/**'
  - '!**/node_modules/**'
catalog:
  '@apidevtools/json-schema-ref-parser': ^11.7.0
  '@aws-sdk/client-s3': ^3.817.0
  '@eslint/js': ^9.28.0
  '@faker-js/faker': ^8.4.1
  '@jest/globals': ^30.0.0
  '@joi/date': ^2.1.1
  '@koa/cors': ^5.0.0
  '@ladjs/koa-views': ^9.0.0
  '@mantine/code-highlight': ^8.1.0
  '@mantine/core': ^8.1.0
  '@mantine/dates': ^8.1.0
  '@mantine/form': ^8.1.0
  '@mantine/hooks': ^8.1.0
  '@mantine/modals': ^8.1.0
  '@mantine/notifications': ^8.1.0
  '@mantine/spotlight': ^8.1.0
  '@oclif/core': ^4.3.3
  '@oclif/plugin-not-found': ^3.2.56
  '@ringcentral/sdk': ^5.0.3
  '@sentry/node': ^9.23.0
  '@sentry/profiling-node': ^9.23.0
  '@swc/core': ^1.11.31
  '@swc/jest': ^0.2.38
  '@types/better-sqlite3': ^7.6.13
  '@types/blessed': ^0.1.25
  '@types/coffeescript': ^2.5.7
  '@types/cson': ^7.20.3
  '@types/eslint__js': ^8.42.3
  '@types/jest': ^29.5.14
  '@types/node': ^24.0.0
  '@types/react': ^19.1.7
  '@types/react-dom': ^19.1.6
  '@typescript-eslint/eslint-plugin': ^8.34.0
  '@typescript-eslint/parser': ^8.34.0
  '@vitejs/plugin-react': ^4.5.2
  '@welldone-software/why-did-you-render': ^10.0.1
  ajv: ^8.16.0
  ajv-errors: ^3.0.0
  ajv-formats: ^3.0.1
  ajv-keywords: ^5.1.0
  async-busboy: ^1.1.0
  async-lock: ^1.4.1
  axios: ^1.8.4
  babel-jest: ^29.7.0
  babel-plugin-react-compiler: 19.1.0-rc.2
  bcrypt: ^5.1.1
  better-sqlite3: ^11.10.0
  bindings: ^1.5.0
  blessed: ^0.1.81
  btoa: ^1.2.1
  camelcase-keys: ^9.1.3
  chalk: ^5.4.1
  cli-table3: ^0.6.5
  coffeescript: ^2.7.0
  cookie: ^1.0.2
  cron-time-generator: ^1.3.2
  cson: ^8.4.0
  currency.js: ^2.0.4
  dotenv-cli: ^7.4.4
  eslint: ^9.28.0
  eslint-config-prettier: ^10.1.5
  eslint-import-resolver-typescript: ^4.4.3
  eslint-plugin-import: ^2.31.0
  eslint-plugin-jsonc: ^2.20.1
  eslint-plugin-no-unsanitized: ^4.1.2
  eslint-plugin-prettier: ^5.4.1
  eslint-plugin-react: ^7.37.5
  eslint-plugin-react-compiler: 19.1.0-rc.2
  eslint-plugin-react-hooks: ^5.2.0
  eslint-plugin-react-perf: ^3.3.3
  eslint-plugin-react-refresh: ^0.4.20
  eslint-plugin-yml: ^1.14.0
  execa: ^9.6.0
  fast-xml-parser: ^4.3.6
  flat: ^5.0.2
  fs-extra: ^11.2.0
  glob: ^11.0.2
  globals: ^16.2.0
  gm: ^1.25.0
  import: ^0.0.6
  jest: ^30.0.0
  joi: ^17.13.3
  jsonwebtoken: ^9.0.2
  koa: ^2.15.3
  koa-body: ^6.0.1
  koa-jwt: ^4.0.4
  koa-router: ^12.0.0
  koa-session: ^6.2.0
  koa-websocket: ~6.0.0
  koa2-winston: ^3.2.0
  lodash: ^4.17.20
  mathjs: ^12.4.3
  mime-detect: ^1.3.0
  module-alias-jest: ^0.0.3
  moment: ^2.29.1
  moment-timezone: ^0.5.37
  mustache: ^4.1.0
  node-fetch: ^2.6.1
  node-forge: ^1.3.1
  node-gyp: ^10.2.0
  node-notifier: ^10.0.1
  numeral: ^2.0.6
  nunjucks: ~3.2.2
  openid-client: ^5.7.0
  path-match: ^1.2.4
  pdf-lib: ^1.17.1
  pdfmake: ^0.2.20
  pg: ~8.5.1
  pg-boss: ^10.1.4
  pg-escape: ~0.2.0
  pg-pool: ~3.2.2
  pg-promise: ~11.5.4
  pg-query-stream: ~3.4.2
  postcss: ^8.5.4
  postcss-preset-mantine: ^1.17.0
  prettier: ^3.3.3
  pretty-format: ^29.7.0
  react: ^19.1.0
  react-dom: ^19.1.0
  react-scan: ^0.3.4
  rrule: ^2.8.1
  sass-embedded: ^1.89.2
  sequelize: ^6.23.1
  sharp: ^0.34.2
  snakecase-keys: ^8.0.1
  source-map: ^0.7.4
  strip-json-comments: ^5.0.2
  stylelint: ^16.20.0
  stylelint-config-clean-order: ^7.0.0
  stylelint-config-standard: ^38.0.0
  stylelint-config-standard-scss: ^15.0.1
  stylelint-order: ^7.0.0
  stylelint-prettier: ^5.0.3
  supertest: ^6.3.4
  svgo: ^3.3.2
  swr: ^2.3.3
  toml: ^3.0.0
  ts-node: ^10.9.2
  typescript: ~5.8.3
  typescript-eslint: ^8.34.0
  usehooks-ts: ^3.1.1
  uuid: ^8.3.2
  vite: ^6.3.5
  vite-css-modules: ^1.8.7
  vite-plugin-babel: ^1.3.1
  vite-plugin-image-optimizer: ^1.1.8
  winston: ^3.13.0
  winston-console-format: ^1.0.8
  winston-daily-rotate-file: ^5.0.0
  winston-slack-webhook-transport: ^2.3.5
  winston-transport: ^4.7.0
  zod: ^3.25.57
  zustand: ^5.0.5
  zxcvbn: ^4.4.2
ignoredBuiltDependencies:
  - libxmljs2
onlyBuiltDependencies:
  - '@sentry-internal/node-cpu-profiler'
  - '@sentry/profiling-node'
  - '@swc/core'
  - bcrypt
  - better-sqlite3
  - canvas
  - core-js
  - core-js-pure
  - esbuild
  - lmdb
  - msgpackr-extract
  - protobufjs
  - sharp
  - sqlite3
  - tesseract.js
  - unrs-resolver
