<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Clara Homebase</title>
  <meta name="description" content="Clara Homebase">
  <meta name="author" content="Clara">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://kit.fontawesome.com/805453c99a.js" crossorigin="anonymous"></script>
  <!-- <script>
    // Configure sentryOnLoad before adding the Loader Script
    window.sentryOnLoad = function () {
      Sentry.init({
        dsn: "https://<EMAIL>/4507080374550528",
        ignoreErrors: [
            'Non-Error exception captured'
        ],
        release: "clara@1.0.0",
        tracesSampleRate: 0.1,
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
  });
    };
  </script> -->
  <!-- <script src="https://js.sentry-cdn.com/1a755cc09127b710a08397534f2989d4.min.js" crossorigin="anonymous"></script> -->
  <script type="module">
    if (module.hot) {
        module.hot.accept();
    }
  </script>
  <script type="module" src="/src/main.tsx"></script>
  <script type="module">
    import React from 'react'
    import ReactDOM from 'react-dom/client'
    if (!window.requestIdleCallback) { // For Safari
      window.requestIdleCallback = setTimeout
    }

    window.unloadComponent = (component, id) => {
      const key = `${component}-${id}`
      if(!id){
        return;
      }
      if (window.rxDom && window.rxDom[key]) {
        requestAnimationFrame(() => { // Wait for next frame to unmount react might be in between renders
          try{
            window.rxDom[key].unmount()
          } catch (e) {
            // Ignore error
            // Component might have been unmounted already or coffee script might have been unloaded first
          }
          delete window.rxDom[key]
        })
      }
    }

    window.loadComponent = (component, container, id, props) => {
      if (!window.rxRegistry) {
        console.error('No RX Register Found.')
        return
      }
      let RenderComponent = window.rxRegistry[component]

      if (!RenderComponent) {
        console.error(`Component not register ${component}`)
        return
      }
      const key = `${component}-${id}`
      const root = ReactDOM.createRoot(container);
      if (!window.rxDom) {
        window.rxDom = {}
      }
      window.rxDom[key] = root
      if (RenderComponent.call) {
        root.render(RenderComponent(props || {}));
      } else {
        if (props) {
          RenderComponent = window.React.cloneElement(RenderComponent, props)
        }
        root.render(RenderComponent)
      }
    }
  </script>
  <link rel="shortcut icon" href="public/img/favicon.ico" type="image/x-icon">
  <link type="text/css" rel="stylesheet" href="public/css/lib.css" media="all">
  <link type="text/css" rel="stylesheet" href="public/css/app.css" media="all">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <!--[if lt IE 9]>
    <script type="text/javascript" charset="utf-8">window.oldIE = true;</script>
  <![endif]-->

</head>

<body>
  <noscript>
    <center><span class="alert alert-info">Please enable JavaScript to access <a href="https://clararx.com">Clara Homebase</a>.</span></center>
  </noscript>