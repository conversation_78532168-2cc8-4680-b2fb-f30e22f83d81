// Root ESLint configuration using the new flat config format
import { createBaseConfig } from "./packages/config/eslint/base.js";

// Create base config
const baseConfig = createBaseConfig([]);

// Modify to only apply to files in the root directory
const rootConfig = baseConfig.map((config) => {
    // Add or extend ignores to exclude all subdirectories
    const existingIgnores = config.ignores || [];
    return {
        ...config,
        // Ignore all subdirectories but keep files in root
        ignores: [...existingIgnores, "./*//**"],
    };
});

export default rootConfig;
