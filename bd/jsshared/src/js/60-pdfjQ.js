(function ($) {

	window['_base64ToArrayBuffer'] = function (base64) {
		var binary_string = window.atob(base64);
		var len = binary_string.length;
		var bytes = new Uint8Array(len);
		for (var i = 0; i < len; i++) {
			bytes[i] = binary_string.charCodeAt(i);
		}
		return bytes.buffer;
	}


	var methods = {
		init: function (options) {
			dfd = $.Deferred()
			var defaults = {
				src: '',
				pageNum: 1,
				toolbar: {
					background: '#333',
					textColor: '#fff',
					prevBtnText: '<',
					nextBtnText: '>'
				},
				formData: [],
				onChange: null,
				defaultFontSize: 7,
				signatureFields: []
			}
			var config = $.extend(true, {}, defaults, options);

			this.addClass('pdf-jq');
			this.html(`
				<div class="toolbar"><button class="prev-btn">${config.toolbar.prevBtnText}</button><input class='current-page' type="number" readonly disabled/><div class='pagecount-wrapper'><div id='totalPages' style="color:${config.toolbar.textColor};">of <span></span></div></div><button class='next-btn'>${config.toolbar.nextBtnText}</button></div>
				<div class="viewer-wrapper">
					<canvas id="the-canvas"></canvas>
					<div id="pdf-annotation-layer" class="annotationLayer"></div>
				</div>
				<!-- <div class="pagination"><button class="prev-btn">${config.toolbar.prevBtnText}</button><input class='current-page' type="number" readonly disabled/></div><button class='next-btn'>${config.toolbar.nextBtnText}</button></div> -->
			`)
			$canvas = this.find('#the-canvas');
			canvas = $canvas[0];
			context = canvas.getContext('2d');
			this.data('scale', 1.5)
			this.data('$canvas', $canvas)
			this.data('canvas', canvas)
			this.data('ctxCanvas', context)
			this.data('pageNum', null);
			this.data('totalPages', null);
			this.data('pageNumPending', null)
			this.data('pageRendering', false)
			this.data('annotations', null)
			this.data('formData', config.formData)
			this.data('onchange', config.onChange)
			this.data('pdfSrc', config.src)
			this.data('defaultFontSize', config.defaultFontSize)
			signatureFields = {}
			empty_png = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
			for(f of config.signatureFields) {
				signatureFields[f] = empty_png
			}
			this.data('signaturefields', signatureFields)
			this.on('change', '#pdf-annotation-layer *:input', $.proxy(methods.updateValues, this))
			this.data('pages', {});
			var loadingTask;

			loadingTask = pdfjsLib.getDocument({
				url: config.src,
				enableXfa: true,
				annotationMode: 3
			});

			_this = this;

			loadingTask.promise.then(function (doc) {
				_this.data('pdfDoc', doc);
				_this.data('totalPages', doc.numPages);
				// doc.saveDocument().then((pdfBytes) => {
				// 	PDFLib.PDFDocument.load(pdfBytes).then((doc) => {
				// 	doc.saveAsBase64({ dataUri: true }).then((pdfBytes) => {
				// 		// download(pdfBytes);
				// 		PDFLib.PDFDocument.load(pdfBytes).then((doc) => {
				// 			PDFLibDoc = doc;
				$.proxy(methods.getAllAnnotations, _this)()
				$.proxy(methods.queueRenderPage, _this)(1)
				$.proxy(methods.queueRenderPage, _this)(config.pageNum)
				_this.find('.toolbar > .prev-btn').click($.proxy(methods.onPrevPage, _this));
				_this.find('.toolbar > .next-btn').click($.proxy(methods.onNextPage, _this));
				_this.find('.toolbar div#totalPages > span').html(doc.numPages);
				dfd.resolve("success")
			}).catch((err)=>{
				dfd.reject("fail")
			});
			return dfd
		},

		updateValues: function (e) {
			this.find(':input, :button').prop('disabled', true)
			currentData = this.data('formData')
			page = this.data('pageNum')
			if (typeof (currentData[page]) !== 'object') {
				currentData[page] = {}
			}
			if ($(e.target).attr('type') === 'checkbox') {
				currentData[page][$(e.target).attr('name')] = $(e.target).is(':checked')
			} else if ($(e.target).attr('type') === 'radio') {
				radioId = $(e.target).attr('id')
				radioVal = _.filter(this.data('annotations')[this.data('pageNum')], {
					id: radioId
				})[0]['buttonValue']
				currentData[page][$(e.target).attr('name')] = radioVal;
			} else {
				currentData[page][$(e.target).attr('name')] = $(e.target).val()
			}
			this.data('formData', currentData)
			onchange = this.data('onchange')
			if (typeof (onchange) === 'function') {
				onchange(currentData)
			}
			this.find(':input, :button').prop('disabled', false)

		},
		normalizeRect: function (rect) {
			const r = rect.slice(0); // clone rect
			if (rect[0] > rect[2]) {
				r[0] = rect[2];
				r[2] = rect[0];
			}
			if (rect[1] > rect[3]) {
				r[1] = rect[3];
				r[3] = rect[1];
			}
			return r;
		},
		attachSignatures: function () {
			dfd = $.Deferred()
			_this = this
			$.proxy(methods.loadPDFLib, this)().then(() => {
				PDFLibDoc = _this.data('pdfLibDoc')
				signatureFields = _this.data('signaturefields')
				if (Object.keys(signatureFields).length === 0) {
					dfd.resolve()
					return
				}
				totalPages = _this.data('totalPages')
				annotationsData = _this.data('annotations')
				doneSigns = 0
				for (i = 1; i <= totalPages; i++) {
					pageNumber = i
					$.each(annotationsData[i], (j, fielddata) => {
						if (signatureFields.hasOwnProperty(fielddata.fieldName)) {
							aData = _.cloneDeep(fielddata)
							PDFLibDoc.embedPng(signatureFields[aData.fieldName]).then(pdfImage => {
								try {
									rect = aData['rect']
									width = rect[2] - rect[0];
									height = rect[3] - rect[1];
									page = PDFLibDoc.getPage(pageNumber - 1)
									jsPage = _this.data('pages')[pageNumber]
									normalRect = methods.normalizeRect([
										rect[0],
										jsPage.view[3] - rect[1] + jsPage.view[1],
										rect[2],
										jsPage.view[3] - rect[3] + jsPage.view[1],
									])
									scaled = pdfImage.scaleToFit(width * 4, height * 4)
									page.drawImage(pdfImage, {
										width: scaled.width,
										height: scaled.height,
										y: page.getHeight() - normalRect[1] - (scaled.height/1.5),
										x: normalRect[0]
									})
								} catch (e) {
									console.log(e)
								}
								doneSigns += 1
								if (pageNumber == totalPages && doneSigns >= Object.keys(signatureFields).length) {
									dfd.resolve()
								}
							})
						}

					})
				}
			})
			return dfd
		},
		loadPDFLib: function () {
			dfd = $.Deferred()
			_this = this;
			PDFLibDoc = null
			PDFjsDoc = this.data('pdfDoc')
			PDFjsDoc.saveDocument().then((pdfBytes) => {
				PDFLib.PDFDocument.load(pdfBytes).then((doc) => {
					PDFLibDoc = doc;
					fontURL = 'https://pdf-lib.js.org/assets/ubuntu/Ubuntu-R.ttf'
					fetch(fontURL).then((res) => res.arrayBuffer()).then((fontBytes) => {
						PDFLibDoc.registerFontkit(fontkit)
						PDFLibDoc.embedFont(fontBytes).then((ubuntuFont) => {
							_this.data('ubuntuFont', ubuntuFont)
							_this.data('pdfLibDoc', PDFLibDoc)
							dfd.resolve(true)
						})
					})
				})
			})
			return dfd
		},
		flatten: function () {
			flattenDFD = $.Deferred()
			this.find(':input, :button').prop('disabled', true)
			$.proxy(methods.attachSignatures, this)().then(() => {
				annotationsData = this.data('annotations')
				formData = this.data('formData')
				defaultFontSize = this.data('defaultFontSize')
				signatureFields = this.data('signaturefields')
				totalPages = this.data('totalPages')
				ubuntuFont = this.data('ubuntuFont')
				PDFLibDoc = this.data('pdfLibDoc')
				formfields = PDFLibDoc.getForm()
				for (i = 1; i < annotationsData.length; i++) {
					pageNumber = i;
					fields = annotationsData[i]
					pageData = formData[i] || {}
					$.each(fields, (j, fielddata) => {
						if (pageData.hasOwnProperty(fielddata.fieldName)) {
							switch (fielddata.fieldType) {
								case "Tx":
									formfields.getTextField(fielddata.fieldName).setText(pageData[fielddata.fieldName])
									if (fielddata.defaultAppearanceData.fontSize > 0) {
										formfields.getTextField(fielddata.fieldName).setFontSize(fielddata.defaultAppearanceData.fontSize)
									} else {
										if(signatureFields.hasOwnProperty(fielddata.fieldName)){
											formfields.getTextField(fielddata.fieldName).setText("")
										}else{
											formfields.getTextField(fielddata.fieldName).setFontSize(defaultFontSize)
										}
									}
									break;
								case 'Btn':
									if (fielddata.checkBox) {
										if (pageData[fielddata.fieldName] == true) {
											formfields.getCheckBox(fielddata.fieldName).check();
										} else {
											formfields.getCheckBox(fielddata.fieldName).uncheck();
										}
									} else if (fielddata.radioButton) {
										formfields.getRadioGroup(fielddata.fieldName).select(pageData[fielddata.fieldName]);
									}
									break;
							}
						}
						if (j == fields.length - 1 && pageNumber == totalPages) {
							formfields.updateFieldAppearances(ubuntuFont);
							formfields.flatten()
							PDFLibDoc.saveAsBase64({ dataUri: true }).then((pdfBytes) => {
								// download(pdfBytes);
								flattenDFD.resolve(pdfBytes)
							});
						}
					})
				}
			})
			return flattenDFD

		},
		setSignatureField: function (fieldName, pngData) {
			if (fieldName == undefined) {
				return
			}
			signatures = this.data('signaturefields');
			signatures[fieldName] = pngData.split('base64,')[1];
			this.find('[name="' + fieldName + '"]').prop('disabled', true)
		},
		renderPage: function (num) {
			if (!(num && num > 0)) {
				$.error("PDFjQ: Invalid Page Specified!");
				return
			}
			this.data('pageRendering', true)
			pdfDoc = this.data('pdfDoc')
			ctx = this.data('ctxCanvas')
			scale = this.data('scale')
			canvas = this.data('canvas')

			_this = this;

			// Using promise to fetch the page
			pdfDoc.getPage(num).then(function (page) {
				var viewport = page.getViewport({ scale: scale });
				canvas.height = viewport.height;
				canvas.width = viewport.width;
				pages = _this.data('pages')
				pages[num] = page
				_this.data('pages', pages)

				// Render PDF page into canvas context
				var renderContext = {
					canvasContext: ctx,
					viewport: viewport
				};
				var renderTask = page.render(renderContext);

				// Wait for rendering to finish
				renderTask.promise.then(function () {
					$canvas = _this.data('$canvas')
					canvas_offset = $canvas.offset()
					canvas_height = $canvas.get(0).height
					canvas_width = $canvas.get(0).width
					_this.find("#pdf-annotation-layer").css({ left: canvas_offset.left + 'px', top: canvas_offset.top + 'px', height: canvas_height + 'px', width: canvas_width + 'px' }).html("");
					page.getAnnotations().then(function (annotationData) {
						if (annotationData !== undefined && _this.data('formData')[num] !== undefined) {
							formData = _this.data('formData')[num]
							for (i = 0; i < annotationData.length; i++) {
								fieldName = annotationData[i]['fieldName']
								if (typeof formData[fieldName] != 'undefined') {
									annotationData[i]['fieldValue'] = formData[fieldName]
								}
								if (annotationData[i]['defaultAppearanceData']['fontSize'] == 0) {
									annotationData[i]['defaultAppearanceData']['fontSize'] = _this.data('defaultFontSize')
								}
								if(_this.data('signaturefields').hasOwnProperty(fieldName)) {
									console.log(annotationData[i])
									annotationData[i]['readOnly'] = true
								}
							}
						}
						pdfjsLib.AnnotationLayer.render({
							viewport: viewport.clone({
								dontFlip: true
							}),
							div: _this.find("#pdf-annotation-layer").get(0),
							annotations: annotationData,
							page: page
						});
					});
					_this.data('pageRendering', false)
					_this.data('pageNum', num);
					_this.find('div.toolbar > .current-page').val(num);
					pageNumPending = _this.data('pageNumPending')
					if (pageNumPending !== null) {
						// New page rendering is pending
						_this.PDFjQ('renderPage', pageNumPending);
						_this.data('pageNumPending', null);
					}
				});
			});
		},
		queueRenderPage: function (num) {
			if (this.data('pageRendering')) {
				this.data('pageNumPending', num);
			} else {
				$.proxy(methods.renderPage, this)(num);
			}
		},
		onPrevPage: function () {
			pageNum = this.data('pageNum');
			if (pageNum <= 1) {
				return;
			}
			pageNum--;
			$.proxy(methods.queueRenderPage, this)(pageNum);
		},
		onNextPage: function () {
			pageNum = this.data('pageNum');
			numPages = this.data('totalPages');
			if (pageNum >= numPages) {
				return;
			}
			pageNum++;
			$.proxy(methods.queueRenderPage, this)(pageNum);
		},
		getAllAnnotations: function () {
			pdfDoc = this.data('pdfDoc')
			annotations = []
			for (i = 1; i <= this.data('totalPages'); i++) {
				_this = this;
				pdfDoc.getPage(i).then(function (page) {
					page.getAnnotations().then(function (annotationData) {
						annotations[page.pageNumber] = annotationData;
						if (page.pageNumber == _this.data('totalPages')) {
							_this.data('annotations', annotations)
						}
					});
				});
			}
		}
	};

	$.getPDFAnnotations = function (pdf_url) {
		dfd = $.Deferred()
		annotations = []
		pdfjsLib.getDocument({
			url: pdf_url,
			enableXfa: true,
			annotationMode: 3
		}).promise.then(function (pdfDoc) {
			totalPages = pdfDoc.numPages
			for (i = 1; i <= totalPages; i++) {
				_this = this;
				pdfDoc.getPage(i).then(function (page) {
					page.getAnnotations().then(function (annotationData) {
						annotations[page.pageNumber] = annotationData;
						if (page.pageNumber == totalPages) {
							dfd.resolve(annotations)
						}
					});
				});
			}
		})
		return dfd
	}

	$.fn.PDFjQ = function (methodOrOptions) {
		if (methods[methodOrOptions]) {
			return methods[methodOrOptions].apply(this, Array.prototype.slice.call(arguments, 1));
		} else if (typeof methodOrOptions === 'object' || !methodOrOptions) {
			// Default to "init"
			return methods.init.apply(this, arguments);
		} else {
			$.error('Method ' + methodOrOptions + ' does not exist on jQuery.PDFjQ');
		}
	};


})(jQuery);