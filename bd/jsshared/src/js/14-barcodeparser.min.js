// https://github.com/PeterBrockfeld/BarcodeParser
var parseGS1Barcode = function () { "use strict"; function parseBarcode(barcode) { var i = 0, fncChar = String.fromCharCode(29), barcodelength = barcode.length, answer = {}, restOfBarcode = "", symbologyIdentifier = barcode.slice(0, 3), firstElement = {}; function ParsedElement(elementAI, elementDataTitle, elementType) { this.ai = elementAI; this.dataTitle = elementDataTitle; switch (elementType) { case "S": this.data = ""; break; case "N": this.data = 0; break; case "D": this.data = new Date; this.data.setHours(0, 0, 0, 0); break; default: this.data = ""; break }this.unit = "" } function identifyAI(codestring) { var firstNumber = codestring.slice(0, 1), secondNumber = codestring.slice(1, 2), thirdNumber = "", fourthNumber = "", codestringToReturn = "", codestringLength = codestring.length, elementToReturn = ""; function cleanCodestring(stringToClean) { var firstChar = stringToClean.slice(0, 1); while (firstChar === fncChar) { stringToClean = stringToClean.slice(1, stringToClean.length); firstChar = stringToClean.slice(0, 1) } return stringToClean } function parseFloatingPoint(stringToParse, numberOfFractionals) { var auxString = "", offset = stringToParse.length - numberOfFractionals, auxFloat = 0; auxString = stringToParse.slice(0, offset) + "." + stringToParse.slice(offset, stringToParse.length); try { auxFloat = parseFloat(auxString) } catch (e36) { throw "36" } return auxFloat } function parseDate(ai, title) { elementToReturn = new ParsedElement(ai, title, "D"); var offSet = ai.length, dateYYMMDD = codestring.slice(offSet, offSet + 6), yearAsNumber = 0, monthAsNumber = 0, dayAsNumber = 0; try { yearAsNumber = parseInt(dateYYMMDD.slice(0, 2), 10) } catch (e33) { throw "33" } try { monthAsNumber = parseInt(dateYYMMDD.slice(2, 4), 10) - 1 } catch (e34) { throw "34" } try { dayAsNumber = parseInt(dateYYMMDD.slice(4, 6), 10) } catch (e35) { throw "35" } if (yearAsNumber > 50) { yearAsNumber = yearAsNumber + 1900 } else { yearAsNumber = yearAsNumber + 2e3 } elementToReturn.data.setFullYear(yearAsNumber, monthAsNumber, dayAsNumber); codestringToReturn = codestring.slice(offSet + 6, codestringLength) } function parseFixedLength(ai, title, length) { elementToReturn = new ParsedElement(ai, title, "S"); var offSet = ai.length; elementToReturn.data = codestring.slice(offSet, length + offSet); codestringToReturn = codestring.slice(length + offSet, codestringLength) } function parseVariableLength(ai, title) { elementToReturn = new ParsedElement(ai, title, "S"); var offSet = ai.length, posOfFNC = codestring.indexOf(fncChar); if (posOfFNC === -1) { elementToReturn.data = codestring.slice(offSet, codestringLength); codestringToReturn = "" } else { elementToReturn.data = codestring.slice(offSet, posOfFNC); codestringToReturn = codestring.slice(posOfFNC + 1, codestringLength) } } function parseFixedLengthMeasure(ai_stem, fourthNumber, title, unit) { elementToReturn = new ParsedElement(ai_stem + fourthNumber, title, "N"); var offSet = ai_stem.length + 1, numberOfDecimals = parseInt(fourthNumber, 10), numberPart = codestring.slice(offSet, offSet + 6); elementToReturn.data = parseFloatingPoint(numberPart, numberOfDecimals); elementToReturn.unit = unit; codestringToReturn = codestring.slice(offSet + 6, codestringLength) } function parseVariableLengthMeasure(ai_stem, fourthNumber, title, unit) { elementToReturn = new ParsedElement(ai_stem + fourthNumber, title, "N"); var offSet = ai_stem.length + 1, posOfFNC = codestring.indexOf(fncChar), numberOfDecimals = parseInt(fourthNumber, 10), numberPart = ""; if (posOfFNC === -1) { numberPart = codestring.slice(offSet, codestringLength); codestringToReturn = "" } else { numberPart = codestring.slice(offSet, posOfFNC); codestringToReturn = codestring.slice(posOfFNC + 1, codestringLength) } elementToReturn.data = parseFloatingPoint(numberPart, numberOfDecimals); elementToReturn.unit = unit } function parseVariableLengthWithISONumbers(ai_stem, fourthNumber, title) { elementToReturn = new ParsedElement(ai_stem + fourthNumber, title, "N"); var offSet = ai_stem.length + 1, posOfFNC = codestring.indexOf(fncChar), numberOfDecimals = parseInt(fourthNumber, 10), isoPlusNumbers = "", numberPart = ""; if (posOfFNC === -1) { isoPlusNumbers = codestring.slice(offSet, codestringLength); codestringToReturn = "" } else { isoPlusNumbers = codestring.slice(offSet, posOfFNC); codestringToReturn = codestring.slice(posOfFNC + 1, codestringLength) } numberPart = isoPlusNumbers.slice(3, isoPlusNumbers.length); elementToReturn.data = parseFloatingPoint(numberPart, numberOfDecimals); elementToReturn.unit = isoPlusNumbers.slice(0, 3) } function parseVariableLengthWithISOChars(ai_stem, title) { elementToReturn = new ParsedElement(ai_stem, title, "S"); var offSet = ai_stem.length, posOfFNC = codestring.indexOf(fncChar), isoPlusNumbers = ""; if (posOfFNC === -1) { isoPlusNumbers = codestring.slice(offSet, codestringLength); codestringToReturn = "" } else { isoPlusNumbers = codestring.slice(offSet, posOfFNC); codestringToReturn = codestring.slice(posOfFNC + 1, codestringLength) } elementToReturn.data = isoPlusNumbers.slice(3, isoPlusNumbers.length); elementToReturn.unit = isoPlusNumbers.slice(0, 3) } switch (firstNumber) { case "0": switch (secondNumber) { case "0": parseFixedLength("00", "SSCC", 18); break; case "1": parseFixedLength("01", "GTIN", 14); break; case "2": parseFixedLength("02", "CONTENT", 14); break; default: throw "01" }break; case "1": switch (secondNumber) { case "0": parseVariableLength("10", "BATCH/LOT"); break; case "1": parseDate("11", "PROD DATE"); break; case "2": parseDate("12", "DUE DATE"); break; case "3": parseDate("13", "PACK DATE"); break; case "5": parseDate("15", "BEST BEFORE or BEST BY"); break; case "6": parseDate("16", "SELL BY"); break; case "7": parseDate("17", "USE BY OR EXPIRY"); break; default: throw "02" }break; case "2": switch (secondNumber) { case "0": parseFixedLength("20", "VARIANT", 2); break; case "1": parseVariableLength("21", "SERIAL"); break; case "4": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("240", "ADDITIONAL ID"); break; case "1": parseVariableLength("241", "CUST. PART NO."); break; case "2": parseVariableLength("242", "MTO VARIANT"); break; case "3": parseVariableLength("243", "PCN"); break; default: throw "03" }break; case "5": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("250", "SECONDARY SERIAL"); break; case "1": parseVariableLength("251", "REF. TO SOURCE"); break; case "3": parseVariableLength("253", "GDTI"); break; case "4": parseVariableLength("254", "GLN EXTENSION COMPONENT"); break; case "5": parseVariableLength("255", "GCN"); break; default: throw "04" }break; default: throw "05" }break; case "3": switch (secondNumber) { case "0": parseVariableLength("30", "VAR. COUNT"); break; case "1": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("310", fourthNumber, "NET WEIGHT (kg)", "KGM"); break; case "1": parseFixedLengthMeasure("311", fourthNumber, "LENGTH (m)", "MTR"); break; case "2": parseFixedLengthMeasure("312", fourthNumber, "WIDTH (m)", "MTR"); break; case "3": parseFixedLengthMeasure("313", fourthNumber, "HEIGHT (m)", "MTR"); break; case "4": parseFixedLengthMeasure("314", fourthNumber, "AREA (m2)", "MTK"); break; case "5": parseFixedLengthMeasure("315", fourthNumber, "NET VOLUME (l)", "LTR"); break; case "6": parseFixedLengthMeasure("316", fourthNumber, "NET VOLUME (m3)", "MTQ"); break; default: throw "06" }break; case "2": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("320", fourthNumber, "NET WEIGHT (lb)", "LBR"); break; case "1": parseFixedLengthMeasure("321", fourthNumber, "LENGTH (i)", "INH"); break; case "2": parseFixedLengthMeasure("322", fourthNumber, "LENGTH (f)", "FOT"); break; case "3": parseFixedLengthMeasure("323", fourthNumber, "LENGTH (y)", "YRD"); break; case "4": parseFixedLengthMeasure("324", fourthNumber, "WIDTH (i)", "INH"); break; case "5": parseFixedLengthMeasure("325", fourthNumber, "WIDTH (f)", "FOT"); break; case "6": parseFixedLengthMeasure("326", fourthNumber, "WIDTH (y)", "YRD"); break; case "7": parseFixedLengthMeasure("327", fourthNumber, "HEIGHT (i)", "INH"); break; case "8": parseFixedLengthMeasure("328", fourthNumber, "HEIGHT (f)", "FOT"); break; case "9": parseFixedLengthMeasure("329", fourthNumber, "HEIGHT (y)", "YRD"); break; default: throw "07" }break; case "3": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("330", fourthNumber, "GROSS WEIGHT (kg)", "KGM"); break; case "1": parseFixedLengthMeasure("331", fourthNumber, "LENGTH (m), log", "MTR"); break; case "2": parseFixedLengthMeasure("332", fourthNumber, "WIDTH (m), log", "MTR"); break; case "3": parseFixedLengthMeasure("333", fourthNumber, "HEIGHT (m), log", "MTR"); break; case "4": parseFixedLengthMeasure("334", fourthNumber, "AREA (m2), log", "MTK"); break; case "5": parseFixedLengthMeasure("335", fourthNumber, "VOLUME (l), log", "LTR"); break; case "6": parseFixedLengthMeasure("336", fourthNumber, "VOLUME (m3), log", "MTQ"); break; case "7": parseFixedLengthMeasure("337", fourthNumber, "KG PER m²", "28"); break; default: throw "08" }break; case "4": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("340", fourthNumber, "GROSS WEIGHT (lb)", "LBR"); break; case "1": parseFixedLengthMeasure("341", fourthNumber, "LENGTH (i), log", "INH"); break; case "2": parseFixedLengthMeasure("342", fourthNumber, "LENGTH (f), log", "FOT"); break; case "3": parseFixedLengthMeasure("343", fourthNumber, "LENGTH (y), log", "YRD"); break; case "4": parseFixedLengthMeasure("344", fourthNumber, "WIDTH (i), log", "INH"); break; case "5": parseFixedLengthMeasure("345", fourthNumber, "WIDTH (f), log", "FOT"); break; case "6": parseFixedLengthMeasure("346", fourthNumber, "WIDTH (y), log", "YRD"); break; case "7": parseFixedLengthMeasure("347", fourthNumber, "HEIGHT (i), log", "INH"); break; case "8": parseFixedLengthMeasure("348", fourthNumber, "HEIGHT (f), log", "FOT"); break; case "9": parseFixedLengthMeasure("349", fourthNumber, "HEIGHT (y), log", "YRD"); break; default: throw "09" }break; case "5": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("350", fourthNumber, "AREA (i2)", "INK"); break; case "1": parseFixedLengthMeasure("351", fourthNumber, "AREA (f2)", "FTK"); break; case "2": parseFixedLengthMeasure("352", fourthNumber, "AREA (y2)", "YDK"); break; case "3": parseFixedLengthMeasure("353", fourthNumber, "AREA (i2), log", "INK"); break; case "4": parseFixedLengthMeasure("354", fourthNumber, "AREA (f2), log", "FTK"); break; case "5": parseFixedLengthMeasure("355", fourthNumber, "AREA (y2), log", "YDK"); break; case "6": parseFixedLengthMeasure("356", fourthNumber, "NET WEIGHT (t)", "APZ"); break; case "7": parseFixedLengthMeasure("357", fourthNumber, "NET VOLUME (oz)", "ONZ"); break; default: throw "10" }break; case "6": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseFixedLengthMeasure("360", fourthNumber, "NET VOLUME (q)", "QT"); break; case "1": parseFixedLengthMeasure("361", fourthNumber, "NET VOLUME (g)", "GLL"); break; case "2": parseFixedLengthMeasure("362", fourthNumber, "VOLUME (q), log", "QT"); break; case "3": parseFixedLengthMeasure("363", fourthNumber, "VOLUME (g), log", "GLL"); break; case "4": parseFixedLengthMeasure("364", fourthNumber, "VOLUME (i3)", "INQ"); break; case "5": parseFixedLengthMeasure("365", fourthNumber, "VOLUME (f3)", "FTQ"); break; case "6": parseFixedLengthMeasure("366", fourthNumber, "VOLUME (y3)", "YDQ"); break; case "7": parseFixedLengthMeasure("367", fourthNumber, "VOLUME (i3), log", "INQ"); break; case "8": parseFixedLengthMeasure("368", fourthNumber, "VOLUME (f3), log", "FTQ"); break; case "9": parseFixedLengthMeasure("369", fourthNumber, "VOLUME (y3), log", "YDQ"); break; default: throw "11" }break; case "7": parseVariableLength("37", "COUNT"); break; case "9": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": parseVariableLengthMeasure("390", fourthNumber, "AMOUNT", ""); break; case "1": parseVariableLengthWithISONumbers("391", fourthNumber, "AMOUNT"); break; case "2": parseVariableLengthMeasure("392", fourthNumber, "PRICE", ""); break; case "3": parseVariableLengthWithISONumbers("393", fourthNumber, "PRICE"); break; default: throw "12" }break; default: throw "13" }break; case "4": switch (secondNumber) { case "0": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("400", "ORDER NUMBER"); break; case "1": parseVariableLength("401", "GINC"); break; case "2": parseVariableLength("402", "GSIN"); break; case "3": parseVariableLength("403", "ROUTE"); break; default: throw "14" }break; case "1": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseFixedLength("410", "SHIP TO LOC", 13); break; case "1": parseFixedLength("411", "BILL TO", 13); break; case "2": parseFixedLength("412", "PURCHASE FROM", 13); break; case "3": parseFixedLength("413", "SHIP FOR LOC", 13); break; default: throw "15" }break; case "2": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("420", "SHIP TO POST"); break; case "1": parseVariableLengthWithISOChars("421", "SHIP TO POST"); break; case "2": parseFixedLength("422", "ORIGIN", 3); break; case "3": parseVariableLength("423", "COUNTRY - INITIAL PROCESS."); break; case "4": parseFixedLength("424", "COUNTRY - PROCESS.", 3); break; case "5": parseFixedLength("425", "COUNTRY - DISASSEMBLY", 3); break; case "6": parseFixedLength("426", "COUNTRY – FULL PROCESS", 3); break; case "7": parseVariableLength("427", "ORIGIN SUBDIVISION"); break; default: throw "16" }break; default: throw "17" }break; case "7": switch (secondNumber) { case "0": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": switch (fourthNumber) { case "1": parseVariableLength("7001", "NSN"); break; case "2": parseVariableLength("7002", "MEAT CUT"); break; case "3": parseVariableLength("7003", "EXPIRY TIME"); break; case "4": parseVariableLength("7004", "ACTIVE POTENCY"); break; default: throw "18" }break; case "3": parseVariableLengthWithISOChars("703" + fourthNumber, "PROCESSOR # " + fourthNumber); break; default: throw "19" }break; case "1": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("710", "NHRN PZN"); break; case "1": parseVariableLength("711", "NHRN CIP"); break; case "2": parseVariableLength("712", "NHRN CN"); break; case "3": parseVariableLength("713", "NHRN DRN"); break; default: throw "20" }break; default: throw "21" }break; case "8": switch (secondNumber) { case "0": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": switch (fourthNumber) { case "1": parseVariableLength("8001", "DIMENSIONS"); break; case "2": parseVariableLength("8002", "CMT No"); break; case "3": parseVariableLength("8003", "GRAI"); break; case "4": parseVariableLength("8004", "GIAI"); break; case "5": parseVariableLength("8005", "PRICE PER UNIT"); break; case "6": parseVariableLength("8006", "GCTIN"); break; case "7": parseVariableLength("8007", "IBAN"); break; case "8": parseVariableLength("8008", "PROD TIME"); break; default: throw "22" }break; case "1": switch (fourthNumber) { case "0": parseVariableLength("8010", "CPID"); break; case "1": parseVariableLength("8011", "CPID SERIAL"); break; case "7": parseVariableLength("8017", "GSRN - PROVIDER"); break; case "8": parseVariableLength("8018", "GSRN - RECIPIENT"); break; case "9": parseVariableLength("8019", "SRIN"); break; default: throw "23" }break; case "2": switch (fourthNumber) { case "0": parseVariableLength("8020", "REF No"); break; default: throw "24" }break; default: throw "25" }break; case "1": thirdNumber = codestring.slice(2, 3); fourthNumber = codestring.slice(3, 4); switch (thirdNumber) { case "0": switch (fourthNumber) { case "0": parseVariableLength("8100", "-"); break; case "1": parseVariableLength("8101", "-"); break; case "2": parseVariableLength("8102", "-"); break; default: throw "26" }break; case "1": switch (fourthNumber) { case "0": parseVariableLength("8110", "-"); break; default: throw "27" }break; default: throw "28" }break; case "2": thirdNumber = codestring.slice(2, 3); switch (thirdNumber) { case "0": parseVariableLength("8200", "PRODUCT URL"); break; default: throw "29" }break; default: throw "30" }break; case "9": switch (secondNumber) { case "0": parseVariableLength("90", "INTERNAL"); break; case "1": parseVariableLength("91", "INTERNAL"); break; case "2": parseVariableLength("92", "INTERNAL"); break; case "3": parseVariableLength("93", "INTERNAL"); break; case "4": parseVariableLength("94", "INTERNAL"); break; case "5": parseVariableLength("95", "INTERNAL"); break; case "6": parseVariableLength("96", "INTERNAL"); break; case "7": parseVariableLength("97", "INTERNAL"); break; case "8": parseVariableLength("98", "INTERNAL"); break; case "9": parseVariableLength("99", "INTERNAL"); break; default: throw "31" }break; default: throw "32" }return { element: elementToReturn, codestring: cleanCodestring(codestringToReturn) } } switch (symbologyIdentifier) { case "]C1": answer.codeName = "GS1-128"; restOfBarcode = barcode.slice(3, barcodelength); break; case "]e0": answer.codeName = "GS1 DataBar"; restOfBarcode = barcode.slice(3, barcodelength); break; case "]e1": answer.codeName = "GS1 Composite"; restOfBarcode = barcode.slice(3, barcodelength); break; case "]e2": answer.codeName = "GS1 Composite"; restOfBarcode = barcode.slice(3, barcodelength); break; case "]d2": answer.codeName = "GS1 DataMatrix"; restOfBarcode = barcode.slice(3, barcodelength); break; case "]Q3": answer.codeName = "GS1 QR Code"; restOfBarcode = barcode.slice(3, barcodelength); break; default: answer.codeName = ""; restOfBarcode = barcode; break }answer.parsedCodeItems = []; while (restOfBarcode.length > 0) { try { firstElement = identifyAI(restOfBarcode); restOfBarcode = firstElement.codestring; answer.parsedCodeItems.push(firstElement.element) } catch (e) { switch (e) { case "01": throw "invalid AI after '0'"; case "02": throw "invalid AI after '1'"; case "03": throw "invalid AI after '24'"; case "04": throw "invalid AI after '25'"; case "05": throw "invalid AI after '2'"; case "06": throw "invalid AI after '31'"; case "07": throw "invalid AI after '32'"; case "08": throw "invalid AI after '33'"; case "09": throw "invalid AI after '34'"; case "10": throw "invalid AI after '35'"; case "11": throw "invalid AI after '36'"; case "12": throw "invalid AI after '39'"; case "13": throw "invalid AI after '3'"; case "14": throw "invalid AI after '40'"; case "15": throw "invalid AI after '41'"; case "16": throw "invalid AI after '42'"; case "17": throw "invalid AI after '4'"; case "18": throw "invalid AI after '700'"; case "19": throw "invalid AI after '70'"; case "20": throw "invalid AI after '71'"; case "21": throw "invalid AI after '7'"; case "22": throw "invalid AI after '800'"; case "23": throw "invalid AI after '801'"; case "24": throw "invalid AI after '802'"; case "25": throw "invalid AI after '80'"; case "26": throw "invalid AI after '810'"; case "27": throw "invalid AI after '811'"; case "28": throw "invalid AI after '81'"; case "29": throw "invalid AI after '82'"; case "30": throw "invalid AI after '8'"; case "31": throw "invalid AI after '9'"; case "32": throw "no valid AI"; case "33": throw "invalid year in date"; case "34": throw "invalid month in date"; case "35": throw "invalid day in date"; case "36": throw "invalid number"; default: throw "unknown error" } } } return answer } return parseBarcode }();