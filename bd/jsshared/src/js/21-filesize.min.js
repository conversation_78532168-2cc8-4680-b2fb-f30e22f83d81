/*
 2017 
 @version 3.5.4
 */
"use strict";!function(a){function b(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},f=[],g=0,h=void 0,i=void 0,j=void 0,k=void 0,l=void 0,m=void 0,n=void 0,o=void 0,p=void 0,q=void 0,r=void 0,s=void 0,t=void 0,u=void 0;if(isNaN(a))throw new Error("Invalid arguments");return j=b.bits===!0,r=b.unix===!0,i=b.base||2,q=void 0!==b.round?b.round:r?1:2,s=void 0!==b.spacer?b.spacer:r?"":" ",u=b.symbols||b.suffixes||{},t=2===i?b.standard||"jedec":"jedec",p=b.output||"string",l=b.fullform===!0,m=b.fullforms instanceof Array?b.fullforms:[],h=void 0!==b.exponent?b.exponent:-1,o=Number(a),n=o<0,k=i>2?1e3:1024,n&&(o=-o),0===o?(h=0,f[0]=0,f[1]=r?"":j?"b":"B"):((h===-1||isNaN(h))&&(h=Math.floor(Math.log(o)/Math.log(k)),h<0&&(h=0)),h>8&&(h=8),g=o/(2===i?Math.pow(2,10*h):Math.pow(1e3,h)),j&&(g*=8,g>=k&&h<8&&(g/=k,h++)),f[0]=Number(g.toFixed(h>0?q:0)),f[1]=10===i&&1===h?j?"kb":"kB":d[t][j?"bits":"bytes"][h],r&&(f[1]="jedec"===t?f[1].charAt(0):h>0?f[1].replace(/B$/,""):f[1],c.test(f[1])&&(f[0]=Math.floor(f[0]),f[1]=""))),n&&(f[0]=-f[0]),f[1]=u[f[1]]||f[1],"array"===p?f:"exponent"===p?h:"object"===p?{value:f[0],suffix:f[1],symbol:f[1]}:(l&&(f[1]=m[h]?m[h]:e[t][h]+(j?"bit":"byte")+(1===f[0]?"":"s")),f.join(s))}var c=/^(b|B)$/,d={iec:{bits:["b","Kib","Mib","Gib","Tib","Pib","Eib","Zib","Yib"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["b","Kb","Mb","Gb","Tb","Pb","Eb","Zb","Yb"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},e={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]};b.partial=function(a){return function(c){return b(c,a)}},"undefined"!=typeof exports?module.exports=b:"function"==typeof define&&define.amd?define(function(){return b}):a.filesize=b}("undefined"!=typeof window?window:global);
//# sourceMappingURL=filesize.min.js.map