@font-face {
	font-family: 'icomoon';
	src:url('/homebase/public/fonts/icomoon.eot?-ijy6ld');
	src:url('/homebase/public/fonts/icomoon.eot?#iefix-ijy6ld') format('embedded-opentype'),
		url('/homebase/public/fonts/icomoon.woff?-ijy6ld') format('woff'),
		url('/homebase/public/fonts/icomoon.ttf?-ijy6ld') format('truetype'),
		url('/homebase/public/fonts/icomoon.svg?-ijy6ld#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
	font-family: 'icomoon';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-Supply:before {
	content: "\e900";
}
.icon-Wound:before {
	content: "\e938";
}
.icon-Add:before {
	content: "\e901";
}
.icon-Allergy:before {
	content: "\e902";
}
.icon-Analytics:before {
	content: "\e903";
}
.icon-Appointment:before {
	content: "\e904";
}
.icon-Approve:before {
	content: "\e905";
}
.icon-Archive {
	line-height:16px;
	&:before {
		font-family: 'FontAwesome';
		content: @fa-var-minus-square;
	}
}
.icon-UnArchive {
	line-height:16px;
	&:before {
		font-family: 'FontAwesome';
		content: @fa-var-plus-square;
	}
}
.icon-Cancel:before {
	content: "\e906";
}
.icon-Care_Plan:before {
	content: "\e907";
}
.icon-Close:before {
	content: "\e908";
}
.icon-Collapse:before {
	content: "\e909";
}
.icon-Conversation:before {
	content: "\e932";
}
.icon-Dashboard:before {
	content: "\e90a";
}
.icon-Decline:before {
	content: "\e90b";
}
.icon-Delete:before {
	content: "\e90c";
}
.icon-Demographics:before {
	content: "\e90d";
}
.icon-Diff:before {
	content: "\e90e";
}
.icon-Draw:before {
	content: "\e90f";
}
.icon-Edit:before {
	content: "\e910";
}
.icon-Encounter:before {
	content: "\e911";
}
.icon-Expand:before {
	content: "\e912";
}
.icon-History:before {
	content: "\e914";
}
.icon-Initial_Assessment:before {
	content: "\e915";
}
.icon-Insurance:before {
	content: "\e916";
}
.icon-Intake:before {
	content: "\e917";
}
.icon-Intervention:before {
	content: "\e918";
}
.icon-Late:before {
	content: "\e919";
}
.icon-Letter:before {
	content: "\e91a";
}
.icon-List:before {
	content: "\e91b";
}
.icon-Locked:before {
	content: "\e91c";
}
.icon-Manage:before {
	content: "\e91d";
}
.icon-Medication:before {
	content: "\e91e";
}
.icon-Missing:before {
	content: "\e91f";
}
.icon-Nutrition:before {
	content: "\e920";
}
.icon-OK:before {
	content: "\e921";
}
.icon-Ongoing:before {
	content: "\e922";
}
.icon-Open_Form:before {
	content: "\e923";
}
.icon-Open:before {
	content: "\e924";
}
.icon-Pain:before {
	content: "\e925";
}
.icon-PAO:before {
	content: "\e926";
}
.icon-Patients:before {
	content: "\e927";
}
.icon-POT:before {
	content: "\e928";
}
.icon-Prefill:before {
	content: "\e929";
}
.icon-Print:before {
	content: "\e92a";
}
.icon-Referral:before {
	content: "\e917";
}
.icon-Refresh:before {
	content: "\e92b";
}
.icon-Reports:before {
	content: "\e92c";
}
.icon-Results:before {
	content: "\e92d";
}
.icon-Safety:before {
	content: "\e92e";
}
.icon-Save:before {
	content: "\e92f";
}
.icon-Search_Flipped:before {
	content: "\e930";
}
.icon-Search:before {
	content: "\e931";
}
.icon-Self_Report:before {
	content: "\e932";
}
.icon-Snapshot:before {
	content: "\e933";
}
.icon-Sync:before {
	content: "\e934";
}
.icon-Teaching:before {
	content: "\e935";
}
.icon-Unlocked:before {
	content: "\e936";
}
.icon-Verify:before {
	content: "\e937";
}
.icon-Warning:before {
	content: "\e937";
}
.icon-Fall:before {
	content: "\e913";
}

.icon-Dashboard_Alert:before {
	content: "\e911";
}
.icon-Dashboard_Approval:before {
	content: "\e905";
}
.icon-Dashboard_Comm:before {
	content: "\e932";
}

.icon-Logout:before {
	content: "\e90c";
}
.icon-Progress_Notes:before {
	content: "\e911";
}
.icon-Attachment:before {
	content: "\e912";
}
