/* CM:2019-06-30 #//TODO - nabeel please make this work for mobile */

@msgHeight: 200px;

.dep-screen-height(@styles) {
	@media (max-height: (@msgHeight * 3)) {
		@styles();
	}
}

.setDropMenuHeight(@minHeight) {
	@media (min-height: @minHeight) {
		#application.mobile {
			.dropmenu {
				height: @minHeight - @navbarHeaderHeight;
				height: calc(~"100% - "@navbarHeaderHeight);
			}
		}
	}
}

.setMobileStyles(@dropMenuWidth, @dropMenuBorderWidth, @tabIconWidth,
	@bodyFont, @menuFont, @subMenuFont, @labelFont, @inputFont, @noteFont) {

	@media (max-width: @screen-xs-max) {
		.select2-search {
			input {
				font-size: @inputFont;
			}
		}

		.select2-results {
			.select2-result-label {
				font-size: @bodyFont;
			}
		}
	}

	#application.mobile {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		font-size: @bodyFont;

		.initload,
		.formload,
		.save,
		.cancel {
			font-size: @bodyFont;
		}

		.dropmenu {
			margin-top: 0;
			padding-top: 0;
			width: @dropMenuWidth;
			top: @navbarHeaderHeight;
			border: @dropMenuBorderWidth solid #000;
			border-left-width: 0;
			box-shadow: 0 4px 12px #000;
			overflow: auto;
			overflow-x: hidden;
			overflow-y: auto;
			z-index: 99990;
			display: none;
		}

		#navigation {
			margin: 0;
			padding: 0;
			left: 0;
			top: 0;
			bottom: 0;
			height: inherit;
			background: transparent;
			overflow: inherit;
			z-index: 19999;

			.brand {
				display: none;
			}

			#nav_showmenu {
				position: absolute;
				display: block;
				top: 11px;
				left: 0;
				padding: 0 12px;
				color: @gray;
				font-size: 24px;

				i {
					margin-right: 12px;
				}

				i:last-child {
					margin-right: 0;
				}
			}

			#nav_dropmenu {
				position: absolute;
				margin-top: 0;
				padding-top: 0;
				width: @dropMenuWidth;
				top: @navbarHeaderHeight;
				border: @dropMenuBorderWidth solid #000;
				border-left-width: 0;
				box-shadow: 0 4px 12px #000;
				overflow: auto;
				overflow-x: hidden;
				overflow-y: auto;
				z-index: 99990;
				display: block;
				background: @navbarBackground;
				left: 0;
				opacity: 0.95;
				font-size: @menuFont;

				#nav_menu {

					#nav_home,
					#nav_home_list,
					#nav_allergy,
					#nav_medication {
						display: none;
					}

					>li {
						>a {
							font-size: @hamburgerFont;
							white-space: nowrap;

							span.badgemark {
								// display:none;
							}
						}
					}
				}

				.nav_submenu {
					li a {
						span.badgemark {
							// display:none;
						}
					}
				}

				.glyphicon-remove {
					display: none;
				}

				#nav_logout {
					margin-top: 21px;
				}

				#nav_footer {
					position: relative;
					text-align: center;
					padding: 12px 0;

					#helpdesk {
						display: none;
					}
				}

			}
		}

		.cardbar {
			.formerror {
				width: 90%;
			}
		}

		.cardarea {
			h3 {
				font-size: @menuFont;
			}

			h4 {
				font-size: @subMenuFont;
			}

			.sectionnote,
			.groupnote {
				font-size: @noteFont;
			}

			.btn {
				font-size: @inputFont;
				padding: 6px 10px;
			}

			.fileupload {
				.btn {
					width: initial;
					padding: 0 10px;
				}

				// @media not all and (min-resolution:.001dpcm) {
				// 	.btn {
				// 		margin-left:0;
				// 		margin-top:-40px;
				// 	}
				// }
			}

			.fieldlink {
				font-size: @menuFont;
			}

			.form-horizontal {
				.manage {
					display: none;
				}

				.form-group {
					.note {
						font-size: @noteFont;

						b {
							font-size: @noteFont - 2;
						}
					}

					>label {
						text-align: left;
						font-size: @labelFont;
					}

					.controls {
						.select2-container {
							.select2-choice {
								height: 40px;
								padding: 6px;

								.select2-arrow {
									b {
										background-size: 60px 60px !important;
									}
								}
							}

							.select2-choice {
								abbr {
									top: 13px;
								}
							}
						}

						>.checkboxes {
							label.checkbox {
								padding-left: 0;
							}
						}
					}

					input,
					select,
					textarea {
						font-size: @inputFont;
						padding: @padding-5-10;
						border: 2px solid @formFieldBorderColor;
						border-radius: @formFieldBorderRadius;
					}
				}
			}
		}

		.tabhead {
			left: 0;
			padding-left: 32px;

			.tabicons {
				position: fixed;
				margin-right: 0;

				.tabicon {
					width: @tabIconWidth;
				}
			}
		}

		.tabarea {
			min-height: 100%;
			background: #fff;

			.cardform,
			.cardread {
				margin-right: 0;

				.cardmenu {
					li {
						overflow: hidden;
						white-space: nowrap;

						&.grouplabel {
							padding-left: 14px;
						}

						a {
							padding-left: 44px;
						}
					}
				}

				.fieldgroup {
					h4 {
						i.scrolltop {
							margin-left: -8px;
							margin-right: 6px;
							color: #ddd;
							top: 2px;
							cursor: pointer;
						}
					}
				}
			}
		}

		.sidemenu {
			border-left-width: @dropMenuBorderWidth;
			left: @dropMenuWidth;
			z-index: 99991;

			div.sidemenugroup {
				font-weight: bold;
				border-bottom: 0;
				cursor: default;
			}

			li {
				a {
					line-height: 35px !important;
					height: 30.6px !important;
					width: 100%;

					&:before,
					&:after {
						display: none !important;
					}

					i {
						margin-top: 8px;
					}
				}
			}
		}

		.sidearea {
			margin-left: 0;

			.cardmenu {
				left: (@dropMenuWidth * 2);
			}
		}

		.oneitem {
			.cardmenu {
				display: none !important;
			}
		}

		// form card menu
		.cardmenu {
			left: @dropMenuWidth;
			width: @dropMenuWidth + 40;
			box-shadow: 4px 4px 12px #000;
			z-index: 99992;

			li {
				height: 30.6px;
				line-height: 25px;
				border-bottom: 1px solid #DDD;

				a {
					font-size: 14px !important;
					padding-left: 32px !important;

					.sec-err {
						top: 2px !important;
					}
				}

				&.active {
					background: none !important;

					a {
						color: #666 !important;
					}
				}
			}

			.grouplabel {
				height: 18px !important;
				line-height: 12px !important;
				padding: 3px 0 3px 4px !important;
				background: #DDD;
				color: #555 !important;
			}
		}

		>#sections {
			height: 100%;
			min-height: 100%;
			margin-left: 0;

			#conversation {
				.navtabitem {
					left: 0;
				}
			}

			#comm {
				height: 100%;
				min-height: 100%;

				.messages {
					padding-bottom: @msgHeight + 80;

					.dep-screen-height({
						padding-bottom:(@msgHeight / 2) + 130;
					});

				.message {

					.sender,
					.sentto,
					.reply {
						font-size: @noteFont;
					}
				}
			}

			.newmessage {
				position: fixed;
				left: 0;
				right: 0;
				bottom: 0;

				.content {
					padding: 4px 0 8px 20px;

					.formhead {
						font-size: @bodyFont;
					}

					.row {
						margin-left: -30px;
						margin-right: -27px;
					}

					.usermenu {
						.dep-screen-height({
							height:@msgHeight / 2;
						});

					li {
						padding: 4px 4px 0 6px;

						.control--checkbox {
							margin-left: 26px;
						}

						.control__indicator {
							margin-left: -26px;
							margin-right: 0;
						}
					}
				}

				.msgtext {
					textarea {
						.dep-screen-height({
							height:@msgHeight / 2;
						});
				}
			}

			.alert {
				margin: 4px 0 8px;
				text-align: center;
			}

			.save {
				margin-right: 14px;
				width: 180px;
			}
		}
	}

}

#dashboard {
	.navtabitem {
		left: 0;
		overflow: scroll;
	}
}

#education {
	height: 100%;
	min-height: 100%;

	.no-ed-msg {
		padding: 40px;
		text-align: center;
	}
}

#schedule {
	.fc-left {
		margin-left: 32px;
	}
}
}

#flyout {
	&:not(:empty) {
		bottom: 0;
		zoom: 70%;
	}

	.blocker {
		height: 51px;
		background: #666;
	}

	.form {
		position: absolute;
		overflow: inherit;
		overflow-y: inherit;
		left: 0;

		.tabhead {
			left: 0;
			padding-left: 0;

			.tabicons {
				margin-right: 0;
			}
		}

	}
}
}

#application.mobile.collapsed {
	>#sections {
		margin-left: 0;

		.tabhead {
			left: 0;

			.tabicons {
				margin-right: 0;
			}
		}
	}

	#navigation {
		bottom: auto;

		#nav_dropmenu {
			display: none;
		}
	}
}

@media (max-width: 639px) and (orientation:portrait) {
	#application.mobile {
		>#sections {
			#comm {
				.newmessage {
					.content {
						.row {
							margin-right: -12px;
						}

						.usermenu {
							height: 120px;
						}

						.msgtext {
							padding: 0;

							textarea {
								margin-top: 8px;
								height: 36px;
							}
						}

						.pull-left {
							float: none !important;

							.alert {}
						}

						.save {
							margin-right: 0;
						}
					}
				}
			}
		}
	}
}

.setDropMenuHeight(320px);
.setDropMenuHeight(400px);
.setDropMenuHeight(480px);
.setDropMenuHeight(600px);
.setDropMenuHeight(640px);
@media (min-height: 720px) {
	#application.mobile {
		.dropmenu {
			height: 720 - @navbarHeaderHeight;
		}
	}
}

}

.new_convo {
	li.select2-search-choice {
		font-size: 10px;
	}
}

.modal-header-custom {
	text-align: center;
	background-color: #068CCF;
	color: white;
}

#s2id_select-channel-dynamic {
	width: 200px;
}

// menu ui
@dropMenuWidth: 250px;
@dropMenuBorderWidth: 3px;

// tab ui
@tabIconWidth: 92px;

// font sizes
@bodyFont: 16px;
@menuFont: 20px;
@subMenuFont: 18px;
@labelFont: 20px;
@inputFont: 20px;
@noteFont: 14px;
@hamburgerFont: 16px;