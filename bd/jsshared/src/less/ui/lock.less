body {
	> span#ui-lock-dialog {
		display:none;
	}
}

body.ui-lock-enable {
	> div {
		display:none !important;
	}

	> span#ui-lock-dialog {
		@marginTop:200px;
		position: relative;
		display:block;
		margin-top:@marginTop;
		width:100%;

		.brand {
			background-color: @highlightBackground;
			position:absolute;
			margin-top:-@marginTop;
			top:0;
			left:0;
			right:0;

			.navhead {
				text-align:center;
				img, p {
					display:none;
				}

				img:first-child {
					display:inline;
					margin:16px 0;
				}

				.icon-NavState {
					display:none;
				}
			}

		}

		h3 {
			color:@primaryColor;
			font-size:32px;
		}

		.username {
			margin-top:24px;
			margin-bottom:12px;
		}

		.errormsg {
			margin-top:12px;
			margin-bottom:16px;
			font-weight:bold;
			color: @warningColor;
		}
	}
}

#mobile-lock-screen {
	#ui-lock-dialog {
		.brand {
			margin-top:0px;
			top:-200px;
			.navhead {
				img:first-child {
					display:inline;
					margin:15px -16px !important;
				}
			}
		}
		.row {
			margin-top:140px;
			.col-xs-offset-4 {
				margin-left: 2.333333%;
				top: -290px;
			}
			.col-xs-4 {
				width: 94.333333%;
				top: -290px;
			}
			.form-login {
				h3 {
					font-size: 24px;
					text-align: center;
				}
				h4 {
					font-size: 12px;
					margin-top: 10px;
					margin-bottom: 10px;
					text-align: center;

				}
				hr {
					margin-top: 0px;
					margin-bottom: -12px;
				}
				span {
					.errormsg {
						margin-top: 0px;
						margin-bottom: 0px;
					}
				}
			}
		}
	}	
}

@media screen and (min-width: 300px) and (max-width: 830px) {
	#mobile-lock-screen;
}
