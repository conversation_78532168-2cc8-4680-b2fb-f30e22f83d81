#application {

	// tab-layout
	.tabhead {
		height:@navbarHeaderHeight + 1;
		width:100%;
		z-index:10000;
		left:@navbarWidth;
		background:#fff;
		border-bottom:1px solid @elementBorderColor;
		.flex-display();
		.flex-direction();
		.flex-wrap();
		h1, h2 {
			.flex(1 100%);
			display:inline-block;
			font-family: @font-family-headers;
			margin:0;
			line-height:@navbarHeaderHeight;
			font-weight:bold;
			white-space:nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
		.pt-tag {
			padding:5px 8px 5px 8px;
			margin:4px;
			color:white;
			border-radius: 4px;
			white-space:nowrap;
			font-size:12px;
			vertical-align:top;
		}
		h1 {
			.flex-max-fit();
			padding:0 0 0 16px;
			font-size:20px;
			color: @gray-dark;
			text-transform: uppercase;
		}
		h2 {
			flex-basis: 0px;
			text-overflow:ellipsis;
			overflow:hidden;
			padding:1px 0 0 16px;
			font-size:14px;
			color: @brand-info;
		}
		.tabmenu {
			position: relative;
			left:160px;
			display:inline-block;
			font-family: @font-family-headers;
			margin:0;
			line-height:@navbarHeaderHeight;
			font-weight:bold;
			font-size:14px;
			color: #ccc;
			span {
				color: #777;
				cursor:pointer;
				padding:0 8px;
				&.active {
					color: @brand-info;
				}
			}
		}
		.tabicons {
			visibility: hidden !important;
			position:relative;
			.flex-min-fit();
			top:0;
			right:0;
			margin-right:@navbarWidth;
			.tabicon {
				.user-select(none);
				text-align:center;
				width:@navbarButtonWidth;
				font-size:13px;
				font-weight: bold;
				padding-top: 16px;
				height:@navbarHeaderHeight;
				cursor:pointer;
				float:right;
				color:#fff;
				span {
					top:5px;
				}
				p {
					display: inline-block;
					vertical-align: middle;
					text-transform: uppercase;
					margin: -5px 0px 0px 0px;
				}
				i {
					margin-left: 6px;
					font-size: 18px;
					font-weight: bold;
				}
			}
			.tabopen {
				background:@primaryColor;
				&:hover {
					background:@primaryColorHover;
				}
			}
			.tabreview {
				background:@secondaryColor;
				&:hover {
					background:@secondaryColorHover;
				}
			}
			.tabapprove {
				background:@secondaryColor;
				&:hover {
					background:@secondaryColorHover;
				}
			}
			.tabreject {
				background:@warningColor;
				&:hover {
					background:@warningColorHover;
				}
			}
			.tabverify {
				background:lighten(@quaternaryColor, 20%);
				&:hover {
					background:lighten(@quaternaryColorHover, 20%);
				}
			}
			.tabsave {
				background:@secondaryColor;
				&:hover {
					background:@secondaryColorHover;
				}
			}
			.tabcancel {
				background:@tertiaryColor;
				&:hover {
					background:darken(@tertiaryColor, 5%);
				}
			}
			.tabsync {
				background:lighten(@secondaryColor, 5%);
				&:hover {
					background:lighten(@secondaryColorHover, 5%);
				}
			}
			.tabletter {
				background:lighten(@quaternaryColor, 20%);
				&:hover {
					background:lighten(@quaternaryColorHover, 20%);
				}
			}
			.tabprint {
				background:@quaternaryColor;
				&:hover {
					background:@quaternaryColorHover;
				}
			}
			.tabedit {
				background:@primaryColor;
				&:hover {
					background:@primaryColorHover;
				}
			}
			.tablist {
				background:@secondaryColor;
				&:hover {
					background:@secondaryColorHover;
				}
			}
			.tabaddfill {
				background:@secondaryColor;
				&:hover {
					background:@secondaryColorHover;
				}
			}
			.tabadd {
				background:@primaryColor;
				&:hover {
					background:@primaryColorHover;
				}
			}
			.tabarchive {
				background:darken(@tertiaryColor, 25%);
				&:hover {
					background:darken(@tertiaryColor, 25%);
				}
				p {
					margin-top:-4px; /* using font-awesome icon, not icomoon */
				}
			}
			.tabunarchive {
				background:darken(@tertiaryColor, 25%);
				&:hover {
					background:darken(@tertiaryColor, 25%);
				}
				p {
					margin-top:-4px; /* using font-awesome icon, not icomoon */
				}
			}
			.tabrefresh {
				background:lighten(@quaternaryColor, 20%);
				&:hover {
					background:lighten(@quaternaryColorHover, 20%);
				}
			}
			.tabclose {
				background:@warningColor;
				&:hover {
					background:@warningColorHover;
				}
			}
			.tabpatient {
				background:lighten(@quaternaryColor, 25%);
				&:hover {
					background:lighten(@quaternaryColorHover, 25%);
				}
			}
		}
	}

	.tabarea {
		padding-top:@navbarHeaderHeight;
		background:@elementBackground;
	}

	// top-tab layout
	.tabbar {
		height:@topTabHeight;
		// border-bottom:0px solid #ccc;
		width:100%;
		padding-left:8px;
		background:@topTabBackground;

		> li {
			float:left;
			cursor:pointer;
			margin:8px 2px 0;
			padding:8px 10px 4px;
			background:#fff;
			white-space: nowrap;
			border:1px solid #ccc;
			border-radius:8px;
			border-bottom-left-radius:0;
			border-bottom-right-radius:0;
			border-bottom: 0px;

			&.active, &:hover {
				background:@topTabActiveColor;
				border:1px solid @topTabActiveColor;
				color:#fff;
			}

			.badge {
				float:none;
				margin:-1px -4px 0 6px;
				border-radius:20px;
				font-size:10px;
				min-width:20px;
				text-align:center;
				color:@badgeColor;
				background:@badgeBackground;
			}

		}
	}

	.tabinfo {
		position:absolute;
		font-size:16px;
		top:0;
		right:0;
		color:#777;
		.tabsrc {
			display:block;
			background:@topTabBackground;
			padding:12px 8px 6px;
		}
	}

	.mute-toggle{
		margin-top: 12px;
	}

	.switch {
		position: relative;
		display: inline-block;
		width: 45px;
		height: 25px;
	}
	
	.switch input { 
		opacity: 0;
		width: 0;
		height: 0;
	}
	
	.slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: .4s;
		transition: .4s;
	}
	
	.slider:before {
		position: absolute;
		content: "";
		height: 18px;
		width: 18px;
		left: 4px;
		bottom: 4px;
		background-color: white;
		-webkit-transition: .4s;
		transition: .4s;
	}
	
	input:checked + .slider {
		background-color: #c42d2d ;
	}
	
	input:focus + .slider {
		box-shadow: 0 0 1px #c42d2d ;
	}
	
	input:checked + .slider:before {
		-webkit-transform: translateX(18px);
		-ms-transform: translateX(18px);
		transform: translateX(18px);
	}
	
	.slider.round {
		border-radius: 34px;
	}
	
	.slider.round:before {
		border-radius: 50%;
	}

	//Comm switches
	.slider-search {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: .4s;
		transition: .4s;
	}
	
	.slider-search:before {
		position: absolute;
		content: "";
		height: 18px;
		width: 18px;
		left: 4px;
		bottom: 4px;
		background-color: white;
		-webkit-transition: .4s;
		transition: .4s;
	}
	
	input:checked + .slider-search {
		background-color: #249B73 ;
	}
	
	input:focus + .slider-search {
		box-shadow: 0 0 1px #249B73 ;
	}
	
	input:checked + .slider-search:before {
		-webkit-transform: translateX(18px);
		-ms-transform: translateX(18px);
		transform: translateX(18px);
	}
	
	.slider-search.round {
		border-radius: 34px;
	}
	
	.slider-search.round:before {
		border-radius: 50%;
	}

	.mute {
		margin-left:-1px;
		padding:8px 6px;
		font-size:16px;
		color:rgb(41, 41, 41);
		text-decoration:none;

		&:hover {
			border:1px solid #c42d2d !important;
			color:#c42d2d !important;
		}

		&.active {
			background:#c42d2d !important;
			border:1px solid #c42d2d;
			color:#fff;

			&:hover {
				color:#f8f8f8 !important;
			}
		}
	}

	.mute-members {
		margin-left:-1px;
		padding:8px 6px;
		color:#c42d2d;
		text-decoration:none;
	}
	.manage-alerts {
		margin-left:-1px;
		padding:8px 6px;
		color: #777;
		text-decoration:none;
	}
	.list-members {
		margin-left:-1px;
		padding:8px 6px;
		color:#058bcf;
		text-decoration:none;
	}
	.view {
		padding:8px 6px;
		text-decoration:none;
		color:#058bcf;
	}
	.mng {
		padding:8px 6px;
		text-decoration:none;
	}
	.muted {
		padding: 8px 6px !important;
		color:#c42d2d;
		text-decoration:none;
	}

	.tabbody {
		padding-top:@topTabHeight;
		max-height:100%;
		overflow:scroll;

		> div {
			display:none;
			font-size:24px;

			&.active {
				display:block;
			}
		}
	}

}

#application.collapsed {
	#sections {
		.tabhead {
			left:@navbarWidthCollapsed;
			.tabicons {
				margin-right:@navbarWidthCollapsed;
			}
		}
	}
}
