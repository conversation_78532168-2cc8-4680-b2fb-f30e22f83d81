#application {
  tr.cardarea.cardform {
    margin-right: 0px;

    .form-horizontal {
      .form-group {
        margin: unset !important;
        margin: unset !important;

        .has-error {
          padding-top: 0px !important;
        }
      }
    }

    .help-block {
      margin: 0px;

      &.errormsg {
        font-size: var(--form-fs-smd);

        &::before {
          font-size: var(--form-fs-sm) !important;
        }
      }
    }
  }

  .module-area.collapse {
    .cardmenu {
      // left: - @nav-bar-width;
    }
  }

  @cardmenuBorder: @border-button-color;

  // title
  .cardlist {
    .formtitle {
      margin: 0;
      padding: var(--cardlist-formtitle-p);
      font-size: var(--form-fs-xl);
    }
  }


  .form-container {

    .form-card-menu {
      min-width: @cardmenuWidthMin;
      width: var(--form-card-menu-w);
      max-width: @cardmenuWidthMax;
      height: auto;

      .section-menu-header {
        .card-two;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
        padding: var(--section-menu-header-p);
        margin-bottom: var(--section-menu-header-mb);
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .para-two;
        color: @black;

        span {
          margin-left: var(--section-menu-header-span-ml);
        }
      }

      .cardmenu {
        width: 100%;
        position: relative;
        height: auto;
        padding: var(--card-menu-p);
        overflow: auto;
        overflow-x: hidden;
        margin-top: 0;
        background: @cardMenuBackground;
        .card-two;
        border-top-right-radius: 0;
        border-top-left-radius: 0;
        flex: 1;
        margin-bottom: 0;

        li {
          list-style-type: none;
          cursor: pointer;

          a {
            cursor: inherit;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 0;
            text-indent: 0px;
            text-decoration: none;
            color: @black;
            .para-two;
            font-weight: 500;

            .sec-err,
            .sec-val {
              visibility: hidden;
              width: 12px;
            }

            span {
              margin-left: 0px;
              margin-right: var(--card-menu-li-span-mr);
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: var(--card-menu-li-span-g);

              i {
                width: 18px;
                height: 100%;
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
              }

              i.sec-val {
                background-image: url('../img/menu-icon.svg');
                margin-left: var(--card-menu-li-i-sec-val-ml);
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                margin-top: var(--card-menu-li-i-sec-val-mt);
              }

              &.sec-err-false {
                .sec-err {
                  margin-left: var(--card-menu-li-sec-err-false-ml);
                  margin-right: var(--card-menu-li-sec-err-false-ml);
                  visibility: visible;
                  .glyphicon-ok;
                  top: 0;
                  border-radius: 16px;
                  color: #fff;
                  padding: var(--card-menu-li-sec-err-false-p);
                  font-size: var(--form-fs-xl);

                  &::before {
                    margin-left: var(--card-menu-li-sec-err-false-before-ml);
                  }
                }
              }

              &.sec-err-true {
                .sec-val-right {
                  visibility: visible;
                  color: @warningColor;
                  top: var(--card-menu-li-sec-err-true-sec-tmp-vr);
                  background-image: url("../img/error-alert-icon.svg");
                }
              }

              &.sec-val-false {
                &.sec-err-false {
                  .sec-val-right {
                    background-image: url("../img/icon-filled-done.svg");
                    margin: 0;
                    background-color: none;

                    &::before {
                      margin: 0;
                    }
                  }
                }

                .sec-val {
                  visibility: visible;
                  color: darken(#aaa, 50%);
                }
              }

              &.sec-val-true {
                .sec-val-right {
                  background-image: url("../img/icon-filled-done.svg");
                  background-color: transparent;
                  vertical-align: middle;
                  visibility: visible;
                }

                .sec-val {
                  visibility: visible;
                  color: @purple;

                  &::before {
                    color: #1ab3ce;
                  }
                }
              }
            }

          }

          &.active {
            background: transparent;
            cursor: default;

            a {
              color: @purple;
              font-weight: 600;

              span {

                &.sec-val-false,
                &.sec-val-true {
                  i.sec-val {
                    background-image: url('../img/menu-icon-active.svg');
                  }
                }
              }
            }
          }

          &.first-item {
            a {
              span {

                &.sec-val-false,
                &.sec-val-true {
                  i.sec-val {
                    background-image: url('../img/menu-first-icon.svg');
                  }
                }
              }
            }
          }

          &.last-item {
            a {
              span {

                &.sec-val-false,
                &.sec-val-true {
                  i.sec-val {
                    background-image: url('../img/menu-last-icon.svg');
                  }
                }
              }
            }
          }

          &.active {
            &.first-item {
              a {
                span {

                  &.sec-val-false,
                  &.sec-val-true {
                    i.sec-val {
                      background-image: url('../img/menu-first-icon-active.svg');
                    }
                  }
                }
              }
            }
          }

          &.active {
            &.last-item {
              a {
                span {

                  &.sec-val-false,
                  &.sec-val-true {
                    i.sec-val {
                      background-image: url('../img/menu-last-icon-active.svg');
                    }
                  }
                }
              }
            }
          }


          &.single-item {

            a {
              span {

                &.sec-val-false,
                &.sec-val-true {
                  i.sec-val {
                    background-image: url('../img/menu-single-icon.svg');
                    background-size: 4px;
                  }
                }
              }
            }
          }

          &.active {
            &.single-item {
              a {
                span {

                  &.sec-val-false,
                  &.sec-val-true {
                    i.sec-val {
                      background-image: url('../img/menu-single-icon-active.svg');
                      background-size: 6px;
                    }
                  }
                }
              }
            }
          }

        }

        .grouplabel {
          padding: 8px 0 4px 8px;
          font-size: var(--form-fs-md);
          font-weight: 600;
          color: @black;
          text-transform: capitalize;
        }
      }
    }
  }

  .cardread {
    .cardbar {
      display: none;

      .reviewed,
      .statuschange {
        float: right;
        display: block;
        margin-right: var(--card-read-reviewed-status-change-bar-mr);
        margin-top: var(--card-read-reviewed-status-change-bar-mt);
        font-family: @font-family-headers;
        text-transform: uppercase;
        font-weight: bold;
        color: #a44;
      }

      .readonlymode {
        float: right;
        display: block;
        margin-right: var(--card-read-bar-readonly-mr);
        margin-top: var(--card-read-bar-readonly-mt);
        font-family: @font-family-headers;
        text-transform: uppercase;
        font-weight: bold;
      }
    }

    .cardmenu {
      li a {
        padding-left: var(--read-cardmenu-li-a-pl);
        text-indent: var(--read-cardmenu-li-a-text-indent);

        span {
          display: none;
        }
      }
    }



    .form-group {
      margin-bottom: var(--card-read-form-tmp-mode) !important;
    }

    .controls-grid {
      .control-label {
        margin-left: 0 !important;
        padding-left: 0 !important;
      }

      tbody tr td {
        font-size: var(--form-fs-md);
      }
    }

    input:not(.tab-toggle):not(.input-checkbox) {
      min-height: 0px !important;
    }

    .select2-choice {
      background-color: @transparent !important;
    }
  }

  .form-horizontal .form-group {
    &.filter_fields {
      opacity: 0.6;
    }

    &.prefilled {
      padding-top: var(--form-group-prefilled-pt);

      >label {
        background-color: @brown;
        padding-left: var(--form-group-prefilled-label-pl);
        padding-top: var(--form-group-prefilled-label-pt);
        padding-bottom: var(--form-group-prefilled-label-pb);
        border-radius: 5px;
      }
    }

    &.highlight,
    &.highlighted {
      padding-top: var(--form-group-highlighted-pt);
      margin-top: var(--form-group-highlighted-mt);
    }

    .controls {

      >input {
        &.label-directions {
          .dotted-field-style;
          min-height: 24px !important;
          padding: 0px;
        }
      }

      .fileupload,
      .fieldlink {
        >i.glyphicon-zoom-in {
          color: #888;
          padding-left: var(--controls-fieldlink-pl);
          cursor: zoom-in;
        }
      }

      .fileupload {
        display: flex;
        align-items: center;
      }

      .disable_links {
        span>a {
          color: #888;
          pointer-events: none;
        }
      }


      >input[type="color"],
      >input[type="color"][readonly],
      >input[type="color"][disabled] {
        width: 35px;
        height: 35px;
        padding: 0px;
      }

      .color-hex {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        height: 40px;
        padding-left: var(--controls-color-hex-pl);
        font-weight: 700;
        padding-top: var(--controls-color-hex-pt);
        text-shadow: 1px 1px 2px black;
      }

      .ace_editor {
        margin: 0;
        width: 1120px;
        height: 460px;
      }



      textarea.paycard {
        .textarea-paycard;
      }

      .form-control,
      .form-control:focus {
        border-color: @primaryColor;
        -webkit-box-shadow: none !important;
        box-shadow: none !important;
        -moz-box-shadow: none !important;
      }

      .form-control:focus+.input-group-addon {
        border-color: none;
        background-color: transparent;
        color: @charcoal-gray;
      }

      >.select2-container {
        padding: 0;
        width: 100%;
      }

      >.treefield {
        padding: 0;

        &.readonly {
          // background-color: @gray-lighter;
        }
      }

      >.radios {
        input {
          display: none;
        }
      }


      >.list {
        label.checkbox {
          padding-left: 0;

          span {
            color: @reddish !important;
          }

          input+span::before,
          input:disabled+span::before,
          input[disabled]+span::before,
          input.disabled+span::before {
            border: 0;
            margin-top: var(--checkbox-input-span-disabled-span-mt);
            color: @primaryColor !important;
            content: '';
            background-image: url("/homebase/public/img/arrows-collapse-right-active.svg");
            background-color: transparent !important;

          }

          input[disabled="disabled"]+span {
            color: #444;
          }
        }
      }
    }




    .controls-grid.readonly,
    .controls-grid.subform {
      margin: 0;

      table {

        th,
        td {
          word-break: break-all;
        }
      }

      .cardarea {
        height: auto;
      }
    }

    .showunit {
      .unit-show-unit;
    }

    &:has(.no-label) {
      >.control-label {
        color: transparent !important;
        display: none;
      }
    }

    &:has(.groups.middle) {
      & {
        border-top: 0;
        margin-bottom: 0 !important;
        border-bottom: 0;
      }
    }

    &:has(.groups.top) {
      & {
        margin-bottom: 0 !important;
        border-bottom: 0;
      }
    }

    &:has(.groups.bottom) {
      & {
        border-top: 0;
      }
    }
  }

  .form-horizontal {
    min-width: 10%;

    &.block-level {
      width: 100%;
    }

    .checkbox {
      padding: var(--fh-checkbox-p);
    }

  }

  .form-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    gap: var(--spacing-large);
    margin: var(--spacing-xxsmall) 0px;
    padding: var(--spacing-large);
  }

  .sectionnote,
  .groupnote {
    margin: var(--app-section-note-group-m);
    font-size: var(--form-fs-lmd);
    color: @gray-dark;
    clear: both;
  }

  .sectionnote,
  .groupnote {
    margin: 8px 0 0 0;
    padding-bottom: var(--app-group-note-pb);
    font-weight: var(--font-weight-medium);
    clear: both;
    font-size: var(--font-size-xxsmall);
    color: var(--color-text-400);
    width: 100%;
  }

  // loading
  .formload {
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    >i {
      font-size: 45px;
    }
  }

  .carddrop {
    padding-right: var(--spacing-xxxlarge);
    display: flex;
    font-size: var(--form-fs-lg);
    background-color: #D4D4D4;
    max-height: 46px;
    align-items: center;

    i {
      color: #aaa;
      cursor: pointer;
    }

    span {
      i {
        color: #aaa;
        cursor: pointer;
      }
    }
  }

  .cardbar {
    display: flex;
    flex-direction: column;
    align-items: center;
    .page-two;
    margin: 0;
    box-shadow: none;

    .formerror {
      width: 410px;
      text-align: center;
      display: none;
    }

    .form-buttons-bottom {
      display: flex;
      justify-content: center;
      gap: var(--cardbar-form-buttons-bottom-g);
    }
  }

  .btn.disabled {
    background: #f4f4f4 !important;
  }

}