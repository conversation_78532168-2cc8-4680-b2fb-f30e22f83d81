#application {

	@padright: 190px;
	@lineheight: 40px;

	// title
	.cardlist {
		.findbar {
			.formtitle {
				padding: 0 0 8px 0;
			}
		}
	}

	// find view
	.findbar {
		padding: @elementMargin;
		background: darken(@elementBackground, 5%);
		border-bottom: 1px solid @elementBorderColor;

		.findbarcontainer {
			background: #fff;
			border: 1px solid darken(@elementBorderColor, 5%);
			border-radius: @elementRounding;
			padding: 4px @padright+10 4px 0;
		}

		.findtypearea {
			position: absolute;
			right: @padright+38;
			font-size: 20px;
			font-weight: bold;
			line-height: @lineheight;
			margin-right: 16px;

			a {
				color: @brand-info;
				text-decoration: none;

				&:hover {
					color: darken(@brand-info, 10%);
				}

				i {
					vertical-align: middle;
					font-weight: bold;
				}
			}
		}

		.findbtn {
			line-height: @lineheight;
			margin-right: -@padright+10;
			margin-left: 8px;

			button {
				i {
					font-weight: bold;
					margin-left: 6px;
					vertical-align: middle;
				}

				p {
					display: inline-block;
					text-transform: uppercase;
					vertical-align: middle;
					margin: 0px;
				}
			}

			.find {
				margin-right: 10px;
			}
		}

		.findnote {
			font-size: 18px;
			line-height: @lineheight;
			color: @brand-info;
			margin-left: 18px;

			i {
				top: 3px;
				font-size: 16px;
				font-weight: bold;
				vertical-align: middle;
			}
		}

		.findtab {

			label {

				>span {
					.form-label-one;
				}

				input {
					.form-input-field;
				}
			}
		}
	}

	// find gap
	.findbarclear {
		height: 1px;
		margin-bottom: -2px;
	}

}