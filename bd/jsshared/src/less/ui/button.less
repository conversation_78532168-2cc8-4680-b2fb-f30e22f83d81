#application {

	.btn {
		.py-2;
		.px-4;

		p {
			font-weight: 500;
		}
	}

	.print,
	.edit,
	.more,
	.archive,
	.unarchive {
		background-color: white;
		font-size: 14px;
		font-family: @font-family-headers;
		font-weight: bold;
		text-transform: uppercase;
		text-align: left;
		padding: 6px 14px 4px 14px;
		width: 124px;
	}

	.print {
		&:after {
			font-family: 'FontAwesome';
			content: @fa-var-print;
			float: right;
			color: @brand-info;
		}

		&:hover,
		&:active,
		&:focus {

			background-color: @brand-info;
			border: 2px solid @brand-info !important;
			color: white;

			&:after {
				color: white;
			}
		}
	}

	.edit {
		&:after {
			font-family: 'FontAwesome';
			content: @fa-var-edit;
			float: right;
			color: @brand-success;
			padding-top: 2px;
		}

		&:hover,
		&:active,
		&:focus {

			background-color: @brand-success;
			border: 2px solid @brand-success !important;
			color: white;

			&:after {
				color: white;
			}
		}
	}

	.more,
	.archive,
	.unarchive {
		padding-bottom: 0px !important;
		padding: 3px 14px 3px 14px !important;

		&:after {
			font-family: 'FontAwesome';
			content: @fa-var-level-down;
			margin-left: 44px;
			color: @primaryColor;
		}

		&:hover,
		&:active,
		&:focus {

			background-color: @primaryColor;
			border: 2px solid @primaryColor !important;
			color: white;

			&:after {
				color: white;
			}
		}
	}

	.archive {
		&:after {
			content: @fa-var-minus-square;
			margin-left: 24px;
			color: @tertiaryColor;
		}

		&:hover,
		&:active,
		&:focus {
			background-color: @tertiaryColor;
			border: 2px solid @tertiaryColor !important;
		}
	}

	.unarchive {
		&:after {
			content: @fa-var-plus-square;
			margin-left: 24px;
			color: @tertiaryColor;
		}

		&:hover,
		&:active,
		&:focus {
			background-color: @tertiaryColor;
			border: 2px solid @tertiaryColor !important;
		}
	}

	.unarchive {
		padding-left: 9px !important;

		&:after {
			margin-left: 9px;
		}
	}


	.btn-default {

		i {
			color: @btn-default-border;
		}

	}

	.btn-info {

		i {
			color: @btn-info-border;
		}
	}

	.btn-danger {

		i {
			color: @btn-danger-border;
		}
	}

	.btn-primary {

		i {
			color: @btn-primary-border;
		}

	}

	.btn-info {

		i {
			color: @btn-info-border;
		}
	}

	.btn-warning {

		i {
			color: @btn-warning-border;
		}
	}
}


#application .fieldlink {
	padding: 8px 0;
}

#application .fileuploadmultiple {
	@uploadBtnHeight: 140px;
	@uploadBtnWidth: 30px;
	padding: 0;

	.btn {
		margin-left: -@uploadBtnHeight;
		width: @uploadBtnHeight;
		height: @uploadBtnWidth;
		padding: 0;
		position: absolute;
		float: left;
	}

	span {
		margin-top: 8px;
		display: block;
		color: #777;
		float: left;
	}

	iframe {
		margin-left: -@uploadBtnHeight;
		border: 0;
		width: @uploadBtnHeight;
		height: @uploadBtnWidth;
		opacity: 0;
		float: left;
		z-index: 2;
		position: absolute;
	}
}