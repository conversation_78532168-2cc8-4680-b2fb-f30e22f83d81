// no outlines
a,
input,
select,
textarea,
li,
span,
div {
  outline: none;
}

// disabled and read-only inputs
.disabledControl,
input[disabled]:not(".MuiSwitch-input"),
input[readonly],
textarea[disabled],
textarea[readonly],
select[readonly],
select[disabled] {
  background-color: var(--white);
  border: none;
  cursor: not-allowed;
  color: var(--gray-700);
  -webkit-text-fill-color: var(--gray-700);
  -webkit-opacity: 1;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  padding: var(--card-disabled-control-p);
}

.cardform {
  .disabledControl,
  input[disabled],
  input[readonly],
  textarea[disabled],
  textarea[readonly],
  select[readonly],
  select[disabled] {
    background-color: var(--white);
  }
}

// used for elements that should not be hidden but still do not want to display
.offscreen {
  position: absolute;
  top: -999999px;
  left: -999999px;
}

// calendar
.datetimepicker-minutes,
.datetimepicker-hours {
  table {
    width: 100%;

    thead {
      display: none;
    }
  }
}

// modal
.bootstrap-dialog {
  .inset {
    padding: 0 20px;
  }
}

// medical icons
.medical-icon(@top, @left) {
  background-image: url(/homebase/public/img/medicalicons.png);
  background-repeat: no-repeat;
  background-position: @top @left;
  width: 22px;
  height: 22px;
  float: left;
  margin-top: -2px;
  display: inline-block;
}

.medical-icon-1 {
  .medical-icon(0px, 3px);
}

.medical-icon-2 {
  .medical-icon(-32px, 0px);
}

.medical-icon-3 {
  .medical-icon(-65px, 0px);
}

.medical-icon-4 {
  .medical-icon(-97px, 0px);
}

.medical-icon-5 {
  .medical-icon(-129px, 0px);
}

.medical-icon-6 {
  .medical-icon(-161px, 0px);
}

.medical-icon-7 {
  .medical-icon(-193px, 0px);
}

.medical-icon-8 {
  .medical-icon(1px, -30px);
}

.medical-icon-9 {
  .medical-icon(-32px, -30px);
}

.medical-icon-10 {
  .medical-icon(-65px, -30px);
}

.medical-icon-11 {
  .medical-icon(-97px, -30px);
}

.medical-icon-12 {
  .medical-icon(-129px, -30px);
}

.medical-icon-13 {
  .medical-icon(-161px, -30px);
}

.medical-icon-14 {
  .medical-icon(-193px, -30px);
}

.medical-icon-15 {
  .medical-icon(0px, -59px);
}

.medical-icon-16 {
  .medical-icon(-32px, -59px);
}

.medical-icon-17 {
  .medical-icon(-65px, -59px);
}

.medical-icon-18 {
  .medical-icon(-97px, -59px);
}

.medical-icon-19 {
  .medical-icon(-129px, -59px);
}

.medical-icon-20 {
  .medical-icon(-161px, -59px);
}

.medical-icon-21 {
  .medical-icon(-193px, -59px);
}

.medical-icon-22 {
  .medical-icon(0px, -89px);
}

.medical-icon-23 {
  .medical-icon(-32px, -89px);
}

.medical-icon-24 {
  .medical-icon(-65px, -89px);
}

.medical-icon-25 {
  .medical-icon(-97px, -89px);
}

.medical-icon-26 {
  .medical-icon(-129px, -89px);
}

.medical-icon-27 {
  .medical-icon(-161px, -89px);
}

.medical-icon-28 {
  .medical-icon(-193px, -89px);
}

.medical-icon-29 {
  .medical-icon(0px, -118px);
}

.medical-icon-30 {
  .medical-icon(-32px, -118px);
}

.medical-icon-31 {
  .medical-icon(-65px, -118px);
}

.medical-icon-32 {
  .medical-icon(-97px, -118px);
}

.medical-icon-33 {
  .medical-icon(-129px, -118px);
}

.medical-icon-34 {
  .medical-icon(-161px, -118px);
}

.medical-icon-35 {
  .medical-icon(-193px, -118px);
}

.medical-icon-36 {
  .medical-icon(0px, -141px);
}

.medical-icon-37 {
  .medical-icon(-32px, -141px);
}

.medical-icon-38 {
  .medical-icon(-65px, -141px);
}

.medical-icon-39 {
  .medical-icon(-97px, -141px);
}

.medical-icon-40 {
  .medical-icon(-129px, -141px);
}

.medical-icon-41 {
  .medical-icon(-161px, -141px);
}

.medical-icon-42 {
  .medical-icon(-193px, -141px);
}

.medical-icon-43 {
  .medical-icon(0px, -166px);
}

.medical-icon-44 {
  .medical-icon(-32px, -166px);
}

.medical-icon-45 {
  .medical-icon(-65px, -166px);
}

.medical-icon-46 {
  .medical-icon(-97px, -166px);
}

.medical-icon-47 {
  .medical-icon(-129px, -166px);
}

.medical-icon-48 {
  .medical-icon(-161px, -166px);
}

.medical-icon-49 {
  .medical-icon(-193px, -166px);
}
