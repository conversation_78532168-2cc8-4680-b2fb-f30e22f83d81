.rounded(@radius: 3px) {
	-webkit-border-radius:@radius;
	-moz-border-radius:@radius;
	-ms-border-radius:@radius;
	border-radius:@radius;
}

.rounded4(@tl: 3px, @tr: 3px, @bl: 3px, @br: 3px) {
	-webkit-border-top-left-radius:@tl;
	-webkit-border-top-right-radius:@tr;
	-webkit-border-bottom-left-radius:@bl;
	-webkit-border-bottom-right-radius:@br;
	-moz-border-radius-topleft:@tl;
	-moz-border-radius-topright:@tr;
	-moz-border-radius-bottomleft:@bl;
	-moz-border-radius-bottomright:@br;
	-ms-border-radius-topleft:@tl;
	-ms-border-radius-topright:@tr;
	-ms-border-radius-bottomleft:@bl;
	-ms-border-radius-bottomright:@br;
	border-top-left-radius:@tl;
	border-top-right-radius:@tr;
	border-bottom-left-radius:@bl;
	border-bottom-right-radius:@br;
}

.box-shadow (@val) {
	-webkit-box-shadow:@val;
	-moz-box-shadow:@val;
	-ms-box-shadow:@val;
	-o-box-shadow:@val;
	box-shadow:@val;
}

.shadow (@x: 0, @y: 1px, @blur: 2px, @color: #fff) {
	text-shadow: @x @y @blur @color;
}

.disabled {
	color:#bbb !important;
	background:#999 !important;
}

.hidden {
	display:none;
}

.noBounce {
	overflow:hidden;
}

.noTouchIssues {
	-webkit-touch-callout: none;
	-webkit-text-size-adjust: none;
	-webkit-user-select: none;
}

html, body, p, h1, h2, h3, h4, h5, h6, a, ul, li, pre {
	margin:0;
	padding:0;
	font-family: Helvetica;
	outline:none;
}

a {
	text-decoration:none;
}

ul {
	list-style:none
}

.text-right {
	text-align:right !important;
}
