// global
@highlightColor: #068CCF;
@highlightBackground: #1F9C71;
@hoverBackground: #068CCF;
@tableRowHover: @hoverBackground;
@badgeColor: #FFF;
@badgeBackground: #FF5F19;

// nav bar
@navbarWidth: 250px;
@navbarWidthCollapsed: 50px;
@navbarBackground: #19272d;
@navbarButtonWidth: 108px;
@navbarLeftGap: 24px;
@navbarMenuColor: #53717f;
@navbarMenuHighlight: #1e323b;
@navbarMenuHighlightTab: @hoverBackground;
@navbarMenuHover: #aaa;
@navbarMenuTabWidth: 4px;
@navbarHeaderHeight: 50px;

// tab ui
@elementBackground: #f6f6f6;
@elementBorderColor: #ddd;
@elementRounding: 8px;
@elementMargin: 16px;

// top-tab ui
//
@topTabHeight: 44px;
@topTabActiveColor: #068CCF;
@topTabBackground: #F8F8F8;

// sidemenu ui
@sideMenuWidth: 250px;
@sideMenuActiveColor: #666;
@sideMenuBackground: #F8F8F8;

// form ui
@formFieldBorderColor: @border-button-color;
@formFieldBorderRadius: 4px;
@horizontalComponentOffset: 210px;
@cardmenuWidthMax: 300px;
@cardmenuWidthMin: 200px;
@cardMenuBackground: @sideMenuBackground;
@cardMenuBorderColor: @elementBorderColor;

// conversation
@conversationListWidth: 400px;
@conversationDateWidth: 110px;
@conversationBorder: #eee;
@conversationFooter: #f4f4f4;

body {
	background: #fff;
	-webkit-font-smoothing: antialiased;
	-moz-font-smoothing: antialiased;
	-ms-font-smoothing: antialiased;
	font-smoothing: antialiased;
}

html,
body,
p,
h1,
h2,
h3,
h4,
h5,
h6,
a,
ul,
li,
pre {
	font-family: @font-family-sans-serif;
}

#application {

	width: 100%;
	height: 100%;
	background-image: url(/homebase/public/img/background.png);
	background-size: cover;
	background-position: center;

	.initload {
		text-align: center;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background: url(/homebase/public/img/select2-spinner.gif) center no-repeat;
		height: 100vh;
		font-size: 12px;
		padding-bottom: 60px;
		background-image: url(/homebase/public/img/background.png);
	}

	>#sections {
		min-height: 400px;
		margin-left: @navbarWidth;

		>div {
			display: none;

			&.active {
				display: block;
			}
		}
	}

	// form
	.section_warning {
		>h3 {
			color: @warningColor;
		}

		>h4 {
			color: @warningColor;
		}

		>.sectionnote {
			padding: 8px;
			margin-top: 8px;
			color: @gray-dark;
			text-transform: uppercase;
			font-family: @font-family-headers;
			border: 2px solid @brand-warning;
			background: rgba(242, 176, 16, 0.1);

			ul {
				margin-top: 8px;
				margin-left: 24px;

				li {
					list-style: square;
				}
			}
		}
	}

	.container {
		min-width: 100%;
	}

	#iprint {
		position: absolute;
		border: 0;
		left: -1000px;
		top: -1000px;
		width: 1px;
		height: 1px;
	}

	#notify {
		position: fixed;
		bottom: 24px;
		right: 8px;
		z-index: 99999;
		display: none;

		span {
			padding: 20px 40px;
			background: #000;
		}
	}

	.dataTable {
		tr.archived {
			background-color: rgb(242, 222, 222);
		}
	}
}

#application.collapsed {
	>#sections {
		margin-left: @navbarWidthCollapsed;
	}
}

.bootstrap-dialog {
	.formload {
	}

	.sub {
		padding-left: 16px;
		color: #666;
	}
}

i.audit-trail {
	color: #888;
	opacity: 1.0;
	margin-top: 2px;
	margin-left: var(--i-audit-trail-ml);
	float: right;
	cursor: pointer;
}

#application.web {
	i.audit-trail:hover {
		opacity: 1.0;
		color: blue;
	}
}

.typeahead:not(td ul.typeahead) {
	position: relative;
	top: 0px !important;
	width: 100%;
	overflow-x: hidden;
	overflow-y: scroll !important;
	max-height: 300px !important;
	.drowp-down-one;

	.dropdown-item {
		.list-item-one;
	}

	.active {
		.dropdown-item {
			.list-item-one-active-colors;
		}
	}

}

table.table-sortable thead {
	tr {
		th {
			// border: 1px solid #DBE4EA;
		}
	}

	font-weight: bold;
	// border: 1px solid #DBE4EA;
}

table.table-sortable thead .sorting_asc:after {
	content: "\f0de";
	float: right;
	font-family: fontawesome;
}

table.table-sortable thead .sorting_desc:after {
	content: "\f0dd";
	float: right;
	font-family: fontawesome;
}

.banner {
	display: flex;
	flex: 1;
	justify-content: center;
}

.banner_text {
	color: red;
	font-size: 18px;
}

.pdf-jq {

	flex-direction: column;
	background: #fff !important;

	#pdf-annotation-layer {
		position: absolute;
	}

	.viewer-wrapper {
		display: flex;
	}

	.toolbar {
		display: flex;
		padding: 10px;
		background: #19272d;
		width: 100%;
		justify-content: center;

		input {
			width: 5rem;
			text-align: center;
			background: #19272d;
			color: #fff !important;
			outline: none;
			border: none;
			-webkit-text-fill-color: #fff;
			align-self: center;
			min-width: 0px;
		}

		button {
			outline: none;
			width: 4rem;
			margin: 0px 5px;
			border-radius: 100px;
		}
	}

	.pagecount-wrapper {
		display: flex;
		align-items: center;
		padding: 0px 10px;
	}



}