.datetimepicker {
	box-shadow: none!important;
	border: 2px solid #ddd;

	th {
		font-family: @font-family-headers!important;
		color: @gray-dark!important;
		border-radius:0px!important;
		text-transform: uppercase!important;
	}

	th:hover {
		background-color: @primaryColorHover!important;
		color: white!important;
	}

	tr {
		border: 1px solid #ddd;

		td {
			border-right:1px solid #ddd;
		}

		td:last-child {
			border-right:none!important;
		}
	}

	.datetimepicker-hours, .datetimepicker-minutes {
		tbody {
			tr:first-child {
				border-top:none!important;
			}

			tr:last-child {
				border-bottom:none!important;
			}

			legend {
				font-family: @font-family-headers;
				color:@gray-dark;
				border-radius:0px!important;
			}

			span {
				color: @gray-dark;
				border-radius:0px!important;
			}

			span:hover, legend:hover {
				background-color: @primaryColorHover!important;
				color:white!important;
			}

			span.active {
				background-color: @primaryColor!important;
				background-image: none!important;
				color:white!important;
			}
		}

	}

	.datetimepicker-days {

		thead {
			tr {
				border:none!important;
			}
		}

		tfoot {
			tr {
				border:none!important;
			}
		}

		.day {
			background-color: #EDF5FF;
			border-radius: 0px!important;
			color: @gray-dark;
		}

		.day:hover {
			background-color: @primaryColorHover!important;
			color: white!important;
		}

		.day.old {
			background-color: white;
			color: #ddd;
		}

		.day.new {
			background-color: white;
			color: #ddd;
		}

		.day.active {
			background-color: @primaryColor;
			background-image: none!important;
			border-radius: 0px!important;
			color:white;
		}

		.day.today:not(.active) {
			background-color: @brand-warning;
			background-image: none!important;
			border-radius: 0px!important;
			color:white;
		}
	}

}

.datetimepicker:before {
	display:none!important;
}

.datetimepicker:after {
	display:none!important;
}
