.controls-grid.readonly {
    .eg-tooltip {
        padding-top: 0px !important;
    }
}

.controls-grid {
    .grid-split {
        font-size: 18px;
        font-weight: bold;
        position: relative;
        display: inline-block;
        padding: 10px;
    }

    .eg-tooltip .eg-tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: #555;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -60px;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .eg-tooltip {
        position: relative;
        display: inline-block;
        border-bottom: 1px dotted black;
        padding-top: 10px;
        font-size: 18px;
    }




    .eg-tooltip .eg-tooltiptext::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #555 transparent transparent transparent;
    }

    .eg-tooltip:hover .eg-tooltiptext {
        visibility: visible;
        opacity: 1;
    }

    .tooltip {
        position: relative;
        display: inline-block;
        border-bottom: 1px dotted black;
    }

    .eg-tooltip .eg-tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: #555;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -60px;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .eg-tooltip .eg-tooltiptext::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #555 transparent transparent transparent;
    }

    .eg-tooltip:hover .eg-tooltiptext {
        visibility: visible;
        opacity: 1;
    }
}