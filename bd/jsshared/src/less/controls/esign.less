
.bootstrap-dialog {
	.esign-wrapper {
		position:relative;
		background:@gray-lighter;
		text-align:center;
		// margin:-15px;
		border-radius: 8px;
		box-shadow: 0px 1px 1px 0px rgba(140, 140, 140, 0.1),
		0px 3px 3px 0px rgba(140, 140, 140, 0.09),
		0px 6px 3px 0px rgba(140, 140, 140, 0.05),
		0px 10px 4px 0px rgba(140, 140, 140, 0.01),
		0px 16px 4px 0px rgba(140, 140, 140, 0);
		background-color: rgba(255, 255, 255, 0.9);

		border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.1) 100%);
		overflow:hidden;
		width: auto !important;
		margin-left: 0px !important;
		height: 262px !important;
		canvas {
			transform-origin: top left;
			position:absolute;
			left:0;
		}
		.esign-name {
			position:absolute;
			top:4px;
			left:4px;
			padding:3px 8px;
			border:1px solid #aaa;
			border-radius:4px;
			z-index:1;
			width:60%;
		}
		.clear {
			position:absolute;
			top:4px;
			right:4px;
			z-index:1;
		}
	}
	.spinner-border{
		height: -webkit-fill-available;
		width: -webkit-fill-available;
		z-index: 3;
		position: absolute;
		// background-color:rgba(0, 0, 0, 0.5); 
	}
	.sr-only{
		position: relative;
		top: 45%;
		left: 45%;
		display: inline-block;
		width: 50px;
		height: 50px;
		border: 5px solid rgba(255, 255, 255, 0.5);
		border-radius: 50%;
		border-top: 5px solid #3498db;
		animation: spin 1s linear infinite;
	}
	@keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

	.additional-text-container{
		display: flex;
		flex-direction: column;
		float: left;
		align-items: flex-start;
	}
	.gps-icon{
		height:45px;
		position: relative;
		bottom: 4px;
		float: left;
	}
}
