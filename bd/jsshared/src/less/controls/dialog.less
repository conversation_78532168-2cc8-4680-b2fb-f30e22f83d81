/* CM:2016-01-31 - customizations */

.modal-backdrop {
	z-index: 29990 !important;
}

.bootstrap-dialog {
	z-index: 9999900 !important
}

@icon-size: 48px;
@icon-margin: 22px 0 0 5px;
@shadow-light: rgba(0, 0, 0, 0.06);
@shadow-dark: rgba(0, 0, 0, 0.04);

.dialog-icon(@icon) {
	content: "";
	display: block;
	width: @icon-size;
	height: @icon-size;
	background-image: url(@icon);
	background-size: contain;
	background-repeat: no-repeat;
	margin: @icon-margin;
}


/* from https://github.com/nakupanda/bootstrap3-dialog/blob/master/src/less/bootstrap-dialog.less */
.bootstrap-dialog {

	.modal-content {
		border: 1px solid #FAFAFAD9;
		background-color: #D4D4D4;
		box-shadow:
			0px 17px 30px -4px rgba(0, 0, 0, 0.06),
			0px 8px 17px -4px rgba(0, 0, 0, 0.06),
			0px 3px 8px -4px rgba(0, 0, 0, 0.04),
			0px 0px 1px -4px rgba(0, 0, 0, 0.04);
		backdrop-filter: blur(4px);

		&:has(.esign-wrapper) {
			.modal-header::before {
				display: none;
			}

			.modal-header {
				.bootstrap-dialog-header {
					.bootstrap-dialog-close-button {
						display: block !important;
						margin-top: 60px;
					}

					.bootstrap-dialog-title {
						font-size: 18px !important;
						font-weight: 500px !important;
						color: #181D27 !important;

						span {
							font-size: 14px !important;
							font-weight: 400 !important;
							color: #535862 !important;
						}
					}
				}
			}

			.modal-footer {
				.bootstrap-dialog-footer {
					padding-inline: 20px;
				}
			}

			.bootstrap-dialog-footer-buttons {
				width: 100%;
				display: block;

				.additional-text-container {
					display: flex;
					width: 100%;
					flex-direction: row;
					font-weight: 500;
					font-size: 14px;
					color: rgba(94, 99, 107, 1);

					span:first-of-type {
						max-width: 50% !important;
					}

					span {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}

				.additional-text-container> :last-child {
					margin-left: auto;
				}
			}

			&:has(.additional-text-container) {
				.bootstrap-dialog-footer-buttons>button {
					margin-top: 32px;

				}
			}

			.bootstrap-dialog-footer-buttons> :last-child {
				flex: 1;
				max-width: 50%;
				background-color: rgba(131, 123, 178, 1) !important;
				color: white !important;
				font-size: 16px !important;
				font-weight: 500 !important;
			}

			.bootstrap-dialog-footer-buttons {
				display: flex;
				flex-wrap: wrap;
			}

			.bootstrap-dialog-footer-buttons>.child-div {
				width: 100%;
			}

			.bootstrap-dialog-footer-buttons>button:first-of-type {
				background-color: rgba(250, 250, 250, 1) !important;
				font-size: 16px !important;
				font-weight: 500 !important;
				color: rgba(65, 70, 81, 1) !important;

			}

			.gps-icon {
				width: 11.2px;
				height: 14px;
				margin-right: 3px;
				align-self: center;
				margin-top: 6px;
			}

			.right-text {
				margin-left: auto;
			}

			.btn {
				width: 50%;
			}

			.clear {
				display: none !important;
			}

		}
	}


	.modal-header {
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
	}

	.bootstrap-dialog-title {
		color: Gray/900;
		display: inline-block;
		font-size: 18px;
		font-weight: 500;
	}

	.bootstrap-dialog-message {
		font-size: 14px;
		font-weight: 400;
	}

	.bootstrap-dialog-button-icon {
		margin-right: 3px;
	}

	.bootstrap-dialog-close-button {
		font-size: 20px;
		float: right;
		.opacity(0.9);

		&:hover {
			cursor: pointer;
			.opacity(1.0);
		}
	}

	/* dialog types */
	&.type-default {
		.modal-header {
			background-color: @modal-content-bg;
		}

		.bootstrap-dialog-title {
			color: #333;
		}
	}

	&.type-info {
		.modal-header::before {
			.dialog-icon('/homebase/public/img/featured_icon_info.svg');
		}

		.bootstrap-dialog-footer-buttons {
			button {
				background-color: rgba(245, 245, 245, 1);
				color: rgba(94, 99, 107, 1) !important;
			}
		}
	}

	&.type-danger {
		.modal-header::before {
			.dialog-icon('/homebase/public/img/alert_icon.svg');
		}

		.bootstrap-dialog-footer-buttons {
			button {
				background-color: rgba(229, 135, 135, 1);
			}
		}
	}

	&.type-success {
		.modal-header::before {
			.dialog-icon('/homebase/public/img/featured_icon.svg');
		}

		.bootstrap-dialog-footer-buttons {
			button {
				background-color: rgba(118, 173, 155, 1);
				;
			}
		}
	}

	&.type-primary {
		.modal-header {
			background-color: @brand-primary;
		}
	}

	&.type-warning {
		.modal-header {
			background-color: @brand-warning;
		}
	}

	.modal-body {
		.bootstrap-dialog-body {
			.bootstrap-dialog-message {
				color: rgba(83, 88, 98, 1);
				margin-inline: 6px;
			}
		}
	}

	&.type-info,
	&.type-danger,
	&.type-success {
		.modal-content {
			position: relative;
			text-align: left;
			background-image: url(/homebase/public/img/background_pattern_decorative.svg);
			background-size: 190px;
			background-repeat: no-repeat;
		}
		.modal-header {
			display: flex;
			flex-direction: row;
			align-items: center;
			.bootstrap-dialog-header {
				// margin: 15px;
				padding: 18px 0 0 18px;
				width: 100%;
				.bootstrap-dialog-close-button {
					.close {
						margin-top: 0 !important;
						padding-top: 7px;
					}
				}
			}
		}
	}

	&.size-large {
		.bootstrap-dialog-title {
			font-size: 24px;
			margin-bottom: 4px;
		}

		.bootstrap-dialog-close-button {
			font-size: 30px;
		}

		.bootstrap-dialog-message {
			font-size: 14px;
			font-weight: 400;
		}
	}

	/**
	 * Icon animation
	 * Copied from font-awesome: http://fontawesome.io/
	 **/
	.icon-spin {
		display: inline-block;
		-moz-animation: spin 2s infinite linear;
		-o-animation: spin 2s infinite linear;
		-webkit-animation: spin 2s infinite linear;
		animation: spin 2s infinite linear;
	}

	@-moz-keyframes spin {
		0% {
			-moz-transform: rotate(0deg);
		}

		100% {
			-moz-transform: rotate(359deg);
		}
	}

	@-webkit-keyframes spin {
		0% {
			-webkit-transform: rotate(0deg);
		}

		100% {
			-webkit-transform: rotate(359deg);
		}
	}

	@-o-keyframes spin {
		0% {
			-o-transform: rotate(0deg);
		}

		100% {
			-o-transform: rotate(359deg);
		}
	}

	@-ms-keyframes spin {
		0% {
			-ms-transform: rotate(0deg);
		}

		100% {
			-ms-transform: rotate(359deg);
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(359deg);
		}
	}

	/** End of icon animation **/
}