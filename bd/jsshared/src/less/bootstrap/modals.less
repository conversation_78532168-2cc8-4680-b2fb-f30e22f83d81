//
// Modals
// --------------------------------------------------

// .modal-open      - body class for killing the scroll
// .modal           - container to scroll within
// .modal-dialog    - positioning shell for the actual modal
// .modal-content   - actual modal w/ bg and corners and shit

// Kill the scroll on the body
.modal-open {
  overflow: hidden;
}

// Container that the modal scrolls within
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: @zindex-modal;
  -webkit-overflow-scrolling: touch;

  // Prevent Chrome on Windows from adding a focus outline. For details, see
  // https://github.com/twbs/bootstrap/pull/10951.
  outline: 0;
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0px);
  transition: top 0.4s ease-out, opacity 0.4s ease-out;
  } 

  .modal::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0; 
    background: linear-gradient(to bottom, rgba(224, 224, 224, 0.3), rgba(224, 224, 224, 0.3));
    backdrop-filter: blur(0px);
    opacity: 0;
    transition: height 0.5s ease-out, opacity 0.5s ease-out;
  }
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal-dialog {
  position: absolute;
  top: -100%; 
  left: 50%;
  transform: translate(-50%, 0);
  opacity: 0;
  transition: top 0.5s ease-out, opacity 0.5s ease-out;
}

.modal.fade {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(1px);
}

.modal.in {
  backdrop-filter: blur(4px);

  &::before {
    // height: 100%;
    opacity: 1;
  }

  .modal-dialog {
    top: 40%; 
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
// Actual modal
.modal-content {
  position: relative;
  background-color: @modal-content-bg;
  border: 1px solid @modal-content-fallback-border-color; //old browsers fallback (ie8 etc)
  border: 1px solid @modal-content-border-color;
  border-radius: @border-radius-large;
  .box-shadow(0 3px 9px rgba(0,0,0,.5));
  background-clip: padding-box;
  // Remove focus outline from opened modal
  outline: 0;
}

// Modal background
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: @zindex-modal-background;
  background-color: @modal-backdrop-bg;
  // Fade for backdrop
  &.fade { .opacity(0); }
  &.in { .opacity(@modal-backdrop-opacity); }
}

// Modal header
// Top section of the modal w/ title and dismiss
.modal-header {
  // padding: @modal-title-padding;
  padding-inline: 15px;
  // border-bottom: 1px solid @modal-header-border-color;
  min-height: (@modal-title-padding + @modal-title-line-height);
}
// Close icon
.modal-header .close {
  margin-top: -52px;
  color:  rgba(164, 167, 174, 1);
}

// Title text within header
.modal-title {
  margin: 0;
  line-height: @modal-title-line-height;
}

// Modal body
// Where all modal content resides (sibling of .modal-header and .modal-footer)
.modal-body {
  position: relative;
  // padding: @modal-inner-padding;
  margin-inline: 26px;
  .bootstrap-dialog-body {
    .bootstrap-dialog-message {
      > h4 {
        color: #181D27;
      }
      li {
        overflow-wrap: break-word;
      }
    }
    &:has(.formload) {
      .bootstrap-dialog-message {
        padding: 20px !important;
      }
    }
  }
}

// Footer (for actions)
.modal-footer {
  padding: @modal-inner-padding;
  text-align: right; // right align buttons
  // border-top: 1px solid @modal-footer-border-color;
  &:extend(.clearfix all); // clear it in case folks use .pull-* classes on buttons

  // Properly space out buttons
  .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0; // account for input[type="submit"] which gets the bottom margin like all other inputs
  }
  // but override that for button groups
  .btn-group .btn + .btn {
    margin-left: -1px;
  }
  // and override it for block buttons as well
  .btn-block + .btn-block {
    margin-left: 0;
  }

  .bootstrap-dialog-footer {
    .bootstrap-dialog-footer-buttons {
      display: flex;
      justify-content: space-between;

      button {
        margin-bottom: 8px;
        width: 100%;
        font-size: 16px;
        font-weight: 500;
        color: White !important;
        box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.22) inset,
        0px 1px 2px 0px rgba(255, 255, 255, 0.12) inset;
        border-radius: 8px;
      }
      .btn-true {
        background-color: var(--color-tertiary);
        border: none;
      }
  
      .btn-false {
        background-color: @gray-light;
        border: none;
      }
      .btn-copy {
        max-width: 10%;
        border: none;
        background: #F5F5F5;

        &::before {
          content: '\f0c5';
          font-family: 'Font Awesome 6 Pro';
          color: #949491;
          font-weight: 600;
          font-size: 16px;
        }
      }
      .copied {
        &::before{
          color: #e58787;
        }
      }
    }
  }
}

// Measure scrollbar width for padding body during modal show/hide
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

// Scale up the modal
@media (min-width: @screen-sm-min) {
  // Automatically set modal's width for larger viewports
  .modal-dialog{
    width: @modal-md;
    margin: 30px auto;
    // max-width: 400px;
  }
  .modal-content {
    background: var(--Modal-Overlay, rgba(0, 0, 0, 0.22));
		box-shadow: 
			0px 17px 30px -4px rgba(0, 0, 0, 0.06),
			0px 8px 17px -4px rgba(0, 0, 0, 0.06),
			0px 3px 8px -4px rgba(0, 0, 0, 0.04),
			0px 0px 1px -4px rgba(0, 0, 0, 0.04);
		border-radius: 12px;
  }

  // Modal sizes
  .modal-sm { width: @modal-sm; }
}

@media (min-width: @screen-md-min) {
  .modal-lg { width: @modal-lg; }
}
