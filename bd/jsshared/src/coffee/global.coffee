# global variables that can be setup in /public/conf/config.js
if window.CRConfig?
	CRBaseURL			= window.CRConfig['CRBaseURL']
	CRBackend			= window.CRConfig['CRBackend']
	CRLoginURL			= window.CRConfig['CRLoginURL']
	CRLocal				= window.CRConfig['CRLocal']
	CREnvironment		= window.CRConfig['CREnvironment']
else
	CRBaseURL			= ''
	CRBackend			= CRBaseURL + '/api'
	CRLoginURL			= CRBaseURL + '/logout/'
	CRLocal				= false
	CREnvironment		= ''

CRProtocolPrefix = 'clararx://'
# global variables that can be overridden in customer repo/client/jsshared/coffee/company.coffee
CRBaseCompany =
	name: 'Clara'

	street:       ''
	street2:      ''
	city:         ''
	state:        ''
	zip:          ''
	phone:        ''
	fax:          ''
	support:      '(*************'
	supmail:      '<EMAIL>'

	bi_refresh:   180   # seconds - only if full-screen
	kb_refresh:   6     # seconds - refresh kanban swim lanes
	snap_refresh: 30    # seconds - only if snapshot is visible
	ui_lock:      7200  # seconds
	filter_wc:    'Yes'  # auto-append * to all searches
	no_download:  'No'
	prevent_first_datetime_focus_popup: [] # add modes in which it should prevent
	toast_form_level_errors: 'No'
	separate_numbers: 'Yes'
	number_separator: ','
	decimal_separator: '.'
	format_currency: 'Yes'
	currency_prefix: '$'

	theme:
		esign:
			title: true
		pdf_logo:
			url:            'public/img/logo.png'
	user:
		is_session_locked:	false
		is_session_expired:	false
		is_internet_avail:	true
		is_sending_message: false

# global variables that should not be edited
$		= jQuery
B		= Backbone
App		= false
Auth	= false

# global parameters
ADMIN_GROUP				= 'admin'
ADMIN_INTERNAL			= [ADMIN_GROUP, 'cm', 'cma', 'csr', 'liaison', 'nurse', 'pharm', 'crn']
CLIENT_ROLES			=
	jshomebase			: ADMIN_INTERNAL
	jspatient			: ['patient']
	jspayor				: ['payor']
	jsphysician			: ['physician']
	ipadscout			: ADMIN_INTERNAL

ERR_CONTACT				= 'Please try again or contact your system administrator.'

FORMAT_DATETIME_COMM	= 'M/D/YY h:mm:ss a'       # momentjs
FORMAT_DATETIME_MISO	= 'YYYY-MM-DD HH:mm:ss'    # momentjs iso
FORMAT_DATETIME_GRID	= 'mmm d, yyyy h:MM:ss tt' # date-format
FORMAT_DATETIME_READ	= 'mm/dd/yyyy hh:MM tt'    # date-format
FORMAT_DATETIME_SAVE	= 'mm/dd/yyyy HH:MM:ss'    # date-format
FORMAT_DATETIME_ISO 	= 'yyyy-mm-dd HH:MM:ss'    # date-format
FORMAT_DATE				= 'MM/DD/YYYY'			   # date-format for moment
FORMAT_TIME				= 'hh:mm:ss A'			   # datetime-format for moment
FORMAT_DATE_TIME		= "#{FORMAT_DATE} #{FORMAT_TIME}" # time-format for moment

MAX_COLLECTIONS			= 5
MAX_GRID_COLUMNS		= 10
MAX_GRID_ROWS			= 100
MAX_FILTER_ROWS			= false
MAX_DATA_ROWS			= 1000

# Web Viewer Toolbar Options
toolbarOptions = [
	[{'header': [1, 2, 3, 4, 5, 6,false]}],
	['bold','italic','underline','strike'],
	['blockquote','code-block'],
	[{'header': 1}, {'header': 2}],
	[{'list':'ordered'}, {'list':'bullet'}],
	[{'script':'sub'}, {'script':'super'}],
	[{'indent':'-1'}, {'indent':'+1'}],
	[{'direction':'rtl'}],
	[{'size': ['small',false,'large','huge']}],
	[{'color': []}, {'background': []}],
	[{'font': ['mirza', 'roboto', 'impact', 'courier', 'comic', 'times-new-roman', 'arial', 'garamond', 'tahoma', 'verdana']}],
	[{'align': []}],
	['link','image'],
	['clean']
]

numeral.register 'format', 'percent',
	regexps:
		format: /percent/ # Match any format that explicitly uses 'percent'
		unformat: /(%)/
	format: (value, format, roundingFunction) ->
		# Always format the number with '0,0.00' for thousand separators and two decimal places
		formattedValue = numeral._.numberToFormat(value, '0,0.[00]', roundingFunction)
		# Append '%' to the formatted result
		formattedValue + '%'
	unformat: (string) ->
		# Strip out the '%' sign and return the number
		numeral._.stringToNumber(string.replace('%', ''))