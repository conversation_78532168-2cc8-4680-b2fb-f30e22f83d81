class CRPrintFormBase extends CRView

	print: (src, snap, op, print_msg = false) ->
		dfd = $.Deferred()
		source = CRPrint.base_images()

		if src
			CRPrint.load_images source, (images) =>
				@print_images src, snap, op, print_msg, images, dfd
				return
		else # op.mode=edit - e.g. see pmx/plan-snap-base
			src = new DSLRecord(op)
			cfd = src.read()
			cfd.done (args...) =>
				CRPrint.load_images source, (images) =>
					@print_images src, snap, op, print_msg, images, dfd
					return
				return
			cfd.fail (err) ->
				dfd.reject err

		dfd

	print_images: (src, snap, op, print_msg, images, dfd) ->
		logo = CRPrint.template_logo(images)
		stamp = @print_stamp(src)
		dd =
			pageMargins: CRPrint.default_margins
			pageOrientation: 'portrait'
			header: @print_page_header(src.options.form, snap)
			footer: @print_page_footer(logo, stamp)
			content: @print_groups(src.dom, src.subforms.recmap, src.options.noif)
			styles: @print_styles()
		CRPrint.generate_pdf(dd, print_msg, 'Clara-Form', dfd)
		return

	print_field_column: (col) ->
		columns: col, columnGap: 18

	print_field_esign: (sec, rec, noif, fld) ->
		val = rec.preset[fld.id]
		v = fld.fieldgen.v
		dashes = '____________________'

		val = esignJSONLabel(val, true, true)
		if $.trim(val?.name) is '' # no signature (no name)
			if noif
				val = dashes
			else if v.model.required
				val = ''
			else
				return false
		else if val.image?.png # full signature (name + image)
			val.sign =
				image: val.image.png
				width: 80
				height: 40
		else if val.name? # partially valid signature (only name)
			val.sign = dashes

		lb: v.view.label.replace(/\:$/, '') + ': ', v: val

	print_field_grid: (sec, rec, noif, fld) ->
		v = fld.fieldgen.v
		ss = v.model.subfields_sort

		# rows
		gv = rec.preset[fld.id] or (if noif then [{}, {}, {}] else [])
		while getType(gv) is 'string'
			gv = JSON.parse(gv)
		return false if getType(gv) isnt 'array' or gv.length is 0

		# widths
		wd = for i in [0..ss.length-2]
			'auto'
		wd.push '*'

		# header
		th = for sk in ss
			text: v.model.subfields[sk].label
			fontSize: 10
			color: CRPrint.style.field.label
		gr = [th]

		for r in gv
			td = for sk in ss
				text: r[sk] or (if noif then '__'.repeat(v.model.subfields[sk].label.length) else '')
				fontSize: 9
				color: if noif then CRPrint.style.table.td.fill else CRPrint.style.field.value
			gr.push td

		[
			table:
				headerRows: 1
				widths: wd
				body: gr
			layout: 'lightHorizontalLines'
		]

	print_field_input: (sec, rec, noif, fld) ->
		val = rec.preset[fld.id + '_auto_name'] or rec.preset[fld.id]
		v = fld.fieldgen.v
		dashes = '__________'
		sep = '  ___'

		if v.model.multi
			if _.isEmpty(val) or getType(val) isnt 'array' or (val.length is 1 and _.isEmpty(val[0]))
				if noif
					if getType(v.model.source) is 'string'
						val = [dashes]
					else
						if getType(v.model.source) is 'object'
							val = [sep + _.values(v.model.source).join(sep)]
						else
							val = [sep + v.model.source.join(sep)]
				else if getType(val) is 'string' and val.substr(0, 1) is '{' and val.substr(-1) is '}' and v.view.control in ['checkbox', 'radio'] #HB-3617
					try
						val = FieldCheckbox.value_from_string(v, val)
					catch e
				else if v.model.required
					val = []
				else
					return false
			else if getType(v.model.source) is 'object'
				val = for r in val
					v.model.source[r] or r
			val = val.join(', ')
		else
			if $.trim(val) is ''
				if noif
					if getType(v.model.source) in ['null', 'string']
						val = dashes
					else
						if getType(v.model.source) is 'object'
							val = sep + _.values(v.model.source).join(sep)
						else
							val = sep + v.model.source.join(sep)
				else if v.model.required
					val = ''
				else
					return false
			else if getType(v.model.source) is 'object' and v.model.source[val]?
				val = v.model.source[val]
		if (v.view.control is 'checkbox') and (not noif)
			val = val.replace /[\["\]]/g, ""
		lb: v.view.label.replace(/\:$/, '') + ': ', v: $.trim(val)

	print_field_json: (sec, rec, noif, fld) ->
		val = rec.preset[fld.id]
		v = fld.fieldgen.v
		dashes = '__________'

		if v.view.control is 'file'
			val = fileJSONLabel(val)
		else if v.view.control is 'link'
			val = linkJSONLabel(val)

		if $.trim(val) is ''
			if noif
				val = dashes
			else if v.model.required
				val = ''
			else
				return false
		lb: v.view.label.replace(/\:$/, '') + ': ', v: $.trim(val)

	print_fields: (sec, rec, noif) ->
		pdf_fields = []
		col = []
		for f in sec.fields
			if col.length > 1
				pdf_fields.push @print_field_column(col)
				col = []

			continue if f.fieldgen.v.view.offscreen
			if f.fieldgen.v.model.type is 'subform' and f.fieldgen.v.model.multi
				continue # don't print grid for multi-subform since we will expand it
			else if rec.shmap.fields[f.id]? and rec.shmap.fields[f.id].current is false
				continue # all non-multi-subform fields must be visible
			else if (not rec.preset[f.id]?) and (not f.fieldgen.v.model.required) and (not noif)
				continue # all non-multi-subform optional fields must have value in non-blank mode
			else if f.fieldgen.v.model.type is 'json' and f.fieldgen.v.view.control is 'esign'
				fld = @print_field_esign(sec, rec, noif, f)
			else if f.fieldgen.v.model.type is 'json' and f.fieldgen.v.view.control is 'grid'
				fld = @print_field_grid(sec, rec, noif, f)
			else if f.fieldgen.v.model.type is 'json' and f.fieldgen.v.view.control in ['file', 'link']
				fld = @print_field_json(sec, rec, noif, f)
			else
				fld = @print_field_input(sec, rec, noif, f)
			continue if not fld

			if getType(fld) is 'array' # grid, subform etc.
				if col.length > 0
					pdf_fields.push @print_field_column(col)
					col = []
				pdf_fields = pdf_fields.concat fld
				continue

			if f.fieldgen.v.model.type is 'json' and f.fieldgen.v.view.control is 'esign' and fld.v?.name?
				col.push
					columns: [
							width: '40%'
							text: fld.lb
							style: 'field_label'
						,
							width: '60%'
							stack: [
								fld.v.sign
								fld.v.name
								fld.v.time
							]
							style: 'field_value'
					]
					columnGap: 4
					width: '50%'
			else if fld.lb.length + fld.v.length > CRPrint.max_field_label_length # long text
				if col.length > 0
					pdf_fields.push @print_field_column(col)
					col = []
				pdf_fields.push
						text: [
							fld.lb
							{ text: fld.v, style: 'field_value' }
						]
						style: 'field_label'
			else # regular input fields
				col.push
					text: [
						fld.lb
						{ text: fld.v, style: 'field_value' }
					]
					style: 'field_label'
					width: '50%'

		if col.length > 0
			pdf_fields.push @print_field_column(col)
		pdf_fields

	print_groups: (dom, rec, noif) ->
		pdf_groups = []
		first_group = '_first'
		for grp in dom
			continue if not grp.sections or grp.sections.length is 0
			if grp.multi
				continue if not rec[grp.form]?.subform?.subrecords?.length > 0
				pdf_sections_list = []
				for r in rec[grp.form].subform.subrecords
					rr = {}
					rr[grp.form] = r
					pdf_sections = @print_sections(grp, rr, noif)
					continue if pdf_sections.length is 0
					pdf_sections_list.push pdf_sections
			else
				pdf_sections = @print_sections(grp, rec, noif)
				continue if pdf_sections.length is 0
				pdf_sections_list = [pdf_sections]
			continue if pdf_sections_list.length is 0
			grptitle = grp.group.replace('View ', '')
			for pdf_secnum, pdf_sections of pdf_sections_list
				if grp.multi
					grpnum = ' ' + (parseInt(pdf_secnum) + 1) + ' of ' + pdf_sections_list.length
					pdf_groups.push
						columns: [
							width: 'auto'
							text: grptitle
						,
							width: '*'
							text: grpnum
							style: 'section_group_header_number'
						]
						style: 'section_group_header' + first_group
				else
					pdf_groups.push
						text: grptitle
						style: 'section_group_header' + first_group
				pdf_groups.push
					table:
						headerRows: 0
						widths: ['*']
						body: [[' ']]
					layout:
						hLineWidth:
							fx: 'line'
							src: '(i == 0 ? 2 : 0)'
						vLineWidth:
							fx: 'line'
							src: '(0)'
						hLineColor:
							fx: 'line'
							src: '(i == 0 ? "' + CRPrint.style.group.line + '" : "white")'
						vLineColor:
							fx: 'line'
							src: '("white")'
				if grp.note
					pdf_groups.push
						text: grp.note
						style: 'section_group_note'
				first_group = ''
				pdf_groups = pdf_groups.concat pdf_sections
		pdf_groups

	print_page_header: (form, snap) ->
		CRPrint.template_header(form, snap)

	print_page_footer: (logo, stamp) ->
		CRPrint.template_footer(logo, stamp)

	print_sections: (grp, rec, noif) ->
		pdf_sections = []
		first_section = '_first'
		for sec in grp.sections
			continue if not sec.fields or sec.fields.length is 0
			continue if not rec[sec.form]?
			continue if sec.section.toLowerCase() in CRPrint.hidden_sections
			r = rec[sec.form]
			continue if r.subform?.hidden_by_root? and r.subform.hidden_by_root
			continue if r.shmap.sections[sec.section]? and r.shmap.sections[sec.section].current is false
			pdf_fields = @print_fields(sec, r, noif)
			continue if pdf_fields.length is 0
			if sec.section isnt grp.group
				if not first_section
					pdf_sections.push
						table:
							headerRows: 0
							widths: ['*']
							body: [[' ']]
						layout:
							hLineWidth:
								fx: 'line'
								src: '(i == 0 ? 1 : 0)'
							vLineWidth:
								fx: 'line'
								src: '(0)'
							hLineColor:
								fx: 'line'
								src: '(i == 0 ? "' + CRPrint.style.section.line + '" : "white")'
							vLineColor:
								fx: 'line'
								src: '("white")'
				pdf_sections.push
					text: sec.section
					style: 'section_header' + first_section
				if sec.note
					pdf_sections.push
						text: sec.note
						style: 'section_note'
				first_section = ''
			pdf_sections = pdf_sections.concat pdf_fields
		pdf_sections

	print_stamp: (src) ->
		st = filterArray([
			CRPrint.template_stamp_filled(src)
			CRPrint.template_stamp_reviewed(src)
			CRPrint.template_stamp_printed()
		])
		stamp =
			stack: st
			fontSize: if st.length is 3 then 7 else 8
			alignment: 'right'
			lineHeight: 1.3
			margin: [0, 6 + (if st.length is 3 then 0 else 4), 18, 0]
			color: CRPrint.style.footer.text
			fillColor: CRPrint.style.footer.fill

	print_styles: ->
		styles =
			section_group_header_first:
				fontSize: 16
				margin: [0, 0, 0, 4]
				bold: true
			section_group_header:
				fontSize: 16
				margin: [0, 8, 0, 4]
				bold: true
			section_group_header_number:
				color: CRPrint.style.header.num
				alignment: 'right'
			section_group_note:
				color: CRPrint.style.section.text
				fontSize: 12
				margin: [0, -4, 0, 12]
				bold: false
			section_header_first:
				fontSize: 14
				margin: [0, -4, 0, 8]
				bold: true
			section_header:
				fontSize: 14
				margin: [0, -4, 0, 8]
				bold: true
			section_note:
				color: CRPrint.style.section.text
				fontSize: 11
				margin: [0, -4, 0, 12]
			field_label:
				color: CRPrint.style.field.label
				fontSize: 9
				margin: [0, 0, 0, 6]
			field_value:
				color: CRPrint.style.field.value
				fontSize: 10
				lineHeight: 1.15

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CRPrintForm extends CRPrintFormBase
