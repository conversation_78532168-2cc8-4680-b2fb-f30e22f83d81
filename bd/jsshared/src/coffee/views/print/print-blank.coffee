class CRPrintBlankBase extends CRView

	print: (src, snap, op, print_msg = false) ->
		dfd = $.Deferred()
		op =
			mode: 'add'
			noif: true
			preset: {}
		for k in ['form', 'id', 'el', 'parent', 'link', 'links', 'linkid']
			op[k] = src.options[k]

		dr = new DSLRecord(op)
		cfd = dr.read()
		cfd.done (args...) =>
			pfd = CRPrint.print_form dr, snap, op, 'Displaying preview...'
			pfd.done (args...) =>
				dfd.resolve ''
				return
			pfd.fail (err) ->
				dfd.reject err
			return
		cfd.fail (err) ->
			dfd.reject err

		dfd


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CRPrintBlank extends CRPrintBlankBase
