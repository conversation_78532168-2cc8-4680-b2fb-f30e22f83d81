class CRPrintFormsBase extends CRView

	print: (src, snap, op, print_msg = false) ->
		@ep = new CRPrintForm() # only used for calling functions
		dfd = $.Deferred()
		source = CRPrint.base_images()

		aj = Ajax.async
			url: src.source_url(fields:'all')
		aj.done (data, status, xhr) =>
			CRPrint.load_images source, (images) =>
				src.rows.raw = data
				src.rows.dr = for d in src.rows.raw
					r = new DSLRecord(src.options)
					r.process_record d
					r
				@print_images src, snap, op, print_msg, images, dfd
				return
			return
		aj.fail (xhr, status, data) ->
			dfd.reject(data, status, xhr)
			return

		dfd

	print_images: (src, snap, op, print_msg, images, dfd) ->
		logo = CRPrint.template_logo(images)
		stamp = @print_stamp(src)
		dd =
			pageMargins: CRPrint.default_margins
			pageOrientation: 'portrait'
			header: @print_page_header(src.options.form, snap)
			footer: @print_page_footer(logo, stamp)
			content: @print_merged(src, op)
			styles: @print_styles()
		CRPrint.generate_pdf(dd, print_msg, 'Clara-Forms', dfd)
		return

	print_merged: (src, op) ->
		st = for i,d of src.rows.dr
			i = parseInt(i)
			fill_review = filterArray([
				CRPrint.template_stamp_filled(d)
				CRPrint.template_stamp_reviewed(d)
			])
			n =
				table:
					widths: ['43%', '*', '45%']
					body: [[
						text: 'Form: ' + DSL[src.options.form].view.label
						style: 'form_header_text'
					,
						text: 'Form ' + (i + 1) + ' of ' + src.rows.dr.length
						style: 'form_header_number'
					,
						stack: fill_review
						style: 'form_header_stamp' + (if fill_review.length is 2 then '_review' else '')
					]]
				layout: CRPrint.template_no_lines()
				style: 'form_header'
			if op.newpage? and op.newpage
				n.pageBreak = 'before' if i > 0
			else if i > 0
				n.style += '_inline'
			[n].concat(@ep.print_groups(d.dom, d.subforms.recmap, src.options.noif))
		st

	print_page_header: (form, snap) ->
		CRPrint.template_header(form, snap)

	print_page_footer: (logo, stamp) ->
		CRPrint.template_footer(logo, stamp)

	print_stamp: (src) ->
		stamp =
			stack: [
				CRPrint.template_stamp_printed()
			]
			fontSize: 9
			alignment: 'right'
			margin: [0, 16, 18, 32]
			color: CRPrint.style.footer.text
			fillColor: CRPrint.style.footer.fill

	print_styles: ->
		styles = @ep.print_styles()
		styles.form_header =
			margin: [-18, -14, -18, 12]
		styles.form_header_inline =
			margin: [-18, 12, -18, 12]
		styles.form_header_text =
			fontSize: 14
			bold: true
			color: CRPrint.style.title.text
			fillColor: CRPrint.style.title.fill
			margin: [18, 4, 0, 4]
		styles.form_header_number =
			fontSize: 9
			alignment: 'center'
			color: CRPrint.style.title.num
			fillColor: CRPrint.style.title.fill
			margin: [0, 8, 0, 0]
		styles.form_header_stamp =
			fontSize: 9
			alignment: 'right'
			color: CRPrint.style.title.num
			fillColor: CRPrint.style.title.fill
			margin: [0, 7, 18, 0]
		styles.form_header_stamp_review =
			fontSize: 8
			alignment: 'right'
			lineHeight: 1.1
			color: CRPrint.style.title.num
			fillColor: CRPrint.style.title.fill
			margin: [0, 2, 18, 0]
		styles

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CRPrintForms extends CRPrintFormsBase
