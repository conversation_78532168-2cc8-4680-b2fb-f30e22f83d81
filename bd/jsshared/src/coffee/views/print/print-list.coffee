class CRPrintListBase extends CRView

	print: (src, snap, op, print_msg = false) ->
		dfd = $.Deferred()
		source = CRPrint.base_images()

		aj = Ajax.async
			url: src.source_url(fields:'list')
		aj.done (data, status, xhr) =>
			CRPrint.load_images source, (images) =>
				fd = DSL[src.options.form]
				selcols = for k in src.column_list(fd)
					k.property
				src.rows.raw = data
				src.rows.parsed = []
				for d in data
					r = src.make_row_html(d, selcols, fd.fields)
					src.rows.parsed.push(r) if r
				@print_images src, snap, op, print_msg, images, dfd
				return
			return
		aj.fail (xhr, status, data) ->
			dfd.reject(data, status, xhr)
			return

		dfd

	print_images: (src, snap, op, print_msg, images, dfd) ->
		logo = CRPrint.template_logo(images)
		stamp = @print_stamp(src)
		dd =
			pageMargins: CRPrint.default_margins
			pageOrientation: 'landscape'
			header: @print_page_header(src.options.form, snap)
			footer: @print_page_footer(logo, stamp)
			content: @print_table(src, snap, op)
			styles: @print_styles()
		CRPrint.generate_pdf(dd, print_msg, 'Clara-Form-List', dfd)
		return

	print_page_header: (form, snap) ->
		CRPrint.template_header(form, snap)

	print_page_footer: (logo, stamp) ->
		CRPrint.template_footer(logo, stamp)

	print_stamp: (src) ->
		stamp =
			stack: [
				CRPrint.template_stamp_printed()
			]
			fontSize: 9
			alignment: 'right'
			margin: [0, 12, 18, 36]
			color: CRPrint.style.footer.text
			fillColor: CRPrint.style.footer.fill

	print_styles: ->
		styles =
			title:
				margin: [-18, -14, -18, 12]
			title_text:
				fontSize: 18
				bold: true
				alignment: 'center'
				color: CRPrint.style.title.text
				fillColor: CRPrint.style.title.fill
				margin: [0, 4, 0, 4]
			th:
				fontSize: 10
				bold: true
				color: CRPrint.style.table.th.text
				fillColor: CRPrint.style.table.th.fill
				margin: [0, 4, 0, 0]
			td:
				fontSize: 9
				lineHeight: 1.2
				color: CRPrint.style.table.td.text
				fillColor: CRPrint.style.table.td.fill

	print_table: (src, snap, op) ->
		form = DSL[src.options.form]
		nc = [] # new list of columns to handle {} in grid fields
		sc = {} # source key for custom grid columns
		hd = [] # header row

		# list all custom key/vals from the {} in grid fields
		for c in form.view.grid.fields
			if getType(c) isnt 'string'
				for k,v of c
					sc[k] = v

		# list all keys from the source columns
		for c in src.columns
			continue if c in ['colstatus_change']
			nc.push c
			if c.indexOf('.') > -1
				[cf, cs, ck] = c.split('.')
				if DSL[cf]?.fields[ck]?.view.label
					tx = DSL[cf].fields[ck].view.label
				else
					tx = ck.proper()
			else if form.fields[c]?.view.label
				tx = form.fields[c].view.label
			else if sc[c]?
				tx = sc[c]
			else
				tx = c.proper()
			hd.push
				text: tx
				style: 'th'

		# set column size
		wd = _.fill(Array(nc.length), 'auto')
		for k,v of hd
			continue if wd[k] is '*'
			wd[k] = '*' if v.text.length > CRPrint.max_auto_column_length

		# insert all the row data
		ln = [hd]
		for dt in src.rows.parsed
			rc = for i,c of nc
				v =  dt[c + '_auto_name'] or dt[c] or ''
				if v.indexOf('<br/>') >= 0
					vl = v.split('<br/>')
					stack: for vr in vl
						tx = textOnly(vr)
						wd[i] = '*' if tx.length > CRPrint.max_auto_column_length
						tx
					style: 'td'
				else
					fd = DSL[src.options.form]
					fv = fd.fields[c]
					if not (_.isUndefined(fv.model.source) || _.isString(fv.model.source))  and fv.model.type in ['int', 'decimal'] and (not (_.endsWith(c, '_id') || c== 'id')) and c in fd.view.grid.fields
						decimal_split = ("" + fv.model.rounding).split(".")
						precision = if fv.model.type is 'int' or decimal_split.length == 1 then 0 else decimal_split[1].length
						prefix_str = if fv.view.class is 'currency' and App?.company.format_currency is 'Yes' then App.company.currency_prefix else ''
						number_separator = if App?.company.separate_numbers is 'Yes' then App.company.number_separator else ''
						decimal_separator = App.company.decimal_separator
						v = $.fn.dataTable.render.number(number_separator, decimal_separator, precision, prefix_str).display(v)
					tx = textOnly(v)
					wd[i] = '*' if tx.length > CRPrint.max_auto_column_length
					text: tx
					style: 'td'
			ln.push rc

		# if all columns are auto, set all to equal size
		if wd.indexOf('*') is -1
			wd = _.fill(Array(nc.length), '*')

		tb = []
		if snap?.patient?
			tb.push
				table:
					widths: ['*']
					body: [[
						text: form.view.label + ' List'
						style: 'title_text'
					]]
				style: 'title'
				layout: CRPrint.template_no_lines()

		tb.push
			margin: [0, 0, 0, 0]
			table:
				widths: wd
				headerRows: 1
				body: ln
			layout: 'lightHorizontalLines'

		tb

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CRPrintList extends CRPrintListBase
