class CRPrint extends CRView

	@default_margins: [18, 72, 18, 54]
	@hidden_sections: ['demographics']
	@max_auto_column_length: 32
	@max_field_label_length: 100

	@style:
		header:
			fill: '#000000'
			text: '#ffffff'
			num:  '#aaaaaa'
		title:
			text: '#ffffff'
			fill: '#555555'
			num:  '#aaaaaa'
		footer:
			text: '#ffffff'
			fill: '#333333'
		group:
			line: '#000000'
		section:
			line: '#999999'
			text: '#777777'
		field:
			label: '#777777'
			value: '#000000'
		table:
			th:
				text: '#333333'
				fill: '#ffffff'
			td:
				text: '#000000'
				fill: '#ffffff'

	@base_images: ->
		source = []
		if App.company.theme?.pdf_logo?.url?
			source.push dataImage 'logo', App.company.theme.pdf_logo.url
		source

	@generate_pdf: (dd, print_msg, filename, dfd) ->
		prettyNotify print_msg if print_msg
		if App.feature.nes
			@print_backend '/print/client/' + filename, filename, dd, dfd
		else
			pdf = pdfMake.createPdf(dd)
			dfd.resolve()
			if isBrowserSafari()
				pdf.download()
			else
				pdf.print()
		return

	@load_images: (source, callback) ->
		images = {}
		if source.length > 0
			$.when.apply($, source).done (args...) =>
				args = [args] if getType(args[0]) isnt 'array'
				for i in args
					continue if not i[1]
					images[i[0]] =
						data: i[1]
						width: i[2]
						height: i[3]
				callback(images)
				return
		else
			callback(images)
		return

	@print_backend: (url, filename = '', postdata = null, dfd = null, method = 'POST') ->
		pdf_download = false

		$('#iprint').remove()
		$('#application').append(
			'<div id="iprint">' +
			'<form id="iprint_form" target="iprint_frame"' +
			' method="' + method + '" action="' +
			(if method is 'GET' then CRBaseURL else CRBackend) + url + '">' +
			'<input type="hidden" name="filename" value="' + escape(filename) + '" />' +
			'<input type="hidden" name="download" value="' + pdf_download + '" />' +
			'<input type="hidden" id="iprint_document" name="document" value="" />' +
			'</form><iframe name="iprint_frame" id="iprint_frame"></iframe></div>'
		)
		$('#iprint_document').val(JSON.stringify(postdata)) if postdata

		fr = $('#iprint_frame')
		fr.load(->
			PDF = document.getElementById('iprint_frame')
			PDF.focus()
			if not pdf_download
				PDF.contentWindow.print()
				dfd.resolve() if dfd
		)
		$('#iprint_form').submit()
		dfd.resolve() if dfd and pdf_download
		return

	@print_blank: (args...) ->
		(new CRPrintBlank()).print(args...)

	@print_form: (args...) ->
		(new CRPrintForm()).print(args...)

	@print_forms: (args...) ->
		(new CRPrintForms()).print(args...)

	@print_list: (args...) ->
		(new CRPrintList()).print(args...)

	@stylize: (tx, forcewrap = false) ->
		st = []
		cn = (nd, sl) ->
			for n in nd.childNodes
				if n.childNodes? and n.childNodes.length > 0
					cn n, sl.concat(n.nodeName)
				else if getType(n.nodeValue) isnt 'null'
					v = n.nodeValue.replaceAll("\n\n", "\n")
					if forcewrap
						v = v.split(' ').map((w) ->
							if w.length > 80
								w.split('').join('\u200b')
							else
								w
						).join(' ')
					t = text: v
					for s in sl
						t.bold = true if s is 'B'
						t.italics = true if s is 'I'
						t.underline = true if s is 'U'
					st.push t
		cn $('<span>' + tx + '</span>')[0], []
		st

	@template_footer: (logo, stamp) ->
		f =
			fx: 'footer'
			src: """
				({
					"margin": [0, 9, 0, 0],
					"table": {
						"widths": ["45%", "10%", "45%"],
						"body": [[
							#{JSON.stringify(logo)}
						,{
							"text": currentPage.toString() + " of " + pageCount,
							"fontSize": 10,
							"alignment": "center",
							"bold": true,
							"margin": [0, 16, 0, 32],
							"color": "#{CRPrint.style.footer.text}",
							"fillColor": "#{CRPrint.style.footer.fill}"
						},
							#{JSON.stringify(stamp)}
						]]
					},
					"layout": #{JSON.stringify(CRPrint.template_no_lines())}
				})
			"""

	@template_header: (form, snap) ->
		if snap?.patient?
			title = formatFullName([snap.patient.firstname, snap.patient.middlename, snap.patient.lastname])
			info = joinValid([snap.patient.gender||'', if snap.patient?.dob then getAge(snap.patient.dob) + ' year old. (' + snap.patient?.dob + ')'  else '']) +
						' ' + (if (snap.patient.external_id||'') or (not (snap.patient.ssn||'')) then DSL.patient.fields.external_id.view.label else 'SSN') + ': ' +
						joinValid([snap.patient.external_id||'', ''], ' / ') +
						'\n' + joinValid([snap.patient.home_street, snap.patient.home_street2, snap.patient.home_city, (snap.patient.home_state||'') + ' ' + (snap.patient.home_zip||'')])
		else
			title = DSL[form].view.label + ' List'
			info = ''

		h =
			margin: [0, 0, 0, 0]
			table:
				widths: ['*', '*']
				body: [[
					text: title
					fontSize: if title.length < 20 then 24 else 18
					margin: [18, 20, 0, 6]
					color: CRPrint.style.header.text
					fillColor: CRPrint.style.header.fill
				,
					text: info
					fontSize: 10
					lineHeight: 1.3
					alignment: 'right'
					margin: [0, 18, 18, 6]
					color: CRPrint.style.header.text
					fillColor: CRPrint.style.header.fill
				]]
			layout: CRPrint.template_no_lines()

	@template_logo: (images) ->
		if images?.logo?.data?
			[iw, ih] = boundBox(180, 26, images.logo.width, images.logo.height)
			logo =
				image: images.logo.data
				width: iw
				height: ih
				alignment: 'left'
				margin: [18, 10 + (26 - ih)/2, 0, 34]
				color: CRPrint.style.footer.text
				fillColor: CRPrint.style.footer.fill
		else
			logo =
				text: ''
				fillColor: CRPrint.style.footer.fill

	@template_no_lines: ->
		tb =
			hLineWidth:
				fx: 'line'
				src: '(0)'
			vLineWidth:
				fx: 'line'
				src: '(0)'

	@template_stamp_filled: (src) ->
		return '' if not src.subforms.records[0]?.preset?
		p = src.subforms.records[0].preset
		f = 'Filled by ' + p.created_by_auto_name||'N/A'
		f += ' on ' + dateTimeFromUTC(p.created_on) if p.created_on
		f

	@template_stamp_printed: ->
		'Printed by ' + App.user.displayname + ' on ' + dateTimeFromTZ(new Date())

	@template_stamp_reviewed: (src) ->
		if (not src.subforms.records[0]?.preset?) or
		(not src.subforms.records[0]?.options?.form) or
		(not (DSL[src.subforms.records[0].options.form].model.access.review?.length > 0))
			return ''

		p = src.subforms.records[0].preset
		if p.reviewed_on
			f = 'Reviewed by ' + cleanup_name(p.reviewed_by_auto_name||'N/A')
			f += ' on ' + dateTimeFromUTC(p.reviewed_on)
		else
			f = '' # no need to print PENDING REVIEW on paper
		f
