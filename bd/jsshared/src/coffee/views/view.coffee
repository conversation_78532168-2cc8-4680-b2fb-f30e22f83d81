
class CRView extends B.View

	id: ''
	tpl: ''
	lastactiveelement: null

	initialize: (options) ->
		if options?
			for k, v of options
				@[k] = v
		@render()
		@load()
		# setTimeout ->
		# 	@$('[data-toggle="tooltip"]').tooltip()
		# 	if (not _.isEmpty(options?.mode)) && options?.mode != 'read'
		# 		focus$ = options.el.find('.controls:visible :visible:not([disabled])[tabindex=0]:first')
		# 		if (focus$.length == 0)
		# 			focus$ = options.el.find('.controls-grid:visible :input:visible:not([disabled]):first')
		# 		focus$.focus()
		# 	return
		# , 500
		return

	load: ->
		@lastactiveelement = document.activeElement
		return

	render: ->
		if @id isnt '' and @tpl isnt ''
			@$el = $('#' + @id).template(@tpl, @)
			@el = @$el[0]
		else if @tpl isnt ''
			@$el.template(@tpl, @)
		else if @id isnt ''
			@$el = $('#' + @id).template(@id, @)
			@el = @$el[0]
		this

	tab_action: (fx, action) =>
		tabaction = 'tab_' + fx + '_' + action
		taboverride = findClosest(@, tabaction + '_override', false)
		if taboverride # this allows parent class to override tab_[fx]
			taboverride()
		else if @[tabaction]?
			@[tabaction]()
		else
			false

	tab_can: (action) ->
		@tab_action 'can', action

	tab_do: (action) ->
		@tab_action 'do', action

	unload: ->
		@undelegateEvents()
		if @autounload?
			for v in @autounload
				continue if (not @[v]?) or (not @[v]) or (getType(@[v]) isnt 'object')
				if @[v].unload?
					@[v].unload()
				else
					for sk, sv of @[v]
						sv.unload() if sv and sv.unload?
		$(@lastactiveelement).focus()
		@


class CRViewCollection extends CRView

	root: ''
	# if inheriting from this class, set to {} in load()
	views: {}

	count: ->
		Object.keys(@views).length

	empty: ->
		@count() is 0

	insert: (k, v) ->
		@views[k] = v
		if @root is ''
			@$el.append(v.$el)
		else
			@$(@root).append(v.$el)
		return
