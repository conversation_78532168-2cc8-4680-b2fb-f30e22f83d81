
class FlyoutHTMLView extends CRView

	className: 'navtabitem'
	autounload: ['view']

	unload: ->
		unloadComponent('FlyoutHTMLViewer', @tabid)
		super()
		return

	cancel: (args...) =>
		Flyout.hide()
		@options.dfd.reject(args...)
		@unload()
		return

	load: ->
		props =
			html: @options.html
			el: @$el
			parent: @
			label: @options.label
			viewid: @tabid
		loadComponent('FlyoutHTMLViewer', @parent.el[0], @tabid, props)
		return

