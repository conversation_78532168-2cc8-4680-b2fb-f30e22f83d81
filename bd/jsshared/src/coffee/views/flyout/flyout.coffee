
class Flyout extends B.View

	@hide: ->
		if $('#flyout').find('.form').length>1
			$('#flyout').find('.form:last').remove()
			$('#flyout').find('.form:last').removeClass('hide-close-button')
			# $('#flyout').find('.form:last').show()
		else
			$('body').removeClass('has-flyout')
			$('#flyout').empty()
		$(window).scrollTop $(window).data('lasttop')
		return

	@open: (options) ->
		###
			form (required)
			action
			label
			link
			links
			linkid
			fields (for dashboard popovers)
			record
			preset
			card
			row
			changeset
			override_formdata
			tab_can_*_override() # used for FlyoutCardView, FlyoutCardSubformView
			tab_label() # used for FlyoutHTMLView, FlyoutCardSubformView
		###

		options.dfd = $.Deferred()
		@show(options)
		container = $($('#flyout > .form:last')[0])
		if options.form
			container.attr('form', options.form)
		options.flyout = new FlyoutView(options: options, el: container, parent: @, audit_mode: true)
		options.dfd

	@show: (opts) ->
		$(window).data('lasttop', $(window).scrollTop())
		$(window).scrollTop 0
		$('body').addClass('has-flyout')
		flyouts = $('#flyout').find('.form').length
		if flyouts > 0
			$('.form').addClass('hide-close-button')
			$('.lvl-' + flyouts - 1 + '-flyout').addClass('hide-close-button')
			fodiv = $('<div class="form lvl-' + flyouts + '-flyout"></div>')
			for prop, value of (opts?.style or {})
				fodiv.css(prop, value)
			fodiv.css('z-index', getNextZIndexFlyoutModelInception())
			$('#flyout .blocker').before(fodiv)
		else
			$('#flyout').template('flyout')
			$('#flyout .form').css('z-index', getNextZIndexFlyoutModelInception())
		
		# check if we need to add compact class
		flyout = $('#flyout')
		if flyout.length > 0
			siblings = flyout.siblings()
			if siblings.hasClass('compact')
				flyout.addClass('compact')
			else
				flyout.removeClass('compact')
		return

class FlyoutView extends CRViewCollection

	tpl: 'navtab'
	tabs: 0

	autounload: ['views']

	load: ->
		@views = {}
		label = if @options.label? then @options.label else DSL[@options.form].view.label
		ActionClass = getDynamicClass(@options.action or 'FlyoutCardView')
		@insert('flyout_root', new ActionClass(parent: @, tabid: 'flyout_root', label: label, options: @options, audit_mode: @options.audit_mode))
		return
