
class CardMultiView extends CRView

	className: 'navtabitem'
	autounload: ['view']

	cancel: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.reject(args...)
		return

	unload: ->
		unloadComponent('DSLCardMultiForm', @cid)
		super()
		return

	load: ->
		$('#flyout').find('.form:last').css('left', '0')
		@options.isFlyout = true
		@options.cancel = @cancel
		loadComponent('DSLCardMultiForm', @parent.el[0], @cid,  @options)
		return

	save: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.resolve(args...)
		return

