
class FlyoutCompareView extends CRView

	className: 'navtabitem compare'
	autounload: ['view']

	cancel: =>
		Flyout.hide()
		@options.dfd.reject()
		@unload()
		return

	load: ->
		@view = new FlyoutCompareFieldView(el: @$el, parent: @, viewid: @tabid, options: @options)
		@view.save = @save
		@view.cancel = @cancel
		return

	save: =>
		Flyout.hide()
		@options.dfd.resolve()
		@unload()
		return


class FlyoutCompareFieldView extends CRView

	tpl: 'card-read'

	autounload: ['ddr']
	change_err: 'Sorry! Cannot approve/reject this approval request.<br/><br/>' + ERR_CONTACT

	approve_reject: (decision) ->
		cs = @options.changeset
		aj = Ajax.async
			url: '/form/' + cs.form + '/' + cs.form_id + '/' + decision + '/'
			type: 'POST'
			data: {}
		aj.done (data, status, xhr) =>
			@save()
		aj.fail (xhr, status, data) =>
			dfd = prettyError false, @change_err
			dfd.done =>
				@cancel()
				return
			return
		return

	cancel: ->
		@parent.cancel()
		return

	load: ->
		cs = @options.changeset
		compare = _.clone(cs.data)
		if cs.change_type is 'create'
			for k,v of compare
				if DSL[cs.form].fields[k]?.model.type is 'subform'
					for kk,vv of v
						for kkk,vvv of vv
							compare[k][kk][kkk] = '&check;'
				else
					compare[k] = '&check;'
		@ddr = new DSLDrawRead(form: cs.form, id: @viewid, el: @$el, parent: @, record: cs.form_id, compare: compare, mode: 'read', menutitle: DSL[cs.form].view.label)
		dfd = @ddr.draw()
		dfd.done =>
			return
		dfd.fail (err) =>
			if err
				if getType(err) is 'string'
					prettyError false, err
				else if err?.error?
					prettyError false, err.error
				else if err?.message?
					prettyError false, err.message
				@cancel() # make sure this doesn't mess with patient loading
			return
		return

	tab_can_approve: ->
		true

	tab_can_cancel: ->
		true

	tab_can_open: ->
		@options.changeset.patient_id?

	tab_can_reject: ->
		true

	tab_do_approve: ->
		@approve_reject 'approve'
		return

	tab_do_cancel: ->
		@cancel()
		return

	tab_do_open: ->
		@cancel()
		cs = @options.changeset
		navto = ['patient', cs.patient_id, cs.form, cs.form_id]
		App.appview.navigation.navto(navto, @parent.label)
		return

	tab_do_reject: ->
		@approve_reject 'reject'
		return
