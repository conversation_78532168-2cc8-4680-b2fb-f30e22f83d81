
class FlyoutCardView extends CRView

	className: 'navtabitem'
	autounload: ['view']

	cancel: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.reject(args...)
		return

	unload: ->
		unloadComponent('DSLCardView', @cid)
		super()
		return

	load: ->
		if @options.card?
			card = @options.card
		else if @options.record?
			if Auth.can_update_any(@options.form)
				card = 'edit'
			else
				card = 'read'
		else
			card = 'addfill'

		on_loaded = (ref) =>
			@view = ref
			@options.on_loaded(@view) if @options.on_loaded
			if card is 'read'
				@view.tab_can_cancel_override = -> true
				@view.tab_can_list_override = -> false
			@view.save = @save
			@view.cancel = @cancel

			for k,v of @options
				if k.substr(0, 8) is 'tab_can_' and k.substr(-9) is '_override'
					@view[k] = v

		cardProps = 
			form: @options.form
			el: @$el
			parent: @
			label: @options.label
			viewid: @tabid
			link: @options.link
			links: @options.links
			linkid: @options.linkid
			fields: @options.fields or []
			record: @options.record
			preset: @options.preset
			disableRouting: true
			isFlyout: true
			card: card
			form_override_url: @options.form_override_url || ''
			save: @saved
			cancel: @cancel
			forceReadOnly: @options.forceReadOnly
			audit_mode: @options.audit_mode
			closeWithOutAck: @options.closeWithOutAck
			override_field_data: @options.override_field_data or {}
			autoRecoverEnabled: @options.autoRecoverEnabled,
			ddRef: on_loaded
			tabViewActions: @options.tabViewActions or {}
			flyoutAllowWidget: @options.flyoutAllowWidget or false
			showSections: @options.showSections or false
			validation_errors: @options.validation_errors or {}
		key = @options.form + ":" + card
		renderComponent = "DSLCardView"
		if window.rxRegistry and window.rxRegistry[key]
			renderComponent = key
		loadComponent(renderComponent, @parent.el[0], @cid, cardProps)
		return

	save: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.resolve(args...)
		return

