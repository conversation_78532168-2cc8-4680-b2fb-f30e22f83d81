
class PreviewFillCardView extends CRView

	className: 'navtabitem'
	autounload: ['view']

	cancel: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.reject(args...)
		return

	unload: ->
		unloadComponent('DSLCardWithPreview', @cid)
		super()
		return

	load: ->
		if @options.card?
			card = @options.card
		else if @options.record?
			if Auth.can_update_any(@options.form)
				card = 'edit'
			else
				card = 'read'
		else
			card = 'addfill'

		on_loaded = (ref) =>
			@view = ref
			if card is 'read'
				@view.tab_can_cancel_override = -> true
				@view.tab_can_list_override = -> false
			else
				@view.save = @save
			@view.cancel = @cancel

			for k,v of @options
				if k.substr(0, 8) is 'tab_can_' and k.substr(-9) is '_override'
					@view[k] = v
		cardProps = 
			form: @options.form
			el: @$el
			parent: @
			label: @options.label
			viewid: @tabid
			link: @options.link
			links: @options.links
			linkid: @options.linkid
			fields: @options.fields or []
			record: @options.record
			preset: @options.preset
			disableRouting: true
			isFlyout: true
			card: card
			save: @saved
			cancel: @cancel
			audit_mode: @options.audit_mode
			override_field_data: @options.override_field_data or {}
			ddRef: on_loaded
			preview: @options.preview
		
		$('#flyout').find('.form:last').css('left', '0')
		loadComponent('DSLCardWithPreview', @parent.el[0], @cid,  cardProps)
		return

	save: (args...) =>
		@unload()
		Flyout.hide()
		@options.dfd.resolve(args...)
		return

