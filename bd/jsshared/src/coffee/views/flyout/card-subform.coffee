
class FlyoutCardSubformView extends CRView

	className: 'navtabitem'
	autounload: ['view']

	cancel: (args...) =>
		Flyout.hide()
		@options.dfd.reject(args...)
		@unload()
		return

	unload: ->
		unloadComponent('DSLCardView', @cid)
		super()
		return

	load: ->
		if @options.card?
			card = @options.card
		else
			card = 'addfill'
		@options.is_subform = true

		on_loaded = (ref) =>
			@options.on_loaded(ref) if @options.on_loaded?
			@view = ref
			if @view.cardform
				@view.cardform.cancel = @cancel
				@view.cardform.tab_do_cancel = @cancel
			@view.cancel = @cancel
			@view.tab_can_cancel_override = -> true

			if card is 'addfill' or card is 'add'
				if @view.cardform
					@view.cardform.save = @save
					@view.cardform.tab_do_save = @save
				@view.save = @save
			else if card is 'read'
				@view.tab_can_print_override = -> false
				@view.tab_can_edit_override = -> false
				@view.tab_can_list_override = -> false
				@view.tab_can_archive_override = -> false

			for k,v of @options
				if k.substr(0, 8) is 'tab_can_' and k.substr(-9) is '_override'
					@view[k] = v
			return
	
		cardProps = 
			form: @options.form
			el: @$el
			parent: @
			viewid: @tabid
			link: @options.link
			links: @options.links
			linkid: @options.linkid
			record: @options.record
			preset: @options.preset
			audit_mode: @options.auto_name
			disableRouting: true
			isFlyout: true
			card: card
			save: @saved
			cancel: @cancel
			subform_parent: @options.subform_parent
			autoRecoverEnabled: @options.autoRecoverEnabled,
			validation_errors: @options.validation_errors or {}
			ddRef: on_loaded
			grid: @options.grid
			showSections: @options.showSections or false

		loadComponent('DSLCardView', @parent.el[0], @cid, cardProps)
		return

	save: =>
		val = @view.cardform?.ddf?.verify()
		return if not val
		Flyout.hide()
		@options.dfd.resolve
			grid: @options.grid
			values: val
		@unload()
		return

	tab_label: (action) ->
		if @options.tab_label?
			@options.tab_label(action)
		else
			false
