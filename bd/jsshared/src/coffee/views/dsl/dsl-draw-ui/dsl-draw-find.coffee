class DSLDrawFind extends DSLDraw

	###
		can init with:
			form
			id
			el
			parent
			allow_radio
			allow_checkbox
	###
	events:
		'change .control-label select'               : 'event_field_select_change'   # select-static
		'change .control-label input[type!=checkbox]': 'event_field_input_change'    # datetime, input, select-dynamic
		'keypress .control-label input[type=text]'         : 'on_enter_press'

	event_field_select_change: (e) ->
		@handle_field_event $(e.target)
		return

	event_field_input_change: (e) ->
		@handle_field_event $(e.target)
		return
	
	on_enter_press: (e) ->
		if e.keyCode is 13
			if @onPressEnter
				@onPressEnter()

	handle_field_event: (e) ->
		id = e.attr('id')
		f = e.parent().parent().find('[type="hidden"]')
		field = @fields[id]
		if field and @options?.parent?.onChange
			@options.parent.onChange?({event: e, filter: @values(), field: field})

		srch = $('#' + id + '_search')
		if srch.val() is App.user.username
			f.select2('close')
		return

	draw: ->
		drawn = {}
		@query_module = @options.form.startsWith("__")
		no_ar_del = ['schedule_event']
		for t in ['basic', 'advanced']
			fa = @options.el.find('.find' + 'basic')
			kf = _.clone(DSL[@options.form].view.find[t])
			if !no_ar_del.includes(@options.form) and DSL[@options.form].view.open is 'read' and Auth.can_update_any(@options.form) and Auth.not_blocked_update(@options.form) and t is 'advanced' and !@query_module
				DSL[@options.form].fields.deleted.model.type = 'boolean'
				kf.push 'archived'
				kf.push 'deleted'
			for k in kf
				continue if drawn[k]?
				continue if not Auth.field_can_read(@options.form, k)
				drawn[k] = true
				@draw_field(fa, k, DSL[@options.form].fields[k]) # sends the 'find' view to each field
			@attach_data()
		
		DSLFields.load(@options.el)
		if !_.isEmpty(drawn)
			#HB-5223
			@options.el.removeClass('hide')
		return

	draw_field: (fa, k, v) ->
		id = window.next_id('fnd')
		fx = DSLDraw.get_field_control v
		fx = 'select' if fx is 'radio'    and not (@options.allow_radio? and @options.allow_radio)
		fx = 'select' if fx is 'checkbox' and not (@options.allow_checkbox? and @options.allow_checkbox)
		fx = 'select' if v.view.findunique
		fx = 'input' if fx in ['area', 'grid', 'link']
		fx = 'checkbox' if k in ['archived', 'deleted']

		filters_fixed = _.cloneDeep(@options.filtersPresetFixed || {})
		filters = _.cloneDeep(@options.initialFilters || {})
		@preset = _.merge(filters, filters_fixed)

		ff = if @preset[k]? then @preset[k] else null

		if v.model.type == 'date' and v.view.findrange
			lbst = $('<label class="control-label" for="' + id + '_st"><span title="' + escapeHTML(v.view.label) +
							'">' + v.view.label + ' Start</span></label>')
			vst = _.cloneDeep(v)
			vst.view.readonly = false
			dst = @draw_base_field(@options.form, 'find', 'field', fx, id + '_st', k+ '_st', vst, ff)
			fa.append(lbst.append(dst.html))

			lbed = $('<label class="control-label" for="' + id + '_ed"><span title="' + escapeHTML(v.view.label) +
							'">' + v.view.label + ' End</span></label>')
			ved = _.cloneDeep(v)
			ved.view.readonly = false
			ded = @draw_base_field(@options.form, 'find', 'field', fx, id + '_ed', k + '_ed', ved, ff)
			fa.append(lbed.append(ded.html))
		else
			lb = $('<label class="control-label" for="' + id + '"><span title="' + escapeHTML(v.view.label) +
							'">' + v.view.label + '</span></label>')
			v = _.cloneDeep(v)
			v.view.readonly = false
			v.model.multi = true if v.view.findmulti
			if !ff and !@options.ignoreFindFilter
				ff = if v.view.findfilter? then v.view.findfilter else null
			# if not ff
			d = @draw_base_field(@options.form, 'find', 'field', fx, id, k, v, ff)
			fa.append(lb.append(d.html))
		return

	find: ->
		true

	unload: ->
		@undelegateEvents()
		DSLFields.unload(@options.el)
		@
