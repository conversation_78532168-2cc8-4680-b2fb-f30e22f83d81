class DSLDrawRead extends DSLDrawForm

	force_readonly: true

	show_reviewed: ->
		return (not (DSL[@options.form].model.access.review?.length > 0)) or
			(not Auth.can_review(@options.form)) or
			(not @subforms.records[0]?.preset?)

		p = @subforms.records[0].preset
		if p.reviewed_on
			f = 'Reviewed by ' + cleanup_name(p.reviewed_by_auto_name||'N/A')
			f += ' on ' + dateTimeFromUTC(p.reviewed_on)
		else
			f = 'PENDING REVIEW'
		@f.b.find('.reviewed').removeClass('hide').html f
		return
