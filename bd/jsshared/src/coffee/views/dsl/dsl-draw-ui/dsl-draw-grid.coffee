class DSLDrawGrid extends DS<PERSON>raw

	###
		can init with:
			form
			id
			el
			parent
			link
			links
			linkid

			embedValues?: unknown[];
			rowSelectionMode?: "single" | "multi";
			rowSelection?: boolean;
			readOnly?: boolean;
	###

	# if inheriting from this class, re-declare these arrays/objects
	columns: []
	custom_columns: []
	custom_dsl_fields: {}
	filter: {}
	rows: {raw: [], parsed: [], dr: []}

	print: ->
		BootstrapDialog.show
			title: 'Select Print View'
			message: 'Do you want to print the list in a grid-layout, all the individual forms in detail, or a blank template form?'
			buttons: [
				label: 'Print List'
				action: (dialog) =>
					dialog.close()
					prettyNotify 'Generating printout...'
					setTimeout =>
						dm = findClosest(@, 'snapshot_data')()
						op = {}
						dfd = CRPrint.print_list @, dm, op, 'Displaying preview...'
						dfd.done ->
							prettyNotify()
							return
						dfd.fail ->
							prettyNotify()
							return
						return
					, 50
					return
			,
				label: 'Print All Forms'
				action: (dialog) =>
					dialog.close()
					prettyNotify 'Generating printout...'
					setTimeout =>
						dm = findClosest(@, 'snapshot_data')()
						op =
							newpage: true
						dfd = CRPrint.print_forms @, dm, op, 'Displaying preview...'
						dfd.done ->
							prettyNotify()
							return
						dfd.fail ->
							prettyNotify()
							prettyError 'Cannot print forms!', 'Sorry! There was an error trying to print the forms.<br/><br/>' + ERR_CONTACT
							return
						return
					, 50
					return
			,
				label: 'Print Blank Template'
				action: (dialog) =>
					dialog.close()
					prettyNotify 'Generating printout...'
					setTimeout =>
						dm = findClosest(@, 'snapshot_data')()
						op = {}
						dfd = CRPrint.print_blank @, dm, op, 'Displaying preview...'
						dfd.done ->
							prettyNotify()
							return
						dfd.fail ->
							prettyNotify()
							prettyError 'Cannot print blank template!', 'Sorry! There was an error trying to print the blank template form.<br/><br/>' + ERR_CONTACT
							return
						return
					, 50
					return
			]
			type: BootstrapDialog.TYPE_PRIMARY
			animate: false
			size: BootstrapDialog.SIZE_LARGE
		return

	callback: (options) ->

		return if options.chain.length is 0
		if options.chain.length is 1 
			setTimeout =>
					if options.items.length < 0
						options.chain[0]
							data: []
							recordsTotal:0
							recordsFiltered: 0
						return 
					options.chain[0]
						data: options.items
						recordsTotal:100
						recordsFiltered: 100
					if options.items.length < 1
						@$('.dataTables_info').html('No Results')
					else
						str = 'Viewing 1'+' - '+options.items.length 
						@$('.dataTables_info').html(str)
					return

				,300	
		else
			options.chain[0]
				chain: options.chain[1..]
				columns: options.columns
				items: options.items
				refid: options.refid
				page: options.page or 0
				pages: options.pages or 0
		return

	set_custom_columns: (cols) ->
		@custom_columns = cols
		return

	trigger_row_changed_event: () ->
		#when a row manually added to table you should trigger this event to let react know
		@options?.onRowCountChangeEvent?(@)
		return

	set_custom_dsl_fields: (fields) ->
		@custom_dsl_fields = fields
		return

	column_list: (fd) ->
		cols = []
		cols_override = @options.customColumns or null
		subfields  = @options.fieldEmbed?.model?.subfields or {}
		@custom_renderer  = Object.keys(subfields)
		widths = @options.columnsWidth or fd.view.grid.width or null
		labels = @options.columnsLabel or fd.view.grid.label or null
		columns_to_loop_over = cols_override or fd.view.grid.fields
		if not _.isEmpty(columns_to_loop_over)
			for k, fidx in columns_to_loop_over
				if @options.rankLevel and @options.rankLevel != 'none' and k is 'rank'
					continue
				w = null
				lbl = null
				if widths and fidx < widths.length and widths[fidx]
					w = widths[fidx] + '%'
				if labels and fidx < labels.length and labels[fidx]
					lbl = labels[fidx]
				if @options.fieldEmbed and subfields[k]
					cols.push
						property: k
						label: lbl or subfields[k].label
						sortable: false
						width: w
						subfield: subfields[k]
				else if getType(k) is 'object'
					cols.push
						property: _.keys(k)[0]
						label: lbl or _.values(k)[0]
						sortable: false
						width: w
				else if k.indexOf('.') > -1
					[ks, kp] = k.split('.')
					if _.isEmpty(fd.fields[ks].model.sourcefilter) # use static source
						kf = fd.fields[ks].model.source
					else # use first source if dynamic
						kf = _.keys(fd.fields[ks].model.sourcefilter)[0]
					if !@options.fieldEmbed?.view?.embed?.query
						continue if not Auth.field_can_read(kf, k)
					if DSL[kf]?.fields[kp]?
						cols.push
							property: kf + '.' + k
							label: lbl or DSL[kf].fields[kp].view.label
							sortable: false
							width: w
				else
					if !@options.fieldEmbed?.view?.embed?.query
						continue if not Auth.field_can_read(@options.form, k)
					continue if not fd?.fields[k]?.view
					cl =
						property: k
						label: lbl or fd.fields[k].view.label
						sortable: true
					if fd.fields[k].model.type is 'datetime'
						cl.width = 190
					else if fd.fields[k].model.type in ['date', 'time']
						cl.width = 95
					if w
						cl.width = w
					cols.push cl
			mxc = MAX_GRID_COLUMNS
		else
			for k,v of fd.fields
				if (not v.model.autoinsert and getType(v.model.source) isnt 'string')
					cols.push
						property: k
						label: v.view.label
						sortable: true
			mxc = Math.round(MAX_GRID_COLUMNS / 2) # don't show all the max possible columns

		cols = cols[0..mxc-1]
		if @show_colstatus_change()
			cols.push
				property: 'colstatus_change'
				label: 'Changes'
				sortable: false
		cols

	customDraw: -> # HB-4346, HB-4320, get column list for re-rendering the grid
		fd = DSL[@options.form]

		fld_array = fd.view.grid.fields
		_.remove fld_array, (i) -> _.startsWith(i, 'customlabdrawdate_')  #need better approach to handle the this removal in actual dsl a filed can start with date_ prefix
		fd.view.grid.fields = _.uniq fld_array.concat(@custom_columns)

		if Object.keys(@custom_dsl_fields).length isnt 0
			fd.fields = Object.assign(fd.fields, @custom_dsl_fields)
		
		if not(fd.view.grid.hide_columns?)
			fd.view.grid.hide_columns = []

		cols = @column_list(fd)

		cols
	
	input_field_change: (e) =>
		f = $(e.target)
		v = f.val() || ""
		d = f.data()
		return if not d.id
		if d.originalType in ['int', 'decimal'] and d.format
			v = v.replace(/[,A-Za-z$%]/g, '')
			f.val(numeral(v).format(d.format))
		if getType(v) is 'string'
			v = v.trim()
		if d.required and !v
			f.addClass('subfield-required')
		else
			f.removeClass('subfield-required')
		@options.embedValues = @options.embedValues.map (r) =>
			if r.id is d.id
				r[d.property] = v
			return r
		return
	
	input_focusout: (e) =>
		@options?.embedValidateTransform?()
	
	setranks: () ->
		rank = 1
		preV = _.cloneDeep(@options.embedValues)
		@options.embedValues = []
		for m in @table_grid.rows({'selected': true})[0]
			data = @table_grid.row(m).data()
			p = (preV.find (r) => r.id == data.id) or {}
			@options.embedValues.push(_.merge(p, {
				id: data.id
				rank: rank
			}))
			rank++
		@table_grid.draw(false)
		@options?.embedValidateTransform?()
		return

	push_embed: (id, silent=false) ->
		if  @options.rowSelectionMode == 'single'
			@options.embedValues = []
		if !@options.embedValues
			@options.embedValues = []
		return if @options.embedValues.find (r) => r.id == id
		@options.embedValues.push({
			id: id
			rank: if _.last(@options.embedValues)?.rank then parseInt(_.last(@options.embedValues)?.rank) + 1 else 1
		})
		if !silent
			@table_grid.draw(false)
			@options?.embedValidateTransform?()

	sort_data_byrank: (data) ->
		selected = data.filter (item) -> item._selected and item.rank isnt ""
		notSelected = data.filter (item) -> not item._selected or item.rank == ""
		selected.sort (a, b) -> a.rank - b.rank
		sortedData = selected.concat notSelected
		return sortedData
	
	disable_mouse_selection: =>
		dt = @table_grid
		ctx = dt.settings()[0];
		selector = ctx._select.selector;
		$(dt.table().container()).off( 'mousedown.dtSelect', selector ).off( 'mouseup.dtSelect', selector ).off( 'click.dtSelect', selector );
		$('body').off( 'click.dtSelect' + dt.table().node().id.replace(/[^a-zA-Z0-9\-\_]/g, '-'));
	
	rank_render: (data, type, row, meta) =>
		if !row._meta.rank
			return ""
		if @options.rowSelectionMode is 'single' or @options.readOnly
			return "<div>" + row._meta.rank + "</div>"
		return  '<div><div class="move-up">▲</div>' + row._meta.rank + '<div class="move-down">▼</div></div>'
	
	subfield_renderer: (k, sf) =>
		renderer = (data, type, row, meta) =>
			if !["text", "int", "decimal"].includes(sf.type)
				return "-"
			if @options.readOnly
				v = row._meta[k]
				if sf.format
					v = numeral(v).format(sf.format)
				return v or '-'
			if not row._meta._selected
				return "-"
			t = sf.type
			v = row._meta[k] or ''
			if sf.type is "int" or sf.type is 'decimal'
				t = "number"
				if sf.format
					t = "text"
					v = numeral(v).format(sf.format) if v
			clr = ""
			attr = ""
			if sf.required
				attr = " data-required=required "
				if getType(v) is 'string'
					v = v.trim()
				if !v
					clr = " subfield-required"
			return '<input type="' + t + '" class="form-control' + clr + '" value="' + (v) + '" data-type="' + t + '" data-format="' + sf.format + '" data-original-type="' + sf.type + '" data-property="' + k + '" data-id="' + row.id + '" ' + attr + '>'
		return renderer


	draw: (dg_cb) ->
		@first_render = true
		@init_basic_filters()
		form_name = @options.form
		fd = DSL[@options.form]
		cols = @customDraw()
		selcols = for k in cols
			k.property
		@columns = selcols
		cb = @callback

		# selected_limit = MAX_GRID_ROWS
		selected_limit = 100

		# these three functions, when defined in the parent class, determine if
		#  this grid gets additional dynamic data or solely static data
		# e.g. ipadscout/PatientFindView for gridload
		#      gridstatic:
		#        data.force_resize not automatically set
		#        findClosest 'loaded' not called automatically

		gl = findClosest(@, 'gridload', false)
		gs = findClosest(@, 'gridstatic', false)
		go = findClosest(@, 'gridoptions', ->)()
		@dg = @$('.repeater').attr('id', @id + 'grid')
		@dg.data('view', @)
		@dg.data('init', =>
			@dg.removeData 'init'

			if @table_grid
				@dg.DataTable().clear().destroy()
				$(@dg).empty()
				@$('#'+@id + 'grid').html('')
			data_tables_cols = []
			if  @options.rowSelection
				data_tables_cols.push({title: '', data: '', orderable: false,  class: 'select-checkbox', width: '34px'})
		
			#Migrate cols structure for datatables grid
			is_style = fd.view.grid?.style
			# hide_columns_dom = []
			that = @
			cols.forEach (element, index) ->
				push_obj = {}
				visible = true
				if element.subfield
					push_obj = {sortable: false, title: element.label, data: element.property, width: element.width, visible: visible, render: that.subfield_renderer(element.property,element.subfield)}
					data_tables_cols.push(push_obj)
					return
				if is_style and is_style?[element.property]?.style
					merge_style = is_style[element.property]
					obj = {sortable: element.sortable || false, title: element.label, data: element.property}
					push_obj = Object.assign(obj, merge_style.style)
				else
					push_obj = {sortable: element.sortable || false, title: element.label, data: element.property}
				# Specify Renderer
				fv = fd.fields[element.property]
				if not (_.isUndefined(fv?.model?.source) || _.isString(fv?.model?.source))  and fv?.model?.type in ['int', 'decimal'] and (not (_.endsWith(element.property, '_id') || element.property == 'id')) and element.property in fd?.view.grid.fields
					decimal_split = ("" + fv.model.rounding).split(".")
					precision = if fv.model.type is 'int' or decimal_split.length == 1 then 0 else decimal_split[1].length
					prefix_str = if fv.view.class is 'currency' and App.company.format_currency is 'Yes' then App.company.currency_prefix  else ''
					number_separator = if App.company.separate_numbers is 'Yes' then App.company.number_separator else ''
					decimal_separator = App.company.decimal_separator
    				if fv.view.class.includes('numeral') and fv.view.format
						push_obj['render'] = (data, type, row) ->
							if !data
								data = ""
							data = data.toString()
							data = data.replace(/[,A-Za-z$%]/g, '')
							numeral(data).format(fv.view.format)
					else
						push_obj['render'] = $.fn.dataTable.render.number( number_separator, decimal_separator, precision, prefix_str)

				if element.width
					push_obj['width'] = element.width
				push_obj['visible'] = visible
				data_tables_cols.push(push_obj)	
			if  @options.rankLevel and @options.rankLevel != 'none'
				push_obj = {title: 'Rank Order', data: 'rank', orderable: false, class: 'rank', render: @rank_render}
				data_tables_cols.push(push_obj)
			url = '/form/' + @options.form + '/'
			columnDefs = []
			selectOptions = false
			if  @options.rowSelection
				columnDefs = [{ orderable: false,  class: 'select-checkbox', targets: 0}]
				selectOptions = { info: true, style: @options.rowSelectionMode || "single", selector: 'td:first-child' }
			@table_grid =   @dg.DataTable(
								fnDrawCallback: dg_cb
								rowCallback: (row, data, index) =>
									if data._meta._selected
										$(row).find('.form-control').off('onchange').on('change', @input_field_change)
										$(row).find('.form-control').off('focusout').on('focusout', @input_focusout)
										@table_grid.row(row._DT_RowIndex).iterator( 'row', ( ctx, idx ) => 
											if ctx._select.style is 'single'
												@table_grid.rows().iterator( 'row', ( ctx, idx ) => 
													ctx.aoData[ idx ]._select_selected = false
													$( ctx.aoData[ idx ].nTr ).removeClass( ctx._select.className )
												)
											ctx.aoData[ idx ]._select_selected = true
											$( ctx.aoData[ idx ].nTr ).addClass( ctx._select.className )
										)
									if @options.readOnly
										@table_grid.row(row._DT_RowIndex).off('click')
								autoWidth: false
								searching: false
								processing:true
								emptyTable: '0 items'
								oLanguage:
									sZeroRecords : '0 items'
								language:
									paginate: 
										previous: '<span class="glyphicon glyphicon-chevron-left"></span>'
										next: '<span class="glyphicon glyphicon-chevron-right"></span>'
									processing: '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span>'
								lengthChange: false
								sortable: true
								serverSide: true
								destroy: true
								pageLength: selected_limit
								scrollCollapse: false
								scrollY: '100%'
								responsive: false
								scrollX: true
								pagingType:'simple'
								order: []
								columns: data_tables_cols
								columnDefs: columnDefs,
								select: selectOptions
								dom: 'Bfrtip'
								buttons: [
									{
										extend: 'csv'
										className:'hide'
										title: @options.form.toUpperCase() + 'S'
										messageTop: moment(Date.now()).format("dddd, MMMM Do YYYY, h:mm:ss a")
									}
									{
										extend: 'excel'
										className:'hide'
										autoFilter:true
										title: @options.form.toUpperCase() + 'S'
										messageTop: moment(Date.now()).format("dddd, MMMM Do YYYY, h:mm:ss a")
										customize:(xlsx) =>
													
													sheet = xlsx.xl.worksheets['sheet1.xml']
													mergeCells = $('mergeCells', sheet)
													mergeCells[0].children[0].remove()
									}
									{
										extend: 'pdf'
										className:'hide'
										title: @options.form.toUpperCase() + 'S'
										messageTop: moment(Date.now()).format("dddd, MMMM Do YYYY, h:mm:ss a")
										customize: (doc) =>
											doc.styles.tableHeader.fontSize  = 10
											if cols.length > 7
												doc.pageOrientation = 'landscape'
												doc.styles.tableHeader.alignment = 'left'
									}
									{
										extend: 'copy'
										title: @options.form.toUpperCase() + 'S'
										messageTop: moment(Date.now()).format("dddd, MMMM Do YYYY, h:mm:ss a")
									}
									{
										extend: 'collection'
										text: 'CSV'
										autoClose: true,
										buttons:[
											{
												text:'Export Selected'
												action:(e,dt,button,config) =>
													@export_all = false
													@table_grid?.ajax?.reload () =>
														@$('.buttons-csv').trigger('click')
														return
													,false
											}
											{
												text: 'Export All'
												action:(e,dt,button,config) =>
													@export_all = true
													@table_grid?.ajax?.reload () =>
														@$('.buttons-csv').trigger('click')
														#after export document, reset to inital page
														setTimeout => 
															@table_grid?.ajax?.reload()
														,300
														return
											}
										]
									}
									{
										extend: 'collection'
										text: 'Excel'
										autoClose: true,
										buttons:[
											{
												text:'Export Selected'
												action:(e,dt,button,config) =>
													@export_all = false
													@table_grid?.ajax?.reload () =>
														@$('.buttons-excel').trigger('click')
														return
													,false
											}
											{
												text: 'Export All'
												action:(e,dt,button,config) =>
													@export_all = true
													@table_grid?.ajax?.reload () =>
														@$('.buttons-excel').trigger('click')
														#after export document, reset to inital page
														setTimeout => 
															@table_grid?.ajax?.reload()
														,300
														return
											}
										]
									}
									{
										extend: 'collection'
										text: 'PDF'
										autoClose: true,
										buttons:[
											{
												text:'Export Selected'
												action:(e,dt,button,config) =>
													@export_all = false
													@table_grid?.ajax?.reload () =>
														@$('.buttons-pdf').trigger('click')
														return
													,false
											}
											{
												text: 'Export All'
												action:(e,dt,button,config) =>
													@export_all = true
													@table_grid?.ajax?.reload () =>
														@$('.buttons-pdf').trigger('click')
														#after export document, reset to inital page
														setTimeout => 
															@table_grid?.ajax?.reload()
														,300
														return
											}
										]
									}
									]
								infoCallback:(settings, start, end, max, total, pre) =>
																		
									if start < 2 and settings.aiDisplay.length < selected_limit
										@$('.pagination').addClass('hide')
										return

									if settings.aiDisplay.length < selected_limit
										@$(".paginate_button.next").addClass('disabled')
									
									# Example array of objects containing key-value pairs
									per_page_list = [
										{ value: 100, label: "100" }
									]
									

									# Construct options for select box dynamically
									optionsHtml = ""
									for option in per_page_list
										selected = if option.value == selected_limit then "selected" else ""
										optionsHtml += "<option value='#{option.value}' #{selected_limit}>#{option.label}</option>"

									# Add select box and input box before .paginate_button.previous
									@$(".paginate_button.previous").before("<li class=\"paginate_button page-item\"><select id=\"#{@id}select\" >#{optionsHtml}</select><button class=\"per-page-btn\" ><i class=\"glyphicon glyphicon-chevron-down\"></i></button></li><li class=\"paginate_button page-item\"><p class=\"per-page\" >per page</p></li>")

									@$(".paginate_button.previous").after('<li class="paginate_button page-item" ><span style="padding: 1px 1px 1px 1px;
									"><input type="number" min="1" max="1000" id="'+@id + 'input'+'"/></span></li><li class=\"paginate_button page-item\"><p class=\"per-page\" > of ' + per_page_list[0]?.value + ' </li>')
									@$('#'+@id + 'input').unbind "keypress"
									@$('#'+@id + 'input').keyup((event) =>
										keycode = event.keyCode && event.keyCode || event.which
										if keycode == 13
											page_number = @$('#'+@id + 'input').val()
											if page_number.length > 0
												@table_grid.page(parseInt(page_number) - 1).draw('page')
												setTimeout => 
													@$('#'+@id + 'input').val(page_number)
												,200
										@input_page_number = @$('#'+@id + 'input').val()
										return
									)

									$('#'+ @id + 'select').change((event) => #Osama need testing
										selected_limit = parseInt($(event.currentTarget).val())
										@filter.limit = parseInt($(event.currentTarget).val())
										@reload(@filter);
										return
									)

									@$('#'+@id + 'input').val(parseInt(start/selected_limit) + 1)  #Osama need testing
									return
										
								createdRow:(row, data, index) => # show background color for archived record
									classes = data.rowOpts?.rowAttributes?.classes or []
									for c in classes
										$(row).addClass(c)
									if data?._meta?.__row_color and getType(data?._meta?.__row_color) is 'string'
										row?.style?.setProperty('background-color', data._meta.__row_color, 'important')
									else if data?._meta?.__row_data?.__row_color and getType(data?._meta?.__row_data?.__row_color) is 'string'
										row?.style?.setProperty('background-color', data._meta.__row_data.__row_color, 'important')
									else if data.archived
										row.style.setProperty('background-color','#E0D3C5', 'important')
										# Added legacy Feature HB-4607 to the new grids
										aj = Ajax.async
											url: '/form/' + form_name + '/?filter=id:' + data.id + '&archived'
										aj.done (data, status, xhr) =>
											msg = 'Archived By ' + data[0].updated_by_auto_name + ' on ' + dateTimetoLocal data[0].updated_on
											$(row).attr('data-original-title', msg).tooltip({placement : 'bottom'})
									else if data.__unavailable
										row.style.setProperty('background-color','#E0D3C5', 'important')
								ajax:(params, callback, settings) =>
									urlOpt = {}
									@refid = refid = Math.random()
									page = if params.start > 0 then params.start/selected_limit else params.start
									if params.order.length > 0
										sortObj = params.order[0]
										colObj = data_tables_cols[sortObj.column]
										urlOpt = {sortDirection:sortObj.dir,sortProperty:colObj.data,limit:selected_limit,pageIndex:page}
									else
										urlOpt = {limit:selected_limit,pageIndex:page}
										if @options.sortProperty?
											urlOpt.sortProperty = @options.sortProperty
										if @options.sortDirection?
											urlOpt.sortDirection = @options.sortDirection

									if gs
										cb
											chain: [gs, callback]
											columns: cols
											items: []
											refid: refid
											page: 0
											pages: 0
										return
									chain = if gl then [gl, callback] else [callback]
									start = params.start
									@$('.dataTables_info').html('')
									if @export_all == true or  @options.embedValues
										urlOpt.limit  = 9999999
										urlOpt.pageIndex  = 0
									data_table_callback = (data) =>
										@first_render = false
										if @options.fieldEmbed
											@original_data_copy = data
										flt = []
										for dt in data
											rd = {}
											if @options.gridMakeRowHTML?
												rd = @options.gridMakeRowHTML(dt, selcols, fd.fields, @make_row_html)
											else
												rd = @make_row_html(dt, selcols, fd.fields, {custom_renderer: @custom_renderer})
											r = rd.fr
											r.rowOpts = rd.rowOpts
											r._meta = dt
											flt.push(r) if r
										if gl
											cb
												chain: chain
												columns: cols
												items: flt
												refid: refid
												page: page
												pages: if flt.length <  selected_limit  then page else 999
										else
											try
												callback({data:flt,"recordsTotal": 9999999,"recordsFiltered": 9999999})
											catch error
												xsv = ''
										findClosest(@, 'loaded')(data.length)
										if @options.readOnly
											@disable_mouse_selection()
											@bind_click_events()
										@$('.pagination').addClass('hide')
										showGridSumColumn(@, @options.sumInfo, data) if @options?.sumInfo
										setTimeout =>
											if @options.onReadyGrid?
												@options.onReadyGrid()
											if page < 1 and flt.length < selected_limit
												@$('.pagination').addClass('hide')
											else
												@$('.pagination').removeClass('hide')

											if flt.length > 0
												start = start + 1
												to = ((start - 1) + flt.length)
												str = 'Viewing '+start+ ' - '+to
												str+= if flt.length >= selected_limit then ' of '+to+' ' else ' '
												# @$('.dataTables_info').removeClass('hide')
												@$('.dataTables_info').html(str)

												if flt.length < selected_limit
													@$('.paginate_button.next').addClass('disabled')
											else
												@$('.paginate_button.next').addClass('disabled')
											
											#  offscreen render issue HB-4885
											if !@dg.is ':visible' 
												@interval = setInterval =>
													if @dg.is ':visible'
														try
															@table_grid.columns.adjust()
															clearInterval(@interval)
														catch error
															return
												, 500
											return
										,200
									url = @source_url(urlOpt)
									vdef = @options.fieldEmbed
									r_type = "GET"
									r_data = undefined
									if vdef.view.embed.request_type == "POST" and vdef.view.embed.query
										r_type = "POST"
										url = '/query/' + vdef.view.embed.query + '/?legacy=true'
										r_data =
											code: vdef.view.embed.query
											params: @options.filtersPresetFixed || {}
									aj = Ajax.async
										url: url
										type: r_type
										data: r_data
									aj.done (data, status, xhr) =>
										return if @refid isnt refid
										@rows = {raw: [], parsed: [], dr: []}
										data = if (@export_all == true or @options.embedValues) then data else data[0..selected_limit-1] 
										@export_all = false
										selectall = @options.fieldEmbed?.view?.grid.selectall
										if selectall and @first_render
											_.forEach data, (r) => @push_embed(r.id, true) 
										else if selectall and !@options.rowSelection
											_.forEach data, (r) => @push_embed(r.id, false) 
										ev = @options.embedValues || []
										selected_ids = []
										evData = {}
										if selectall or @options.rowSelection
											selected_ids = ev.map((r) -> 
												evData[parseInt(r.id)] = r
												return parseInt(r.id)
												)
										archived_ids = []
										if @options.fieldEmbed
											data = data.map((r) -> 
												r._selected = false
												r.rank = ""
												if parseInt(r.id) in selected_ids
													r = _.merge(r, evData[parseInt(r.id)])
													r._selected = true
													r.rank = evData[r.id].rank?.toString?() or ""
												return r
												)
											data = @sort_data_byrank(data)
											current_ids = data.map((r) -> 
															return  parseInt(r.id)
														)
											archived_ids = _.difference(selected_ids, current_ids)
										if archived_ids.length == 0
											data_table_callback(data)
										else
											if vdef.view.embed.query
												data_table_callback(data)
												return
											url = url + '&archived=all&' + generateDSLFormQueryParams(@options.form, {filter: {id: archived_ids}}, true).join('&')
											aaj = Ajax.async
												url: url
											aaj.done (a_data, a_status, a_xhr) =>
												a_data = a_data.map((r) -> 
													r._selected = false
													r.rank = ""
													if parseInt(r.id) in selected_ids
														r = _.merge(r, evData[parseInt(r.id)])
														r._selected = true
														r.__unavailable = true
														r.rank = evData[r.id].rank?.toString?() or ""
													return r
													)
												data =  data.concat(a_data)
												data =  @sort_data_byrank(data)
												data_table_callback(data)
											aaj.fail (a_xhr, a_status, a_data) =>
												callback({data:[],"recordsTotal": 0,"recordsFiltered": 0})
												if a_status is 403
													$('.findbarcontainer').addClass('hide') #Osama need testing
													$('.dataTables_empty').text(a_data?.error or 'Unexpected Error Contact Support')
												if a_xhr?.status is 404 and
														@options?.form and
														DSL[@options.form].view.max_rows? and
														(DSL[@options.form].view.max_rows is 1)
													findClosest(@, 'loaded')(0)
												return
										return
									aj.fail (xhr, status, data) =>
										callback({data:[],"recordsTotal": 0,"recordsFiltered": 0})
										if status is 403
											$('.findbarcontainer').addClass('hide') #Osama need testing
											$('.dataTables_empty').text(data?.error) #Osama need testing

										if xhr?.status is 404 and
												@options?.form and
												DSL[@options.form].view.max_rows? and
												(DSL[@options.form].view.max_rows is 1)
											findClosest(@, 'loaded')(0)
										return
							)
			$.fn.dataTable.ext.errMode = 'none'
			if not @options.parent.overrideClickEvent
				return

			@table_grid.on 'select', (e, dt, type, indexes) =>
				if indexes?[0]?[0]?.row
					dt.row(indexes[0][0].row).select()
				@setranks()
				return

			@table_grid.on 'deselect', (e, dt, type, indexes) =>
				@setranks()
				return

			@bind_click_events()

			)
	
	bind_click_events: =>
		@table_grid.off('click').on 'click', 'tbody tr', (e) =>
			target = $(e.target)
			if @options.fieldEmbed and !@options.readOnly
				if target.hasClass('form-control')
					return
				if target.hasClass('move-down') or target.hasClass('move-up')
					originalRow = e.target.closest('tr')._DT_RowIndex
					dir = if target.hasClass('move-down') then 1 else -1
					moveTo = originalRow + dir
					if moveTo < 0 or moveTo >= @table_grid.rows({'selected': true}).data().length
						return
					orgData = @table_grid.row(originalRow).data()
					moveData = @table_grid.row(moveTo).data()
					@table_grid.row(originalRow).data(moveData)
					@table_grid.row(moveTo).data(orgData)
					@setranks()
					return
			return if target.hasClass('select-checkbox') or target.hasClass('rank')
			event = target.attr('event')
			if event
				d = @table_grid.rows().data()[$(e.target).closest('tr').index()] || {}
				@options.gridRowColumnEvent(event, d)
				return
			p = target.parent()
			if p.hasClass('fieldlink gridlink')
				url = p.attr('href')
				if url.startsWith('#')
					App.reactNav.goTo(url)
				else						
					window.open(url, '_blank')
			else
				d = @table_grid.rows().data()[$(e.target).closest('tr').index()] || {}
				@options.parent.overrideClickEvent(e, d)
			return
		return

	init: -> # loads the data (does not matter if UI is visible/hidden)
		@dg.data('force_resize', true).data('init')?()
		return

	init_basic_filters: ->
		@filter = {}
		if !_.isEmpty(@options.initialFilters)
			@filter = @options.initialFilters or {}

		for k in DSL[@options.form].view.find.basic
			if !@options.fieldEmbed?.view?.embed?.query
				continue if not Auth.field_can_read(@options.form, k)
			v = DSL[@options.form].fields[k]
			if not (getType(v.view.findfilter) in ['null', 'undefined'])
				if !@options.ignoreFindFilter
					@filter[k] = v.view.findfilter
		return

	params: (options) ->
		if @options.sumInfo?.sumColumn?
			options.limit = 1000
		p = if options?.limit and options?.limit > 10 then DSLDraw.default_params(false, options.limit) else DSLDraw.default_params(false)
		# simplifiied list without subforms
		if options.fields?
			p.push 'fields=' + options.fields
		else if DSL[@options.form].view.grid.fields.join('/').indexOf('.') is -1
			p.push 'fields=list'
		if @options.filtersPresetFixed
			if @options.overrideGridFilters
				@filter = @options.filtersPresetFixed
			else
				@filter = _.merge(@filter, @options.filtersPresetFixed)

		if @options.gridparams?
			for gp in @options.gridparams
				p.push gp
		fd = DSL[@options.form]

		# sorting
		if options.sortProperty?
			p.push 'sort=' + (if options.sortDirection is 'asc' then '' else '-') + options.sortProperty
		else if fd.view.grid.sort.length > 0
			for f in fd.view.grid.sort
				p.push 'sort=' + f

		# pagination
		p.push 'page_number=' + options.pageIndex

		# only show rows relevant to specific patient_id etc.
		presetFilter = @options.filtersPresetFixed or {}
		[linkkey, oid] = @param_link()

		if @filter.archived?
			if @filter.archived is 'true'
				p.push 'archived=all'
			else if @filter.archived is 'all'
				p.push 'archived=all'

		if @filter.deleted?
			if @filter.deleted is 'true'
				p.push 'deleted=all'
			else if @filter.deleted is 'all'
				p.push 'deleted=all'

		# return without addition filters if searching
		if @filter._keywords
			p.push 'keywords=' + encodeURIComponent(@filter._keywords)
			if !@options.filtersPresetFixed or _.isEmpty(@options.filtersPresetFixed)
				return p
		# filtering
		for k,v of @filter
			kk = k
			k = k.replace(/_st/gi,'') # Handle date range fields
			continue if (k is 'archived') or (k is 'deleted') 
			
			if not fd.fields[k]?
				if not fd.fields[kk]?
					if k.startsWith('_x')
						p.push k + '=' + v
						continue
					else
						console.log('Field Embed Filter Not Found For Field', k)
						continue
				else
					k = kk
			fv = fd.fields[k]
			wcp = wcs = ''

			if (typeof v == 'string') and v != 'null'
				v =  v.replace(/\s/g, '*') if (v != '') and (v?) and (v != undefined)

			if (fv.model.type in ['json']) and (fv.view.control in ['area', 'input', 'link']) and v != 'null'
				wcp = '*' if App.company.filter_wc is 'Yes' or (fv.view.control in ['area', 'link'])
				wcs = '*'
			# remove all extra characters from custom-formatted input fields
			if fv.view.format and getType(v) is 'string'
				v = v.replace(/[^0-9]/gi, '*').replace(/(\*)+/g, '*')
			vv = if getType(v) is 'array' then v else [v]
			if fv.model.type is 'date' and fv.view.findrange
				vst = $.trim(v)
				ved = $.trim(@filter[k + '_ed'])
				continue if (vst is '' and ved is '')
				vf = ''
				if vst != '' and ved is ''
					ved = '01/01/2099'
				else if vst is '' and ved != ''
					vst = '01/01/1899'
				vst = moment(vst).format('MM/DD/YYYY')
				ved = moment(ved).format('MM/DD/YYYY')
				re = /^(0?[1-9]|1[0-2])\/(0?[1-9]|1\d|2\d|3[01])\/(19|20)\d{2}$/
				continue if (not vst.match(re) or not ved.match(re))
				vv = [moment(vst).format('MM/DD/YYYY') + '..' + moment(ved).format('MM/DD/YYYY')]
			for v in vv
				v = $.trim(v)
				continue if (v is '')
				p.push 'filter=' + k + ':' + wcp + encodeURIComponent(v) + wcs

		p

	param_link: ->
		# we want to filter rows by patient_id, even if link is "---"
		# http://pm.envoylabs.net/youtrack/issue/HB-1659
		linkkey = oid = false
		if @options.link
			linkkey = @options.link
			oid = getLinkID(linkkey, @options.linkid, @$el) or @options.record
			# this is what make_row_html will use to determine if row is from active intake
			@options.linkactive = [linkkey + '_id', oid]
		else
			delete @options.linkactive if @options.linkactive?

		if getType(@options.links) is 'array' and @options.links.length > 1
			linkkey = @options.links[0]
			oid = getLinkID(linkkey, @options.linkid, @$el) or @options.record

		[linkkey, oid]

	reload: (fl = {}) ->
		@filter = fl
		@export_all = false
		@table_grid?.ajax?.reload()
		return

	show_colstatus_change: ->
		Auth.can_request(@options.form) and Auth.can_write_any(@options.form)

	source_url: (options = {}) ->
		url = '/form/' + @options.form + '/?'
		if @options.gridSourceOverrideURL
			url = @options.gridSourceOverrideURL
			if(url.indexOf('?') > -1)
				url += '&'
			else
				url += '?'
		url + @params(options).join('&')	

	unload: ->

		@undelegateEvents()
		$dg?.DataTable()?.clear()?
		@$('.repeater').removeData('view')

		@


$.fn.resizegrid = ->
	# only runs if :visible
	t = $(@)
	return if not t.attr('id')?

	# only runs once
	if t.data('init')?
		t.data('init')()
		t.removeData('init')

	#Draw datatables when there is window resize delegate calls
	if t.data('force_resize')?
		t.removeData('force_resize')
		t.DataTable().columns.adjust()
	
	p = t.closest('.repeaterwrap')
	nh = window.innerHeight - p.offset().top - 1
	return if p.find('.dataTables_scrollBody').height() is parseInt(nh - 150)
	p.find('.dataTables_scrollBody').height(parseInt(nh - 150))
	this
