class DSLDrawSubform extends DS<PERSON>raw
	@HAS_CHANGED_IGNORE_FIELD_TYPE = ['barcode', 'esign', 'file', 'grid', 'paycard', 'subform']
	###
		can init with @options:
			all options from DSLDrawForm
			wrapper

		shmap{}   - see DSLRecordSubform
		subform{} - see DSLRecordSubform
	###

	compare_field: (k) ->
		if not @compare?[k]?
			undefined
		else if @compare[k + '_auto_name']?
			id: @compare[k], text: @compare[k + '_auto_name']
		else
			@compare[k]
	
	get_subform_parent: ->
		@options?.parent?.parent?.subform_parent or null

	has_changed: (vctx, k, with_value = false) ->
		if not @field_nodes[k]
			console.error('DSLDrawSubform.has_changed: Field not found', k)
			return true
		
		if DSLDrawSubform.HAS_CHANGED_IGNORE_FIELD_TYPE.includes(@field_nodes[k].data('fieldtype'))
			if with_value
				return {
					changed: true
					value: DSLFields.value_get(@field_nodes[k])
				}
			console.error('DSLDrawSubform.has_changed: Field type not supported', k)
			return true
		
		if not @changed_ctx
			@changed_ctx = {}
		
		if not @changed_ctx[vctx]
			@changed_ctx[vctx] = _.cloneDeep(@preset or {})
		
		cv = DSLFields.value_get(@field_nodes[k])
		lv = @changed_ctx[vctx][k]

		changed = false
		
		if getType(cv) is 'array' and getType(lv) is 'array'
			changed = !_.isEqual(cv.sort(), lv.sort())
		else if getType(cv) is 'object' and getType(lv) is 'object'
			changed = !_.isEqual(cv, lv)
		else
			if !cv and !lv
				changed = false
			else if `cv == lv || lv == cv`
				changed = false
			else
				changed = true
		if App.feature.debug
			console.log('DSLDrawSubform.has_changed', k, cv, lv, changed)
		@changed_ctx[vctx][k] = cv
		if with_value
			return {
				changed: changed
				value: cv
			}
		return changed
	
	get_parent_form: ->
		return @options.wrapper.subforms.child[@options.form]
	
	get_field_in_parent: ->
		return @options.wrapper.subforms.fieldrev[@options.form]
	
	trigger_if_prefill: ->
		pf = @get_parent_form()
		pk = @get_field_in_parent()
		if pf and pk 
			if !@if_by
				@if_by = allSubformIfTriggersFields(pf, pk)
			triggerIfConditions(@options.wrapper.subforms.formmap, pf, pk)
		else
			@if_by = []
	
	draw_field: (k, vs, sec) ->
		df = @default(k, vs)
		dfempty = false
		if df is '' or df is '[]'
			dfempty = true
		else if getType(df) is 'array'
			if df.length is 0
				dfempty = true
			else if (df.length is 1) and (df[0] is null) or (df[0] is '')
				dfempty = true
		else if getType(df) is 'object'
			dfempty = _.isEmpty(df)
		filter_fields = false
		freq = if @options.fields?.required? then @options.fields.required else @options.fields
		fopt = if @options.fields?.optional? then @options.fields.optional else @options.fields
		allowed_edit = true;
		parent_form = @options.wrapper.options.form;
		main_form_preset = @options.wrapper.subforms.forms?[0]?.preset or {}
		main_presets = @preset 
		if !main_presets.__main_form_preset
			main_presets.__main_form_preset = main_form_preset
		if  @options.mode in ['add', 'addfill', 'edit'] and parent_form != @options.form and DSL[parent_form]?.fields[k]
			parent_dd = @options.wrapper.subforms.formmap[parent_form]
			if parent_dd and DSL[parent_form].fields[k].model.type == 'subform'
				ps_subform = parent_dd.preset or {}
				if !ps_subform.__main_form_preset
					ps_subform.__main_form_preset = main_form_preset
				allowed_edit = Auth.field_not_blocked(parent_form, @options.wrapper, ps_subform, k);
		if @force_readonly or !allowed_edit or
				(@options.mode in ['list', 'read']) or
				(@options.mode in ['add', 'addfill'] and (not Auth.field_can_create(@options.form, k, @preset))) or
				(@options.mode in ['edit'] and (not Auth.field_can_update(@options.form, k, @preset))) or
				(@options.mode in ['edit'] and (not Auth.field_not_blocked(@options.form, @options.wrapper, main_presets, k)))
			v = $.extend(true, {}, vs)
			v.view.readonly = true
		else if freq?.length > 0
			v = $.extend(true, {}, vs)
			if k in freq
				v.model.required = true #HB-2840 - do not change read-only status
			else if not (v.model.required and dfempty)
				v.view.readonly = true
				filter_fields = true
		else if fopt?.length > 0
			v = $.extend(true, {}, vs)
			if k in fopt
				v.view.highlight = '#F1EB9C'
				v.view.readonly = false
			else if not (k in fopt)
				v.view.readonly = true
				if !_.isEmpty(v.model.subfields)
					for g, j of v.model.subfields
						g.readonly = false
		else
			v = vs

		id = window.next_id('fld')
		card_width = @$el.parent().width()
		if @options.parent.parent.inline
			card_width = 10
		fx = DSLDraw.get_field_control v, card_width
		fc = if (fx is 'grid') or (fx is 'subform') then 'grid' else 'field'
		if @options.parent.parent.inline
			fc = 'fieldinline'
		cm = @compare_field(k)

		fgc = ['form-group', @options.form, @options.form + '_' + k]
		fgc.push 'filter_fields' if filter_fields
		fgc.push 'prefilled' if not v.view.readonly and @prefill[k]?
		fgc.push 'highlighted' if v.view.highlight

		attr =
			class: fgc.join(' ')
			style: if v.view.highlight then 'background-color:' + v.view.highlight else ''
			tabindex: if v.view.highlight then 0 else -1
			field: k
			fieldid: id
			form: @options.form
			empty: dfempty
		
		addr_field = ['addr_1', 'addr_2', 'addr_city', 'addr_state', 'addr_zip', 'modifier']
		fcls = "form-horizontal form-col-#{Math.trunc(Math.abs(v.view.columns)) or if addr_field.includes(v.view.columns) then v.view.columns else 1}" + if v.view.offscreen then ' offscreen' else ''
		if v.view.class?.includes?('section-required-check')
			fcls += ' section-required-check'
		if v.view.offscreen then attr.tabindex = '-1'
		n = []
		if typeof v.view.columns is 'number' and Math.trunc(v.view.columns) < 0
  			n.push('<br />')
		if typeof v.view.columns is 'string' and v.view.columns.includes('addr_1')
			n.push('<div style="width:100%" />')
		n.push('<div class="' + fcls + '"><div ' + DSLFields.attr(attr) + '>')

		d = @draw_base_field(@options.form, 'form', fc, fx, id, k, v, df)
		d.data.section = sec

		n.push(@['get_container_' + fc](d, k, v, fx, cm, df, id, sec))
		n.push('</div></div>')
		if typeof v.view.columns is 'number' and Math.floor(v.view.columns) isnt parseFloat(v.view.columns)
  			n.push('<br />')
		html: n.join(''), data: d.data
	field_node_value: (v, d) ->
		# transforms may set data.raw on element if doing unit conversions
		if v.model.multi and (getType(v.model.source) is 'string') and (v.view.control is 'select')
			fv = d.select2('data')
			fv = [] if getType(fv) isnt 'array'
		else if v.model.multi and (v.view.control is 'select') and d.hasClass('activeselect2field')
			fv = d.select2('val')
			fv = [] if getType(fv) isnt 'array'
		else if (v.view.control is 'select') and d.hasClass('activeselect2field')
			fsd = d.select2('data')
			if not fsd
				fsd =
					id: DSLFields.value_get(d)
					text: ''
			if v.model.sourceid == 'mcr_ref' && fsd?.id?
				fv = fsd.mcr_ref
			else if v.model.sourceid == 'code' && fsd?.id?
				fv = fsd.id
			else if fsd?.text?
				fv = fsd.text
			else if fsd?.id?
				fv = fsd.id
			else
				fv = []
		else if v.view.control is 'checkbox'
			fv = DSLFields.value_get(d)
			fv = [] if v.model.multi and getType(fv) isnt 'array'
		else if d?.data('raw')?
			fv = d.data('raw')
		else
			fv = DSLFields.value_get(d)
		fv

	get_container_fieldinline: (d, k, v, fx, cm, df, id, sec) ->
		n = []
		nonfocusable = if v.view.readonly or v.view.offscreen then ' control-nonfocusable ' else ''
		n.push('<div class="controls inline-control' + nonfocusable +  '">')
		if @has_new_value(cm, df)
			n.push('<div class="newvalue col-md-offset-8 col-md-4 col-xs-offset-8 col-xs-4">' + (if cm.text? then cm.text else cm) + '</div>')
		if v.view.class.indexOf('unit') > -1
			n.push('<div class="input-group col-md-offset-0 col-md-3 col-xs-offset-1 col-xs-10">')
			n.push(d.html)
			
			n.push('<span class="input-group-addon showunit">&nbsp;</span>')
			n.push('</div>')
		else
			n.push(d.html)
		n.push('</div><div class="help-container">')
		if d.html && d.html.includes("ignoreRequired")
			n.push('<span class="help-block note"><b>WORKFLOW AUTOSET</b></span>')
		n.push('<span class="help-block warningmsg"></span>')
		n.push('<span class="help-block errormsg"></span></div>')
		n.join('')

	get_container_field: (d, k, v, fx, cm, df, id, sec) ->
		n = []
		label_class = ''
		if v.view.form_link_enabled
			if !v.model?.multi and v.model?.source and window.DSL[v.model.source] and (Auth.can_read(v.model.source) or Auth.can_update(v.model.source))
				label_class = ' clickable-label '
		if v.model.required
			n.push '<label class="control-label' + label_class + (v.view?.class?.includes("check-field") && " check-field" || "")  + (v.view?.class?.includes("status") && " status" || "") + (v.view?.class?.includes("discount") && " discount" || "") + (v.view?.class?.includes("important") && " important" || "") + (v.view?.class?.includes("money") && " money" || "") + (v.view?.class?.includes("claim-field") && " claim-field" || "") + (v.view?.class?.includes("fdb-field") && " fdb-field" || "") + (v.view?.class?.includes("cms-1500-field") && " cms-1500-field" || "") + '" for="' + id + '"><span title="' + v.view.label + '">' + escapeHTML(v.view.label) + '</span><div class="required-label">*</div>'
		else
			n.push('<label class="control-label' + label_class + (v.view?.class?.includes("check-field") && " check-field" || "") + (v.view?.class?.includes("status") && " status" || "") + (v.view?.class?.includes("discount") && " discount" || "") + (v.view?.class?.includes("important") && " important" || "") + (v.view?.class?.includes("money") && " money" || "") + (v.view?.class?.includes("claim-field") && " claim-field" || "") + (v.view?.class?.includes("fdb-field") && " fdb-field" || "") + (v.view?.class?.includes("cms-1500-field") && " cms-1500-field" || "") + '" for="' + id + '"><span title="' + escapeHTML(v.view.label) + '">' + v.view.label)
		if not isBrowserMobile() and @drs?.options?.form != 'audit' and v.model?.type? != 'json' and v.view.control != 'file' and v.view.control != 'link' and v.view.class != 'list' and @preset.id and v.model.type != 'subform' or @options.mode != 'add' 
			n.push('</span><i class="audit-trail fa fa-history"  data-html="true" ></i></label>')
		else
			n.push('</span></label>')
		nonfocusable = if v.view.readonly or v.view.offscreen then ' control-nonfocusable ' else ''
		n.push('<div class="controls' + (if v.view.class == 'unit' then ' unit-field-control' else '') + nonfocusable +  '">')
		if @has_new_value(cm, df)
			n.push('<div class="newvalue col-md-offset-8 col-md-4 col-xs-offset-8 col-xs-4">' + (if cm.text? then cm.text else cm) + '</div>')
		if v.view.class.indexOf('unit') > -1
			n.push('<div class="input-group col-md-offset-0 col-md-3 col-xs-offset-1 col-xs-10">')
			n.push(d.html)
			n.push('<span class="input-group-addon showunit">&nbsp;</span>')
			n.push('</div>')
		else
			n.push(d.html)
		n.push('</div>')
		is_flyout = @$el.parents('#flyout').length is 0
		if (not v.view.readonly) and d.data?.manage? and d.data.manage
			mngopt = []
			tindex = ""
			if v.view.offscreen
				tindex=' tabindex="-1"'
			if ((v.model.sourceid isnt 'id') and Auth.can_write_any(d.data.manage))
				if d.data.form == 'document' and d.data.manage == 'careplan_order' and Auth.can_create_any(d.data.manage)
					mngopt.push('<a href="#' + d.data.manage + '" class="manage-add"' + tindex + '></a>')
				mngopt.push('<a href="#' + d.data.manage + '" class="manage-list"' + tindex + '></a>') unless is_flyout
			else
				if Auth.can_create_any(d.data.manage)
					mngopt.push('<a href="#' + d.data.manage + '" class="manage-add"' + tindex + '></a>')
				if Auth.can_update_any(d.data.manage)
					if v.model.multi
						mngopt.push('<a href="#' + d.data.manage + '" class="manage-list"' + tindex + '></a>') unless is_flyout
					else
						mngopt.push('<a href="#' + d.data.manage + '" class="manage-edit"' + tindex + '></a>')
			if mngopt.length > 0
				n.push('<div class="manage">' + mngopt.join('') + '</div>')
		if d.html && d.html.includes("ignoreRequired")
			n.push('<span class="help-block note"><b>WORKFLOW AUTOSET</b><span>' + formatNote(v.view.note) + '</span></span>')
		else
			n.push('<div class="help-container"><span class="help-block note"><span>' + formatNote(v.view.note) + '</span></span>')

		n.push('<span class="help-block warningmsg"></span>')
		n.push('<span class="help-block errormsg"></span></div>')
		n.join('')

	get_container_grid: (d, k, v, fx, cm, df, id, sec) ->
		n = []
		gc = []
		nt = v.view.note
		if v.view.readonly
			gc.push 'readonly'
		if v.model.type is 'subform' and v.model.multi
			gc.push 'subform'
			if v.view.readonly and df.length > 0
				nt = (if nt is '' then '' else nt + '. ') +
						'Click on the row' + (if df.length > 1 then 's' else '') + ' above for additional details.'

		# do not show label if same as section, subform, or only item in section
		s = DSL[@options.form]?.model.sections[sec]?.fields
		sl_1 = s and (s.length is 1)
		if (v.view.label is sec) or (v.model.type is 'subform') or (sl_1)
			nl = ''
			label = '</span></label>'
			# label = 'History'
			# label += ' <i class="audit-trail-grid fa fa-history"  data-html="true" ></i></label>'
		else
			label = v.view.label
			if v.model.required
				label += ((v.view.control == "grid" or v.model.type = 'subform') && (sl_1) && " </span>" || '</span><div class="required-label">*</div>')
			if @options.mode == 'read'
				if _.isEmpty(label.trim())
					label = 'History'
				label += ' </span><i class="audit-trail-grid fa fa-history"  data-html="true" ></i>'
			label += '</label>'
		if v.model.required
			nl = '<label class="control-label '+ (if v.view.control == "grid" or v.model.type = 'subform' then "" else "required-label") + (v.view?.class?.includes("check-field") && " check-field" || "") + (v.view?.class?.includes("status") && " status" || "") + (v.view?.class?.includes("discount") && " discount" || "") + (v.view?.class?.includes("important") && " important" || "") + (v.view?.class?.includes("money") && " money" || "")  + (v.view?.class?.includes("claim-field") && " claim-field" || "") + (v.view?.class?.includes("fdb-field") && " fdb-field" || "") + (v.view?.class?.includes("cms-1500-field") && " cms-1500-field" || "") + '" for="' + id + '"><span title="' + escapeHTML(label) + '">' + label			
		else
			nl = '<label class="control-label' + (v.view?.class?.includes("check-field") && " check-field" || "") + (v.view?.class?.includes("status") && " status" || "") + (v.view?.class?.includes("discount") && " discount" || "") + (v.view?.class?.includes("important") && " important" || "") + (v.view?.class?.includes("money") && " money" || "")  + (v.view?.class?.includes("claim-field") && " claim-field" || "") + (v.view?.class?.includes("fdb-field") && " fdb-field" || "") + (v.view?.class?.includes("cms-1500-field") && " cms-1500-field" || "") + '" for="' + id + '"><span title="' + escapeHTML(label) + '">' + label
		n.push('<div class="controls-grid' + (if gc.length > 0 then ' ' + gc.join(' ') else '') + '">' + nl + d.html + '</div>')
		n.push('<div class="help-container"><span class="help-block note"><span>' + formatNote(nt) + '</span></span>')
		n.push('<span class="help-block warningmsg"></span>')
		n.push('<span class="help-block errormsg"></span></div>')
		n.join('')

	get_record_data: (k = null) ->
		if k
			@preset[k]
		else
			@preset

	has_new_value: (cm, df) ->
		return false if not @compare?
		return false if getType(cm) is 'undefined'
		return @has_new_value(cm.id, df) if cm.id?
		return false if cm is df
		if isFinite(cm) and isFinite(df)
			return parseFloat(cm) isnt parseFloat(df)
		return $.trim(cm) isnt $.trim(df)

	load: ->
		# this enables inserting code into any subform
		DynamicClass = getDynamicClass(@options.form, 'HandlerView')
		if DynamicClass
			@handler = new DynamicClass(
												form:      @options.form
												el:        @$el
												parent:    @
												link:      @options.wrapper.options.link
												links:     @options.wrapper.options.links
												linkid:    @options.wrapper.options.linkid
												mode:      @options.wrapper.options.mode
											)
		return

	load_handler: (dfd) ->
		if @handler?
			@handler.handle(dfd)
		else
			dfd.resolve()
		return

	note: (f, tx) ->
		@field_nodes[f]?.data('group').find('.note span').text(tx)
		return

	section_check: (s) ->
		return if not @sections[s]?
		val = true
		for d in @section_fields[s]
			continue if d.v.view.readonly or (not DSLFields.visible(d.node))
			dv = DSLFields.value(d.node)
			if (not _.isNumber(dv)) and _.isEmpty(dv)
				val = false
				break

		mnu = @sections[s]?.data('menu')?.removeClass('has-error')?.find('a span')
		mnu?.removeClass('sec-err-false sec-err-true sec-val-' + !val)
			.addClass('sec-val-' + val)
		return

	section_check_all: ->
		for s,v of DSL[@options.form].model.sections
			@section_check s
		return

	set_record: (drs) ->
		# preset  - merged existing data + prefill + initial values generated by front-end
		# compare - compare to old data in same row
		@drs = drs
		@preset = if drs.preset? then drs.preset else {}
		@compare = drs.compare
		@prefill = drs.prefill
		@shmap = drs.shmap
		@readonly_map = drs.readonly_map
		@required_if_map = drs.required_if_map
		@subform = drs.subform
		return

	set_section_map: (id, k, data={}) ->
		@section_map[id] = k
		@section_tab_map[id] = data.tabs if data.tabs
		@tab_toggle_map[id] = data.tab_toggles if data.tab_toggles
		return

	toggle_field: (k = false, show = true) ->
		return if not k
		show = false if @subform.hidden_by_root
		if DSL[@options.form].fields[k]?.model.type is 'subform'
			@options.wrapper.toggle_subform k, show
		else if @field_map[k]?
			for d in @field_map[k]
				group = d.data('group')
				if show == false and !@preset?[k] and (group.css('display') != 'none')
					DSLFields.clear(d)
				group?.toggle show
				field_id = group?.attr?('fieldid')
				return if not field_id
				field = group?.find('#'+field_id)
				return if field.length == 0
				if field?.is('input:text') and field?.attr('input_val') 
					field.val(field.attr('input_val'))
		return

	toggle_headers_menus: (s, show) ->
		return if !s
		sc = s.attr('secgroup')
		sch = $('#' + sc) #Osama need testing
		scnn = $('#' + sc + '_note')

		if show
			s.removeClass('hide')
			sch.removeClass('hide')
			scnn.removeClass('hide')
		else
			s.addClass('hide')
			sch.addClass('hide')
			scnn.addClass('hide')
		return

	toggle_section: (k = false, show = true) ->
		return if not k
		show = false if @subform.hidden_by_root
		if @sections[k]?
			s = @sections[k]
			@toggle_headers_menus s, show
		@update_section_tabs()
		return
	
	update_section_tabs: (force_allow = false) ->
		if force_allow or @options?.wrapper?.form_rendered
			TabController.handleSectionTabsIf(@)

	toggle_subforms: (show = true) ->
		@subform.hidden_by_root = not show
		for k,s of @sections
			@toggle_headers_menus s, show

		# make sure to show/hide all sections/fields correctly on display
		DSLRecordSubform.show_hide @, true, true if show
		return
