class DSLDrawForm extends DSLDrawFormEvents

	###
		can init with @options:
			form
			id
			el
			parent
			link
			links
			linkid
			fields
			record
			preset
			compare
			mode
			domcols
			menutitle

		@dom[]      - see DSLRecord
		@subforms{} - see DSLRecord
	###

	form_autorecover: false
	form_edited: false
	form_rendered: false
	form_missing_fields_log: false
	form_missing_fields_message: 'Please review the missing or invalid fields and try again.'

	form_error:
		form: ['There was an error trying to save your data.<br/><br/>' + ERR_CONTACT]

	form_local:
		form: ['Cannot save data in local mode.']

	allow_partial_save: ->
		false


	autorecover_clear: ->
		# TODO: test it, cleanup key
		ark = @autorecover_key()
		store.user_remove ark
		opts =
			url: '/cache?key=' + ark
			type: 'DELETE'
		aj = Ajax.async(opts)
		aj.done (data, status, xhr) =>
			return
		aj.fail (xhr, status, data) =>
			return

	autorecover_enabled: ->
		@options.form? and @options.form and @options.link? and @options.link and App?.version?.app?.type in ['jshomebase'] and @options.parent.parent.autoRecoverEnabled

	autorecover_get: ->
		# add form and id to check on the backend if the form has not been submitted already.
		lookup_vals = if (@options.form and @options.record) then "&form=#{@options.form}&id=#{@options.record}" else ''

		if @autorecover_enabled()
			ark = @autorecover_key()
			data = store.user_get_json(ark)
			if(data?)
				data
			else
				aj = Ajax.sync
					url: '/cache?key=' + ark + lookup_vals
				if aj and Object.keys(aj).length > 0 and !aj.error
					aj
				else
					null
		else
			null

	autorecover_key: (id = null)->
		['autorecover'
			@options.link
			getLinkID(@options.link, @options.linkid, @$el)
			@options.form
			(id or @options.record or '0')
			App.user.id
		].join('_')

	autorecover_set: ->
		return if not (@form_rendered and @form_edited and
									(@form_edited isnt @form_autorecover) and
									@autorecover_enabled() and
									@$el.is(':visible') and @$el.parents('#flyout').length is 0)
		ark = @autorecover_key()
		@form_autorecover = @form_edited
		try
			val = @save_form_values(true)
		catch e
			#TODO: Fix error when it gets called while editable subform is rendering
			return
		delete val._meta;
		store.user_set_json(ark, val)
		aj = Ajax.async
			url: '/cache?key=' + ark
			type: 'PUT'
			data:
				value: val
		aj.done (data, status, xhr) ->
			return
		aj.fail (xhr, status, data) ->
			return
		return

	check_hidden_fields: (values, options) ->
		hide=true
		subform=options.wrapper.subforms
		fields=subform.field
		for key in Object.keys fields
			if !_.isEmpty(values[key])
				for k,v of subform.formmap[fields[key]].sections
					if (v.css('display') == 'block')
						hide=false
						break
				if hide==true
					meta={}
					list=[]
					for obj in values[key]
						obj._meta={
							archive : obj.id
						}
						list.push obj.id
					values._meta[key]= {archive:list}
		return values

	check_missing_required_fields: ->
		if @options.parent?.set_missing_field_count?
			clearTimeout(@reqts) if @reqts
			@reqts = setTimeout =>
				@missing_field_count = @count_missing_required_fields()
				master = @get_master()
				if master and master.wrapper
					master.wrapper?.options?.parent?.set_slave_missing_field_count?(@missing_field_count)
					master.wrapper?.options?.parent?.parent?.triggerActionsChange?()
				@options.parent.set_missing_field_count @missing_field_count
				@options?.parent?.parent?.triggerActionsChange?()
				if @missing_field_count is 0 and
						@f.b.find('.formerror').text() is @form_missing_fields_message
					@reset_errors()
			, 200
		return

	set_validation_error: (form, field, err) ->
		if !err
			if !@validation_errors or _.isEmpty(@validation_errors)
				return
			if @validation_errors[form]?
				delete @validation_errors[form][field]
				if Object.keys(@validation_errors[form]).length is 0
					delete @validation_errors[form]
				if Object.keys(@validation_errors).length is 0
					delete @validation_errors
			return
		if not @validation_errors
			@validation_errors = {}
		if not @validation_errors[form]
			@validation_errors[form] = {}
		@validation_errors[form][field] = err
		return
			
	get_validation_errors: ->
		if !@validation_errors or _.isEmpty(@validation_errors)
		 	@validation_errors = @options.parent.parent.options.validation_errors or {}
		return @validation_errors or {}
	
	make_grid_level_error: (errors={}) ->
		faulty_recs = getNumericKeys(errors).map (key) -> (Number(key) + 1)
		if faulty_recs.length > 0
			return "The following records have validation errors: " + faulty_recs.join(", ")
		return null

	is_form_multi: (dd) ->
		parent_field = dd.get_field_in_parent()
		parent_form = dd.get_parent_form() or @options.form
		multi = false 
		if DSL[parent_form] and DSL[parent_form].fields[parent_field]
			multi = DSL[parent_form].fields[parent_field].model.multi
		return multi

	count_missing_required_fields: ->
		missing = 0
		if !@validation_errors
			@get_validation_errors()
		val_error = @validation_errors
		# get values from subforms
		if val_error and val_error.form
			ef = @f?.b?.find('.formerror').empty()
			if getType(val_error.form) is 'array' and val_error.form.filter(Boolean).length > 0 
				ef.append val_error.form.filter((v) -> v).map((v) -> '<div>' + v + '</div>').join('')
				ef.css('display', 'block')
			else if getType(val_error.form) is 'string'
				ef.append '<div>' + val_error.form + '</div>'
				ef.css('display', 'block')
			else if getType(val_error.form) is 'object' and Object.values(val_error.form).filter(Boolean).length > 0
				ef.append Object.values(val_error.form).filter((v) -> v).map((v) -> '<div>' + v + '</div>').join('')
				ef.css('display', 'block')
		val = @root().values()
		sfval = @root().values({ flattend: true})
		verrs = @get_validation_errors()
		slave = @get_slave()
		if slave and slave.wrapper and slave.wrapper.count_missing_required_fields
			missing += slave.wrapper.count_missing_required_fields() or 0
		# master = @get_master()
		# if master
		# 	master?.wrapper?.check_missing_required_fields?()
		@form_missing_fields_log = false
		for k,v of @subforms.formmap
			is_multi = @is_form_multi(v)
			if k != @options.form
				continue if @ignore_hidden_subform(v)
			if k is @options.form
				fxv = val
			else if is_multi
				# er = DSLFx.ValidateTransformSubForm(@options.parent, v)
				# if er
				# 	missing += 1
				# 	@form_missing_fields_log = {} if not @form_missing_fields_log
				# 	@form_missing_fields_log[k] = [er]
				parent_field_node  = v.get_field_in_parent()
				gnode = v.field_nodes[parent_field_node]
				if gnode
					er = @make_grid_level_error(verrs[k])
					if er
						missing += 1
						@form_missing_fields_log = {} if not @form_missing_fields_log
						@form_missing_fields_log[k] = {
							[parent_field_node]: er
						}
					DSLFx.ValidateFieldError gnode, (er or false)
				continue
			else if not sfval[k]?
				continue
			else # inline subform (multi:false)
				fxv = sfval[k]
			
			if not fxv?
				continue

			# handle field level validation - send all form values
			vreq = if @options.fields?.required? then @options.fields.required else @options.fields
			vreq = (vreq or []).concat(Object.keys(v.required_if_map))
			er = DSLFx.ValidateFields(DSL[k], v, fxv, vreq)
			if getType(verrs[k]) is 'object' and Object.keys(verrs[k]).length > 0 
				if !er 
					er = {}
				if is_multi
					gnode = v.field_nodes[v.get_field_in_parent()]
					if gnode
						gerr = @make_grid_level_error(verrs[k])
						if gerr
							er[v.get_field_in_parent()] = gerr
						DSLFx.ValidateFieldError gnode, (gerr or false)
				else
					for ek, ev of verrs[k]
						if v.field_map[ek]
							if !er[ek]
								er[ek] = ev
							for fld in v.field_map[ek] or []
								DSLFx.ValidateFieldError fld, ev
				if getType(er) is 'object' and Object.keys(er).length == 0
					er = false

			if er
				missing += Object.keys(er).length
				@form_missing_fields_log = {} if not @form_missing_fields_log
				@form_missing_fields_log[k] = er
		missing

	go_to_field: (form, field) =>
		return if !form or !field
		return if !DSL[form]
		v = DSL[form].fields[field]
		return if !v
		if v.model.type is 'subform' && v.model.multi
			fdd = @subforms.formmap[v.model.source]
			if fdd and fdd.field_nodes[field]
				window.TabController.goToField(fdd.field_nodes[field])
		else
			fdd = @subforms.formmap[form]
			if fdd and fdd.field_nodes[field]
				window.TabController.goToField(fdd.field_nodes[field])		
		return
	
	get_controls: () =>
		cv = @options.parent.parent
		controls =
			triggerActionsChange: cv.triggerActionsChange
			tabViewActions: cv.tabViewActions
			actionButtonShowHide: cv.actionButtonShowHide
			goToField: @go_to_field
		return controls

	draw: (rowdata={}) ->
		#l, p, m, d, b
		@f =
			l: @$('>.formload')      # loading indicator
			a: @$('>.cardarea')      # main form area
			p: @$('>.carddrop')      # popup dropdown menu
			m: @$('>.cardmenu')      # section menu
			b: @$('>.cardbar')       # save/cancel button area
		cview = @options.parent.parent

		if cview?.forceReadOnly
			@force_readonly = true

		if cview.inline
			@f.a.addClass('inline-magic')
			@f.l.hide()
			@f.p.remove()
			@f.b.remove()
			@f.a = @f.a.closest('tr')
			@f.a.find('.cardform').hide()
			@$('>.cardarea').remove()
			@$('>.cardform').hide()
			@f.a.addClass('cardarea').addClass('cardform')
		else
			aul = @f.a.parent().parent().parent().parent().find('.form-active-users')
			if aul.length > 0
				if ['add', 'addfill'].includes(@options.mode)
					aul.addClass('hide')
				else
					aul.removeClass('hide')
					aul.attr('form', @options.form)
					aul.attr('mode', @options.mode)
					aul.attr('id', @options.record)	

		dfd = $.Deferred()
		if not @f.a
			dfd.reject 'Invalid data!'
			return dfd

		@dr = new DSLRecord(@options)
		cfd = @dr.read(rowdata)
		cfd.done (args...) =>
			@draw_root dfd, args...
			return
		cfd.fail (err) ->
			dfd.reject err

		dfd

	draw_cardinline: ->
		ar = []
		ln = false
		lg = false
		grid = @options.parent.parent?.grid or {}
		ar.push('<td class="edit-subrow"><i></i></td>')
		if grid.split
			clss = 'grid-split'
			if !FieldSubform.can_split_grid(grid.splitif, @options.preset or {})
				clss += ' offscreen'
			ar.push('<td><i id=grid-split class="fa fa-code-fork ' + clss + '">&nbsp;</i></td>')
		if grid.tooltip?.length
			ttt = get_text_from_formdata(@options.preset, grid.tooltip)
			ar.push('<td><i id=eg-tooltip class="fa fa-list eg-tooltip'+ (if !ttt then " offscreen" else "") + '"><span id="eg-tooltiptext" class="eg-tooltiptext">'+ttt+'</span></i></td>')
		aro = []
		fm = {}
		for gv in @dom
			continue if gv.multi
			secgroup = mergesec = false
			if gv.group is lg
				mergesec = true
			else if gv.sections.length is 1
				if _.last(gv.sections).section.toLowerCase() is gv.group.toLowerCase()
					mergesec = true
			if not mergesec
				lg = gv.group
				secgroup = window.next_id('secgroup')

			for sv in gv.sections
				continue if sv.section_tab
				@subforms.formmap[sv.form].set_section_map(sv.id, sv.section)
				ln = false

				for fk,fv of sv.fields
					fg = fv.fieldgen
					fd = @subforms.formmap[sv.form].draw_field fg.fld, fg.v, fg.sec
					sv.fields[fk].field = fd
					cls = if sv.form != @options.form or !grid.fields?.includes?(fg.fld) then 'offscreen' else ''
					if !fg.v.view.label and !cls
						cls = 'offscreen'
						log('Missing grid column label: ' + fg.fld + ' in form: ' + sv.form)
					td = '<td class="gridedit-subrow subformfield-'+fg.fld+' '+cls+' ">'+fd.html+'</td>'
					if cls
						aro.push td
					else
						fm[fg.fld] = td
		for fk in grid?.fields or []
			ar.push fm[fk] if fm[fk]
		if grid.delete
			clss = ''
			if !FieldSubform.can_delete_row(grid.deleteif, @options.preset or {})
				clss += ' offscreen'
			ar.push '<td id=grid-delete class="delete-subrow' + clss + '"><i></i></td>'
		@f.a.append ar.join("")
		@f.a.append aro.join("") if aro.length > 0
		@f.a.parent().addClass('oneitem') if @f.a.find('.fieldgroup').length < 2
		@f.a.addClass(@options.form)
		return

	draw_cardarea: ->
		ar = []
		hm = false
		ln = false
		lg = false
		tabs = []
		tab_container = () =>
			return _.last(tabs) or {}
		tab_area_started = false

		if @options.parent.parent.inline
			@draw_cardinline()
			return

		for gv in @dom
			continue if gv.multi
			secgroup = mergesec = false
			if gv.group is lg
				mergesec = true
			else if gv.is_tabcontroller
				mergesec = true
				lg = false
			else if gv.sections.length is 1
				gv_sec = _.last(gv.sections)
				if gv_sec.section.toLowerCase() is gv.group.toLowerCase()
					mergesec = true
				if gv_sec.fields.length is 1 and ar.length
					fgv = gv_sec.fields[0]?.fieldgen?.v
					if fgv and fgv.model.type is 'subform' and fgv.model.multi
						mergesec = true
			tgattr = (if gv.tc.parent_tabgroup then 'parent_tabgroupid="' + gv.tc.parent_tabgroup + '"' else '') + (if gv.tc.tabgroup then ' tabgroupid="' + gv.tc.tabgroup + '"' else '')
			if not mergesec
				lg = gv.group
				secgroup = window.next_id('secgroup')
				if not gv.hide_header && gv.group && !gv.group.startsWith('_Add') && !gv.group.startsWith('_Edit')
					ar.push '<h3 id="' + secgroup + '" ' + tgattr + ' >' + gv.group + '</h3>' if gv.group
				if gv.note isnt ''
					ar.push('<h5 id="' + secgroup + '_note" ' + tgattr + ' class="groupnote">' + formatNote(gv.note) + '</h5>')
					ln = gv.note

			for sv in gv.sections
				level = sv.tc.parent_tab_level = 'level_' + (sv.tc.parent_tab_level or 1) + ' '
				if sv.section_tab
					tabs.push(_.cloneDeep(sv.tc))
					ar.push '<div class="dsl-tab-container ' + level + '">'
					tab_area_started = true
				@subforms.formmap[sv.form].set_section_map(sv.id, sv.section, sv.tc)
				tab_class  = level
				if sv.tc.parent_tabgroup 
					tab_class = level
				ar.push '<div ' +
					'class="' +
						(if sv.section_tab then 'dsl-tab-header tab-list-default ' + tab_class else ' ') +
						'scrolltarget container fieldgroup ' +
						(if sv.compact then 'section-compact ' else ' ') +
						'form_' + sv.form + ' ' +
						'area_' + sv.area +
						'"' +
					'id="' + sv.id + '" ' +
					'form="' + sv.form + '" ' +
					'area="' + sv.area + '" ' +
					(if sv.tc.parent_tabgroup then 'parent_tabgroupid="' + sv.tc.parent_tabgroup + '"' else '') +
					(if sv.tc.tabgroup then 'tabgroupid="' + sv.tc.tabgroup + '"' else '') +
					(if sv.tabcontroller then 'tabcontroller="' + sv.tabcontroller + '"' else '') +
					(if sv.parent_tab then 'parent_tab="' + sv.parent_tab + '"' else '') +
					'section="' + escapeHTML(sv.section.toLowerCase()) + '" ' +
					(if secgroup then 'secgroup="' + secgroup + '"' else '') +
					'>'

				if sv.section_tab
					for tab, value of sv.tc.tabs
						cbx  = "" 
						cb_en = if @options.parent.mode is 'read' then 'disabled' else ''  
						if sv.tc.tab_toggles?[tab]
							cbx = '<input type="checkbox" class="tab-toggle" id="' + tab + '" name="' + tab + '" ' + cb_en + ' >'
						classname = 'section-tab tab-list-button'
						if sv.tc.tab_toggles?[tab]
							classname += ' toggle-tab'
						ar.push('<div tab="' +  tab + '" class="' + classname + '">' + cbx + '<div class="tab-label" aria-label="' + tab  + '" data-mui-internal-clone-element="true">' + tab + '</div><span class="right-side-curve"></span></div>')
					ar.push('</div>')
					if tab_area_started
						ar.push '<div class="dsl-tab-area ' + level + '">'
						tab_area_started = false
					continue

				if sv.section isnt 'Main'
					hm = true
					if mergesec or (
						(sv.fields?[0]?.fieldgen?.v?.model.type is 'subform') and
						(sv.fields?[0]?.fieldgen?.v?.model.multi))
						if not sv.hide_header
							ar.push '<h3>' + sv.section + '</h3>'
						if gv.note isnt ''
							ar.push('<h5 class="groupnote">' + formatNote(gv.note) + '</h5>')
							ln = gv.note
					else if not sv.hide_header
						ar.push '<h4 class="' + ((sv.fields?.length is 1 and sv.fields[0]?.fieldgen?.v.view.control == "grid" and sv.fields[0]?.fieldgen?.v?.model?.required) and "required-section" or "") + '">' + sv.section + '</h4>'

				ar.push('<div class="sectionnote">' + formatNote(sv.note) + '</div>') if sv.note and sv.note isnt '' and sv.note isnt ln
				ln = false

				for fk,fv of sv.fields
					fg = fv.fieldgen
					fd = @subforms.formmap[sv.form].draw_field fg.fld, fg.v, fg.sec
					sv.fields[fk].field = fd
					ar.push fd.html
				ar.push '</div>'
				tc = tab_container()
				if tc.last_tab and sv.parent_tab and tc.tabs[tc.last_tab] and _.last(tc.tabs[tc.last_tab]) == sv.section
					ar.push '</div>' # tab area
					ar.push '</div>' # tab container
					tabs.pop()
		is_end_br = false
		pro_arr = ar.map (el_str) ->
			if el_str.startsWith("<br />")
				if is_end_br
					el_str = el_str.replace /<br\s*\/?>/, ''
					is_end_br = false
				if el_str.endsWith("<br />")
					is_end_br = true
				return el_str
			else
				return el_str

		@f.a.append pro_arr.join("")
		@f.a.parent().addClass('hasmenu') if hm
		@f.a.parent().addClass('oneitem') if @f.a.find('.fieldgroup').length < 2
		@f.a.addClass(@options.form)
		return

	draw_root: (dfd, dom, subforms) ->
		$.notify 'Setting up form...'

		@dom = dom
		@subforms = subforms
		for sr in @subforms.records
			frm = @insert_subform sr.options
			frm.set_record sr

		@draw_cardarea() # draw section + fields, set subform @section_map using @dom

		for frm in @subforms.forms
			frm.attach_data() # attach subform field data vars to dom.field.data
		
		DSLFields.load(@f.a) # attach field handlers: ace editor, input mask, select2 etc.
		# handle-if i.e. toggle appropriate fields/sections
		@events_handle = true
		@events_show_hide = true
		for frm in @subforms.forms
			DSLRecordSubform.show_hide frm, true, true
			for _,f of frm.fields # run transforms/validators
				@handle_field_event f.node

		# section check
		if not @force_readonly
			@events_section_check = true
			for frm in @subforms.forms
				frm.section_check_all()
		else # read-only
			if DSLRecordSubform.pending_status_change(frm.get_record_data())
				@f?.b?.find?('.statuschange').removeClass('hide')
			else
				@show_reviewed?()

		if @f?.a?.find('>.cardbar').length == 0
			@f.a.append @f.b
			if @f.a.find('>.cardbar').length != 0
				@f.b = @f.a.find('>.cardbar')
		# show verify tab if necessary
		@check_missing_required_fields()

		# execute custom handlers if available
		dfsub = []
		for frm in @subforms.forms
			dfh = $.Deferred()
			frm.load_handler(dfh)
			dfsub.push dfh

		erred = false
		$.when.apply($, dfsub).then =>
			@draw_root_show dfd
			return
		, (err) ->
			if not erred
				erred = true
				dfd.reject err
			return
		return
	
	section_required_check: ->
		@f?.a?.find('.checkboxes.checkbox-only.section-required-check').each ->
			d = $(this)
			dd = d.data('dd')
			return if not dd
			form = d.data('form')
			return if not form
			k = d.data('k')
			return if not k
			DSLRecordSubform.handle_if dd, DSL[form], k, d

	draw_root_show: (dfd) =>
		# render form
		@f.l.hide()
		inline = @options.parent.parent.inline
		@f.a.removeClass('offscreen') # main form area
		if not isBrowserMobile() and not inline
			@f?.p?.removeClass?('offscreen') # popup dropdown menu
		if not inline
			@f.b.removeClass('offscreen') # save/cancel button area
		@verify() if (@options.fields?.required?.length > 0) or (@options.fields?.length > 0)
		# workflow
		WF.trigger('form', @options.form, @options.mode, 'show', @)

		# render done
		dfd.resolve @options.mode
		$.notify()
		@options.parent.parent.parent?.triggerActionsChange?()
		# allow saving the form
		for frm in @subforms.forms
			@handle_tabs_sections(frm)
			frm.trigger_if_prefill()
		@options.parent.parent.parent?.onDrawShown?()
		setTimeout =>
			for frmx in @subforms.forms
				@handle_tabs_sections(frmx)
			@handle_tabs_sections(@subforms.forms[0])
			@form_rendered = true
			try
				@check_missing_required_fields()
				verr = @get_validation_errors()
				if verr and Object.keys(verr).length > 0
					@verify()
			catch e
				console.error('draw_root_show: Error in check_missing_required_fields', e)

			@section_required_check()
			if @options?.parent?.parent?.options?.onFormReady?
				@options.parent.parent.options.onFormReady()
			@options.parent.parent.publish('ready')
			@options.formBuss?.subscribeFormCallbacks(@options.form, @options.mode)
		, 1000
		return
	
	handle_tabs_sections: (frm) -> 
		return if not frm
		frm.update_section_tabs(true)
		for secid, secl of frm.sections
			continue if !secid.includes("__tg__")
			tbtn = TabController.findActiveBtn(secl.find('.tab-list-button').not('.hide'))
			DSLDrawFormEvents.handle_section_tab_event(tbtn) 

	field_nodes: ->
		@root().field_nodes

	handle_error: (e, xhr = null) ->
		$.notify()
		prettyNotify()
		t = @
		e = form: [e] if getType(e) is 'string' # convert plaintext error to form error
		ef = @f?.b?.find('.formerror').empty()
		ff = false
		if @options?.parent?.parent?.inline
			ff = true # don't auto focuse required field in editable subform
		# mark all sections as val-true/val-false, remove err-*
		for frm in @subforms.forms
			frm.section_check_all()

		# form/subform-level errors
		error_text = ''
		add_form_error = (fe) ->
			if getType(fe) is 'string'
				ef.append('<div>' + fe + '</div>')
				if fe.trim() != ''
					error_text = error_text + '\n' + fe
				return
			else if getType(fe) is 'object'
				for k,v of fe
					if k is 'form' or t.subforms.field[k]?
						add_form_error v
			else if getType(fe) is 'array'
				for k,v of fe
					if getType(v) is 'string'
						ef.append('<div>' + v + '</div>')
						error_text = error_text + '\n' + v
						continue
					else
						add_form_error v
			return
		add_form_error e
		# @f?.a?.append(@f.b)
		# @f?.b?.hide()

		if App.company.toast_form_level_errors is 'Yes'
			if error_text.replaceAll('\n', '').trim().length > 0
				toast({type:'error', position: "bottom-center", theme: "dark", message: error_text, prodIgnore: true, autoClose: 2000})
		# add errors for fields not in form
		if getType(e) is 'object'
			for k,v of e
				continue if k is 'form' or DSL[t.options.form].fields[k]?
				v = v[0] if getType(v) is 'array' and v.length is 1
				continue if getType(v) isnt 'string'
				if v.indexOf(k) > -1
					ef.append('<div>' + v + '</div>')
				else
					ef.append('<div>' + getFormLabel(k) + ': ' + v + '</div>')

		# field-level error
		get_field_error = (fe, k) ->
			if getType(fe) is 'array' and fe.length > 0
				for f in fe
					err = get_field_error f, k
					return err if err
			else if fe[k]?
				return fe[k]
			else if fe.form?[k]?
				return fe.form[k]
			else
				if getType(fe.form) is 'array' and fe.form?.length > 0
					return get_field_error fe.form, k
			false
		that = @

		@f.a.find('.form-group').each ->
			fg = $(this)
			k = fg.attr('field')
			return if not k
			form = fg.attr('form')
			return if not form
			if form is t.options.form
				err = get_field_error e, k
			else
				sf = t.subforms.fieldrev[form]
				if e[sf]?
					err = get_field_error e[sf], k
				else if e[form]?
					err = get_field_error e[form], k
				else
					err = false
			return if not err
			err = get_string_value(err)
			return if !err
			fg.addClass('has-error')
			if !DSL[form]?.fields?[k] # multi true forms error is weird
				if t.subforms.formmap[form]
					form = t.subforms.formmap[form].get_parent_form()
					if !DSL[form]?.fields?[k]
						console.log('Unable to navigate to field: Missing field name for subform', form, k)
						return
			fn = DSL[form].fields[k].view.label
			fn = 'Field' if fn.length > 16 or fn.indexOf('?') isnt -1
			fgerr = fg.find('.errormsg')
			if that?.options?.parent?.parent?.inline
				if err.includes('is required')
					fgerr.html('Required')
					fgerr.attr('title', 'Required')
				else
					fgerr.html(err)
					fgerr.attr('title', err)
			else
				msg_err = if err.startsWith('is') then fn + ' ' + err else err
				fgerr.html(msg_err)
				fgerr.attr('title', msg_err)

			# handle menu
			secmnu = '#menu_' + fg.closest('.scrolltarget').attr('id')
			t.f.m.find(secmnu).addClass('has-error')
			t.f.m.find(secmnu + ' a span').removeClass('sec-err-false').addClass('sec-err-true')

			# select first field
			if not ff
				if !fg.is(':visible')
					cfd = fg.parent().parent().data()
					window.TabController.goToField(fg)

				ffl = fg.find('.controls').children().first().focus()
				ffl = fg.find('.controls-grid').children().first() if ffl.length is 0
				return if ffl.length is 0

				d = ffl.closest('div.form-group')
				if d.length == 1
					ffl = d
				# bring the field in scroll view
				y = ffl.offset().top - t.$el?.parent().offset().top
				t.$el?.parent()?.scrollTop y

				# highlight the field's cardmenu
				#   repeat & delay because sometimes scroll event takes a few ms to trigger
				setTimeout ->
					t.f.m.find(secmnu).trigger 'select'
				, 25
				setTimeout ->
					t.f.m.find(secmnu).trigger 'select'
				, 75
			ff = true
			return

		if ef.html() isnt ''
			ef.show()
			if xhr?.status in [400, 500]
				log('The server returned the following error message:<br/><br/><p>' + ef.html() + '</p>')
				prettyError 'Could not save data!', 'The server returned the following error message:<br/><br/><div class="sub">' + ef.html() + '</div><br/>' + ERR_CONTACT

		return

	#HB-4585
	# if form is root form then ignore it
	# else get the subform section with sec_id as it is unique for all subform. If sec_id of subform is hidden
	# then ignore the subform and it should not include in post request.
	ignore_hidden_subform:(frm) ->

		return false if frm is @options.form
		return false if @subforms?.formmap?[frm]?.get_record_data?().id? #if it's the put request then don't ignore the subform with the record id since we need to archive it.
		if @subforms.formmap?[frm]?.section_map
			is_subform_hide = false
			for sec, secName of @subforms.formmap[frm].section_map
				continue if secName is 'Tab Toggles' or  secName.includes('Tab Toggles')
				return false if not $(@subforms.formmap[@options.form].el).find('#'+sec).hasClass('hide')
			return is_subform_hide
		else
			return false

	insert_subform: (options) ->
		opt = _.clone(options)
		opt.me = @subforms.forms.length
		opt.wrapper = @
		if not opt?.mode?
			opt.mode = opt.parent?.options?.mode
		if not opt?.fields?
			opt.fields = opt.parent?.options?.fields
		nf = new DSLDrawSubform(opt)
		nf.formBuss = @options.formBuss
		nf.force_readonly = @force_readonly if @force_readonly
		@subforms.forms.push nf
		@subforms.formmap[options.form] = nf

	mark_edited: ->
		@form_edited = (new Date()).getTime() if @form_rendered
		return

	request_url: (method = 'POST') ->
		return '' if not Auth.can_request(@options.form)
		if method is 'POST' and Auth.can_create_any(@options.form)
			'change/'
		else if method is 'PUT' and Auth.can_update_any(@options.form)
			'/change/'
		else
			''

	reset_errors: ->
		$.notify()
		# resets left-menu and form field errors
		@f.a.find('.form-group.has-error').removeClass('has-error')
		# hide form-level error box
		@f.b.find('.formerror').empty().hide()
		return

	root: ->
		@subforms.forms[0]

	get_slave: (slave) ->
		slave = @duelCardRefs?.slave
		return null if !slave
		return null if @options.form is slave.form # don't return self
		return slave
	
	get_master: ->
		master = @duelCardRefs?.master
		return null if !master
		return null if @options.form is master.form # don't return self
		return master

	save: ->
		@reset_errors()
		$.notify 'Saving...'
		dfd = $.Deferred()

		# get values from root & all subform after transforms/validation
		# do not validate anything in app view
		val = @save_form_values(@allow_partial_save())

		return dfd if not val
		log(val) if App.feature.debug

		# don't save if running locally
		if CRLocal
			@handle_error @form_local
			return dfd

		# mark all sections as ok, let backend override it
		slave = @get_slave()
		if slave
			swrap = slave.wrapper
			return dfd if !swrap or !swrap.verify()
			val._meta.slave =
				form: slave.form
				record: slave.record or slave.wrapper?.options?.preset?.id or null
				data: swrap.save_form_values(@allow_partial_save())
		@ui_lock() #HB-3248 - lock ui while saving
		@save_type_web dfd, val
		dfd

	save_form_values: (silent = false) ->
		# get values from root form
		DSLFx.TransformForm(DSL[@options.form], @root())
		val = @root().values()
		fsfval = @root().values({ flattend: true})
		sfval = {}

		# root _meta is needed for editing subforms
		if @options.mode is 'edit' and @subforms.forms.length > 1 and not val._meta?
			val._meta = {}

		# get values from subforms

		for frm, form_dd of @subforms.formmap
			continue if frm is @options.form
			continue if @ignore_hidden_subform(frm)
			sff = Object.values(form_dd.fields or {})
			multi = false
			sfld = @subforms.fieldrev[frm] or null
			if sff.length == 1
				sffd = sff[0]
				if sffd and sffd.fieldtype == 'subform' and sffd.v.model.multi
					multi = true
					sfld = sffd.k
			if not sfld
				console.log('Error: Missing field name for subform')
				continue
			continue if _.isEmpty(fsfval[frm]) or !fsfval[frm]?
			sfval[frm] = fsfval[frm]

			# setup subform id & _meta subform updated [] list
			continue if not val._meta?
			if multi
				for k,v of val[sfld]
					continue if not v?._meta?
					val._meta[sfld] = {} if not val._meta[sfld]?
					i = parseInt(k)
					if v._meta.update?
						val._meta[sfld].update = [] if not val._meta[sfld].update?
						val._meta[sfld].update.push v._meta.update
					else if v._meta.delete?
						val._meta[sfld].delete = [] if not val._meta[sfld].delete?
						val._meta[sfld].delete.push v._meta.delete
			else
				sfid = form_dd.get_record_data('id')
				val._meta[sfld] =
					update: [sfid]

		vferr = {}
		clrfields = {}
		missing = false
		ignore_list = ['reviewed_by', 'reviewed_on', 'created_by', 'created_on', 'updated_by', 'updated_on', 'patient_id']
		verrs = @get_validation_errors()
		for k,v of @subforms.formmap
			is_multi = @is_form_multi(v)
			continue if @ignore_hidden_subform(k)

			# Record all hidden fields to clear out later
			clrfields[k] = []
			for fk,fv of @subforms.formmap[k].shmap?.fields
				# Only clear if purposefully hidden using model.if
				if !fv.current && (!_.isEmpty(fv.hide_by) || !_.isEmpty(fv.show_by))
					if fk not in ignore_list
						clrfields[k].push fk
			for sk,sv of @subforms.formmap[k].shmap?.sections
				if !sv.current && (!_.isEmpty(sv.hide_by) || !_.isEmpty(sv.show_by))
					clrfields[k] = _.difference(_.union(clrfields[k], DSL[k].model.sections[sk]?.fields || []),ignore_list)
			sffv = null
			childof = v.get_parent_form()
			if childof
				sffv = DSL[childof].fields[@subforms.fieldrev[k]]
			else
				sffv = DSL[@options.form].fields[@subforms.fieldrev[k]]
			
			if !sffv
				sff = Object.values(form_dd.fields or {})
				if sff.length == 1
					sffd = sff[0]
				if sffd and sffd.fieldtype == 'subform' 
					sffv = sffd.v
			if k is @options.form
				fxv = val
			else if not sffv?
				console.log('Field not found in form: ' + @options.form + ' for subform: ' + k)
				continue
			else if is_multi
				is_multi = true
				parent_field_node  = v.get_field_in_parent()
				er = false
				if !silent
					if v.field_nodes[parent_field_node]
						er = FieldSubform.is_missing(v.field_nodes[parent_field_node])
					if not er
						er = DSLFx.ValidateTransformSubForm(@options.parent, v)
						if typeof er?.then is 'function' and typeof er?.catch is 'function'
							er = false
					if not er
						gnode = v.field_nodes[parent_field_node]
						if gnode
							er = @make_grid_level_error(verrs[k])
							if er
								vferr[k] = {
									[parent_field_node]: er
								}
							else
								delete vferr[parent_field_node]
							DSLFx.ValidateFieldError gnode, (er or false)
				if er
					missing = true
					vferr[k] = {
						[parent_field_node]: er
					}
				continue
			else # inline subform (multi:false)
				fxv = sfval[k]

			# handle form level validation - send all form values
			# DSLText.text(@, fxv)
			# if fxv?._meta?.text and val?._meta?[@subforms.fieldrev[k]]
			# 	val?._meta?[@subforms.fieldrev[k]]['text'] = fxv?._meta?.text
			if not fxv
				fxv = {}

			er = DSLFx.ValidateForm(DSL[k], v, fxv)
			if er
				vferr.form = if vferr.form? then vferr.form.concat(er) else er

			# highlight missing required fields (if not silent)
			if silent
				er = false
			else
				# handle field level validation - send all form values
				vreq = if @options.fields?.required? then @options.fields.required else @options.fields
				vreq = (vreq or []).concat(Object.keys(v.required_if_map))
				er = DSLFx.ValidateFields(DSL[k], v, fxv, vreq)
				if getType(verrs[k]) is 'object' and Object.keys(verrs[k]).length > 0 
					if !er 
						er = {}
					if !is_multi
						for ek, ev of verrs[k]
							if v.field_map[ek]
								if !er[ek]
									er[ek] = ev
								for fld in v.field_map[ek] or []
									DSLFx.ValidateFieldError fld, ev
					if getType(er) is 'object' and Object.keys(er).length == 0
						er = false

			if er
				missing = true
				if k is @options.form
					vferr = _.defaults(vferr, er)
				else
					vferr[k] = er

		if missing
			er = [@form_missing_fields_message]
			vferr.form = if vferr.form? then vferr.form.concat(er) else er
		if not _.isEmpty(vferr)
			@handle_error vferr if not silent
			log(val) if App.feature.debug
			return false

		# always send _meta now as per HB-1143
		val._meta = {} if not val._meta?
		val._meta.client = {}
		val._meta.client.updated_by = App.user.id
		val._meta.client.updated_by_auto_name = App.user.auto_name
		val._meta.client.updated_on = dateTimeNow()

		if !_.isEmpty(clrfields)
			val._meta['clear_fields'] = clrfields

		# let server know to null all fields not included in this POST/PUT
		val._meta.server =
			null_missing_fields: true

		# add in required subform source fields
		for k,v of @root().subform.field_source
			val[k] = v if not val[k]?

		# setup progress notes
		# val = DSLText.text(@, val)

		val

	save_handle_failure: (data, status, xhr) ->
		if xhr.responseJSON?.validation_errors?
			@validation_errors = xhr.responseJSON.validation_errors
		if xhr.responseJSON?.Error?
			@handle_error xhr.responseJSON.Error, xhr
			true
		else if xhr.responseJSON?.error?
			@handle_error xhr.responseJSON.error, xhr
			true
		else if getType(data) is 'undefined'
			@handle_error @form_error, xhr
			prettyError false, dataerr
			true
		else if getType(data) is 'object' and data?.error?
			@handle_error data.error, xhr
			true
		else if getType(data) is 'string' and data isnt '' # there was an error or recommend
			dataerr = @form_error
			if data.substr(0, 1) is '{' and data.substr(data.length - 1, 1)
				data = JSON.parse(data)
				dataerr = data.error if data?.error?
			@handle_error dataerr, xhr
			true
		else if getType(data) is 'array' and data.length is 0 # backend saved but error on GET HB-3691
			dataerr = 'It appears that your data was saved but the system cannot retrieve the ' +
								'newly created form. You may close or cancel out of this screen and ' +
								'click \'Find\' in the list-view to access the saved form.<br/><br/>' +
								ERR_CONTACT
			@handle_error dataerr, xhr
			prettyError false, dataerr
			true
		else if !data or status is 'error'
			@handle_error 'Unexpected Error', xhr
			true
		else
			false

	save_handle_success: (dfd, data, values, id) -> # deferred, saved record, form ui values, id
		@ui_unlock()
		$.notify 'Saved ' + DSL[@options.form].view.label
		if getType(data) is 'array' and data.length is 1 and getType(data[0]) is 'object'
			data = data[0]

		# return saved record id, values, grid row html
		if data?.id?
			@options.record = data.id
		else if id
			@options.record = id
		dfd.resolve
			record: @options.record
			values: data
			rowhtml: @root().make_row_html(data, @options.domcols, DSL[@options.form].fields).fr

		# workflow - sending both pre-save data (ui values) and post-save data (db data)
		wfdata =
			db: data
			ui: values
		WF.trigger('form', @options.form, @options.mode, 'save', @, wfdata)
		return

		process_save = (data, status, xhr) =>
			if @save_handle_failure(data, status, xhr)
				dfd.reject()
				return @ui_unlock()
			# figure out id if backend sends one
			if getType(data) is 'array' and data?[0]?.id? # homebase backend is available
				@save_handle_success dfd, data, v
			else # offline mode
				ak = Ajax.async
					url: '/form/' + @options.form + '/' + cid
					type: 'GET'
				ak.fail (xhr2, status2, data2) =>
					@save_handle_failure data2, status2, xhr2
					@ui_unlock()
					dfd.reject()
					return
				ak.done (data2, status2, xhr2) =>
					@save_handle_success dfd, data2, v, cid
					return
			return

		return

	save_type_web: (dfd, v) ->
		# add or edit
		@validation_errors = {}
		if @options.parent?.parent?.options?.validation_errors
			@options.parent.parent.options.validation_errors = {}

		v=@check_hidden_fields(v, @options)
		cid = @options.record
		if cid is null
			form_url = if @options.form_override_url then  @options.form_override_url else '/form/' + @options.form + '/'
			aj = Ajax.async
				url: form_url + @request_url('POST') + (@options.form_override_params || '')
				type: 'POST'
				data: v
		else
			form_url = if @options.form_override_url then  @options.form_override_url else '/form/' + @options.form
			aj = Ajax.async
				url: form_url + '/' + cid + @request_url('PUT') + (@options.form_override_params || '')
				type: 'PUT'
				data: v
		that = @
		aj.fail (xhr, status, data) =>
			process_save data, status, xhr
			return
		aj.done (data, status, xhr) =>
			@autorecover_clear() # delete form data after post
			process_save data, status, xhr
			return

		process_save = (data, status, xhr) =>
			return @ui_unlock() if @save_handle_failure(data, status, xhr)
			# load saved record
			if (cid is null) or (data isnt '') # homebase returned GET
				@save_handle_success dfd, data, v
			else # homebase returned blank
				ak = Ajax.async
					url: '/form/' + @options.form + '/' + cid
					type: 'GET'
				ak.fail (xhr2, status2, data2) =>
					@save_handle_failure data2, status2, xhr2
					@ui_unlock()
					return
				ak.done (data2, status2, xhr2) =>
					@save_handle_success dfd, data2, v
					return
			return

		return

	toggle_subform: (k = false, show = true, force = false) ->
		return if not k

		# HB-3202 - allow forced hiding of subforms, used by client-side workflow
		@force_toggle_subform = {} if not @force_toggle_subform?
		if force
			@force_toggle_subform[k] = show
		else if @force_toggle_subform[k]?
			show = @force_toggle_subform[k]

		form = @subforms.field[k]
		return if not form
		@subforms.formmap[form].toggle_subforms show
		return

	ui_lock: ->
		opts =
			message: null
			fadeIn: 0
			fadeOut: 0
			overlayCSS:
				backgroundColor: '#fff'
		@f.a.block(opts)
		@f.b.block(opts)
		@get_slave()?.wrapper?.ui_lock?()
		return

	ui_unlock: ->
		@f.a.unblock()
		@f.b.unblock()
		@get_slave()?.wrapper?.ui_unlock?()
		return

	unload: ->
		@undelegateEvents()
		DSLFields.unload(@f.a)
		return if not @subforms?.forms?
		for v in @subforms.forms
			continue if getType(v) isnt 'object'
			if v.unload?
				v.unload()
		@

	# called by CoffeedslCardFormView (manage.coffee) which also accesses @options
	values: ->
		@root().values()

	get_formdata: =>
		return @save_form_values(true)

	verify: ->
		@reset_errors()
		$.notify 'Verifying...'

		# get values from root & all subform after transforms/validation
		val = @save_form_values(false)
		slave = @get_slave()
		if slave
			slave?.wrapper?.verify?()
		if @form_missing_fields_log
			log('Missing/Invalid field(s)': @form_missing_fields_log)
			ofs = []
			for f,l of @form_missing_fields_log
				continue if not DSL[f]?
				for k,_ of l
					continue if not DSL[f].fields[k]?
					v = DSL[f].fields[k]
					if v.model.required and v.view.offscreen
						ofs.push DSL[f].view.label + ' > ' + v.view.label
			if ofs.length > 0
				prettyError 'Missing Hidden Fields', 'This form has required fields ' +
					'that are hidden due to one or more selections:<br><br>&nbsp; &nbsp; ' +
					ofs.join('<br>&nbsp; &nbsp; ') + '<br><br>' + ERR_CONTACT

		val
