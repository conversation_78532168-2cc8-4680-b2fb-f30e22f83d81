class DSLDrawFormEvents extends B.View

	events:
		# change field events
		'change .controls textarea'             : 'event_field_area_change'     # area
		'change .controls input[type=checkbox]' : 'event_field_checkbox_change' # checkbox-each
		'change .controls .checkbox-only input[type=checkbox]' : 'event_field_checkbox_only_change' # checkbox-only
		'change .controls input[type=color]' 	: 'event_field_color_change'    # color picker
		'focus  input.datepicker'               : 'date_picker_focus'
		'keydown input.datepicker'               : 'date_picker_change'
		'change .controls input[type!=checkbox]': 'event_field_input_change'    # datetime, input, select-dynamic
		'keydown .controls input[type!=checkbox]' : 'event_field_input_keydown'
		'click  .controls .esignfield'       	: 'event_field_esign_click'     # esign click
		'change .controls .radios'              : 'event_field_radio_change'    # radio-change
		'click  .controls .radios .enabled'     : 'event_field_radio_click'     # radio-click
		'change .controls select'               : 'event_field_select_change'   # select-static
		'click .controls .fileupload .preview'  : 'event_field_preview_file'    # preview file
		'click .control-label.clickable-label' : 'event_field_clickable_label'
		'click .controls .fileupload .delete-file-icon'  : 'event_field_file_delete' # delete file
		'click .controls .fieldlink .preview'   : 'event_field_preview_link'    # preview link
		'click .tab-list-button'				: 'event_section_tab_click'
		'click .clararx-link'					: 'event_clararx_link_click'
		'change .tab-list-button input[type=checkbox]' : 'tab_toggle_event' # checkbox-each

		# change grid actions
		'click  .controls-grid .header'         : 'event_grid_header'           # sort table
		'click  .controls-grid .add-row'        : 'event_grid_add_row'          # add row
		'click  .controls-grid .delete-row'     : 'event_grid_delete_row'       # del row
		'change .controls-grid input'           : 'event_grid_input_change' 
		'click .controls-grid input[type=checkbox]'        : 'event_grid_checkbox_change'     # input
		'change .controls-grid select'          : 'event_grid_select_change'    # select

		# multi-subform grid actions
		'click  .controls-grid .add-subrow'     : 'event_subform_add_row'       # add subform row
		'click  .controls-grid .add-subrow-flyout'     : 'event_subform_add_row_flyout'       # add subform row
		'click  .controls-grid .add-subrow-dum' : 'event_subform_add_row_dum'
		'click  .controls-grid .delete-subrow'  : 'event_subform_delete_row'    # del subform row
		'click  .controls-grid .edit-subrow'    : 'event_subform_edit_row'      # edit subform row
		'click  .controls-grid .preview-subrow' : 'event_subform_preview_row'   # preview subform row
		'click  .controls-grid .read-subrow'    : 'event_subform_read_row'      # read subform row
		'click  .controls-grid .grid-split'     : 'event_subform_split_row'     # split row
		'select2-open .dynamicFilter'           : 'event_subform_load_dynamic'  # load data from dynamic filter

		# focus field events
		'focus .controls textarea'              : 'event_focus'                 # area
		'focus .controls input'                 : 'event_focus'                 # input
		'focus .controls select'                : 'event_focus'                 # select
		# focus grid actions
		'focus .controls-grid input'            : 'event_focus'                 # input
		'focus .controls-grid select'           : 'event_focus'                 # select

		# manage
		'click  .manage a.manage-add'           : 'event_manage_add'
		'click  .manage a.manage-edit'          : 'event_manage_edit'
		'click  .manage a.manage-list'          : 'event_manage_list'

		# menu
		'click .scrolltop'                      : 'event_top'

		# audit tral
		'click .audit-trail'                    : 'load_audit'
		'click .audit-trail-grid'               : 'load_audit_select'
		'mouseover .audit-trail'                : 'load_audit_tooltip'
		'mouseover .audit-trail-grid'           : 'load_audit_tooltip'
		'change .audit-select'                  : 'load_audit_grid'
		'click .close_audit_grid'               : 'close_audit'
		'keypress .controls .checkboxes .checkbox'		: 'checkbox_keyboard_select'
		'keypress .controls .radios .enabled'		: 'horiz_radios_select'
		'show .cardarea .controls:not(.control-nonfocusable):first .datepicker[tabindex=0]' : 'disable_popup_for_first_focus'

	events_handle: false
	events_section_check: false
	events_show_hide: false
	first_datetime_focusing: true

	date_picker_focus: (e) ->
		input = $(e.target)
		helper = input.data('helper')
		if !helper
			return
		if !helper.isShown()
			helper.togglePicker()
			helper.setDate(input.val())
		return

	date_picker_change: (e) ->
		input = $(e.target)
		helper = input.data('helper')
		if !helper
			return
		if helper.isShown()
			helper.togglePicker()
		return

	event_clararx_link_click: (e) ->
		link = $(e.target).attr('href')
		return unless link.startsWith(CRProtocolPrefix)
		e.preventDefault()
		parts = link.replace(CRProtocolPrefix, '').split('/')
		return unless parts.length == 3
		fieldlink = $(e.target).closest('.fieldlink')
		return unless fieldlink.length
		dd = fieldlink.data('dd')
		return unless dd
		action = parts[0]
		form = parts[1]
		id = parts[2]
		if !id
			console.error "event_clararx_link_click: No id", link
			return
		id = parseInt(id)
		if isNaN(id)
			console.error "event_clararx_link_click: Invalid id", link
			return
		if !window.DSL[form]
			console.error "event_clararx_link_click: Form not found", form
			return
		return unless Auth.can_read(form)
		if action == 'flyout'
			cnfg = {
				form: form,
				record: id,
				card: 'read'
			}
			if dd.options.mode == 'read'
				cnfg.tab_can_edit_override = -> false
			window.Flyout.open cnfg
		else 
			console.info "event_clararx_link_click: Unsupported action", action
		return

	event_field_clickable_label: (e) ->
		fieldid = $(e.target).closest('.clickable-label').parent().attr('fieldid')
		if !fieldid
			return
		node = $('#' + fieldid)
		if !node.length
			return
		value = DSLFields.value_get(node)
		if !value
			return
		dd = node.data('dd') 
		v = node.data('v')
		if !dd
			return
		mode = if dd.options.mode == 'edit' then 'edit' else 'read'
		return if !window.DSL[v.model.source]
		can_edit = Auth.can_update(v.model.source)
		can_read = Auth.can_read(v.model.source)
		if can_read and !can_edit
			mode = 'read'
		Flyout.open
			form: v.model.source,
			record: value,
			card: mode,

	# field events
	event_field_area_change: (e) ->
		@handle_field_event $(e.target)
		return

	event_field_color_change: (e) ->
		t = $(e.target)
		hex = t.parent().find('.color-hex')
		if hex.length is 1
			color = (t.val() or '').toUpperCase()
			hex.html(color)
			hex.css('color', color)
		@handle_field_event $(e.target)
		return
	
	event_section_tab_click: (e) ->
		t = $(e.target)
		if t.hasClass('tab-label')
			t = t.parent()
		DSLDrawFormEvents.handle_section_tab_event(t, e)
		return
	
	
	@handle_section_tab_event: (t, e) ->
		TabController.handleSectionTabEvent(t, e)
		return;

	tab_toggle_event: (e) ->
		j = $(e.target)
		tab = j.closest('.tab-list-button').attr("tab")
		data =  j.closest('.fieldgroup').data()
		dd = data.dd
		dd.tab_toggle_map[data.id][tab].forEach (section) =>
			field = "tabif_" + section.replace(/[^\w]|\s/gi, '').toLowerCase()
			if e.target.checked
				dd.value_field field, "Yes", true, true
				TabController.triggerIfOnAllFieldsTabToggle(dd, field)
			else
				dd.value_field field, "", true, true
				TabController.unCheckAllChildTabs(data.id, tab)
		return

	event_field_checkbox_change: (e) ->
		t = $(e.target)
		c = t.closest('.checkbox').find('input')
		return false if c.length isnt 1 or c.is(':disabled')

		# uncheck all others if multi=false
		t.closest('[multi=false]').find("input[id!='" + c.attr('id') + "']").prop('checked', false).removeAttr('checked')
		ocb = t.closest('[multi=false]').find("input[id='" + c.attr('id') + "']")
		if ocb.length > 0
			if ocb.is(':checked')
				ocb.attr('checked', 'checked')
			else
				ocb.removeAttr('checked')
		# iOS older than 12.2 doesn't toggle the checkbox properly, so do it manually
		@handle_field_event $(e.target).closest('.checkboxes')
		return

	event_field_checkbox_only_change: (e) ->
		t = if $(e.target)?.length then $(e.target) else $(e)
		label_checkbox = t.closest('.checkbox')
		checkbox_input = label_checkbox.find('input')
		return false if checkbox_input.length isnt 1 or checkbox_input.is(':disabled')

		# toggle checkbox-only-slider-toggled classes
		isChecked = checkbox_input.is(':checked');
		slider_checkbox_container = label_checkbox.find('.slider-checkbox-container')
		slider_checkbox = slider_checkbox_container.find('.slider-checkbox')
		slider_checkbox_container.toggleClass('slider-checkbox-container-toggled', isChecked)
		slider_checkbox.toggleClass('slider-checkbox-toggled', isChecked)
		return

	event_field_esign_click: (e) ->
		d = $(e.target).closest('.esignfield')
		v = d.data('v')
		return if v.view.readonly
		return if d.hasClass('is-readonly')
		if not d.hasClass('readonly')
			if d?.find('.esign-prefill')?.length
				dfd = FieldESign.prefill_sign(d)
				dfd.done () =>
					@handle_field_event d
					return
			else
				dfd = FieldESign.get_signature(d,"Confirm Location")
				dfd.done (nv, args...) =>
					DSLFields.value(d, nv)
					@handle_field_event d
					return
		false

	event_field_input_change: (e) ->
		@handle_field_event $(e.target)
		return

	event_field_radio_change: (e) ->
		@handle_field_event $(e.target).closest('.radios')
		return

	event_field_radio_click: (e) ->
		# not using bootstrap's radio handler anymore
		return if $(e.target)[0].tagName isnt 'INPUT'
		r = $(e.target).closest('label')
		r.toggleClass('active').siblings('.active').removeClass('active')
		r.closest('.radios').trigger 'change'
		return

	event_field_select_change: (e) ->
		@handle_field_event $(e.target)
		return

	event_field_preview_link: (e) ->
		@event_field_preview(e,'.fieldlink')
		return

	event_field_preview_file: (e) ->
		@event_field_preview(e,'.fileupload')
		return

	event_field_file_delete: (e) ->
		d = $(e.target).closest('.fileupload')
		return false if @options.mode is 'read'

		# delete file
		FieldFile.value_set(d, '')
		d.removeClass('has-file')
		return

	event_field_preview: (e, cls) ->
		return if e.target.tagName is 'A' # allow normal file links for download
		t = $(e.target)
		d = t.closest(cls)
		a = d.find('a[target=_blank]')
		if a.length == 0
			prettyError false, 'Sorry ! There was no file attached to show a preview.'
			return
		src = encodeURIComponent(a.attr('href').trim())
		fn = a.html().trim()
		ext = /(?:\.([^.]+))?$/.exec(fn)[1].toLowerCase()
		m = $('#preview_modal')
		html = '<div>'

		if ext is 'pdf'
			html += '<iframe src="public/viewer/web/viewer.html?file=' + src + '" width="100%" height="1024px"></iframe>'
		else if ext in ['gif', 'jpeg', 'jpg', 'png']
			html += '<img src="' + a.attr('href').trim() + '" />'
		else if ext in ['doc', 'docx']
			html += '<iframe src="https://view.officeapps.live.com/op/embed.aspx?src=' + CRBaseURL + a.attr('href').trim() + '" width="100%" height="1024px"></iframe>'
		else
			html += '<span>Sorry! Cannot preview file: ' + fn + '</span>'
		html += '</div>'

		Flyout.open
			action: 'FlyoutHTMLView'
			label: fn
			html: html
		return

	# focus event
	event_focus: (e) ->
		return if not @events_handle
		$('#menu_' + $(e.target).closest('.scrolltarget').attr('id')).trigger 'select'
		return

	# grid events
	event_grid_add_row: (e) ->
		FieldGrid.add_row(e)
		@handle_grid_event $(e.target)
		return

	event_grid_header: (e) ->
		if @options.mode isnt 'read'
			FieldGrid.handle_sort(e)
			@handle_grid_event $(e.target)
		return

	event_grid_delete_row: (e) ->
		t = $(e.target).closest('table')
		FieldGrid.delete_row(e)
		@handle_grid_event t
		return

	event_grid_checkbox_change: (e) ->
		d = $(e.target)
		if d.parent().parent().parent().hasClass('inline-control')
			return
		@handle_grid_event d
		return

	event_grid_input_change: (e) ->
		d = $(e.target)
		if d.parent().parent().parent().hasClass('inline-control')
			return
		@handle_grid_event d
		return

	event_grid_select_change: (e) ->
		@handle_grid_event $(e.target)
		return
	
	event_manage_add_careplan_order: (e) ->
		mng = e.target.hash.substr(1)
		return false if mng is ''
		d = $(e.target).parents('.form-horizontal').find('input.select2field,select.select2field')
		return false if not d
		values = d.data('dd').values()
		if !values.patient_id
			prettyError false, 'Please select a patient first'
			return
		ps = {}
		ps.patient_id = values.patient_id
		createTherapySet = (r) => 
			openFlyout("Therapy Set")
		createSinglePrescription = (r) => 
			openFlyout("Single Prescription")
		prettyConfirm("Referral Creating", "Select Referral type", "Therapy Set", "Single Prescription", createTherapySet, createSinglePrescription, BootstrapDialog.TYPE_SUCCESS)
		linkMap = 
			link: "patient",
			links: ["patient"],
			linkid: { patient: values.patient_id }
		openFlyout = (referralType) => 
			fr = window.getCarePlanLinkMap(linkMap)
			fr.then (lm) =>
				if lm.error
					prettyError false, lm.error
					return
				ps.careplan_id = lm.linkid.careplan
				k = $(e.target)?.parent()?.parent()?.attr('field')
				is_flyout = $('#flyout').length
				ps.order_format = referralType
				style = {}
				if is_flyout
					style = {left: '40%', width: '60%', 'backdrop-filter': 'unset', 'background-color': 'unset'}
				dfd = Flyout.open
					form: mng,
					linkMap: lm,
					link: lm.link,
					links: lm.links,
					linkid: lm.linkid,
					preset: ps
					style: style
				dfd.done (values, args...) ->
					if values? and values.code? and values.auto_name?
						nv = id: values.code, text: values.auto_name, fd: values
						if d.data('selopt')?.multiple? and d.data('selopt').multiple
							dv = d.select2('data')
							dv.push nv
						else
							dv = nv
						DSLFields.value(d, dv)
					return
		false


	event_manage_add: (e) ->
		mng = e.target.hash.substr(1)
		return false if mng is ''
		d = $(e.target).parents('.form-horizontal').find('input.select2field,select.select2field')
		return false if not d
		if mng == "careplan_order"
			@event_manage_add_careplan_order(e)
			return
		style = {}
		if mng == "patient"
			mng = "view_create_patient"
			style = {left: '40%', width: '60%', 'backdrop-filter': 'unset', 'background-color': 'unset'}
		dd = d.data('dd')
		v = d.data('v')
		if !dd or !v
			console.error "Missing Data For Add Manage", dd, v
			return
		psd = generatePreset(dd, mng, v.view.add_preset || {})
		psd.then (data) =>
			if data.error
				prettyError "Error Generating Preset", data.error
				return
			dfd = Flyout.open
				linkMap: data?.linkMap,
				link: data?.linkMap?.link,
				links: data?.linkMap?.links,
				linkid: data?.linkMap?.linkid,
				form: mng,
				preset: data.preset,
				style: style
			dfd.done (values, args...) ->
				if values? and values.id? and values.auto_name?
					nv = id: values.id, text: values.auto_name, fd: values
					if d.data('selopt')?.multiple? and d.data('selopt').multiple
						dv = d.select2('data')
						dv.push nv
					else
						dv = nv
					DSLFields.value(d, dv)
				return
		psd.catch (err) ->
			console.error err
			prettyError "Unexpected Error", 'Unable to perform operation'
			return
		false

	event_manage_edit: (e) ->
		mng = e.target.hash.substr(1)
		return false if mng is ''
		d = $(e.target).parents('.form-horizontal').find('input.select2field,select.select2field')
		return false if not d
		r = DSLFields.value(d)
		style = {}
		return false if not r
		dfd = Flyout.open
			form: mng
			record: r
			style: style
		dfd.done (values, args...) ->
			# this will not be called for multi
			DSLFields.value(d, {id: r, text: values.auto_name}) if values?.auto_name?
			return
		false

	event_manage_list: (e) ->
		mng = e.target.hash.substr(1)
		if mng is ''
			App.reactNav.goTo("/settings/manage/")
		else
			App.reactNav.goTo("/settings/manage/#{mng}/")
		false


	# subform events
	event_subform_add_row_dum: (e) ->
		btn_id =  $(e.target).closest('.suform-editable-add').find('.add-subrow-dum').attr('id')
		return if !btn_id
		b = $(e.target).closest('.controls-grid').find('.add-subrow#' + btn_id)
		if b.length
			v = $(e.target).closest('.controls-grid').find('.sf-table-container>table').first().data('v')
			opts = 
				force_flyout: false
			if v.view?.grid?.add is 'flyout'
				opts.force_flyout = true
			FieldSubform.add_row(b, null, opts)
		return
	
	event_subform_add_row_flyout: (e) ->
		btn_id =  $(e.target).closest('.suform-editable-add').find('.add-subrow-flyout').attr('id')
		return if !btn_id
		b = $(e.target).closest('.controls-grid').find('.add-subrow#' + btn_id)
		if b.length
			FieldSubform.add_row(b, null, {force_flyout: true})
		return

	event_subform_add_row: (e) ->
		FieldSubform.add_row(e, null, {force_flyout: true})
		return

	event_subform_delete_row: (e) ->
		t = $(e.target).closest('table')
		FieldSubform.delete_row(e)
		return

	event_subform_split_row: (e) ->
		t = $(e.target).closest('table')
		FieldSubform.split_row(e)
		return

	event_subform_edit_row: (e) ->
		return if e.target.tagName is 'A' # allow normal file links for download
		t = $(e.target).closest('table')
		FieldSubform.edit_row(e)
		return

	event_subform_preview_row: (e) ->
		return if e.target.tagName is 'A' # allow normal file links for download
		t = $(e.target).closest('table')
		FieldSubform.preview_row(e)
		return

	event_subform_read_row: (e) ->
		return if e.target.tagName is 'A' # allow normal file links for download
		t = $(e.target).closest('table')
		FieldSubform.read_row(e)
		return

	event_top: ->
		scrollTop()
		return

	close_audit: (e) ->
		$(e.target).parent().parent().parent().parent().find('input.audit-select')?.select2('destroy')
		$(e.target).parent().parent().parent().parent().find('input.audit-select')?.remove()

		p = $(e.target).parent().parent().parent().parent().parent()
		$(p).find('.table')?.removeClass('hidden')
		$(e.target).closest('.form-horizontal').find('.note').show()

		table_id = $(e.target).attr('grid')
		$('.'+table_id).DataTable().destroy()
		$('.root_'+table_id).remove()

	load_audit_tooltip: (e) ->
		if not isBrowserIpad()
			$(e.target).attr('data-placement', 'right').attr('data-original-title','Click to view audit trail').tooltip('show')
			
	load_audit:  (e) ->
		elc = $(e.target).parent()
		el = elc.parent()
		pfm = @options.form
		fid = @options.record
		if elc.find('div.fld-adt-hsty-con').length is 0
			elc.append('<div class="fld-adt-hsty-con"></div>')
		fld = elc.find('div.fld-adt-hsty-con')[0]
		fld_id = elc.attr('for')
		loadComponent("FieldAuditHistory", fld, fld_id, {pfm, fid, fld_id, el})
		return

	load_audit_select: (e) ->
		p = $(e.target).parent().parent().parent()
		fm = p.attr('form')
		fd = p.attr('field')
		pfm = @options.form
		fid = @options.record
		table_id = 'table_' + fd

		if $(e.target).parent().find('input.audit-select').length > 0
			$(e.target).parent().find('input.audit-select').select2('destroy')
			$(e.target).parent().find('input.audit-select').remove()
			$(p).find('.table').removeClass('hidden')
			$(e.target).closest('.form-horizontal').find('.note').show()

			if $('.root_'+table_id+'').length > 0
				$('.'+table_id).DataTable().destroy()
				$('.root_'+table_id).remove()
			return
		select = '<input type="select" class="audit-select" style="width:200px;margin-left:10px"/>'
		$(e.target).parent().append(select)

		aj = Ajax.async
			url: '/form/audit?filter=form_name:'+pfm+'&filter=form_id:'+fid
		aj.done (data) ->
			data = data.filter((d)->
				!_.isEmpty(d.fields.updated_by_auto_name || d.fields.created_by_auto_name || d.fields._meta?.client?.updated_by_auto_name || d.fields._meta?.client?.created_by_auto_name)
			)
			data = data.filter (data) ->
				data?.event_name == 'create' or data?.data_diff?.$update?[fd]
			obj = data.map((d) ->
				return {'id': d.id, 'text': ((d.fields.updated_on || d.fields.created_on) + ' by ' +  (d.fields.updated_by_auto_name || d.fields.created_by_auto_name || d.fields._meta?.client?.updated_by_auto_name || d.fields._meta?.client?.created_by_auto_name))}
			)
			$('.audit-select').select2({
					data:obj
					placeholder: 'Select Historical Values'
					allowClear: true 
				}
			)
			return
		aj.fail () ->
			if ($(e.target).siblings('.tooltip').length > 0)
				$(e.target).attr('data-original-title','<i>Error loading audit history...</i>').tooltip('show')
			return


	load_audit_grid: (e) ->
		p = $(e.target).parent().parent().parent()
		fm = p.attr('form')
		fd = p.attr('field')
		pfm = @options.form
		fid = @options.record
		table_id = 'table_' + fd

		fdsl = DSL[pfm]

		value = $(e.target).val()
		if _.isEmpty(value)
			if $('.root_'+table_id+'').length > 0
				$('.'+table_id).DataTable().destroy()
				$('.root_'+table_id).remove()
				$(p).find('.table').removeClass('hidden')
				$(e.target).closest('.form-horizontal').find('.note').show()
			return
		
		$(p).find('.table').addClass('hidden')
	
		if $('.root_'+table_id+'').length > 0
			$('.'+table_id).DataTable().destroy()
			$('.root_'+table_id).remove()
			$(e.target).closest('.form-horizontal').find('.note').show()

		$(e.target).attr('data-original-title','Loading...').tooltip('show')
		$(e.target).closest('.form-horizontal').append('<div class="row root_'+table_id+' history_grid"><div  style="padding-left:1px;" class="col-sm-12 col-md-12 col-lg-12"><span grid='+table_id+'  class="close_audit_grid" style=" position: absolute; right: 16px; top: -12px; z-index: 1;cursor: pointer;"><i grid='+table_id+' class="fa fa-times-circle fa-lg" aria-hidden="true"></i></span><table  class="display '+table_id+'" width="100%"></table></div></div>')
		if pfm != fm
			for k,v of @subforms.field
				if v == fm
					sfd = k
			if not sfd
				if ($(e.target).siblings('.tooltip').length > 0)
					$(e.target).attr('data-original-title','<i>Error finding audit history...</i>').tooltip('show')

		dsl = DSL

		aj = Ajax.async
			url: '/form/audit?filter=form_name:'+pfm+'&filter=form_id:'+fid
		aj.done (data) =>
			hist_str = ''
			idx = 0
			grid_data = []
			data.reverse()
			
			for hist in data
				if !_.isEmpty(value) && hist.id > value
					continue
				hist_data = hist.data_diff.$update?[fd] || hist.data_diff[fd]
				for k,v of hist_data
					if !k.startsWith '$'
						isParent = true
						for key,val of v
							if key is '_meta'
								continue
							if typeof val is 'number'
								v[key] = val.toString()
							if key.startsWith '$'
								isParent = false
								grid_data[k][Object.keys(val)[0]] = Object.values(val)[0]
						if isParent
							delete v._meta
							grid_data.push(v)
					else
						if k is '$insert'
							for key,val of v
								for obj in val
									if typeof obj is 'object'
										delete obj._meta
										grid_data.push(obj)
						else if k is '$delete'
							for val in v
								grid_data.splice(val, 1)
			if Object.keys(dsl[pfm].fields[fd]?.model?.subfields).length > 0
				cls = Object.keys(dsl[pfm].fields[fd].model.subfields).map((d)->
					return { 'title': d }
				)
			else
				cls = dsl[fm].view.grid.fields.map((d)->
					return { 'title': if typeof dsl[fm].fields[d].model.source is 'string' then d + '_auto_name' else d }
				)
			objectConstructor = ({}).constructor;
			data = grid_data.map((d)->
				return cls.map((c)->
					if !d[c.title]
						return d[c.title]
					if Array.isArray d[c.title]
						return d[c.title].join(', ')
					if d[c.title].constructor == objectConstructor
						return JSON.stringify d[c.title]
					return d[c.title]
				)
			)

			cls = cls.map((c)->
				return {'title': c.title.replace('_auto_name', '').replace('_', ' ')}
			)

			if _.isEmpty(value)
				data=[]

			$.fn.dataTable.moment('MM/DD/YYYY HH:mm:ss')

			$('.'+table_id).DataTable(
				data: data
				searching: false
				scrollY: '200px'
				scrollX: true
				destroy: true
				columns: cls
				emptyTable: '0 items'
				scrollCollapse: true
				infoCallback:(settings, start, end, max, total, pre) =>
					@$('.pagination').addClass('hide')
			)
			$(e.target).attr('data-original-title','').tooltip('hide')
			$(e.target).closest('.form-horizontal').find('.note').hide()
			return
		aj.fail () =>
			if ($(e.target).siblings('.tooltip').length > 0)
				$(e.target).attr('data-original-title','<i>Error loading audit history...</i>').tooltip('show')
			return

		return

	event_field_input_keydown: (e) ->
		d = $(e.target)
		ft = d.data('fieldtype')
		v = d.data('v')
		return if not v?.model?

		if ft is 'input'
			if v.model.type is 'text' and (v.model.dynamic?.source? or v.model.dynamic?.query?)
				if e.keyCode is 8 or e.keyCode is 46
					d.text ''
					d.typeahead 'show'
		return

	handle_field_event: (d) ->
		return if not @events_handle
		return if d.data('container') is 'grid'
		form = d.data('form')
		ft = d.data('fieldtype')
		k = d.data('k')
		v = d.data('v')
		dd = d.data('dd')
		df = d.data('df')
		view = d.data('view')
		id = d.data('id')
		return if not v?.model?

		if ft is 'input'
			if v.model.type is 'text' and (v.model.dynamic?.source? or v.model.dynamic?.query?)
				DSLFields.action 'field_populate', d, {d, form, view, id, k, v, df, dd}

		@mark_edited()
		sec = d.data('section')
		dd = @subforms.formmap[form]
		return if !dd
		if dd.options?.parent?.ddf
			if dd.options.wrapper?.form_rendered
				dd.options?.parent?.ddf.set_validation_error(form, k, false)
		# safe to call $.val() on text input fields
		if ft in ['area', 'datetime', 'input', 'select']
			# trim text / inputs
			dv1 = d.val()
			dv2 = $.trim(dv1)

			# HB-4584
			# if field type is select then dv1 will may have array value ['value1'] after trim it will become string 'value1'
			# As we are assigning string value to array type then unexpected behvaiour will occur. And in select type case it show same value after select

			if getType(dv1) in ['string'] and getType(dv2) in ['string']
				d.val(dv2) if dv1 isnt dv2
			
			if v.model.type in ['int', 'decimal'] and (v.view.class.includes('numeral')) and (v.view.format)
				dv1 = d.val().toString().replace(/[,A-Za-z$%]/g, '')
				d.val(dv1) if dv1 isnt dv2
	
			# rounding decimals only (skip values like: 5'8)
			if (v.model.type is 'decimal') and (v.model.rounding isnt null)
				dv1 = d.val()
				if dv1.replace(/[^0-9\.]/gi, '') is dv1 # if decimals only
					dv2 = roundTo(dv1, v.model.rounding)
					d.val(dv2) if $.trim(dv1) isnt $.trim(dv2)

			# handle transforms / validations
			if v.model.transform.length > 0
				DSLFx.TransformField(DSL[form], dd, v.model.transform, d, k)
			if v.view.transform.length > 0
				DSLFx.TransformField(DSL[form], dd, v.view.transform, d, k)

			# rounding everything
			if (v.model.type is 'decimal') and (v.model.rounding isnt null)
				dv1 = d.val()
				if dv1 isnt ''
					dv2 = roundTo(parseFloat(dv1), v.model.rounding)
					if isNaN(dv2)
						d.val('')
					else if dv1 isnt $.trim(dv2)
						d.val(dv2)

			# min/max
			if v.model.type in ['int', 'decimal']
				dv = d.val()
				if dv isnt ''
					dv2 = if v.model.type is 'int' then parseInt(dv) else parseFloat(dv)
					dv2 = Math.max(dv2, v.model.min) if v.model.min
					dv2 = Math.min(dv2, v.model.max) if v.model.max
					if isNaN(dv2)
						d.val('')
					else if $.trim(dv) isnt $.trim(dv2)
						d.val(dv2)
						if (v.model.type is 'decimal') and (v.model.rounding isnt null)
							dv2 = roundTo(parseFloat(dv2), v.model.rounding)
							d.val(dv2)

			# Numeral Formating
			if v.model.type in ['int', 'decimal']
				if(v.view.class.includes('numeral')) and (v.view.format) and dv2
					dv2 = numeral(dv2.toLocaleString()).format(v.view.format);
					d.val(dv2)
		else if ft in ['radio', 'checkbox']
			if v.model.transform.length > 0
				DSLFx.TransformField(DSL[form], dd, v.model.transform, d, k)
			if v.view.transform.length > 0
				DSLFx.TransformField(DSL[form], dd, v.view.transform, d, k)  

		if v.model?.required_all == true and v.model?.multi == true and v.view.control == 'checkbox'
				if DSLFields.value_get(d).length > 0 and v.model.source.length != DSLFields.value_get(d)
					DSLFx.ValidateFieldError d, 'Field is required (All options must be select).'
				else
					DSLFx.ValidateFieldError d, false
		else	
			ferr = DSLFx.ValidateFieldRules(DSL[form], dd, v, d, k, true)

		# handle model.if conditions after all transforms
		if not _.isEmpty(v.model.if)
			DSLRecordSubform.handle_if dd, DSL[form], k, d
			DSLRecordSubform.show_hide dd if @events_show_hide

		# update section checkmark
		if @events_section_check and sec
			dd.section_check sec

		@check_missing_required_fields()

		# automatically call verify if toggling a required_if field to show required on fields
		if @options.mode isnt 'read' and DSL[form]?.model?.required_if? and DSL[form].model?.required_if is k
			if DSL[form].model.required_if_fields? and DSL[form].model.required_if_fields.length > 0
				for k,v of dd.field_map
					if dd.field_nodes[k].view?.requireif_bypass and dd.field_nodes[k].model?.required
						DSLFx.ValidateFieldError dd.field_nodes[k], 'Field is required'
					if k in DSL[form].model.required_if_fields
						if	DSLFields.value_get(dd.field_nodes[DSL[form].model?.required_if]) is 'Yes'
							DSLFx.ValidateFieldError dd.field_nodes[k], 'Field is required'
						else
							DSLFx.ValidateFieldError dd.field_nodes[k], false
		cfv = dd.options.parent
		if cfv?.ddf?.form_rendered and cfv.parent?.inline
			gflds = cfv.parent?.grid?.tooltip
			if gflds?.length and gflds.includes(k)
				ps = dd.options.parent.ddf.values()
				ttv = window.get_text_from_formdata(ps, gflds)
				ttc = dd.$el.find("#eg-tooltip")
				if ttv
					ttc.removeClass("offscreen")
					ttc.find("#eg-tooltiptext").html(ttv)
				else
					ttc.addClass("offscreen")
			gridc = cfv.parent?.grid
			splitif = gridc?.splitif
			deleteif = gridc?.deleteif
			pas = null
			if splitif
				if pas is null
					pas = dd.options.parent.ddf.values()
				ttc = dd.$el.find("#grid-split")
				if !FieldSubform.can_split_grid(splitif, pas or {})
					ttc.addClass('offscreen')
				else
					ttc.removeClass('offscreen')
			if deleteif
				if pas is null
					pas = dd.options.parent.ddf.values()
				ttc = dd.$el.find("#grid-delete")
				if !FieldSubform.can_delete_row(deleteif, pas or {})
					ttc.addClass('offscreen')
				else
					ttc.removeClass('offscreen')
			
		# react will call this function whenever a field changes needs to be passed in through rxOpts
		if cfv?.ddf?.form_rendered
			cfv?.parent?.options?.onFieldChange?(k)
		dd.formBuss.hopOnFormBuss(d) if dd.formBuss
		return

	handle_grid_event: (d) ->
		return if not @events_handle
		return if d.data('container') is 'fieldinline'
		return if d.closest('table').data('container') isnt 'grid'
		@mark_edited()
		st = d.attr('subtype')
		if st isnt 'select'
			dv = d.val()
			sv = $.trim(dv)
			if (sv isnt '') and (st in ['decimal', 'int'])
				sp = if st is 'int' then parseInt(sv) else parseFloat(sv)
				if isNaN(sp)
					d.val('')
				else if sv isnt $.trim(sp)
					d.val(sp)
			else if dv isnt sv
				d.val(sv)

		# handle transforms / validations
		t = d.closest('table')
		form = t.data('form')
		k = t.data('k')
		v = t.data('v')
		dd = @subforms.formmap[form]

		if v.model.transform.length > 0
			DSLFx.TransformField(DSL[form], dd, v.model.transform, d, k, true)
		if v.view.transform.length > 0
			DSLFx.TransformField(DSL[form], dd, v.view.transform, d, k, true)

		ferr = DSLFx.ValidateFieldRules(DSL[form], dd, v, d, k, true)

		@check_missing_required_fields()
		cfv = dd.options.parent
		if cfv?.ddf?.form_rendered
			cfv?.parent?.options?.onFieldChange?(k)
		dd.formBuss.hopOnFormBuss(d) if dd.formBuss
		return

	checkbox_keyboard_select: (e) ->
		if e.which is 32 or e.which is 13
			e.stopPropagation()
			e.preventDefault()
			$('[type=checkbox]', e.target).trigger('click')
			return

	horiz_radios_select: (e) ->
		if e.which is 32 or e.which is 13
			e.preventDefault()
			e.stopPropagation()
			r = $(e.target).closest('label')
			r.toggleClass('active').siblings('.active').removeClass('active')
			r.closest('.radios').trigger 'change'
		return


	event_subform_load_dynamic:(e) ->
		if $(e.target).find('option')?
			fields = $(e.target).attr('data-fields')
			form = $(e.target).attr("source")
			label = $(e.target).attr("label")
			update_not_required = $(e.target).attr('dynamicUpdate')
			static_filter = $(e.target).attr('sf')
			sk = $(e.target).attr('sk')
			vl = $(e.target).attr('vl')
			if update_not_required == "true"
				return
			
			fieldsList = if fields.length>0? then fields.substring(0, fields.length-1).split(",") else []
			if fieldsList.length > 0
				dynamicFieldValues={}
				url = '/form/' + form + '/?limit=1000&sort=auto_name&'
				for col in fieldsList
					if col == "patient_id"
						dynamicFieldValues[col]= $(e.target).closest('div[id*="patient_id"]').attr("patient")
						url += 'filter=' + col + ':' + $(e.target).closest('div[id*="patient_id"]').attr("patient")
					else if $('div[field="'+col+'"] input')? #Osama need testing
						dynamicFieldValues[col]= $(e.target).closest('div[id*="patient_id"]').find('div[field="'+col+'"] input').val()
						url += 'filter=' + col + ':' + $(e.target).closest('div[id*="patient_id"]').attr("patient")

				changed = false
				form = $('.sidesubmenu.active').find('a').attr('href').substring(1)

				data = Ajax.sync 
						url: url
						type: 'GET'
						headers: 
							'Clara-proxy-local': true
						

				if !Array.isArray(data)
					data=[data]

				$(e.target).attr('dynamicUpdate',true)

				options = '<option value=""> Select ' + label + '</option>'
				$(e.target).empty()
				$(e.target).append(options)

				for val of data
					$(e.target).append('<option value="' + 
					(if data[val]['id'] == vl then ' selected="selected"' else ' ') +
					(if data[val].name? then data[val].name else (if data[val][sk+"_auto_name"]? then data[val][sk+"_auto_name"])) + '"> ' + 
					(if data[val].name? then data[val].name else (if data[val][sk+"_auto_name"]? then data[val][sk+"_auto_name"])) + 
						'</option>')
					changed = true

				if changed
					$(e.target).select2('destroy')
					$(e.target).select2()
					$(e.target).select2('open')

		return
	disable_popup_for_first_focus: (e) ->
		if @first_datetime_focusing
			if not App.company.prevent_first_datetime_focus_popup.includes(@options.mode)
				@first_datetime_focusing = false
				return
			if not @form_rendered
				$('.datetimepicker').css('display', 'none')
				ref = @
				setTimeout ->
					ref.disable_popup_for_first_focus e
				, 1000
				return
			@first_datetime_focusing = false
			$(e.target).datetimepicker 'hide'
		return

	initialize: (options) ->
		options.wrapper = @
		@options = options
		return