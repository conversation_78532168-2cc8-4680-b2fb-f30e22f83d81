class DSLDraw extends B.View

	attach_data: ->
		for k,v of @section_map
			s = @$('#' + k)
			s.data('id', k)
			s.data('dd', @)
			@sections[v] = s
			@section_fields[v] = []

		for k,v of @fields
			d = @$('#' + k)
			@fields[k].node = d
			@field_nodes[v.k] = d
			@field_map[v.k] = [] if not @field_map[v.k]?
			@field_map[v.k].push d

			if v.view is 'form'
				@section_fields[v.section]?.push @fields[k]
				d.data('group', d.closest('.form-group'))

			for kk,vv of v
				d.data(kk, vv)
		return

	clear: ->
		for k,v of @fields
			DSLFields.clear(v.node)
		return

	default: (k, v) ->
		if v.model.type is 'subform' and v.model.multi and @preset?
			df = @preset
		else if @preset?[k]?
			df = @preset[k]
		else
			df = v.model.default
			if k.startsWith('tabif_')
				df = null
			if v.view.class?.includes?('checkbox-only') and v.view.control is 'checkbox' and  ["read", "edit"].includes(@options.mode)
				df = null
		if getType(df) is 'null'
			''
		else
			df

	@default_params: (join = true, max_rows_override = false) ->
		p = ['limit=' + (if max_rows_override then max_rows_override else MAX_GRID_ROWS)]
		p = p.join('&') if join
		p

	draw_base_field: (form, view, fc, fx, id, k, v, df) ->

		# fill in the template defaults for add/edit forms
		if (view is 'form') and ((not @force_readonly?) or (not @force_readonly)) and ($.trim(df) is '') and
				(getType(v.model.default) is 'null') and (getType(v.view.template) isnt 'null')
			df = DSLDraw.parse_template_field(v.view.template, v.model.type)

		d = DSLFields.field(fx).draw(form, view, id, k, v, df, @options?.mode)
		# make it easy to go from $(#field) to dsldraw, dsl etc.
		d.data.container = fc
		d.data.dd        = @
		d.data.form      = form
		d.data.view      = view
		d.data.k         = k
		d.data.v         = v
		@fields[id]      = d.data
		d

	@get_field_control: (v, card_width=null) ->
		if card_width and v.view.control is 'radio' and ['array', 'object'].includes(getType(v.model.source)) and v.model.type isnt 'boolean' && getType(v.view.columns) is 'number'
			cols = Math.trunc(v.view.columns)
			if cols < 0
				cols = cols * -1
			sources = []
			if getType(v.model.source) is 'object'
				sources = Object.values(v.model.source)
			else
				sources = v.model.source
			total_width = card_width
			area_pm = 12
			section_pm =  14
			radio_box_padding = 18
			font_size = 13
			usable_area = total_width - area_pm - section_pm;
			field_area = usable_area / cols
			usable_field_area  = field_area - (sources.length * radio_box_padding)
			area_required = sources.join('').length * font_size
			if area_required > usable_field_area
				'select'
			else
				'radio'
		else if (v.model.type is 'subform') and v.model.multi # inline subform (multi=false) will never get to here
			'subform'
		else if v.model.type in ['date', 'datetime', 'time']
			'datetime'
		else if v.view.control in DSLFields.field_types # all supported field types
			v.view.control
		else
			'input'

	initialize: (options) ->
		@options = options
		@preset = {}          # field key default
		@fields = {}          # fld_id => $.data
		@field_map = {}       # field key => list of nodes
		@field_nodes = {}     # field key => last node
		@sections = {}        # sec key => section node
		@section_fields = {}  # sec key => list of $.data
		@section_map = {}     # sec_id => sec key
		@section_tab_map = {}
		@tab_toggle_map = {}
		@load()
		return

	load: ->
		return

	make_row_html: (row, cols, fields, opts={}) =>
		return false if DSLRecordSubform.pending_status_created_by_others(row)
		custom_renderer = opts?.custom_renderer or []
		fr = {}
		id_field = @options['gridIDField'] or 'id'
		if id_field == 'code' and not DSL[@options.form].fields.code?
			id_field = 'id'
		if cols? and (getType(cols) is 'array') and (cols.length > 0)
			id = '<u class="id hide" tr_id="'+row[id_field]+'" tr_auto_name="'+row.auto_name+'"></u>'
			fr.id = row.id
			fr.archived = row.archived or false
			if @options.linkactive?
				fr.disable_editing = row[@options.linkactive[0]]? and
					parseInt(row[@options.linkactive[0]]) isnt parseInt(@options.linkactive[1])
			if DSLRecordSubform.pending_status_change(row)
				row = _.defaults(row.change_data, row)
				fr.colstatus_change = 'Pending'
			else if 'colstatus_change' in cols
				fr.colstatus_change = ''
			for k in cols
				continue if k in ['colstatus_change']
				continue if k in custom_renderer
				if k.indexOf('.') > -1 # show data from subforms
					[kt, kf, kp] = k.split('.') # [table name, subform_field, field key]
					if row[kf]? and row[kf][0]? and DSL[kt]?
						if row[kf][0][kp + '_auto_name']?
							dk = escapeHTMLGrid(row[kf][0][kp + '_auto_name'])
						else
							dk = escapeHTMLGrid(row[kf][0][kp])
						v = DSL[kt].fields[kp]
					else
						dk = ''
						v = false
				else if k in DSL[@options.form]?.model.collections # auto-concat collections
					ak = []
					for i in [1..MAX_COLLECTIONS]
						if row[k + '_' + i + '_auto_name']?
							rk = row[k + '_' + i + '_auto_name']
						else if row[k + '_' + i]?
							rk = row[k + '_' + i]
						else
							continue
						rk = escapeHTMLGrid(if getType(rk) is 'array' then rk.sort().join(', ') else rk)
						ak.push rk if rk isnt ''
					dk = ak.join('<br/>')
					v = false
				else if row[k]?
					v = fields[k]
					if row[k + '_auto_name']? and ($.trim(row[k + '_auto_name']) != '')
						dk = row[k + '_auto_name']
					else
						dk = row[k]
						if v.model.type is 'json' and v.view.control is 'esign'
							dk = esignJSONLabel(dk)
						else if v.model.type is 'json' and v.view.control is 'file'
							dk = fileJSONLabel(dk)
						else if v.model.type is 'json' and v.view.control is 'link'
							dk = linkJSONLabel(dk)
						else if v.model.multi and (getType(dk) is 'array') and (getType(v.model.source) is 'object')
							nk = []
							for dkk in dk
								if (v.model.source[dkk]?)
									nk.push v.model.source[dkk]
							dk = nk
						else if getType(dk) is 'string' and dk.substr(0, 1) is '[' and dk.substr(-1) is ']' and v.view.control in ['checkbox', 'radio']
							try
								nk = JSON.parse(dk)
								dk = nk if nk
							catch e
						else if getType(dk) is 'string' and dk.substr(0, 1) is '{' and dk.substr(-1) is '}' and v.view.control in ['checkbox', 'radio'] #HB-3617
							try
								dk = FieldCheckbox.value_from_string(v, dk)
							catch e
					dk = escapeHTMLGrid(if getType(dk) is 'array' then dk.sort().join(', ') else dk)
				else
					ef = DSL[@options.form].fields[k]
					dk = ''
					if ef.model.type is 'json' and ef.view.control is 'link'
						dk = FieldLink.grid_draw_link(ef, row)
					v = false
				if v
					if (getType(v.model.source) is 'object') and (v.model.source[dk]?)
						dk = v.model.source[dk]
					if (v.model.type is 'decimal') and (v.model.rounding isnt null) # rounding
						if v.view.class.includes("numeral") and v.view.format and dk
							dk = numeral(dk).format(v.view.format)
						else
							dk = roundTo(dk, v.model.rounding)
					else if v.model.type in ['date', 'datetime', 'time']
						allowed_time_format = [
							'hh:mm A',
							'HH:mm',
							'hh:mm:ss A',
							'HH:mm:ss',
							'H:mm',
							'H:mm:ss',
							'h:mm',
							'h:mm:ss',
							'YYYY-MM-DDTHH:mm:ssZ',
							'YYYY/MM/DDTHH:mm:ssZ',
							'YYYY-MM-DDTHH:mm:ss',
							'YYYY/MM/DDTHH:mm:ss'
						]
						parsed_moment = if v.model.type == 'time' then moment(dk, allowed_time_format) else moment(dk)
						if parsed_moment.isValid()
							dk = switch v.model.type
								when 'date' then parsed_moment.format(FORMAT_DATE)
								when 'datetime' then parsed_moment.format(FORMAT_DATE_TIME)
								when 'time' then parsed_moment.format(FORMAT_TIME)
						else
							dk = dateTimeFromTZ(dk)

				if opts?.preRenderedColumns?[k]
					dk = opts.preRenderedColumns[k]
				fr[k] = dk + id
				id = ''
		fr = @options.parent.gridrow(fr, row, cols, fields) if @options.parent?.gridrow?
		fr: fr, rowOpts: opts

	offscreen_field: (k) ->
		if @field_map[k]?
			# set the first visible instance of field to offscreen
			for v in @field_map[k]
				v.closest('.form-group').addClass('offscreen')
				return
		return

	@parse_template_field: (template, type) ->
		if template is '{{site_id}}'
			if window.sitesSelected and window.sitesSelected.length == 1 and window.sitesSelected[0] != 0
				return window.sitesSelected[0]
			else
				return
		else if template is '{{site_id_default}}'
			if window.UserPreference?.default_prefill_site_id?
				return window.UserPreference.default_prefill_site_id
			else
				return
		else if template is '{{user.id}}'
			App.user.id
		else if template is '{{user.auto_name}}'
			App.user.auto_name
		else if template is '{{user.displayname}}' or template is '{{user.name}}'
			App.user.displayname
		else if template is '{{user.role}}'
			if App.user.job_title?
				App.user.job_title
			else
				App.user.role_auto_name
		else if template is '{{user.role.code}}'
			App.user.role
		else if template is '{{now}}'
			if type is 'date'
				(new Date()).format('mm/dd/yyyy')
			else if type is 'time'
				(new Date()).format('hh:MM TT')
			else if type is 'datetime'
				(new Date()).format(FORMAT_DATETIME_SAVE)
			else if type is 'timestamp'
				(new Date()).format('m/d/yy h:MMtt')
			else if type is 'timenow'
				(new Date()).format('h:MMtt')
			else if type is 'datenow'
				(new Date()).format('m/d/yy')
		else if template is '{{past}}'
			'..' + (new Date()).format('mm/dd/yyyy')
		else if template is '{{future}}'
			(new Date()).format('mm/dd/yyyy') + '..'
		else if template is '{{last_month}}'
			moment().subtract(1, 'months').endOf('month').format('MM/DD/YYYY')
		else if template is '{{year}}'
			(new Date()).format('yyyy')
		else if template is '{{next_business_day}}'
			today = moment().day()
			if type is 'date'
				if today == 5 or today == 6
					moment().weekday(8).format("MM/DD/YYYY")
				else
					moment().add(1, 'days').format("MM/DD/YYYY")
	toggle_subform: (show = true, force = false) ->
		w = @options.wrapper
		k = w.subforms?.fieldrev?[@options.form]
		w.toggle_subform k, show, force if k

	unload: ->
		@handler.unload() if @handler?
		@undelegateEvents()
		@

	value_field_get: (k, ignore_if = false) ->
		if @field_map[k]?
			for v in @field_map[k]
				if ignore_if
					vl = DSLFields.value_get(v)
				else
					vl = DSLFields.value(v)
				return vl if vl isnt null
		null

	value_field: (k, n = null, allow_null = false, allow_hidden = false, ignore_if = false) ->
		if @field_map[k]?
			# return the first visible instance of field with data
			for v in @field_map[k]
				if ignore_if
					vl = DSLFields.value_get(v)
				else
					vl = DSLFields.value(v)
				# only set new value if visible/non-null unless using flags
				if (allow_hidden or getType(vl) isnt 'null') and (allow_null or n)
					if ignore_if
						vl = DSLFields.value_set(v, n)
					else
						vl = DSLFields.value(v, n)
				return vl if vl isnt null

		# check self. also check parent if self is subform
		if !@can_set_link_id(k)
			return null
		for optel in [[@options, @$el], [@options.parent, @options.parent.$el]]
			[opt, el] = optel
			# used for dynamic sourcefilters for link fields #HB-2305
			if k and opt.link and (k is opt.link + '_id')
				oid = getLinkID(opt.link, opt.linkid, el)
				return oid if oid
			else if k and opt.linkid and opt.linkid[k.replace('_id', '')]?
				lid = getLinkID(k.replace('_id', ''), opt.linkid, el)
				return lid if lid
			else if k and opt.links and k.replace('_id', '') in opt.links
				lid = getLinkID(k.replace('_id', ''), opt.links, el)
				return lid if lid

		null

	value_field_fixed: (k, v) ->
		@fields_fixed = {} if not @fields_fixed?
		@fields_fixed[k] = v
		return

	value_parse: (v, vl, opt) ->
		if v.model.type in ['int', 'decimal'] and (v.view.class.includes('numeral')) and (v.view.format)
			vl = vl.toString().replace(/[,A-Za-z$%]/g, '')
		if v.model.type is 'int'
			vl = parseInt(vl)
			vl = '' if isNaN(vl)
		else if v.model.type is 'decimal'
			vl = parseFloat(vl)
			vl = '' if isNaN(vl)
		else if v.model.type is 'datetime'
			if opt?.allow_date_range
				vl = FieldDatetime.date_time_parse(v.model.type, vl, true)
			else
				vl = FieldDatetime.date_time_parse(v.model.type, vl)
				vl = if isNaN(vl.getTime()) then '' else vl.format(FORMAT_DATETIME_SAVE)
		else if v.model.type is 'json' and v.view.control is 'area'
			try
				nv = JSON.parse(vl)
				vl = nv
			catch e
		vl
	# Todo: Merge values_filter and values --- @Osama
	# currently don't wanna mess up DSLCard
	values_filter: (opt) ->
		r = {}
		# form values
		for k,v of @fields
			vl = DSLFields.value(v.node)
			continue if vl is null
			if (getType(vl) is 'array') and (vl.length > 0)
				vl = for vi in vl
					@value_parse(v.v, vi, opt)
			else if vl isnt ''
				vl = @value_parse(v.v, vl, opt)
			r[v.k] = vl
			vla = DSLFields.value_auto_name(v.node)
			r[v.k + '_auto_name'] = vla if vla isnt null and vla isnt vl
		# handle optional required_if fields
		r = DSLFx.TransformRequiredIf(@options.form, r)
		# fixed values (filled via transforms)
		for k,v of @fields_fixed
			r[k] = v
		# hard-coded values (filled based on logic)
		r.prefilled = 'Y' if DSL[@options.form].fields.prefilled? and (@find?() isnt true)

		r

	can_set_link_id: (link_field) ->
		form_id = @get_record_data('id')
		if !form_id
			return true
		if !@field_nodes[link_field]
			return true
		fld_def = DSL[@options.form].fields[link_field]
		if fld_def.view.readonly or fld_def.view.offscreen 
			return true
		return false

	values: (opt) ->
		r = {}
		fval = {}
		if !@options.wrapper
			return @values_filter(opt)
		subforms = @options.wrapper.subforms
		DSLFx.TransformForm(DSL[@options.form], subforms.formmap[@options.form])
		# form values
		for k,v of @fields
			vl = DSLFields.value(v.node)
			continue if vl is null
			if (getType(vl) is 'array') and (vl.length > 0)
				vl = for vi in vl
					@value_parse(v.v, vi, opt)
			else if vl isnt ''
				vl = @value_parse(v.v, vl, opt)
			r[v.k] = vl
			if vl is '' or vl is null
				if v.v.model.multi
					r[v.k] = []
				else if ['decimal', 'int', 'text'].includes(v.v.model.type)
					r[v.k] = null
				else if ['date', 'datetime'].includes(v.v.model.type)
					r[v.k] = null
			vla = DSLFields.value_auto_name(v.node)
			r[v.k + '_auto_name'] = vla if vla isnt null and vla isnt vl
		fields =  DSL[@options.form].fields
		for k, v of fields
			continue if v.model.type isnt 'subform' 
			source_form = DSLRecordSubform.get_subform_source(@options.form, k, r)
			continue if not source_form
			[source_form, __sfs] = source_form
			continue if !window.DSL[source_form]
			continue if !subforms.formmap[source_form]
			continue if @options.form is source_form
			continue if !DSLFields.is_subform_field_visible(subforms.formmap, @options.form, k)
			if v.model.multi
				sr = subforms.formmap[source_form].values(opt)
				if opt?.flattend
					fval[source_form] = sr[source_form][k] or []
				else 
					r[k] = sr[k] or []
			else
				sr = subforms.formmap[source_form].values(opt)
				if opt?.flattend
					r[k] = [sr[source_form]]
					fval = _.merge(fval, sr) if opt?.flattend
				else
					r[k] = [sr]
		# handle optional required_if fields
		r = DSLFx.TransformRequiredIf(@options.form, r)

		# fixed values (filled via transforms)
		for k,v of @fields_fixed
			r[k] = v
		if not opt?.flattend
			id = @get_record_data('id')
			if !r.id and id
				r.id = id

		# make sure primary link (patient_id etc) is filled in for add/edit
		if @options.link and (@options.link isnt @options.form)
			oid = getLinkID(@options.link, @options.linkid, @$el)
			if oid
				olk = @options.link + '_id'
				if @can_set_link_id(olk)
					r[olk] = oid

		# fill in additional links (e.g. patient_id for encounter, where order_id is primary)
		if @options.links
			for link in @options.links
				continue if (link is @options.link) or (link is @options.form)
				lid = getLinkID(link, @options.linkid, @$el)
				continue if not lid
				llk = link + '_id'
				if @can_set_link_id(llk)
					r[llk] = lid

		# hard-coded values (filled based on logic)
		r.prefilled = 'Y' if DSL[@options.form].fields.prefilled? and (@find?() isnt true)
		if opt?.flattend
			fval[@options.form] = r
			return fval
		r
