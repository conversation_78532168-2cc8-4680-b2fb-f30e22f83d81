class FieldFile extends DSLFields

	###
		sample json value:
			filehash: "ebd1c3cd5be44eef8450a24ad656c88b"
			filename: "215.json"
			filesize: "12051934"
			mimetype: "application/json"
	###

	@clear: (d) ->
		d.data('value', '')
		return

	@draw: (form, view, id, k, v, df, mode) ->

		if App.company.no_download is 'Yes'
			classes = $.trim('fileupload disable_links ' + v.view.class)
		else
			classes = $.trim('fileupload ' + v.view.class)
		
		if Object.keys(df)?.length or df?.length
			classes += ' has-file'
		attr =
			id: id
			class: classes
			type: 'file'
		attr.readonly = 'readonly' if v.view.readonly

		if df and getType(df) is 'string'
			try
				df = JSON.parse(df)
			catch e
				df = ''
		
		if ['document_management'].includes(form)
			df.token_access = true

		if attr.readonly and $('#flyout').find('.form').length == 0 #<PERSON><PERSON><PERSON> need testing
			html = '<div ' + @attr(attr) + '><span>' + @draw_label(df) + '</span><i class="preview glyphicon glyphicon-zoom-in">&nbsp;</i></div>'
		else 
			html = '<div ' + @attr(attr) + '><span>' + @draw_label(df) + '</span></div>'

		d =
			html: html
			data:
				fieldtype: 'file'
				value: df

	@draw_frame: (id) ->
		templateRaw('field-file', id: id)

	@draw_frame_multiple: (id) ->
		templateRaw('field-file-multiple', id: id)

	@draw_file: (d, n) ->
		return if d.attr('readonly')
		d.find('iframe').remove()
		d.prepend(templateRaw('field-file-write', id: d.attr('id')))
		if n?
			@value d, JSON.parse(JSON.stringify(n)) # only want the data, not DOM artefacts
			d.data('dd').options.wrapper.handle_field_event(d)
		return {d}

	@draw_file_multiple: (d, n) ->
		return @draw_frame_multiple(d)

	@draw_label: (v) ->
		fileJSONLabel(v, true, v.token_access)

	@load_all: (fa) ->
		setTimeout ->
			fa.find('.fileupload').each ->
				d = $(this)
				FieldFile.draw_file d
				FieldFile.draw_media_viewer(d, d.data('value'))
				return
		, 100
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: (d) ->
		if @visible(d)
			v = d.data('value')
			if v and v.filename? and v.filesize?
				fileJSONLabel(v)
			else
				null
		else
			null

	@value_get: (d) ->
		d.data('value')

	@value_set: (d, n) ->
		d.data('value', n)
		d.find('>span').html(@draw_label n)
		try		
			if Object.keys(n)?.length or n?.length
				d.closest('.fileupload').addClass('has-file').find('.file-upload-area-container').remove()
			else if not d.closest('.fileupload').find('.file-upload-area-container')?.length
				upload_drag_drop_context = """
				<div class="file-upload-area-container">
					<div class="file-icon-area">
						<div class="icon-container">
							<span class="upload-icon"><span>
						</span></span></div>
					</div>
					<div class="file-text-area">
						<div class="text-container">
							<span class="file-upload-text">
								<a href="#">Click to upload</a>
							</span>
							<p>PDF, PNG, JPG or GIF (max. 20MB)</p>
						</div>
					</div>
				</div>
				"""
				d.closest('.fileupload').removeClass('has-file').children().first().after(upload_drag_drop_context)
		catch e
			console.error("FieldFile: value_set: failed to set/unset file")
		
		@draw_media_viewer(d, n)
		n

	@draw_media_viewer: (d, n) ->
		return if not d.hasClass('media-preview')
		d.closest('.form-group').find('.field-field-media-viewer').remove()
		if n and not _.isEmpty(n)
			newRender = getMediaViewerHTML(n)
			d.closest('.form-group').append(newRender)

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		console.error('FieldFile.readonly not Supported')
		return false
