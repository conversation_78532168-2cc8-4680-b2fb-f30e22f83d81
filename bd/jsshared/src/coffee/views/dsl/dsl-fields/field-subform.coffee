class FieldSubform extends DSLFields

	@clear: (d) ->
		@mark_rows_delete(d, d.data('rowdata') || [])
		return

	@update_row_by_index: (d, did, dfr={}, trigger=true) ->
		e = d.parent().parent().find('.add-subrow#' + d.data('addId'))
		if e.length is 0
			console.error('FieldSubform.update_row_by_index:','Invalid subform d node.')
			return
		if (d.data('rowdata') || []).length <= did
			console.error('FieldSubform.update_row_by_index:','Invalid subform row index.')
			return
		dfr = @prefill_dfr(d, dfr)
		rows = d.data('rowdata')
		dw = d.data('dd').options.wrapper.options
		form = d.data('form')
		row = d.find("> tbody > [rowid='" + did + "']")
		if(row.length is 0)
			console.error('FieldSubform.update_row_by_index:','Invalid subform row index.')
			return
		row.show()
		if rows[did].id
			dfr.id = rows[did].id
			dfr._meta =
				client:
					updated_by: App.user.id
					updated_by_auto_name: App.user.auto_name
					updated_on: dateTimeNow()
				server:
					null_missing_fields: true
				update: dfr.id
		else
			dfr._meta =
				prefill_row: dfr?._meta?.prefill_row or false
				validate: 'none'
		cardopts =
			form: form
			link: dw.link
			links: dw.links
			linkid: @make_linkid(d, dw)
		dfr.__index = did
		rows[did] = dfr
		d.data('rowdata', rows)
		rd = FieldSubform.draw_row(d.data('v'), d.data('subformsource'), did, dfr, cardopts)
		row.replaceWith(rd.html)
		event_info = 
			user_event: false
			event: 'update'
			row_index: did
			field: null
		FieldSubform.render_form(d, rd.cardopts, dw, event_info, trigger)
		return
	
	@prefill_dfr: (d, dfr={}) ->
		if dfr == null
			dfr = {}
		return dfr if !d 
		dd = d.data('dd')
		return dfr if !dd
		ifprefill = generatePrefillValues(dd) or {}
		dfr = _.merge(dfr, ifprefill)
		return dfr

	@trigger_if_condition: (d) ->
		dd = d.data('dd')
		return if !dd
		dd.trigger_if_prefill()

	@add_index_to_dfr: (d, index) ->
		if not d
			console.error('FieldSubform.add_index_to_dfr:','Invalid subform d node.')
			return null
		if not d.data?('rowdata')?[index]
			console.error('FieldSubform.add_index_to_dfr:','Invalid subform row index.')
			return null
		d.data?('rowdata')?[index]?.__index = index

	@add_row: (e, dfr = {}, opts={}, trigger = true) ->
		t = null
		user_event = true
		if e?.target
			t = $(e.target)
		else
			user_event =  false
			t = $(e)

		d = t.closest('table')
		dfr = @prefill_dfr(d, dfr)
		dw = d.data('dd').options.wrapper.options
		form = d.data('form')
		v = d.data('v')

		max_count = v.view.max_count
		if max_count
			if getSubformActiveRowCount(d) >= max_count
				prettyError 'Error', "Maximum row limit of #{max_count} reached."
				return

		cardopts =
			form: form
			link: dw.link
			links: dw.links
			linkid: @make_linkid(d, dw)
			subform_parent: dw
		meta =
			client:
				created_by: App.user.id
				created_by_auto_name: App.user.auto_name
				created_on: dateTimeNow()
			server:
				null_missing_fields: true
		meta = _.defaults(meta, dfr._meta or {}) if dfr?
		if dfr?
			link_preset = {}
			for kf,vid of dw.linkid
				link_preset[kf+"_id"] = vid
			dfr = _.merge(link_preset, dfr)
			dfr._meta = meta

		event_info = 
			user_event: user_event
			event: 'add'
			row_index: null
			field: null

		# TODO: merge these two if conditions to avoid code duplication currently
		if !opts?.force_flyout
			if dfr? and not opts?.force_default_add
				did = (d.data('rowdata').push(dfr or {})) - 1
				@add_index_to_dfr(d, did)
				rd = FieldSubform.draw_row(v, d.data('subformsource'), did, dfr, cardopts)
				d.find('tbody').append(rd.html)
				event_info.row_index = did
				FieldSubform.render_form(d, rd.cardopts, dw, event_info, trigger)
				return;
			if v.view.grid?.edit and v.view.grid?.add == 'inline'
				did = (d.data('rowdata').push(dfr or {})) - 1
				@add_index_to_dfr(d, did)
				rd = FieldSubform.draw_row(v, d.data('subformsource'), did, (dfr or {}), cardopts)
				d.find('tbody').append(rd.html)
				event_info.row_index = did
				FieldSubform.render_form(d, rd.cardopts, dw, event_info, trigger)
				return
		dfd = Flyout.open
			action: 'FlyoutCardSubformView'
			form: form
			link: dw.link
			links: dw.links
			linkid: @make_linkid(d, dw)
			preset: dfr
			subform_parent: dw
			grid:
				table: d
		dfd.done (data) =>
			[d, dfr] = @save_values(data)
			dfr = @prefill_dfr(d, dfr)
			dfr._meta = meta
			did = (d.data('rowdata').push dfr) - 1
			@add_index_to_dfr(d, did)
			rd = FieldSubform.draw_row(d.data('v'), d.data('subformsource'), did, dfr, cardopts, d)
			d.find('tbody').append(rd.html)
			event_info.row_index = did
			FieldSubform.render_form(d, rd.cardopts, dw, event_info, trigger)
		return

	@load_all: (fa) ->
		fa.find('.controls-grid.subform').find('table').each ->
			t = $(this)
			v = t.data('v')
			dw = t.data('dd').options.wrapper.options
			pfn = dw.wrapper.field_nodes()
			return if not v.view.grid?.edit
			return if v.view.readonly
			cardrows = t.data('cardrows')
			for options in cardrows
				FieldSubform.render_form(t, options, dw, null, false)
		return

	@render_form: (t, options, dw=null, event_info=null, trigger=true) ->
		tr = $(t.find("> tbody > [editId=#{options.viewid}]"))
		if _.isEmpty(options)
			@save_parent(dw.parent, t, event_info)
			@trigger_if_condition(t)
			return
		if dw
			options.subform_parent = dw
		form_ready = false
		that = @
		on_field_change = (k) ->
			# dont run events as long form is not rendered
			return if not form_ready
			event_info = 
				user_event: (options?.grid?.fields or []).includes(k)
				event: 'field_change'
				row_index: options?.grid?.rowid
				field: k
			t.data('event_info', event_info)
			try
				DSLFx.ValidateTransformSubForm(dw.parent, t.data('dd'))
				that.hopon_form_buss(t, event_info)
			catch e
				console.error(e)
			t.data('event_info', null)

		view = new CRCardView(
				form: options.form
				el: tr
				parent: {}
				viewid: options.viewid,
				link: options.link
				links: options.links
				linkid: options.linkid
				record: options.record
				preset: options.preset
				card: options.card,
				inline: true,
				onFieldChange: on_field_change,
				grid: options.grid or {}
				subform_parent: options.subform_parent
			)
		idx = view.subscribe "ready", (ref) ->
			form_ready = true
			that.trigger_if_condition(t)
			view.unsubscribe("ready", idx)
			view?.cardform?.ddf?.save_form_values?() # 
			that.save_parent dw.parent, t, event_info, trigger
			return

		save = () -> null
		# call the validators when the form is renders
		view.cardform.save = save
		view.cardform.tab_do_save = save
		view.save = save
		tr.data('cardview', view)
		return 

	@delete_row: (e) ->
		log('Button clicked: delete subform row?')
		t = $(e.target)
		d = t.closest('table')
		dw = d.data('dd').options.wrapper.options

		r = t.closest('tr')
		rw = d.data('rowdata')[r.attr('rowid')]
		event_info = 
			user_event: true
			event: 'delete'
			row_index: r.attr('rowid')
			field: null
		if (not rw?) or (not rw)
			log('ERROR! User tried to delete a row that is not in memory: ' + r.attr('rowid'))
			log(rw)
			prettyError 'Cannot delete row!', 'Sorry! There was an error trying to delete the row.<br/><br/>' + ERR_CONTACT
			return

		prettyYesNo 'Delete row?', 'Do you want to delete this row?', =>
			if rw.id?
				rw._meta =
					delete: rw.id
					validate: 'none'
			else
				rw._meta =
					softdelete: true
					validate: 'none'
			r.hide()
			@save_parent dw.parent, d, event_info
			@trigger_if_condition(d)
			return
		return

	@delete_row_by_index: (d, index, trigger=false) ->
		rows = d.data('rowdata')
		r = d.find("> tbody > [rowid='" + index + "']")
		if !r
			console.error('FieldSubform.delete_row_by_index:','Invalid subform row index.')
			return
		row = rows?[index]
		if !row
			console.error('FieldSubform.delete_row_by_index:','Invalid subform row index.')
			return
		if row.id?
			row.__index = index
			row._meta =
				delete: row.id
				validate: 'none'
		else
			row.__index = index
			row._meta =
				softdelete: true
				validate: 'none'
		$(r).hide()
		event_info = 
			user_event: false
			event: 'delete'
			row_index: index
			field: null
		dw = d.data('dd').options.wrapper.options
		@trigger_if_condition(d)
		@save_parent dw.parent, d, event_info, trigger
		return
	
	@undelete_row_by_index: (d, index, trigger=false) ->
		rows = d.data('rowdata')
		dfr = rows?[index]
		if !dfr
			console.error('FieldSubform.undelete_row_by_index:','Invalid subform row index.')
			return
		@update_row_by_index(d, index, dfr, trigger)
		return

	# @deprecated
	@mark_rows_delete: (d, trs) ->
		toRemove = []
		for row in trs
			rowId = row.__index
			if rowId == undefined || rowId == null
				console.error('mark_rows_delete: Invalid subform row index.')
				continue
			@delete_row_by_index(d, rowId, false)
		return

	@can_split_grid = (splitif, preset) =>
		return true if !splitif
		allow_split	= true
		for k,v of splitif
			if v is '*'
				if !preset[k]
					allow_split = false
					break
			else if v is '!'
				if preset[k]
					allow_split = false
					break
			else if v.startsWith('!') 
				if preset[k] is v.substring(1)
					allow_split = false
					break
			else if `preset[k] != v`
				allow_split = false
				break
		allow_split
	
	@can_delete_row = (deleteif, preset) =>
		return @can_split_grid(deleteif, preset)
	
	@can_add_row = (addif, preset) =>
		return @can_split_grid(addif, preset)

	@split_row: (e) ->
		log('Button clicked: split subform row?')
		t = $(e.target)
		d = t.closest('table')
		@value_get(d)
		dw = d.data('dd').options.wrapper.options
		r = t.closest('tr')
		rw = d.data('rowdata')[r.attr('rowid')]
		if (not rw?) or (not rw)
			log('ERROR! User tried to delete a row that is not in memory: ' + r.attr('rowid'))
			log(rw)
			prettyError 'Cannot copy row!', 'Sorry! There was an error trying to copy the row.<br/><br/>' + ERR_CONTACT
			return
		rw = _.cloneDeep(rw)
		cf = d.data('v').view.grid?.copy or []
		dfr = {}
		if cf.length > 0
			for sk in cf
				if rw[sk]?
					dfr[sk] = rw[sk]
				an = sk + '_auto_name'
				if rw[an]?
					dfr[an] = rw[an]
		else
			df = ['_meta','created_on','updated_on','created_by','created_by_auto_name','updated_by','updated_by_auto_name', "id", "external_id"]
			for d in df
				if rw[d]?
					delete rw[d]
			dfr = rw
		@add_row(e, dfr, {force_default_add: true})

	@get_grid_tootip_fields: (v) ->
		gridtooltip = []
		if v.view.grid?.tooltip?.length > 0
			gridtooltip = v.view.grid.tooltip
		return gridtooltip
	
	@can_delete: (v) ->
		return v.view.grid.delete if v.view.grid?.delete?
		true

	@draw: (form, view, id, k, v, df, mode) ->
		if not DSL[form]?
			return @draw_error 'Invalid form source: ' + form

		gridfields = get_grid_fields(DSL[form], v)
		gridlabels = get_grid_labels(DSL[form], v)
		fieldwidths = get_grid_width(DSL[form], v)

		data =
			fieldtype: 'subform'
			subformsource: DSL[form]
		attr =
			id: id
			class: 'table table-hover table-striped' + if v.view.grid?.edit then ' subform-editable-table' else ''
			tabindex: if (v.view.readonly or v.view.offscreen) then -1 else 0

		sf = data.subformsource
		if gridfields.length is 0
			return @draw_error 'Invalid form view.grid: ' + form
		n = []
		if v.view.grid?.edit
			n.push('<div class="sf-table-container">')
		n.push('<table ' + @attr(attr) + '>')
		
		th = []
		if not v.view.readonly
			if v.view.grid?.edit
				th.push '<th class="icon-placeholder" style="width: 30px;">&nbsp;</th>'
			if v.view.grid?.edit and v.view.grid?.split
				th.push '<th class="icon-placeholder" style="width: 30px;">&nbsp;</th>'
		if @get_grid_tootip_fields(v).length
			th.push '<th class="icon-placeholder" style="width: 30px;">&nbsp;</th>'
		for sk, i in gridfields
			sfv = sf.fields[sk]
			lbl = sfv?.view.label
			if gridlabels.length > i and gridlabels[i]
				lbl = gridlabels[i]
			if lbl
				w = 0
				if fieldwidths.length > i
					w = fieldwidths[i]
				w = if w then ' style="width: ' + w + '%;" ' else ''
				classes = ['subformfield-' + sk]

				classes.push('required-th') if sfv?.model.required && v.view.grid.edit && !v.view.readonly
				classes.push('check-field') if sfv?.view.class?.includes("check-field")
				classes.push('status') if sfv?.view.class?.includes("status")
				classes.push('discount') if sfv?.view.class?.includes("discount")
				classes.push('important') if sfv?.view.class?.includes("important")
				classes.push('money') if sfv?.view.class?.includes("money")
				classes.push('claim-field') if sfv?.view.class?.includes("claim-field")
				classes.push('fdb-field') if sfv?.view.class?.includes("fdb-field")
				classes.push('cms-1500-field') if sfv?.view.class?.includes("cms-1500-field")
				classes.push('numeral-readonly') if sfv?.view.format and sfv?.view.class?.includes('numeral')
				
				th.push '<th ' + w + 'class="' + classes.join(' ') + '">' + lbl + '</th>'
			else
				log('Missing grid column: ' + sk + ' in form: ' + form)
		if v.view.readonly
			tddl=''
			if v.model.source is 'patient_attachment'
				tddl = '<th class="preview-subrow icon-placeholder" style="width: 30px;">&nbsp;</th>'
		else if v.view.grid?.edit 
			if @can_delete(v)
				tddl = '<th class="icon-placeholder" style="width: 30px;">&nbsp;</th>'
			else
				tddl = ''
		else
			if @can_delete(v)
				tddl = '<th class="icon-placeholder" style="width: 30px;">&nbsp;</th>'
			else
				tddl = ''

		n.push('<thead>' + th.join('') + tddl + '</thead>')

		n.push('<tbody>')
		if df and df.length > 0
			data.rowdata = _.without(df, null, undefined, false)
		else
			data.rowdata = []
		cardrows = []
		if data.rowdata.length > 0
			for did, dfr of data.rowdata
				dfr.__index = did
				cardopts = form: form, preset: dfr, subform_parent: {}
				cr = @draw_row(v, sf, did, dfr, cardopts)
				n.push(cr.html)
				cardrows.push(cr.cardopts)
				# set the _meta key used for save
				if not data.rowdata[did]._meta?
					data.rowdata[did]._meta =
						validate: 'none'
				if not data.rowdata[did]._meta.validate?
					data.rowdata[did]._meta.validate = 'none'
		else
			if v.view.readonly
				n.push('<tr><td id="no-data-row" colspan="' + th.length + '">No data entered.</td></tr>')
		n.push('</tbody>')
		addId = next_id('sf_btn_')
		add_btn = '<div class="dsl-action-btn btn-add {class}" id="' + addId + '" tabindex="0"><div class="inner-cont"><span class="icon" /><p class="label">Add</p></div></div>'
		if not v.view.readonly
			rpw = 'add-subrow'
			if v.view.grid?.add == 'none'
				rpw = rpw + ' hide'
			n.push('<tfoot><tr><th colspan="' + (th.length +  2) + '">' + add_btn.replace('{class}', rpw) + '</th></tr></tfoot>')
		n.push('</table>')
		if v.view.grid?.edit
			n.push('</div>')
		if not v.view.readonly and v.view.grid?.add != 'none' and v.view.grid?.edit
			if v.view.grid?.add == 'inline'
				n.push('<div class="suform-editable-add">' + add_btn.replace('{class}', 'add-subrow-dum').replace('Add', 'Quick Add') + add_btn.replace('{class}', 'add-subrow-flyout').replace('Add', 'Add Full Form') + '</div>')
			else
				n.push('<div class="suform-editable-add">' + add_btn.replace('{class}', 'add-subrow-dum') + '</div>')
		data.cardrows = cardrows
		data.addId = addId
		html: n.join(''), data: data

	@draw_error: (err, data) ->
		html: '<h4 class="">' + err + '</h4>', data: {fieldtype: 'subform'}

	@draw_row_edit: (v, sf, did = 0, dfr = null, opts) ->
		form = opts?.form
		if not form
			return @draw_error 'Invalid form prop found contact admin.'
		view = "sf_"+form+"_row_"+did
		form_render_data =
			form: form
			link: opts.link
			links: opts.links
			linkid: opts.linkid
			record: dfr.id or undefined
			preset: dfr or {}
			card: if dfr.id then 'add' else 'addfill'
			viewid: view
			inline: true
			parent: {}
			grid:
				add: v.view.grid.add
				row: v.view.grid.row
				rowid: did
				edit: v.view.grid.edit or true
				split: v.view.grid.split
				splitif: v.view.grid.splitif
				copy: v.view.grid.copy or []
				tooltip: @get_grid_tootip_fields(v)
				fields: get_grid_fields(sf, v)
				width: v.view.grid.width or []
				delete: @can_delete(v)
				deleteif: v.view.grid.deleteif
		fields = '<tr rowid="' + did + '" editId="' + view + '"></tr>'
		return html: fields, cardopts: form_render_data

	@draw_row: (v, sf, did = 0, dfr = null, opts={}) ->
		if (not v.view.readonly) and v.view.grid?.edit
			return @draw_row_edit(v, sf, did, dfr, opts)
		tr = []
		tooltip_fields = @get_grid_tootip_fields(v)
		if tooltip_fields?.length
			ttt = get_text_from_formdata(dfr, tooltip_fields)
			tr.push('<td><i id=eg-tooltip class="fa fa-list eg-tooltip" style="top: unset;"><span id="eg-tooltiptext" class="eg-tooltiptext">'+ttt+'</span></i></td>')
		for sk in get_grid_fields(sf, v)
			continue if not sf.fields[sk]?.view.label
			if dfr?[sk + '_auto_name']?
				vl = dfr[sk + '_auto_name']
			else if dfr?[sk]?
				vl = dfr[sk]
			else
				vl = ''
			tr.push @draw_subfield v, sk, sf.fields[sk], vl
		if v.view.readonly and v.model.source is 'patient_attachment'
			if dfr.patient_file?.filename
				fe = dfr.patient_file.filename.split(".")
				fe = fe[fe.length-1]
				if fe in ['gif', 'jpeg', 'jpg', 'png', 'pdf']
					tr.push '<td class="preview-subrow"><i class="glyphicon glyphicon-zoom-in">&nbsp;</i></td>'
				else
					tr.push '<td class="preview-subrow"><i>&nbsp;</i></td>'
		else if !v.view.readonly and @can_delete(v)
			clss = ''
			if not @can_delete_row(v.view.grid.deleteif, dfr)
				clss += ' offscreen'
			tr.push '<td id=grid-delete class="delete-subrow' + clss + '"><i></i></td>'
		fields = '<tr rowid="' + did + '">' + tr.join('') + '</tr><tr class="row-preview hide"><td colspan="' + tr.length + '">&nbsp;</td></tr>'
		return html: fields, cardopts: {}

	@draw_subfield = (v, sk, sv, vl = '', fld_dom = null) ->
		if v.view.readonly
			cls = 'read-subrow '
		else
			if v.view.grid?.edit
				cls = 'gridedit-subrow '
			else
				cls = 'edit-subrow '
		cls += 'subformfield-' + sk
		if fld_dom

			return '<td class="' + cls + '"><div class="controls">' + fld_dom.html + '</div></td>'

		vlen = v.view.grid?.text_trim or 32
		if vl is ''
			inp = '-'
		else if sv.model.type is 'date'
			inp = (new Date(vl)).format('mm/dd/yyyy')
		else if sv.model.type is 'time'
			inp = (new Date('1 Jan 2013 ' + vl.substr(0, 8))).format('hh:MM tt')
		else if sv.model.type is 'datetime'
			if sv.model.autoinsert
				inp = dateTimeFromUTC(vl)
			else
				inp = dateTimeFromTZ(vl)
		else if sv.model.type is 'decimal'
			if sv.view.format and sv.view.class?.includes('numeral')
				cls += ' numeral-readonly ' if v.view.readonly
				inp = numeral(vl).format(sv.view.format)
			else
				inp = roundTo(vl, sv.model.rounding or 0.0001)
		else if sv.model.type is 'int' and sv.model.source is null
			if sv.view.format and sv.view.class?.includes('numeral')
				cls += ' numeral-readonly ' if v.view.readonly
				inp = numeral(vl).format(sv.view.format)
			else
				inp = roundTo(vl, 1)
		else if sv.model.type is 'json' and sv.view.control is 'esign'
			inp = esignJSONLabel(vl)
		else if sv.model.type is 'json' and sv.view.control is 'file'
			inp = fileJSONLabel(vl, true)
		else if sv.model.type is 'json' and sv.view.control is 'link'
			inp = linkJSONLabel(vl, true)
		else if getType(sv.model.source) is 'object' and sv.model.source[vl]? #HB-3721
			inp = $.trim(sv.model.source[vl]).substr(0, vlen)
		else if getType(sv.model.source) is 'array' and getType(vl) is 'string' and
				vl.substr(0, 1) is '[' and vl.substr(-1) is ']' and
				sv.view.control in ['checkbox', 'radio'] #HB-3750
			try
				nv = JSON.parse(vl)
				vl = nv.join(', ') if nv
			catch e
			inp = $.trim(nv).substr(0, vlen)
		else
			inp = $.trim(vl).substr(0, vlen)

		'<td class="' + cls + '">' + inp + '</td>'

	@edit_row: (e) ->
		t = $(e.target)
		d = t.closest('table')
		dw = d.data('dd').options.wrapper.options
		row = t.closest('tr')
		rowid = row.attr('rowid')
		form = d.data('form')
		cv = row.data('cardview')
		rows = d.data('rowdata')
		if cv
			rows[rowid] = _.merge(rows[rowid], cv.cardform.ddf.get_formdata())
			d.data('rowdata', rows)

		subform_error = @get_subform_validation_error(d, rowid)

		event_info = 
			user_event: true
			event: 'update'
			row_index: rowid
			field: null

		dfd = Flyout.open
			action: 'FlyoutCardSubformView'
			form: form
			link: dw.link
			links: dw.links
			linkid: @make_linkid(d, dw)
			subform_parent: dw
			validation_errors: subform_error
			card: 'add'
			grid:
				table: d
				row: row
				rowid: rowid
				editable: d.data('v')?.view?.grid?.edit
			preset: rows[rowid]

		dfd.done (data) =>
			[d, dfr] = @save_values(data)
			did = data.grid.rowid
			dfr = @prefill_dfr(d,dfr)
			if rows[did].id?
				dfr.id = rows[did].id
				dfr._meta =
					client:
						updated_by: App.user.id
						updated_by_auto_name: App.user.auto_name
						updated_on: dateTimeNow()
					server:
						null_missing_fields: true
					update: dfr.id
			cardopts =
				form: form
				link: dw.link
				links: dw.links
				linkid: @make_linkid(d, dw)
			rows[did] = dfr
			dfr.__index = did
			d.data('rowdata', rows)
			@add_index_to_dfr(d, did)
			rd = FieldSubform.draw_row(d.data('v'), d.data('subformsource'), did, dfr, cardopts)
			row.replaceWith(rd.html)
			FieldSubform.render_form(d, rd.cardopts, dw, event_info, true)
		return

	@make_linkid: (el, options) ->
		lks = {}
		if options.links?
			for link in options.links
				lid = getLinkID(link, options.linkid, el)
				lks[link] = lid if lid
		lks

	@preview_row: (e) ->
		t = $(e.target)
		t.closest('table').find('.row-preview').addClass('hide').find('td').html('&nbsp;')
		r = t.closest('tr')
		a = r.find('a[target=_blank]')
		src = a.attr('href').trim()
		fn = a.html().trim()
		ext = /(?:\.([^.]+))?$/.exec(fn)[1].toLowerCase()
		p = r.next().removeClass('hide').find('td')
		if ext is 'pdf'
			p.html('<iframe src="public/viewer/web/viewer.html?file=' + src + '" width="100%" height="400px"></iframe>')
		else if ext in ['gif', 'jpeg', 'jpg', 'png']
			p.html('<img src="' + src + '" />')
		else
			p.html('Sorry! Cannot preview file: ' + fn)
		return

	@read_row: (e) ->
		t = $(e.target)
		d = t.closest('table')
		rowid = t.closest('tr').attr('rowid')
		dw = d.data('dd')?.options?.wrapper?.options
		if not d.data('rowdata')[rowid]?.id?
			if d.data('v')?.view?.grid?.allow_read_wo_id
				Flyout.open
					form: d.data('form')
					subform_parent: dw
					card: 'add'
					preset: d.data('rowdata')[rowid]
					forceReadOnly: true
					tab_can_print_override: -> false
					tab_can_edit_override: -> false
					tab_can_list_override: -> false
					tab_can_archive_override: -> false
			else
				prettyError false, 'Sorry! Could not load the details for this form.<br/><br/>' + ERR_CONTACT
			return
		Flyout.open
			action: 'FlyoutCardSubformView'
			form: d.data('form')
			subform_parent: dw
			record: d.data('rowdata')[rowid].id
			card: 'read'
		return

	@save_parent: (p, d, event_info, trigger=true) ->
		s = d.data('dd')
		return if not p.ddf # still rendering
		p.ddf.mark_edited()
		$.notify()
		d.data('event_info', event_info)
		
		try
			if trigger
				DSLFx.ValidateTransformSubForm(p, s)
			@hopon_form_buss(d, event_info)
			if event_info and (event_info.event == 'update' or event_info.event == 'delete')
				@remove_subform_validation_error(d, event_info.row_index)
			s?.options?.parent?.ddf?.check_missing_required_fields?()
		catch e
			console.error(e)
		d.data('event_info', null)

	@save_values: (data) ->
		d = data.grid.table
		dfr = data.values
		[d, dfr]

	@validate: (d) ->
		false

	@value: (d, n) ->
		@value_get d

	@value_auto_name: ->
		null

	@get_subform_validation_error: (d, index) ->
		data = d.data()
		dw = data.dd?.options?.wrapper
		form = data.form
		if !dw?.validation_errors?[form]
			return {}
		if dw.validation_errors[form] and dw.validation_errors[form]['__index_' + index]
			return dw.validation_errors[form]['__index_' + index]
		return {}
	
	@remove_subform_validation_error: (d, index) ->
		dw = d.data('dd')?.options?.wrapper
		form = d.data('form')
		if !dw?.validation_errors?[form]
			return
		if dw.validation_errors[form] and dw.validation_errors[form]['__index_' + index]
			delete dw.validation_errors[form]['__index_' + index]
			try
				dw?.count_missing_required_fields?()
			catch e
				console.error(e)
		return

	@is_missing: (d) ->
		v = d.data().v
		if !v
			return false
		missing = false
		return missing if !v.view.grid?.edit
		return missing if v.view.readonly
		d.find('> tbody > [editId]').each ->
			tr = $(this)
			return if !tr.is(':visible')
			cf = tr.data()?.cardview?.cardform
			if !cf or !cf?.ddf?.form_rendered
				return 'System Error: Calculation is in progress. (' + cf?.formname + ')'
			if !cf.ddf.save_form_values(false)
				missing = "Missing fields in #{cf.formname}. Please fill in all required fields."
		return missing

	@value_get_interal: (d) ->
		v = d.data().v
		if v.view.grid?.edit && not v.view.readonly
			rows = d.data('rowdata')
			d.find('> tbody > [editId]').each ->
				tr = $(this)
				return if !tr.is(':visible')
				rowid = tr.attr('rowid')
				cv = tr.data().cardview
				nv = {}
				if(cv?.cardform?.ddf?.form_rendered)
					cv?.cardform?.verify?()
					nv = cv?.cardform?.ddf?.get_formdata?() || {}
				rows[rowid] = _.merge(rows[rowid], nv)
			d.data('rowdata', rows)
		return _.map(d.data('rowdata'), (d) ->
			if d == null or d == undefined or d == false or _.isEmpty(d)
				return null
			tmp = _.cloneDeep(d)
			tmp = Object.values(tmp).filter (o) ->
				!(o == null or o == undefined or o == false or _.isEmpty(o))
			if tmp.length == 0
				return null
			d
		).filter Boolean

	@value_get: (d) ->
		vl = @value_get_dirty(d)
		vl = _.map(vl, (r) ->
			if r._meta?.softdelete
				return null
			r
		).filter Boolean
		return vl

	@get_active_values: (arr=[]) ->
		vl = _.map(arr, (r) ->
			if r._meta?.softdelete
				return null
			if r._meta?.delete
				return null
			r
		).filter Boolean
		return []
	
	@value_get_dirty: (d) ->
		v = FieldSubform.value_get_interal(d)
		index = null
		data = null
		if data
			if index
				v[index] = data
			else
				v.push(data)
		return v

	@value_set: (d, n) ->
		# do not need to set this dynamically
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		console.error('FieldSubform.readonly not Supported')
		return false
