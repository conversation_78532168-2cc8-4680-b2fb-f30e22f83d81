class FieldArea extends DSLFields

	@lang:
		cs: 'coffee'
		js: 'javascript'

	@clear: (d) ->
		if d.data('editor') # clear the text area in case of data('editor') is null/undefined
			d.data('editor').setValue('')
		else
			d.val('')
		return

	@draw: (form, view, id, k, v, df = '', mode) ->
		if v.view.readonly
			tag = 'textarea'
			cls = ''
		else
			tag = if v.view.class.indexOf('ace_') > -1 then 'div' else 'textarea'
			cls = ' ' + v.view.class

		if v.model.type is 'json' and getType(df) is 'object'
			df = JSON.stringify(df)

		d =
			html:
				'<' + tag + ' autocomplete="off" id="' + id + '" class="' +
					$.trim('' + cls) + '"' +
					(if v.model.max isnt null then ' maxlength="' + v.model.max + '"' else '') +
					(if v.view.readonly then ' readonly="readonly" tabindex="-1" ' else 'tabindex="0" ') + '>' +
					df +
				'</' + tag + '>'
			data:
				fieldtype: 'area'

	@editor = (t, mode) ->
		ace.config.set("basePath", "/public/js/edit")
		ed = ace.edit(t[0])
		t.data('editor', ed)
		ed.setTheme('ace/theme/chrome')
		ed.setShowPrintMargin(false)
		ed.setShowInvisibles(false)
		e = ed.getSession()
		e.setMode('ace/mode/' + mode)
		e.setUseSoftTabs(false)
		e.setTabSize(4)
		e.setUseWrapMode(true)
		e.setWrapLimitRange(null, null)
		e.setFoldStyle('markbegin')
		return

	@load_all: (fa) ->
		ed = @editor
		for k,v of @lang
			# .controls ensures this does not run for find bar etc.
			fa.find('.controls .ace_' + k).each ->
				ed($(this), v)
				return
		return

	@unload_all: (fa) ->
		# .ace_editor is only available if ace is active
		fa.find('.ace_editor').data('editor')?.destroy()
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		if d.hasClass('ace_editor')
			d.data('editor').getValue()
		else
			d.val()

	@value_set: (d, n) ->
		if d.hasClass('ace_editor')
			d.data('editor').setValue(n, -1).change()
		else
			d.val(n).change()
		n

	@visible: (d) ->
		@is_visible(d)

	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d)
		if ro
			d.attr('readonly', 'readonly')
		else
			d.removeAttr('readonly')
		return true
