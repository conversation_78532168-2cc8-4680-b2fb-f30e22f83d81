class FieldCheckbox extends DSLFields

	@clear: (d) ->
		d.find('input:checked').removeAttr('checked')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		add_class = v.view.class
		if getType(v.model.source) is 'string'
			bo = @load_options(form, view, id, k, v, df)
			vs = {null:'Loading...'}
			add_class = 'checkbox-group'
		else if v.model.type is 'boolean' and getType(v.model.source) is 'null'
			bo = null
			vs = {'true': 'Yes'}
			add_class = 'checkbox-only'
		else
			bo = null
			vs = v.model.source
			add_class = if v.view.class?.includes('checkbox-only') then v.view.class else 'checkbox-group'
		
		attr =
			id: id
			class: $.trim('checkboxes ' + $.trim(add_class))
			multi: v.model.multi

		d =
			html: '<div ' + @attr(attr) + '>' + @items(form, view, id, k, v, df, vs).join('') + '</div>',
			data:
				boxopt: bo
				fieldtype: 'checkbox'
				multi: v.model.multi

	@items: (form, view, id, k, v, df, vs) ->
		chk = []
		ty = getType(vs)
		dft = getType(df)

		if dft is 'string' and df isnt ''
			try
				df = JSON.parse(df)
				df = [df] if getType(df) isnt 'array'
			catch e
				# handle non-multi string, e.g.: {other}
				nm = df.match(/^{(.*)}$/)
				if nm and nm.length > 1
					df = nm[1].split(',')
				else
					df = [df]
		else if dft isnt 'array'
			df = [df]
		df = _.map(df, (dfv) => $.trim(dfv))

		cnt = 0
		if vs
			if form is 'user' and k is 'group_role' and ty is 'object'
				#HB-3237 - sort for ease-of-data-entry
				src = _.chain(vs).map((v,k) => key:k, val:v).sortBy('val').indexBy('key').mapValues('val').value()
			else
				src = vs
		else
			src = {}

		for kk,vv of src
			kk = vv if ty is 'array'
			# remove sort_ added previously to preserve sorting order for numeric keys
			kk = kk.substr(5) if kk.substr(0, 5) is 'sort_'
			attr =
				id: id + '_opt_' + (++cnt)
				type: 'checkbox'
				value: escapeHTML(kk)
				title: escapeHTML(kk)
			attr.disabled = 'disabled' if v.view.readonly
			attr.checked = 'checked' if ('*' in df) or (kk in df)
			attr_lbl = !v.view.class?.includes("checkbox-only") and {title: escapeHTML(kk)} or {}
			label_tabindex = if (v.view.readonly or v.view.offscreen ) then -1 else 0
			chk.push('<label ' + DSLFields.attr(attr_lbl) + ' tabindex="' + label_tabindex + '" class="checkbox">' +
					'<input tabindex="-1" autocomplete="off" ' + DSLFields.attr(attr) + ' />' +
					'<span>' + vv + '</span>' +
					(
						if v.view.class?.includes("checkbox-only")
							'<div class="slider-checkbox-container' + (if attr.checked?.length then " slider-checkbox-container-toggled" else "") +
  							(if v.view.readonly then ' checkbox-only-disabled' else '') +
							'">' + 
								'<div class="slider-checkbox' + (if attr.checked?.length then " slider-checkbox-toggled" else "") + '"></div>' +
							'</div>'
						else
							''
					) +
				'</label>')
		chk

	@load_all: (fa) ->
		fa.find('.checkboxes').each ->
			t = $(this)
			bo = t.data('boxopt')
			bo.init(t) if bo?.init?
			return
		return

	@load_options: (form, view, id, k, v, df) ->
		filter_url = @filter_url
		items = @items
		opt =
			init: (e) ->
				g = Ajax.async
					url: filter_url(form, k, v)
				g.done (data, status, xhr) ->
					vs = null
					for o in data
						if getType(o) is 'string' # api/unique returns plain arrays
							o = $.trim(o)
							continue if o is ''
							vs = [] if not vs
							vs[o] = o
						else
							continue if ($.trim(o.id) is '') or ($.trim(o.auto_name) is '')
							vs = {} if not vs
							# add sort_ to preserve sorting order for numeric keys
							vs['sort_' + o[v.model.sourceid]] = o.auto_name
					e.html items(form, view, id, k, v, df, vs)
					# during initial render this data is not avaible for if condition so need to manually trigger it once data  is avaible 
					e.find('input').first().change() 
					return
				g.fail (xhr, status, data) ->
					e.html = items(form, view, id, k, v, df, {null:'-'})
					return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get(d)
		else
			null

	@value_auto_name: ->
		null

	@value_from_string: (v, n) -> #HB-3617
		s = n.slice(1, -1).split(',')
		n = []
		for r in s
			r = $.trim(r)
			continue if r is ''
			if v.model.source[r]?
				n.push v.model.source[r]
			else
				n.push r
		n

	@value_get: (d) ->
		r = []
		d.find('input:checked').each ->
			r.push($(this).val())
		if d.data('multi')
			mc = d.data('v').view.max_count
			if mc and d.data('dd').options.mode isnt 'read'
				@chk_unchk(d, r.length >= mc)
				r
			else
				r
		else if r.length > 0
			r[0]
		else
			''
			
	@chk_unchk: (d, is_disabled) ->
		d.find('input[type="checkbox"]').each ->
			if !$(this).is(':checked')
				$(this).prop('disabled', is_disabled)
		v = d.data('v')
		fn = v.view.note
		fn_el = d.parent().parent().find('.help-block.note')
		mc_nt = "You can select a maximum of #{v.view.max_count} checkboxes"
		ff_nt = fn.length > 0 and fn + " and " + mc_nt or mc_nt
		is_disabled and fn_el.text(ff_nt).show() or fn and fn_el.text(fn).show() or fn_el.text('').hide()
		return ''

	@value_set: (d, n) ->
		return if d.find('span').html() == 'Loading...'
		v = d.data('v')
		mc = v.view.max_count
		if v.model.multi and mc and n
			if n.length > mc
				console.warn("FieldCheckbox: value_set: value exceeds max_count")
				return
		d.find('input:checked').removeAttr('checked').prop('checked', false)
		if _.isNumber(n) or (not _.isEmpty(n))
			sfields = d.find('input').filter(->
				if v.model.multi
					$(this).val() in n
				else
					$(this).val() is n
			)
			sfields.attr('checked', 'checked').prop('checked', true)
		d.change()
		dd = d.data('dd')
		draw_event = (dd?.options?.parent?.ddf or dd?.options?.parent?.ddr)
		if draw_event
			draw_event.handle_field_event?(d)
			if v.view.class?.includes("checkbox-only")
				draw_event.event_field_checkbox_only_change?(d.find('input'))
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		if ro
			d.find("input").attr("disabled","disabled")
			d.find('.slider-checkbox-container').css('background', 'var(--color-background-400)')
		else
			d.find("input").removeAttr("disabled","disabled")
			d.find('.slider-checkbox-container').css('background', '')
		return true
