class FieldLink extends DSLFields

	###
		sample json value:
			url: "https://www.example.com/photo-file.pdf"
			title: "Photo File.pdf"
	###

	@blank: ->
		r =
			url: '#'
			title: '-'

	@clear: (d) ->
		d.data('value', @blank())
		return

	@draw: (form, view, id, k, v, df, mode) ->

		if App.company.no_download is 'Yes'
			classes = $.trim('fieldlink disable_links ' + v.view.class)
		else
			classes = $.trim('fieldlink ' + v.view.class)

		attr =
			id: id
			class: classes
			type: 'link'
		attr.readonly = 'readonly' if v.view.readonly

		vdef = @blank()

		if df and getType(df) is 'string'
			try
				df = JSON.parse(df)
			catch e
				df = @blank()
		else if not (df and df.url)
			df = @blank()
		try
			vdef = JSON.parse(v.model.default)
		catch e
			vdef = @blank()

		ext = ''
		if df?.title and /\./.test(df.title)
			ext = /(?:\.([^.]+))?$/.exec(df.title)[1].toLowerCase()

		html = '<div ' + @attr(attr) + '><span>' + @draw_label(df) + '</span>'

		if ext in ['pdf', 'gif', 'jpeg', 'jpg', 'png', 'JPEG', 'JPG', 'PNG', 'GIF']
			html += '<i class="preview glyphicon glyphicon-zoom-in">&nbsp;</i></div>'
		else
			html += '</div>'

		d =
			html: html
			data:
				fieldtype: 'link'
				value: df
				df: _.cloneDeep(vdef)

	@draw_label: (v) ->
		linkJSONLabel(v, true)

	@grid_draw_link: (v, data) ->
		df = v.model.default
		if df and getType(df) is 'string'
			try
				df = JSON.parse(df)
			catch e
				return ""
		else if not (df and df.url)
			return ""
		url = string_template(df.url, data)
		if not url
			return ""
		attr =
			class: "fieldlink gridlink"
			type: 'link'
			href: url
			target: if url.startsWith('#') then '' else ' target="_blank"'
		icon =  if df.icon then df.icon else 'fa fa-link'
		return '<a '+@attr(attr)+' ><i class="'+icon+'"></i></a>'

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: (d) ->
		if @visible(d)
			v = d.data('value')
			if v and v.url
				linkJSONLabel(v)
			else
				null
		else
			null

	@value_get: (d) ->
		d.data('value')

	@value_set: (d, n) ->
		df = d.data('df')
		df_url = df?.url
		df_title = df?.title
		df_icon = df?.icon
		n = @blank() if not (n and n.url)
		n.url = string_template(df_url, {}, d.data('dd'))
		if (!n.title or n.title == '-') and n.url
			n.title = df_title
		if !n.icon
			n.icon = df_icon
		d.data('value', n)
		d.find('span').html(@draw_label n)
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		# its always readonly
		return true
