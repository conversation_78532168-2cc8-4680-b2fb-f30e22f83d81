class FieldInput extends DSLFields

	@clear: (d) ->
		d.val('')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		unit = v.view.class.indexOf('unit') > -1
		cls = if unit then 'form-control' else ''
		attr =
			id: id
			class: $.trim(cls + ' ' + v.view.class)
			type: 'text'
		attr.readonly = 'readonly' if v.view.readonly
		attr.tabindex = if (v.view.readonly or v.view.offscreen) then -1 else 0
		attr.input_val =  escapeHTML(df) if v.view.readonly
		attr.value = escapeHTML(df)
		attr.title = escapeHTML(df) if v.view.readonly
		if v.view.readonly && !attr?.value?.length && !v?.view?.class?.includes?('json-viewer')
			attr.class = $.trim(attr.class + ' no-val')

		if (v.model.type is 'decimal') and (v.model.rounding isnt null)
			attr.value = roundTo(attr.value, v.model.rounding)
		if not (v.model.type in ['boolean', 'decimal', 'int', 'json', 'password', 'text'])
			attr.value = 'Field will have type: ' + v.model.type + ', control: ' + v.view.control

		if v.model.type is 'boolean'
			return FieldSelect.draw(form, view, id, k, v, df, mode)
		else if v.model.type in ['json', 'password', 'text', 'color']
			attr.maxlength = v.model.max if v.model.max isnt null
			if v.model.type is 'password'
				attr.type = 'password'
				attr.value = ''
			else if v.model.type is 'color'
				attr.type = 'color'
				delete attr.maxlength
				attr.value = df
				attr.input_val = df
				attr.disabled = 'disabled' if v.view.readonly
		else if v.model.type in ['int']
			attr.maxlength = 20
			attr.type = 'tel' if @browser_use_html5() and (not unit)
		else if v.model.type in ['decimal']
			attr.maxlength = 20
			attr.type = 'number' if @browser_use_html5() and (not unit)

		data =
			fieldtype: 'input'
			mask: @get_mask(v)
		if data.mask
			attr.class += ' maskable'

		html = '<input autocomplete="off" ' + @attr(attr) + ' />'
		if v.model.type is 'color'
			html += '<div class="color-hex" style="color: ' + df + '">' + (if df then df.toUpperCase() else '') + '</div>'
		d =
			html: html
			data: data

	@load_options: (form, view, k, v, dd) ->
		filter_url = @filter_url
		dt = []
		aj = Ajax.async
			url: filter_url(form, k, v, dd)
		aj.done (data, status, xhr) =>
			for o in data
				continue if !o.auto_name or typeof o.auto_name isnt 'string'
				if v.model.type is 'text' and (v.model.dynamic?.source? or v.model.dynamic?.query?)
					if v.model.dynamic?.query
						dt.push({name: o.auto_name})
					else
						rf = o.auto_name.split(" (")
						dt.push({name: rf[0]})
			return
		aj.fail (xhr, status, data) =>
			console.log data
		dt

	@get_mask: (v) ->
		if v.view.format is 'hic'
			'***********?-*9'
		else if v.view.format is 'ssn'
			'***********'
		else if v.view.format is 'us_phone'
			'(*************? x99999'
		else if v.view.format is 'us_zip'
			'99999?-9999'
		else
			false

	@load_all: (fa) ->
		fa.find('.maskable').each ->
			t = $(this)
			t.mask t.data('mask')
			t.bind 'paste', ->
				$(this).val ''
				return
			return
		return if fa.hasClass('findbar')
		fa.find('.api_prefill').each ->
			d = $(this)
			k = d.data('k')
			form = d.data('form')
			dd = d.data('dd')
			v = d.data('v')
			id = d.data('id')
			return if id and id?.startsWith?('fnd_')
			uid = d.data('apiprefill_uid')
			if not uid
				uid = window.next_id('apiprefill')
				d.data('apiprefill_uid', uid)
			if ((fa.find("""##{uid}""").length == 0))
				(d?.parent()?.append("""<div id=#{uid} form=#{form} field=#{k}></div>""")) # add symbol to show its api prefill field
				loadComponent('ApiPrefill', fa.find("""##{uid}""")[0],uid, { uid: uid, dd: dd, v: v } )
			return
		fa.find('.json-viewer').each ->
			d = $(this)
			k = d.data('k')
			form = d.data('form')
			dd = d.data('dd')
			v = d.data('v')
			id = d.data('id')
			return if id and id?.startsWith?('fnd_')
			uid = d.data('json-viewer_uid')
			if not uid
				uid = window.next_id('json-viewer')
				d.data('json-viewer_uid', uid)
			if ((fa.find("""##{uid}""").length == 0))
				(d?.parent()?.append("""<div id=#{uid} form=#{form} field=#{k}></div>""")) # add symbol to show its api prefill field
				loadComponent('FieldJSON', fa.find("""##{uid}""")[0],uid, { uid: uid, dd: dd, v: v, k: k, form: form } )
			return
		return

	@unload_all: (fa) ->
		fa.find('.maskable').unmask()
		fa.find('.api_prefill').each ->
			d = $(this)
			uid = d.data('apiprefill_uid')
			unloadComponent('ApiPrefill', uid)
		fa.find('.json-viewer').each ->
			d = $(this)
			uid = d.data('json-viewer_uid')
			unloadComponent('FieldJSON', uid)
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		v = d.data('v')
		if v.model.type in ['int', 'decimal'] and (v.view.class.includes('numeral')) and (v.view.format)
			return d.val().toString().replace(/[,A-Za-z$%]/g, '')
		d.val()

	@value_set: (d, n) ->
		v = d.data('v')
		if (n? && typeof n == 'object' && Object.keys(n)?.length || n?) && d.hasClass('no-val')
			d.removeClass('no-val')
		else if (v?.view.readonly && !n? && !v?.view?.class?.includes?('json-viewer'))
			d.addClass('no-val')
		d.attr('title', n)
		d.val(n).change()
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		if ro
			d.attr('readonly', 'readonly')
		else
			d.removeAttr('readonly')
		return true

	@field_populate: (d, n) ->
		d.typeahead(
			source: @load_options(n.form, n.view, n.k, n.v, n.dd)
			minLength: 0
			autoSelect: true
			fitToElement: true
			showHintOnFocus: 'all'
			selectOnBlur: false
			items: 'all'
			afterSelect: (value) ->
				setTimeout () ->
					d.blur();
					d.data('typeahead').focused = false;
					return
		)
		return