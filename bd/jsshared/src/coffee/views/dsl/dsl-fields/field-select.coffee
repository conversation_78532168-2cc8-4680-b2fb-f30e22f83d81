class FieldSelect extends DSLFields

	@clear: (d) ->
		if d.hasClass('activeselect2field')
			d.select2('val', '')
		else
			d.val('')
		return

	@close: ->
		$('.select2-container-active').select2('close')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		so = {}
		ty = 'select'
		src = []
		mng = false
		readonly = v.view.readonly
		#CM:2015-06-07 - this is to allow list filtering on
		#                  field 'archived' which is set as
		#                  type = boolean, control = input, readonly = true
		#                this can be obsoleted once backend sets
		#                  type = boolean, control = select, readonly = false
		if v.model.type is 'boolean' and getType(v.model.source) is 'null'
			readonly = false
			df = 'false'

		if v.model.type is 'boolean' and getType(v.model.source) is 'null'
			src.push('<option value=""></option>') # add blank row: default selection = none
			for ok, ov of {'all': 'All', 'true': 'Yes', 'false':'No'}
				src.push('<option value="' + escapeHTML(ok) + '">' + ov + '</option>')
		else if getType(v.model.source) is 'array'
			if not v.model.multi
				src.push('<option value=""></option>') # add blank row: default selection = none
			for o in v.model.source
				src.push('<option value="' + escapeHTML(o) + '">' + o + '</option>')
		else if getType(v.model.source) is 'object'
			if not v.model.multi
				src.push('<option value=""></option>') # add blank row: default selection = none
			vms = _.invert(v.model.source)
			for ov in sortedKeys(vms)
				ok = vms[ov]
				src.push('<option value="' + escapeHTML(ok) + '">' + ov + '</option>')
		else if !v.model.source? && v.view.control == 'select'
			src.push('<option value=""></option>') # add blank row: default selection = none
		else
			mng = v.model.source if DSL[v.model.source]?.model.bundle.indexOf('manage') > -1
			ty = 'input'
			so = @load_options(form, view, id, k, v, df)

		if form is 'document'
			if v.model.source is 'patient'
				mng = 'patient'
			else if v.model.source is 'careplan_order'
				mng = 'careplan_order'
		if v.view.add_preset and window.DSL[v.model.source]
			mng = v.model.source
		# options that apply to select2 boxes regardless of source
		so.allowClear = true
		so.placeholder = if readonly or v.model.multi then '' else 'Select ' + v.view.label
		so.dropdownAutoWidth = false
		so.initValue = df
		so.formName = form
		so.formatResult = @get_select2_template(v.model.querytemplate)
			# so.formatSelection = @get_select2_template(v.model.querytemplate)
		if v.model.multi and v.view.max_count
			so.maximumSelectionSize = v.view.max_count

		attr =
			id: id
			class: 'select2field field_' + k + " " + if v.view.class then v.view.class else '' 
			type: 'hidden'
		attr.readonly = 'readonly' if readonly
		attr.disabled = 'disabled' if readonly
		attr.tabindex = if v.view.offscreen || v.view.readonly then '-1' else '0'
		attr.multiple = 'multiple' if v.model.multi
		attr.ignoreRequired = 'ignoreRequired'  if getType(df) is 'string' and df.startsWith('{') and df.endsWith('}')
		if so.source == 'site' and ["add", "addfill"].includes(mode)
			df = FieldSelect.initial_selection(so, df)
		d =
			html:
				if ty is 'select'
					'<select ' + @attr(attr) + '>' + src.join('') + '</select>'
				else # if ty is 'input'
					'<input autocomplete="off" ' + @attr(attr) + ' />'
			data:
				fieldtype: 'select'
				selopt: so
				df: df
				id: id
				manage: mng


	@get_select2_template: (querytemplate) ->
		inventoryTemplate = (data) ->
			if data?.fd?.type and data.fd.type is 'Billable'
				"""
				<span>#{data.text}</span>
				"""
			else
				"""
				<span class="select2-inventory-template">
					<span style='font-weight: 600;'>#{data.fd.auto_name}<span class="select2-inventory-template-right-label"'>#{if data.fd.manufacture then data.fd.manufacture else ""}</span></span>
					<span class="select2-inventory-template-stock #{if parseInt(data.fd.quantity_in_stock) == 0 then 'warning' else ''}">#{'  ' + data.fd.quantity_in_stock}
						<span>in-stock</span><span class="select2-inventory-template-right-sublabel"'>#{if data.fd.code then data.fd.code else ""}</span>
					</span>
				</span>
				"""
		labelTemplate = (data) ->
				"""
				<span class="select2-label-template">
					<strong>#{data.fd.auto_name}</strong>
					<span class="select2-label-template-sublabel">#{data.fd.summary.replace(/\n/g, ' ').substring(0, 125)}...</span>
				</span>
				"""
		cmpInstrTemplate = (data) ->
				"""
				<span class="select2-cmp-instr-template">
					<strong>#{data.fd.auto_name}</strong>
					<span class="select2-label-template-sublabel">#{data.fd.compounding_instructions.replace(/\n/g, ' ').substring(0, 125)}...</span>
				</span>
				"""
		defaultTemplate = (data) ->
			"""
			<span>#{data.text}</span>
			"""
		if(querytemplate == 'inventoryTemplate')
			inventoryTemplate
		else if(querytemplate == 'labelTemplate')
			labelTemplate
		else if(querytemplate == 'cmpInstrTemplate')
			cmpInstrTemplate
		else
			defaultTemplate

	@load_all: (fa) ->
		fa.find('.select2field').each ->
			t = $(this)
			data = t.data()
			so = data.selopt
			return if not so

			nosearch = DSLFields.browser_use_html5() and t.prop('tagName') is 'SELECT'
			so.minimumResultsForSearch = 'Infinity' if nosearch

			t.val(so.initValue)
			if so.initSelection? or (not DSLFields.browser_use_html5())
				t.addClass 'activeselect2field'
				t.select2 so
				t.select2('container').attr('tabindex',-1)
				t.select2('readonly', true) if t.prop('readonly') is 'readonly'
				t.select2('disabled', true) if t.prop('disabled') is 'disabled'
				t.select2('container').find('.select2-search, .select2-focusser').remove() if nosearch
			return
			# v = data.v
			# cls = v.view.class
			# if !cls or !cls.includes('select_prefill')
			# 	return
			# k = data.k
			# return if !k
			# form = data.form
			# dd = data.dd
			# return if !dd
			# fld = data.id
			# return if !fld
			# return if fld and fld.startsWith?('fnd_')
			# console.log(t, 'select_prefill', form, k, dd)
			# uid = 'selectprefill_uid' + fld
			# onReady = (doPrefilling) =>
			# 	t.data('doPrefilling', doPrefilling)
			# 	if(t.data('initSet'))
			# 		doPrefilling()
			# t?.parent()?.append("""<div id=#{uid} form=#{form} field=#{k}></div>""") # add symbol to show its select prefill field
			# loadComponent('SelectPrefill', fa.find("""##{uid}""")[0], uid, { uid: uid, dd: dd, v: v, k: k, onReady: onReady} )
		fa.find('.select_prefill').each ->
			d = $(this)
			k = d.data('k')
			if (!k)
				return true
			form = d.data('form')
			dd = d.data('dd')
			fld = d.data('id')
			return if !dd
			return if fld and fld?.startsWith?('fnd_')
			v = d.data('v')
			uid = 'selectprefill_uid' + fld
			onReady = (doPrefilling) =>
				d.data('doPrefilling', doPrefilling)
				if(d.data('initSet'))
					doPrefilling()
			d?.parent()?.append("""<div id=#{uid} form=#{form} field=#{k}></div>""") # add symbol to show its select prefill field
			loadComponent('SelectPrefill', fa.find("""##{uid}""")[0], uid, { uid: uid, dd: dd, v: v, k: k, onReady: onReady} )
			return

	@initial_selection: (so, df) ->
		if so.formName == 'view_site_selector'
			return df
		if df and df != '' and df != '*'
			return df
		site_len = window.sitesSelected.length
		if site_len == 0
			return df
		if site_len == 1 && window.sitesSelected[0] == 0
			return df
		if so.multiple
			return window.sitesSelected
		else
			return window.sitesSelected[0]

	@load_options: (form, view, id, k, v, df, sk=null, sv=null) ->
		filter_url = @filter_url
		multi = if sv?.multi then sv.multi else false
		sourceid = if sv?.sourceid then sv.sourceid else "id"
		source = sv?.source
		is_template = false
		if !(sk?)
			multi = v.model.multi
			sourceid = v.model.sourceid
			source = v.model.source
		always_apply = getSourceFilterValues(source, v)
		if getType(source) is 'string' and source.includes('{')
			is_template = true
		opt =
			sourceid: sourceid
			source: source
			is_template: is_template
			multiple: multi
			filter_static: always_apply
			fields: if v.view.class.includes('select_prefill') then 'all' else 'min'
			initSelection: (e, cb) ->
				dd = if sk? then e.closest('table').data('dd') else e.data('dd')
				preset = if sk then (dd.preset?[k]?[e.closest('tr').index()] or {}) else dd.preset
				auto_name_key = if sk? then sk + '_auto_name' else k + '_auto_name' 
				if preset?[auto_name_key]? and `df == e.val()` and !v.view.class.includes('select_prefill') # Only used cached data of unchanged
					iv = false
					if multi
						iv = []
						for kk,vv of df
							iv.push id: vv, text: preset[auto_name_key][kk]
					else
						if df
							iv = id: df, text: preset[auto_name_key]
					cb iv
				else # NOTE: does not handle source fields with multi=true
					if window.App.feature.debug
						if v.view.class.includes('select_prefill')
							console.log("""%cSelectCall: #{form}.#{k} : Additional data Required, Calling server""", 'background: #00ff00; color: #ffffff')
						else
							if !preset?[auto_name_key]
								console.log("""%cSelectCall: #{form}.#{k}: Missing AutoName, Calling server""",'background: #ff0000; color: #ffffff')
							else
								console.log("""%cSelectCall: #{form}.#{k}: Default Value To DOM Mismatch, Calling server""", 'background: #222; color: #bada55')
					df = e.val() # Always evaluate since df can be cached
					return if df in ['', '*']
					if getType(df) is 'string'
						df = df.trim()
						return if df.startsWith('{') or df.endsWith('}')
					flt = {}
					flt.auto_name = '!..'
					flt[sourceid] = df
					l = '&limit=1'
					if is_template
						source = string_template(source, {}, dd)
					if multi
						flt[sourceid] = flt[sourceid].split(',')
						l = '&limit=' + flt[sourceid].length
					if(!_.isEmpty(always_apply))
						for fk, fv of always_apply
							continue if sourceid is 'code' and fk is 'code'
							flt[fk] = fv
					fields = if v.view.class.includes('select_prefill') then 'all' else 'min'
					rurl = '/form/' + source + "/?fields=#{fields}" + l + '&' + joinFilters(flt)
					g = Ajax.async
						url: rurl
					g.done (data, status, xhr) ->
						iv = false
						if multi
							iv = []
							for o in data
								iv.push id: o[sourceid], text: o.auto_name, fd: o
						else
							if data.length > 0
								iv = id: data[0][sourceid], text: data[0].auto_name, fd: data[0]
						try
							cb iv
							if not e.data('initSet')
								e.data('initSet', true)
								e.trigger('change')
								if(e.data('doPrefilling'))
									e.data('doPrefilling')()
						catch err
							return
						return
					g.fail (xhr, status, data) ->
						return
				return
			query: (q) ->
				dd = q.element.data('dd')
				row_data = {}
				if sk?
					rowIndex = q.element.closest('tr').index()
					dd = q.element.closest('table').data('dd')
					row_data = (dd.value_field(k))?[rowIndex]
				g = Ajax.async
					url: filter_url(form, k, v, dd, $.trim(q.term), sk, sv, row_data)
				g.done (data, status, xhr) ->
					config = getFormConfig(v.model.source)
					if config?.selectConfig?.filterFunction
						data = config.selectConfig.filterFunction(data, dd, form, k, v)
					data = data.sort (a, b) ->
						a.auto_name.localeCompare b.auto_name, undefined,
							numeric: true
							sensitivity: 'base'
					dt = []
					for o in data
						if v?.model?.source is 'site' and (o.code == 'TRANSFER' or o.id == 1000)
							continue
						if getType(o) is 'string' # api/bi/unique returns plain arrays
							o = $.trim(o)
							continue if o is ''
							dt.push
								fd: o
								id: o
								text: o
						else
							continue if ($.trim(o.id) is '') or ($.trim(o.auto_name) is '')
							dt.push
								fd: o
								id: o[sourceid]
								text: o.auto_name
					q.callback results: dt
				g.fail (xhr, status, data) ->
					q.callback results:[]
				return
		opt

	@unload_all: (fa) ->
		fa.find('.activeselect2field').loop (sl) -> # destroy doesn't work without loop/each
			sl.select2('destroy')
			return
		fa.find('.select_prefill').each ->
			d = $(this)
			uid = 'selectprefill_uid' + d.data('id') 
			unloadComponent('SelectPrefill', uid);
			return
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: (d) ->
		if @visible(d)
			if d.hasClass('activeselect2field')
				dt = d.select2('data')
				if getType(dt) is 'array'
					ds = {}
					for dv in dt
						ds[dv.id] = dv.text
					dt = for dv in d.select2('val')
						if ds[dv]?
							ds[dv]
						else
							dv
				else
					dt?.text or null
			else
				d.children('option:selected').first().text() # cannot have multi = true
		else
			null

	@value_get: (d) ->
		# HB-4575 fixed
		selectedOption = []
		try
			if d.hasClass('activeselect2field')
				values = d.select2('data')
				if getType(values) in ['array']
					values.forEach (element, index) ->
						if getType(element) in ['object']
							selectedOption.push(element.id)
						else
							selectedOption.push(element)
					selectedOption
				else if getType(values) in ['object']
					values.id
				else
					d.select2('val')
			else
				d.val()
		catch err
			return null

	@pre_tranform: (n) ->
		if !n
			return null
		if getType(n) is 'array'
			if n.length > 0
				n = n.map (v) -> if getType(v) is 'object' then v.id else v
				n = n.filter (v) -> v
		else if getType(n) is 'object'
			if n.id
				n = [n.id]
			else
				n = null
		else if getType(n) is 'string' or getType(n) is 'number'
			n = [n]
		else
			n = null 
		if n and n.length is 0
			n = null
		n
	
	@get_select2_values: (d, n=[]) ->
		selopt = d.data('selopt')
		multi = selopt.multiple
		sourceid = selopt.sourceid
		source = selopt.source
		is_template = selopt.is_template
		if is_template
			source = string_template(source, {}, d.data('dd'))
		filter_static = selopt.filter_static
		fields = selopt.fields
		dfd = $.Deferred()
		if !n or n.length is 0
			dfd.resolve(null)
			return
		flt = {}
		nv = n.map (v) -> "filter=" + sourceid + ":" + v
		if filter_static
			for fk, fv of filter_static
				continue if sourceid is 'code' and fk is 'code'
				flt[fk] = fv
		rurl = '/form/' + source + "/?fields=#{fields}&limit=" + nv.length + '&' + joinFilters(flt) + '&' + nv.join('&')
		g = Ajax.async
			url: rurl
		g.done (data, status, xhr) ->
			if getType(data) is 'array'
				if data.length is 0
					dfd.resolve(null)
					return
				iv = []
				for o in data
					iv.push id: o[sourceid], text: o.auto_name, fd: o
				if multi
					dfd.resolve(iv)
				else
					dfd.resolve(iv[0])
			else
				dfd.resolve(null)
		g.fail (xhr, status, data) ->
			dfd.resolve(null)
		dfd

	@transform_value: (d, n=[]) ->
		v = d.data('v')
		multi = v.model.multi
		sty = getType(v.model.source)
		dfd = $.Deferred()
		if !n or n.length is 0
			dfd.resolve(null)
			return dfd
		if ['object', 'array'].includes(sty) # select is driven by DSL
			n = n.map (element) ->
				if sty is 'object'
					if v.model.source[element]
						return {id: element, text: v.model.source[element]}
				else
					if v.model.source.includes(element)
						return {id: element, text: element}
			n = n.filter (element) -> element
			if n.length is 0
				n = null
			if !n or multi
				dfd.resolve(n)
			else
				dfd.resolve(n[0])
		else # select is driven by API
			afd = @get_select2_values(d, n)
			afd.done (data) ->
				dfd.resolve(data)
		dfd

	@value_set: (d, n) ->
		v = d.data('v')
		multi = v.model.multi
		max_selection_size = v.view.max_count
		if n and multi and max_selection_size 
			if n.length > max_selection_size
				console.warn("FieldSelect: value_set: value exceeds max_count")
				return
		that = @
		try
			if d.hasClass('activeselect2field')
				xf = @transform_value(d, @pre_tranform(n))
				xf.done (data) ->
					if data
						d.select2('data', data, true)
					else
						d.select2('val', '', true)
					that.hopon_form_buss(d)
			else
				that.hopon_form_buss(d)
				d.val(n)
		catch err 
			return
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		if ro
			d.select2("disable")
		else
			d.select2("enable")
		return true
