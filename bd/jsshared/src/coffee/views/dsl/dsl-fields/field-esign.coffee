class FieldESign extends DS<PERSON>ields

	###
		sample json value:
			name: "<PERSON>"
			title: "<PERSON>armacist"
			image: "..." # base64 encoded svg
			timestamp: unix_timestamp # Date.now()
	###
	@geoLocation = null
	@clear: (d) ->
		d.data('value', '')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		attr =
			id: id
			class: $.trim('esignfield ' + (if v.view.readonly then 'readonly ' else '') + v.view.class)

		bdf =
			name: if v.view.readonly then 'No signature' else if getUserSignature()? and Object.keys(getUserSignature()).length > 0  then 'Fill to e-sign' else 'Click to e-sign'
			title: null
			image: null
			timestamp: 0

		if df and getType(df) is 'string'
			try
				df = JSON.parse(df)
			catch e
				df = bdf
		else if not (getType(df) is 'object' and df.name?)
			df = bdf

		buttonHtml = '<button class="esign-btn-prefill btn-primary" style="display: block;">Auto Fill Sign</button>'
		d =
			html: '<div ' + @attr(attr) + '><span>' + @draw_label(df) + '</span></div>'
			data:
				fieldtype: 'esign'
				value: df

	@prefill_sign: (d) ->
		dfd = $.Deferred()
		if not d or typeof d isnt 'object'
			console.error('The provided data object of esign field is not valid.')
			dfd.resolve()
			return dfd

		# Get user signature from helper function
		user_signature = getUserSignature()
		if not user_signature
			dfd.resolve()
			return dfd

		FieldESign.get_location().then (gpsLocation) =>
			if gpsLocation
				latLng = "#{gpsLocation.coords.latitude} x #{gpsLocation.coords.longitude}"
				sign = user_signature
				sign = Object.assign sign,
					position: latLng
					timestamp: Date.now()
				DSLFields.value(d, sign)
				prettyAlert('Signature Added', 'The signature was successfully auto-filled.')
				DSLFx.ValidateFieldError d, false
				dfd.resolve()
		.catch (error) =>
			if error.code is 1
				prettyError('Unable to get location', 'Location access was denied. Please enable location permissions in your browser settings to continue.')
			dfd.resolve()
		return dfd


	@draw_label: (v) ->
		esignJSONLabel(v, true)

	@get_location: () ->
		return new Promise (resolve,reject) ->
			if navigator.geolocation
				navigator.geolocation.getCurrentPosition(resolve,reject)
			else
				reject("Geolocation is not supported by this browser.")
	
	@showLocationOnFooter: (dialog) ->
		footer = dialog.getModalFooter()[0].childNodes[0].childNodes[0]	
		lastChild = footer.childNodes[footer.childNodes.length - 1]
		if $(footer.childNodes[0]).text() != 'Cancel'
			footer.childNodes[0].remove()

		container = $('<span class="additional-text-container"></span>')
		location = null
		if !FieldESign.geoLocation
			FieldESign.geoLocation = {
				timestamp:Date.now()
				coords: {
					latitude: 0,
					longitude: 0
				}
			}

		location = if FieldESign.geoLocation.coords.latitude == 0 and FieldESign.geoLocation.coords.longitude == 0 then $("<span> N/A </span>") else $("<span>#{FieldESign.geoLocation.coords.latitude} x #{FieldESign.geoLocation.coords.longitude} </span>")
		timestamp = $("<span>#{(new Date(FieldESign.geoLocation.timestamp)).format(FORMAT_DATETIME_GRID)}</span>")

		if $(footer.childNodes[0]).text()
			gpsIcon = FieldESign.createGPSIcon(dialog)
			gpsIcon.prependTo(container)

		location.appendTo(container)
		timestamp.appendTo(container)
		footer.insertBefore(container[0],footer.childNodes[0])

		if $(footer.childNodes[2]).text() == 'Confirm Location'
			$(footer.childNodes[2]).text('Save')
		return

	@fetchAndShowLocation: (dialog) =>
		spinner = $('<div class="spinner-border" role="status"><div class="sr-only"></div></div>')
		dialog.getModalContent().prepend(spinner)
		
		FieldESign.get_location().then (gpsLocation) =>
			if gpsLocation
				FieldESign.geoLocation = gpsLocation	
		.catch (error) =>
			console.log("Unable to get location ", error)
			FieldESign.geoLocation = null
		.finally () =>
			FieldESign.showLocationOnFooter(dialog)
			spinner.remove()
		
		return

	@createGPSIcon: (dialog) =>
		gpsIcon = $('<img class="gps-icon" src="public/img/location.svg"> </img>')
		return gpsIcon

	@get_signature: (d,button_text,html=null,position=null) ->
		f = d.data().v
		esign = {}
		if f.view.template
			esign.name = f.view.template


		dfd = $.Deferred()
		html = templateRaw('esign-dialog', esign)
		prettyForm 'Please E-Sign <br><span className="sig-subtitle">Use your mouse to draw your signature</span></br>', html, 'Cancel',button_text, (dialog) ->  # init 
			f = dialog.$modalContent
			canvas = f.find('#esign-pad')[0]
			wrapper = f.find('.esign-wrapper')
			sp = f.esign = new SignaturePad(canvas,
				minWidth: 0.75
				maxWidth: 5
				throttle: 4
			)
			f.find('.clear').on('click', ->
				sp.clear()
				return
			)

			sp.resize = ->
				# setup canvas dims
				mw = cw = f.closest('.modal-content').width()
				mh = $(window).height() - 200
				ch = Math.round(cw * 0.5)
				if(ch > mh)
					ch = mh
					cw = ch * 2
				wrapper.css('margin-left', Math.max(0, (mw - cw) / 2) - 15)
				wrapper.width(cw)
				wrapper.height(ch)

				# setup dpi
				ratio =  Math.max(window.devicePixelRatio or 1, 1)
				canvas.width = cw * ratio
				canvas.height = ch * ratio
				canvas.getContext('2d').scale(ratio, ratio)
				$(canvas).css(transform: 'scale(' + Math.round(100/ratio)/100 + ')')
				sp.clear() # otherwise isEmpty() might return incorrect value

			sp.resize()
			$(window).on('resize', sp.resize)
			return 
		, (button, dialog) -> # save
			if button == 'Cancel'
				dialog.close()
				dfd.reject()
				return
			if $(dialog.getModalFooter()[0].childNodes[0].childNodes[0].childNodes).last().text() == 'Confirm Location'
				FieldESign.geoLocation = null
			if !FieldESign.geoLocation 
				FieldESign.fetchAndShowLocation(dialog)
				return 
			latLng = "#{FieldESign.geoLocation.coords.latitude} x #{FieldESign.geoLocation.coords.longitude}"
			sp = dialog.$modalContent.esign
			return if sp.isEmpty() 

			user = App.user.displayname || App.user.firstname + " " + App.user.lastname
			usertitle = if CRCompany?.theme?.esign?.title then App?.user?.job_title else null

			if esign.name? and f.find('.esign-name').length > 0 # manually entered name
				name = $.trim(f.find('.esign-name').val())
				return if not name
				title = null
			else # e-sign as the logged-in user
				name = user
				title = usertitle
				user = usertitle = null # no need to set user if there is no manual name entry
			v =
				name: name
				title: title
				user: user
				usertitle: usertitle
				image:
					png: sp.toDataURL()
					svg: sp.toDataURL('image/svg+xml')
				position : latLng
				timestamp: FieldESign.geoLocation.timestamp
			$(window).off('resize', sp.resize)
			sp.off()
			FieldESign.setSignatureInUserPreference(v)
			dialog.close()
			dfd.resolve v
			return
		, (button, dialog) -> # cancel
			sp = dialog.$modalContent.esign
			$(window).off('resize', sp.resize)
			dialog.close()
			dfd.reject()
			return
		, undefined
		, position
		dfd

	@setSignatureInUserPreference: (value) ->
		if not value or typeof value isnt 'object'
			console.error('The signature object is not valid.')
			return
		fro =
			url: """/api/my/preference/"""
			method: "PUT"
			data:
				'user_signature': value

		fr = request(fro)
		fr.then (resp) =>
			if resp?.data?.user_signature
				window.UserPreference.user_signature = resp.data.user_signature
			else
				console.error('The UserPreference did not have the signature.')
		fr.catch (resp) ->
			errorMessage = resp.data?.error or resp.data?.response?.data?.error or "An unexpected error occurred while saving the signature."
			prettyError 'Error While Setting Signature', errorMessage
			return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: (d) ->
		if @visible(d)
			v = d.data('value')
			if v and v.name? and v.image? and v.name and v.image
				esignJSONLabel(v)
			else
				null
		else
			null

	@value_get: (d) ->
		v = d.data('value')
		if v and v.name? and v.image? and v.name and v.image
			v
		else
			''

	@value_set: (d, n) ->
		d.data('value', n)
		d.find('span').html(@draw_label n)
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		if ro
			d.css('box-shadow', 'unset')
			d.find('.edit-esign-icon').hide()
			d.css('background-color', 'var(--color-background-400)')
			d.addClass('is-readonly')
		else
			d.css('box-shadow', '')
			d.find('.edit-esign-icon').show()
			d.css('background-color', '')
			d.removeClass('is-readonly')
		return true
