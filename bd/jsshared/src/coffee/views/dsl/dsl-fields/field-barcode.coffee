class FieldBarcode extends DSLFields

	###
		sample json value:
			raw: "(01)50344206455109(21)25184376974640"
			item: "50344206455109"
			serial: "25184376974640"
	###

	@clear: (d) ->
		d.val('')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		cls = ' ' + v.view.class

		if v.model.type is 'json' and getType(df) is 'object'
			df = JSON.stringify(df)

		if v.view.readonly
			d =
				html:
					'<div id="' + id + '" class="' +
						$.trim('' + cls) + '" readonly="readonly">' + df +
					'</div>'
				data:
					fieldtype: 'barcode'
		else if v.view.class and v.view.class.includes('continues')
			d =
				html:
					'<input autocomplete="off" id="' + id + '" class="' +
						$.trim('' + cls) + '"' +
						(if v.model.max isnt null then ' maxlength="' + v.model.max + '"' else '') +
						' tabindex="-1" value="' + df + '">' +
						'<div class="barcode-container" forfield="' + k + '" forid="' + id + '">Continues Scanner</div>'
				data:
					fieldtype: 'barcode'
		else
			d =
				html:
					'<input autocomplete="off" id="' + id + '" class="' +
						$.trim('' + cls) + '"' +
						(if v.model.max isnt null then ' maxlength="' + v.model.max + '"' else '') +
						' tabindex="-1" value="' + df + '">' +
						'<div class="barcode-cam" forfield="' + k + '" forid="' + id + '"><span class="icon-camera-scanner"></span>Camera Scanner</div>' +
						'<div class="barcode-hid" forfield="' + k + '" forid="' + id + '"><span class="icon-handheld-scanner"></span>Handheld Scanner</div>'
				data:
					fieldtype: 'barcode'

	@load_all: (fa) ->
		setval = @value_set
		fa.find('.barcode-continues').each ->
			d = $(this)
			k = d.data('k')
			return if not k
			form = d.data('form')
			dd = d.data('dd')
			return if not dd
			v = d.data('v')
			fld = d.data('id') 		
			sb = d.siblings('div.barcode-container')
			return if sb.length is 0
			loadComponent('ContinuesScannerScanner', sb[0], fld, { fld: fld, dd: dd, v: v, form: form, k: k} )
		# camera scanner
		fa.find('.barcode-cam').on('click', ->
			ERR_INVALID = 'Please scan a valid barcode. 2D DataMatrix barcodes are not supported with camera scanner.'
			d = $(this).siblings("input")
			window.cameraScanner("Please scan a barcode", (data, error) =>
				if error and error.error and error.error isnt 'User Cancelled'
					prettyError 'Cannot access Camera!', error.error
					return
				if not (data and data[0] and data[0].rawValue)
					prettyError 'Invalid barcode!', ERR_INVALID
					return

				try
					bc = data[0].rawValue
					parse_bc = parseBarcode(bc)
					setval d, bc
				catch e
					prettyError 'Invalid barcode!', ERR_INVALID
				return
			)
			return
		)

		# keyboard-wedge scanner
		fa.find('.barcode-hid').on('click', ->
			d = $(this).siblings("input")
			window.textScanner("Please scan a barcode", (data, actions) =>
				return if not data or data.substr(-1) isnt "\n"
				try
					bc = data.trim()
					parse_bc = parseBarcode(bc)
					actions.setError('')
					actions.closeModal()
					setval d, bc
				catch
					actions.setError('Invalid barcode. Please try again.')
				return
			)
			return
		)
		return

	@unload_all: (fa) ->
		fa.find('.barcode-cam, .barcode-hid').off('click')
		fa.find('.barcode-continues').each ->
			d = $(this)
			unloadComponent('ContinuesScannerScanner', d.data('id'))
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		d.val()

	@value_set: (d, n) ->
		d.val(n).change()
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		console.error('FieldBarcode.readonly not Supported')
		return false
