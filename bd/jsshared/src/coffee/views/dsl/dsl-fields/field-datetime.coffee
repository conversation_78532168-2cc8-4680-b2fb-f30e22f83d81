class FieldDatetime extends DSLFields

	@clear: (d) ->
		d.val('')
		return

	@date_time_parse: (type, dtp, allow_range = false) ->
		dutc = (dt, tm) ->
			if dt is ''
				dt
			else
				try
					dtr = new Date(dt.replace(/\-/g, '/').replace('T', ' ')).format('yyyy-mm-dd')
					dtu = moment(new Date(dt + ' ' + tm)).utc().format(FORMAT_DATETIME_MISO)
				catch
					dtu = if tm is '00:00:00' then '2099-12-31' else '1900-01-01' + ' ' + tm
				dtu

		if allow_range
			dta = dtp.split('..')
			for k,v of dta
				dta[k] = '' if @date_time_parse(type, v) is 'Invalid Date'
			if dta.length is 1
				if type is 'datetime'
					dutc(dta[0], '00:00:00') + '..' + dutc(dta[0], '23:59:59')
				else
					dta[0]
			else
				if type is 'datetime'
					dutc(dta[0], '00:00:00') + '..' + dutc(dta[1], '23:59:59')
				else
					dta.join('..')
		else
			new Date(dtp.replace(/\-/g, '/').replace('T', ' '))

	@draw: (form, view, id, k, v, df, mode) ->
		fdt = 'MM/dd/yyyy'
		ftm = 'HH:mm pp'
		ty = v.model.type
		dateval = df

		if v.model.type is 'date'
			if @browser_use_html5()
				aclass = ''
				if dateval and not v.view.readonly
					dateval = dateYMD(dateval)
			else
				aclass = ''
			aformat = fdt
		else if v.model.type is 'time'
			if @browser_use_html5()
				aclass = ''
				if dateval and not v.view.readonly
					dateval = (new Date('1 Jan 2013 ' + dateval)).format('HH:MM')
			else
				aclass = ''
			aformat = ftm
		else # v.model.type is 'datetime'
			if dateval
				dateval = @date_time_parse(v.model.type, dateval)
				dateval = '' if $.trim(dateval) is 'Invalid Date'
			if @browser_use_html5()
				aclass = ''
				ty = 'datetime-local'
				if dateval
					if v.view.readonly
						dateval = dateval.format(FORMAT_DATETIME_READ)
					else
						# convert to ISO manually, ignoring time zones
						dateval = dateval.format(FORMAT_DATETIME_ISO).replace(' ', 'T')
			else
				aclass = ''
				dateval = dateval.format(FORMAT_DATETIME_READ) if dateval
			aformat = fdt + ' ' + ftm

		data =
			fieldtype: 'datetime'
		if not (@browser_use_html5() or view is 'find')
			if v.view.control is 'picker' and not v.view.readonly
				datdef =
					autoclose: true
					language: 'en'
					bootcssVer: 3
					minView: 0
					showMeridian: true
					todayBtn: true
					todayHighlight: true
					forceParse: false
				if v.model.type is 'date'
					datext =
						minView: 2
						format: 'mm/dd/yyyy'
				else if v.model.type is 'time'
					datext =
						maxView: 1
						startView: 1
						todayBtn: false
						todayHighlight: false
						format: 'HH:ii P'
				else # if v.model.type is 'datetime'
					datext =
						format: 'mm/dd/yyyy HH:ii p'
				aclass += ' datepicker'
				data.datopt = _.extend({}, datdef, datext)
			else if v.model.type is 'date'
				data.mask = '99/99/9999'
				aclass += ' maskable datepicker'
			else if v.model.type is 'time'
				data.mask = '99:99 aa'
				aclass += ' maskable datepicker'
			else # if v.model.type is 'datetime'
				data.mask = '99/99/9999 99:99 aa'
				aclass += ' maskable datepicker'
		
		if v?.view?.class
			aclass += ' ' + v.view.class
		
		attr =
			id: id
			class: aclass
			type: if @browser_use_html5() and (not v.view.readonly) then ty else 'text'
			'data-format': aformat
			maxlength: aformat.length
			value: escapeHTML(dateval)
			tabindex: if (v.view.readonly or v.view.offscreen) then -1 else 0
		attr.readonly = 'readonly' if v.view.readonly
		attr.disabled = 'disabled' if v.view.readonly

		d =
			html: """
			<input autocomplete="off" #{@attr(attr)} /> <span class="field-icon"></span>
			<div class="picker" style="display: flex; justify-content: flex-end;"></div>
			"""
			data: data

	@load_all: (fa) ->
		fa.find('.field-icon').on 'click', (e) ->
			icon = $(this)
			input = icon.siblings('input.datepicker')
			helper = input.data('helper')
			return if not helper
			helper.togglePicker(e)
			helper.setDate(input.val())
			return

		fa.find('.datepicker').each ->
			inputField = $(this)
			pickerDiv = inputField.siblings('.picker')
			props = inputField.data()
			props.dslFieldRef = (helper) ->
				inputField.data('helper', helper)
			loadComponent('DSLFieldDateTime', pickerDiv[0], props.id, props)
		return

	@unload_all: (fa) ->
		fa.find('.maskable').unmask()
		fa.find('.datepickered').datetimepicker('remove')
		fa.find('.picker').each ->
			d = $(this).parent().find('.datepicker')
			fldid = d.data('id')
			unloadComponent('DSLFieldDateTime', fldid)
			return
		return

	@validate: (d) ->
		fail = (ft) =>
			@value(d.focus(), '')
			'Invalid ' + ft + '! Please try again.'

		return false if @browser_use_html5() # skip html5 fields since browser validates them
		r = n = @value(d)
		return false if not r # no need to validate blank fields
		return false if r.includes('_') # no need to validate fields being edited

		v = d.data('v')
		if v.model.type is 'date'
			r = r.replace(/\-/g, '/')
			# http://momentjs.com/docs/#/parsing/string-formats/
			dtv = moment(r, ['M/D/YYYY', 'M/D/YY', 'M/D', 'D', 'M/D/Y'], true)
			return fail('date') if not dtv.isValid()
			r = dtv.toDate().format('mm/dd/yyyy')
		else if v.model.type is 'time'
			r = r.replace(/\./g, ':').replace(/\s/g, '')
			dtv = moment(r, ['h:m:sa', 'H:m:s', 'h:ma', 'H:m', 'ha', 'H'], true)
			return fail('time') if not dtv.isValid()
			r = dtv.toDate().format('hh:MM tt')
		else if v.model.type is 'datetime'
			dtv = @date_time_parse(v.model.type, r)
			return fail('date/time') if $.trim(dtv) is 'Invalid Date'
			r = dtv.format(FORMAT_DATETIME_READ)

		@value(d, r) if r isnt n
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		r = d.val().replace(/\s*(am|pm)+/gi, " $1")
		if @browser_use_html5() and r isnt ''
			v = d.data('v')
			if v.model.type is 'date'
				dtv = new Date(r.replace(/\-/g, '/'))
				r = dtv.format('mm/dd/yyyy')
			else if v.model.type is 'time'
				dtv = new Date('1 Jan 2013 ' + r)
				r = dtv.format('hh:MM tt')
			else if v.model.type is 'datetime'
				dtv = @date_time_parse(v.model.type, r)
				r = dtv.format(FORMAT_DATETIME_READ) if $.trim(dtv) isnt 'Invalid Date'
		r

	@value_set: (d, n) ->
		if @browser_use_html5() and n.includes("/")
			d.val(dateYMD(n)).change()
		else
			d.val(n).change()
		n

	@visible: (d) ->
		@is_visible(d)

	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d)
		if ro
			d.attr('readonly', 'readonly').attr("disabled","disabled")
		else
			d.removeAttr('readonly').removeAttr("disabled")
		return true
