class FieldGrid extends DSLFields

	@sourcedata: {}

	@handle_sort: (e) ->
		t = $(e.target).closest('th')
		table = t.closest('table')
		table.find('thead th').removeClass('sorting_asc')
		table.find('thead th').removeClass('sorting_desc')
		table.find('tbody td').css('background-color', '')
		ref=@
		table.on 'DOMNodeRemoved', (e) ->
			e.stopImmediatePropagation()
			return
		new Promise((resolve) ->
			resolve ref.sort(e)
			return
			).then (value) ->
				table.find('tbody tr').remove()
				table.find('tbody').append(value)
				table.find('tbody td .select2-container').remove()
				table.find('tbody td select').css('display','').select2()
				gselects = table.find('tbody td input[type=hidden][subtype=select]')
				gselects.each () ->
					$sfld = $(this)
					gdata = table.data()
					ref.render_select2_dynamic($sfld.css('display',''), gdata)
				table.find('tbody td input[subfield-dynamic]').each (_, d) ->
					$(d).typeahead(
						source: ref.load_options($(d).attr("subfield-dynamic"))
						minLength: 0
						autoSelect: true
						fitToElement: true
						showHintOnFocus: 'all'
						blurOnTab: true
						changeInputOnMove: false
						selectOnBlur: false
						items: 'all'
						afterSelect: (value) ->
							setTimeout () ->
								$(d).blur();
								$(d).data('typeahead').focused = false;
								return
					)
				table.unbind()

	@sort: (e) ->
		switching =true
		t = $(e.target).closest('th')
		index=null
		table = t.closest('table')
		row_trav = if table.find('tbody td:nth-child(' + (t.index() + 1) + ') select').length > 0 then table.find('tbody td:nth-child(' + (t.index() + 1) + ') select') else table.find('tbody td:nth-child('+(t.index()+1)+') input')
		table.find('tbody td:nth-child('+(t.index()+1)+')').css('background-color', '#e8e8e8')
		type = row_trav[0].getAttribute('subtype')
		if table.hasClass('no-sort')
			return table.find('tbody tr')
		sort = table.attr('sort')
		if sort
			t.find('i.'+sort).addClass('hide')
			t.removeClass('sorting_'+sort)
			sort = (if table.attr('sort') is 'asc' then 'desc' else 'asc')
		else
			sort = 'desc'
		table.attr('sort', sort)
		t.addClass('sorting_'+sort)
		t.find('i.'+sort).removeClass('hide')
		inc = if sort is 'desc' then (1) else (-1)
		rows = table.find('tbody tr')
		while switching
			switching = false
			if row_trav.length==1
				break;
			for i in [(if sort is 'desc' then 0 else row_trav.length-1)..(if sort is 'desc' then row_trav.length-1 else 0)]
				shouldSwitch = false
				index=i;
				cur = row_trav[i]
				nxt = row_trav[i + inc]
				if not nxt
					break;
				if type is 'text' and $(nxt).val() > $(cur).val() then shouldSwitch = true
				else if type is 'select' and $(nxt).val().substring(0,1) > $(cur).val().substring(0,1) then shouldSwitch = true
				else if type is 'int' and parseInt($(nxt).val()) > parseInt($(cur).val()) then shouldSwitch = true
				else if type is 'decimal' and parseFloat($(nxt).val()) > parseFloat($(cur).val()) then shouldSwitch = true
				else if ['date', 'datenow', 'datetime', 'timestamp'].includes(type)
					if new Date($(nxt).val()) > new Date($(cur).val()) then shouldSwitch = true
				else if ['timenow', 'time'].includes(type)
					if new Date('July 1, 1900 :' + $(nxt).val()) > new Date('July 1, 1900 :' +$(cur).val()) then shouldSwitch = true
					if moment.duration(moment($(cur).val(), "HH:mm:ss a").diff(moment($(nxt).val(), "HH:mm:ss a"))).asMinutes() < 0 then shouldSwitch = true
				if shouldSwitch then break;
			if shouldSwitch
				from = if inc < 0 then i else (i + inc)
				to = if inc < 0 then (i + inc) else (i)
				s1 = rows[to]
				s2 = row_trav[to]
				rows[to] = rows[from]
				rows[from] = s1
				row_trav[to] = row_trav[from]
				row_trav[from] = s2
				switching = true
		return rows

	@clear: (d) ->
		d.find('tbody').html('')
		return

	@add_row: (e, dfr = null) ->
		ref = @
		d = if e.target? then $(e.target).closest('table') else e
		max_count = d.data('v').view.max_count
		if max_count
			if d.find('tbody tr').length >= max_count
				prettyError 'Error', "Maximum row limit of #{max_count} reached."
				return
		d.find('tbody').append(FieldGrid.draw_row(d.data('k'), d.data('v'), dfr))
		d.find('.initselect').removeClass('initselect').select2()
		gselects = d.find('.initselect_dynamic')
		gselects.each () ->
			$sfld = $(this)
			gdata = d.data()
			ref.render_select2_dynamic($sfld, gdata)
		load_options = @load_options
		d.find('tbody input[subfield-dynamic]:last').each (_, d) ->
			$(d).typeahead(
				source: load_options($(d).attr("subfield-dynamic"))
				minLength: 0
				autoSelect: true
				fitToElement: true
				showHintOnFocus: 'all'
				blurOnTab: true
				changeInputOnMove: false
				selectOnBlur: false
				items: 'all'
				afterSelect: (value) ->
					setTimeout () ->
						$(d).blur();
						$(d).data('typeahead').focused = false;
						return
			)
		return

	@delete_row: (e) ->
		t = $(e.target)
		d = t.closest('table')
		log('Button clicked: delete grid row?')
		prettyYesNo 'Delete row?', 'Do you want to delete this row?', ->
			t.closest('tr').remove()
			return
		return

	@draw: (form, view, id, k, v, df, mode) ->
		@sourcedata = {}	# Invalidate Old Data
		data =
			fieldtype: 'grid'
		attr =
			id: id
			class: 'table table-hover table-striped'

		if not v.view.readonly
			data.sortopt =
				axis: 'y'
				containment: 'parent'
				handle: 'td.handle'
			attr.class += ' table-sortable'

		n = ['<table ' + @attr(attr) + '>']
		while getType(df) is 'string' # backend sometimes returns ''[]''
			try
				df = if df isnt '' then JSON.parse(df) else null
			catch e
				df = null
		df = null if getType(df) is 'array' and df.length is 0
		cntw = v.model.subfields_sort.length
		wdcnt =
			1: '8'
			2: '6'
			3: '4'
			4: '3'
		wd = ''
		sf = v.model.subfields
		th = for sk in v.model.subfields_sort
			hs = 'header ' + wd + ' subfield-' + sk
			if sk == '_meta' or sf[sk]?.offscreen
				hs  += ' offscreen hide'
			style = ""
			for key, value of sf[sk].style
				style += key+':'+value+ ';'
			'<th class="' + hs + (sf[sk].class && " " + sf[sk].class || "") + '" style="'+style+'" subfield-key="' + sk + '" >' + sf[sk].label + '</th>'
		tdmv = if v.view.readonly or @browser_use_html5() then '' else '<th class="delete-row" >&nbsp;</th>'
		tddl = if v.view.readonly then '' else '<th class="handle" >&nbsp;</th>'
		n.push('<thead>' + tdmv + th.join('') + tddl + '</thead>')

		n.push('<tbody>')
		if df
			for dfr in df
				n.push(@draw_row(k, v, dfr))
		else
			if v.view.readonly
				n.push('<tr><td colspan="' + th.length + '">No data entered.</td></tr>')
			else if v.view.template != 'empty' or (v.view.template == 'undefined')
				n.push(@draw_row(k, v, df))
		n.push('</tbody>')
		add_btn = '<div class="dsl-action-btn btn-add {class}" tabindex="0" ><div class="inner-cont"><span class="icon"/><p class="label">Add Row</p></div></div>'
		if not v.view.readonly
			n.push('<tfoot><tr><th colspan="' + (th.length + (if @browser_use_html5() then 1 else 2)) + '">' + add_btn.replace('{class}', 'add-row') + '</th></tr></tfoot>')

		n.push('</table>')
		html: n.join(''), data: data

	@draw_row: (k, v, dfr = null) ->
		tr = for sk in v.model.subfields_sort
			sv = v.model.subfields[sk]
			vl = ''
			if dfr?
				vl = dfr[sk] if dfr[sk]?
			else if sv.type is 'datenow' and not v.view.readonly
				vl = (new Date()).format('m/d/yy')
			else if sv.type is 'timenow' and not v.view.readonly
				vl = (new Date()).format('h:MMtt')
			else if sv.type is 'timestamp' and not v.view.readonly
				vl = (new Date()).format('m/d/yy h:MMtt')
			else if (v.view.control in ['checkbox'] or sv.type in ['checkbox']) and getType(sv.source) is 'array' and sv.source.length is 1 and sv.source[0] is 'yes'
				vl = 'yes'
			@draw_subfield k, v, sk, sv, v.model.subfields_sort.length, vl, dfr
		if v.view.readonly
			r = '<tr>' + tr.join('') + '</tr>'
		else
			if @browser_use_html5()
				mv = ''
			else
				mv = '<td class="handle"><i class="glyphicon glyphicon-resize-vertical"></i></td>'
			r = '<tr>' +
						mv +
						tr.join('') +
						'<td class="delete-row"><i></i></td>' +
					'</tr>'
		r

	@draw_select = (k, sk, sv, op, rd, wd, dfs='',fields='', static_filter='', vl='') ->
		data=sv.sourcefilter
		if getType(sv.source) is 'string'
			#'<input autocomplete="off" subfield="' + sk + '" subtype="' + sv.type + '" class="' + sk + ' s2_dynamic_query select2grid" type="hidden"' + (if rd then ' readonly="readonly" disabled="disabled"' else '') + ' value=' + vl + '>'
			'<input type="hidden" source ="' + sv.source + '" vl="' + vl + '" sk="' + sk + '" sf="' + static_filter + '" dynamicUpdate=false label="' + sv.label + '" subfield="'+ sk + '" subtype="select" data-fields="'+fields+'" '+dfs+' class="' + sk + ' initselect_dynamic select2grid dynamicFilter' + '"' + (if rd then ' readonly="readonly" disabled="disabled"' else '') + ' style="width:' + wd + ';" value="' + vl + '"/>'
		else
			'<select source ="' + sv.source + '" vl="' + vl + '" sk="' + sk + '" sf="' + static_filter + '" dynamicUpdate=false label="' + sv.label + '" subfield="'+ sk + '" subtype="select" data-fields="'+fields+'" '+dfs+' class="' + sk + (if @browser_use_html5() then '' else ' initselect select2grid dynamicFilter') + '"' + (if rd then ' readonly="readonly" disabled="disabled"' else '') + ' style="width:' + wd + ';">' + '<option value="">Select ' + sv.label + '</option>' + op.join('') + '</select>'

	@draw_checkbox = (sk, sv, op, rd, wd) ->
		'<div class="form-group" style="margin-bottom: 1px; margin-left: 5px;">
			<div class="controls">
				<div class="checkboxes ' + ( if sv.source?.length > 1 then "checkbox-group" else "checkbox-only" ) + '" multi="true">
				'+ op.join('') + '
				 </div>
			</div>
		</div>
		'

	@draw_subfield = (k, v, sk, sv, cnt, vl = '', dfr={}) ->
		st = getType(sv.source)
		stpl = (st is 'string') and (sv.source.substr(0, 2) is '{{') and (sv.source.substr(-2) is '}}')
		rd = v.view.readonly || sv.readonly 
		wdcnt =
			1: '8'
			2: '6'
			3: '4'
			4: '3'
		wd = ''
		if sk == '_meta' or sv?.offscreen
			wd += ' offscreen hide'
		if st in ['array', 'object'] and not ((sv.type in ['checkbox'] or v.view.control in ['checkbox']) and vl is 'yes')
			if st is 'array'
				ls = for arv in sv.source
					[arv, arv]
			else if st is 'object'
				ls = for ark, arv of sv.source
					[ark, arv]
			vltx = vl

			if sv.type in ['checkbox']
				op = for ar in ls
					sl = ''
					for value in vl
						if value is ar[0] or value is ar[1]
							sl = ' checked="checked"'
					'<label class="checkbox" style="float: left;">
					<input  '+sl+' subfield="' + sk + '" subtype="checkbox" autocomplete="off" type="checkbox" value="' + ar[0] + '" ' + (if rd then "disabled readonly" else "")+ '/>
					<span>' + ar[1] + '</span>
					</label>'
				inp =  @draw_checkbox(sk, sv, op, rd)
			else
				op = for ar in ls
					if (vl is ar[0]) or (vl is ar[1])
						sl = ' selected="selected"'
						vltx = ar[1]
					else
						sl = ''
					'<option value="' + ar[0] + '"' + sl + '>' + ar[1] + '</option>'
				if v.view.readonly
					inp = vltx
				else
					inp = @draw_select(k, sk, sv, op, rd, (if wdcnt[cnt]? then '100%' else '125px'))
		else if (st is 'string' and (not stpl))
			if v.view.readonly
				if dfr[sk + '_auto_name']?
					inp = dfr[sk + '_auto_name']
				if !$.trim(inp)
					inp = vl
				if getType(inp) is 'array'
					inp = inp.join(', ')
				if !inp
					inp = ''
			else
				inp = @draw_select(k, sk, sv, op, rd, (if wdcnt[cnt]? then '100%' else '200px'), '', '', '', vl)
		else if st is 'empty'
			# Handled by validators
		else
			ty = 'text'
			is_checked = '' # datenow, text, timenow, timestamp
			no_tabindex = ''
			if sv.type in ['date', 'time']
				ty = sv.type
			else if sv.type in ['checkbox']
				no_tabindex = ' tabindex="-1" '
				ty = sv.type
				if vl == 'yes'
					is_checked =  ' checked="checked"'
				else
					is_checked =  ''
			else if sv.type in ['datetime']
				ty = 'datetime-local'
			else if sv.type in ['int']
				ty = 'tel' if @browser_use_html5()
			else if sv.type in ['decimal']
				ty = 'number' if @browser_use_html5()
			else if stpl and $.trim(vl) is ''
				vl = DSLDraw.parse_template_field(sv.source, 'text')

			dFormat = 'YYYY-MM-DD'
			if not moment(vl, dFormat, true).isValid()
				dFormat = 'MM/DD/YYYY'

			if sv.type is 'date' and sv.readonly
				vl = moment(vl, dFormat).format('MM/DD/YYYY')
				dFormat = 'MM/DD/YYYY'

			if v.view.readonly
					inp = vl
			else
				# ee variable added for handling quotation marks in subfields
				if (typeof(vl) is 'string')
					ee = "'" + vl.replace(/'/gi, "&#39;") + "'"
				else
					ee = "'" + vl + "'"
				if ty is 'date'
					if not sv.readonly
						ee = moment(ee, dFormat).format('YYYY-MM-DD')
					else
						ee = vl
						ty = 'text'

				dyn = if sv.dynamic then 'subfield-dynamic="' + sv.dynamic + '"' else ''
				inp = '<input autocomplete="off" '+is_checked+no_tabindex+' subfield="' + sk + '" subtype="' + sv.type +
					'" ' + dyn +  ' class="' + sk + '" type="' + ty + '"' +
					(if rd then ' readonly="readonly" disabled="disabled"' else '') +
					' value=' + ee + '>'
				if sv.type in ['checkbox']
					inp = '<label class="checkbox" style="float: left;" tabindex="0" >
					'+inp+'
					<span></span>' +
					(
						if (sv.source?.length || 0) <= 1
							'<div class="slider-checkbox-container' + (if sv.source?.length == 1 then " slider-checkbox-container-toggled" else "") +
  							(if rd then ' checkbox-only-disabled' else '') +
							'">' + 
								'<div class="slider-checkbox' + (if sv.source?.length == 1 then " slider-checkbox-toggled" else "") + '"></div>' +
							'</div>'
						else
							''
					) +
					'</label>'
					inp =  @draw_checkbox(sk, sv, [inp], rd)
				if sv.type in ['area']
					inp = '<textarea autocomplete="off" '+is_checked+no_tabindex+' subfield="' + sk + '" subtype="' + sv.type +
					'" ' + dyn +  ' class="' + sk + '" type="' + ty + '"' +
					(if rd then ' readonly="readonly" disabled="disabled"' else '') +
					'>' + vl + '</textarea>'


		style = ""
		for key, value of sv.style
			style += key+':'+value+ ';'
		'<td style="'+style+'" class="' + wd + ' subfield-' + sk + '" subfield-key="'+sk+'">' + inp + '</td>'

	@render_select2_dynamic: ($sfld, gdata) ->
		sk = $sfld.attr('subfield')
		index = $sfld.closest('tr').index()
		val = gdata.dd.preset?[gdata.k]?[index]?[sk] || null
		$sfld.val(val)
		df = val
		v = gdata.v
		k = gdata.k
		sv = v.model.subfields[sk]
		so = null
		if(getType(sv.source) is 'string' and sv.type)
			id = $sfld.data('id') || window.next_id('fld')
			so =  FieldSelect.load_options(gdata.form, 'form', id, k, v, df, sk, sv) # FieldSelect.draw(gdata.form, 'form', id, k, v, )
			so.allowClear = true
			so.placeholder = if sv.readonly then '' else 'Select ' + sv.label
			so.dropdownAutoWidth = false
			so.initValue = df
			so.debug = true
			s2data =
				selopt: so
				fieldtype: 'select'
				manage: false
			$sfld.data(s2data)
			if so.initSelection?
				$sfld.addClass 'activeselect2field'
				$sfld.removeClass('initselect_dynamic').select2(so)
				$sfld.select2('readonly', true) if $sfld.prop('readonly') is 'readonly'
				$sfld.select2('disabled', true) if $sfld.prop('disabled') is 'disabled'

	@load_all: (fa) ->
		ref = @
		fa.find('.table-sortable').each ->
			t = $(this)
			t.find('tbody').sortable t.data('sortopt')
			t.data('sorton', true)
			t.find('.initselect').removeClass('initselect').select2()
			gselects = t.find('.initselect_dynamic')
			gselects.each () ->
				$sfld = $(this)
				gdata = t.data()
				ref.render_select2_dynamic($sfld, gdata)
			return
		load_options = @load_options
		fa.find('input[subfield-dynamic]').each (_, d) ->
			$(d).typeahead(
				source: load_options($(d).attr("subfield-dynamic"))
				minLength: 0
				autoSelect: true
				fitToElement: true
				showHintOnFocus: 'all'
				blurOnTab: true
				changeInputOnMove: false
				selectOnBlur: false
				items: 'all'
				afterSelect: (value) ->
					setTimeout () ->
						$(d).blur();
						$(d).data('typeahead').focused = false;
						return
			)
		return

	@unload_all: (fa) ->
		fa.find('.table-sortable').each ->
			t = $(this)
			if t.data('sorton')
				t.data('sorton', false)
				t.find('tbody').sortable('destroy')
			return
		fa.find('.select2grid').loop (sl) -> # destroy doesn't work without loop/each
			sl.select2('destroy')
			return
		return

	@load_options: (svdyn) ->
		dt = []
		aj = Ajax.async
			url: "/form/" + svdyn +  "/?fields=min&sort=auto_name&limit=1000&filter=auto_name:!.."
		aj.done (data, status, xhr) =>
			for o in data
				rf = o.auto_name.split(" (")
				dt.push({name: rf[0]})
			return
		aj.fail (xhr, status, data) =>
			console.log data
		dt

	@validate: (d) ->
		false

	@value: (d, n) ->
		if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		gd = []
		v = d.data('v')
		d.find('tbody tr').each ->
			gr = {}
			nts = false
			$(this).find('td>.checkbox-group').each ->
				checkboxes_count = $(this).find('input[type=checkbox]').length
				if checkboxes_count > 0
					if checkboxes_count > 1
						checkbox_arr = []
						$(this).find('input[type=checkbox]').each ->
							t = $(this)
							if t.is(":checked")
								nts = true
								tk = t.attr('subfield')
								return if not tk
								tv = $.trim(t.val())
								checkbox_arr.push(tv)
								gr[tk] = checkbox_arr
					else
						t = $(this).find('input[type=checkbox]')
						tk = t.attr('subfield')
						return if not tk
						if t.is(":checked")
							gr[tk] = "yes"
						else
							gr[tk] = "no"

			$(this).find('td input, td select, td textarea').each ->
				t = $(this)
				tk = t.attr('subfield')
				return if not tk or v.model.subfields[tk]?.type is 'checkbox'

				tv = $.trim(t.val())
				if !(v.model.subfields[tk]?.type in ['datenow', 'timenow', 'timestamp']) and tv isnt ''
					nts = true
				source = v.model.subfields[tk].source
				st = getType(source)
				stpl = (st is 'string') and (source.substr(0, 2) is '{{') and (source.substr(-2) is '}}')
				if st is 'string' and not stpl
					d = t.select2('data')
					dtype = getType(d)
					if dtype is 'array'
						gr[tk] = []
						gr[tk + "_auto_name"] = []
						for i in d
							gr[tk].push i.id
							gr[tk + "_auto_name"].push i.text
					else
						gr[tk] = if d? then d.id else ""
						gr[tk + "_auto_name"] = if d? then d.text else ""
				else
					gr[tk] = tv

			$(this).find('td').each ->
				t = $(this)
				if(t.children().length is 0) #if readonly, no children is pushed instead the value is simply placed in td tag hence this check
					nts = true
					cls = t.attr('class')
					return if _.isEmpty(cls) or cls?.length == 0 or cls?.split(' ')?.length < 3 or cls?.split('-')?.length < 2
					tk = t?.attr('class')?.split(' ')[2]?.split('-')[1]
					if !tk
						tk = t.attr('subfield-key')
					gr[tk] = t.html()

			gd.push gr if nts
		gd

	@value_set: (d, n) ->
		# do not need to set this dynamically
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		console.error('FieldGrid.readonly not Supported')
		return true
