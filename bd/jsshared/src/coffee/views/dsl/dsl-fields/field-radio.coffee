class FieldRadio extends DSLFields

	@clear: (d) ->
		d.find('.active').removeClass('active')
		return

	@draw: (form, view, id, k, v, df, mode) ->
		droplen = if isBrowserMobile() then 32 else 60
		if $.map(v.model.source, (v,i) -> [v]).join('  ').length > droplen
			d = FieldSelect.draw(form, view, id, k, v, df, mode)
			return d
		ty = getType(v.model.source)
		if v.model.source_order and ty is 'object'
			sorted = {}
			for vv in v.model.source_order
				sorted[vv] = v.model.source[vv]
			v.model.source = sorted
		btn = []
		dftx = $.trim(df)
		for kk,vv of v.model.source
			cls = []
			cls.push('enabled') if not v.view.readonly
			if (ty is 'array') and (vv is df or vv is dftx)
				cls.push('active') # do not want active AND disabled
			else if (ty is 'object') and (kk is df or kk is dftx)
				cls.push('active') # do not want active AND disabled
			else if v.view.readonly
				cls.push('disabled')
			attr =
				class: 'btn ' + cls.join(' ')
				value: if ty is 'array' then escapeHTML(vv) else kk
				tabindex: if (v.view.readonly or v.view.offscreen) then -1 else 0
			prop = if v.view.readonly then 'readonly' else ''
			btn.push('<label ' + @attr(attr) + ' ' + prop + '><input autocomplete="off" type="text">' + vv + '</label>')

		attr =
			id: id
			class: 'btn-group radios '

		d =
			html: '<div ' + @attr(attr) + '>' + btn.join('') + '</div>'
			data:
				fieldtype: 'radio'

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		d.find('.active:first').attr('value') or ''

	@value_set: (d, n) ->
		d.find('.active').removeClass('active')
		d.find('.btn').addClass('disabled')if d.find('.btn').attr('readonly')
		d.find('.btn').filter(-> $(@).attr('value') is n).addClass('active').removeClass('disabled')  if n isnt ''
		d.change()
		n

	@visible: (d) ->
		@is_visible(d)
	
	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d) 
		if ro
			d.find("label").removeClass("enabled").addClass("disabled").attr("readonly","readonly")
			d.find("label.active").removeClass("disabled")
		else
			d.find("label").removeClass("disabled").addClass("enabled").removeAttr("readonly")
		return true