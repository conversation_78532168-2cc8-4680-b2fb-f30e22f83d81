class FieldPaycard extends DSLFields

	###
		sample authnet response: {
			"opaqueData": {
				"dataDescriptor": "COMMON.ACCEPT.INAPP.PAYMENT",
				"dataValue": "eyJjb2RlIjoiNTBfMl8wNjAwMDUzNUE1OTkzREQ1NEM1NzY0OTgwNTZDQzY5MEVBRjY5MTU3RjBEQThEMjU2N0EyQkUwMUNBNzQ5QkY0ODRDMTgyMjRGN0IzMEE4REI2MUJDQUI1NDMxMTZCNzkwOUM3OUMwIiwidG9rZW4iOiI5NTA3OTE3MzE1NTg5OTYzOTA0NjA0IiwidiI6IjEuMSJ9"
			},
			"messages": {
				"resultCode": "Ok",
				"message": [{
				"code": "I_WC_01",
				"text": "Successful."
				}]
			},
			"encryptedCardData": {
				"cardNumber": "XXXXXXXXXXXX1111",
				"expDate": "12/22",
				"bin": "411111"
			},
			"customerInformation": {
				"Ellen": "",
				"Johnson": ""
			}
		}

		sample braintree response: {
			"nonce": "tokencc_bd_c6rqp2_2kp2b5_kz95d5_gnhmqg_kg2",
			"details": {
				"cardholderName": null,
				"expirationMonth": "10",
				"expirationYear": "2029",
				"bin": "411111",
				"cardType": "Visa",
				"lastFour": "1111",
				"lastTwo": "11"
			},
			"type": "CreditCard",
			"description": "ending in 11",
			"binData": {
				"prepaid": "Unknown",
				"healthcare": "Unknown",
				"debit": "Unknown",
				"durbinRegulated": "Unknown",
				"commercial": "Unknown",
				"payroll": "Unknown",
				"issuingBank": "Unknown",
				"countryOfIssuance": "Unknown",
				"productId": "Unknown"
			}
		}

		sample stripe response: {
			"id": "tok_1N3T00LkdIwHu7ixt44h1F8k",
			"object": "token",
			"card": {
				"id": "card_1N3T00LkdIwHu7ixRdxpVI1Q",
				"object": "card",
				"address_city": null,
				"address_country": null,
				"address_line1": null,
				"address_line1_check": null,
				"address_line2": null,
				"address_state": null,
				"address_zip": null,
				"address_zip_check": null,
				"brand": "Visa",
				"country": "US",
				"cvc_check": "unchecked",
				"dynamic_last4": null,
				"exp_month": 5,
				"exp_year": 2026,
				"fingerprint": "mToisGZ01V71BCos",
				"funding": "credit",
				"last4": "4242",
				"metadata": {},
				"name": null,
				"tokenization_method": null,
				"wallet": null
			},
			"client_ip": "**********",
			"created": **********,
			"livemode": false,
			"type": "card",
			"used": false
		}
	###

	@clear: (d) ->
		d.val('')
		return

	@label: (v, df) ->
		label = ''
		switch v.view.reference
			when 'authnet'
				if df?.encryptedCardData
					dt = df.encryptedCardData
					lb = []
					lb.push 'Card x' + dt.cardNumber.substr(-4) if dt.cardNumber
					if dt.expDate
						lb.push '(Exp: ' + dt.expDate + ')'
					label = lb.join ' '
			when 'btree'
				if df?.details
					dt = df.details
					lb = []
					lb.push dt.cardType if dt.cardType
					lb.push 'x' + dt.lastFour if dt.lastFour
					if dt.expirationMonth and dt.expirationYear
						lb.push '(Exp: ' + dt.expirationMonth + '/' +  dt.expirationYear.substr(-2) + ')'
					label = lb.join ' '
			when 'stripe'
				if df?.card
					dt = df.card
					lb = []
					lb.push dt.brand if dt.brand
					lb.push 'x' + dt.last4 if dt.last4
					if dt.exp_month and dt.exp_year
						lb.push '(Exp: ' + dt.exp_month + '/' +  (dt.exp_year + "").substr(-2) + ')'
					label = lb.join ' '
		return label

	@draw: (form, view, id, k, v, df, mode) ->
		cls = ' ' + v.view.class

		label = @label(v, df)

		if v.model.type is 'json' and getType(df) is 'object'
			df = JSON.stringify(df)

		if v.view.readonly
			d =
				html:
					'<div id="' + id + '" class="' +
						$.trim('' + cls) + '" readonly="readonly">' + (label or '-') +
					'</div>'
				data:
					fieldtype: 'paycard'
		else
			if df and label
				cardbtn = 'X'
			else
				cardbtn = 'New Card'

			d =
				html:
					'<textarea autocomplete="off" id="' + id + '" class="' +
						$.trim('' + cls) + '"' +
						' tabindex="-1">' + df + '</textarea>' +
						'<span class="paycard-label "' + $.trim('' + cls) + '"">' + label + '</span>' +
						'<div class="paycard-btn paycard-' + v.view.reference + '" forfield="' + k + '" forid="' + id + '">' + cardbtn + '</div>'
				data:
					fieldtype: 'paycard'

	@form_authnet: ->
		if App.version.nes.is_prod
			jslib = "https://js.authorize.net/v3/AcceptUI.js"
		else
			jslib = "https://jstest.authorize.net/v3/AcceptUI.js"

		"""
<div id="authnet_form">
	<form id="paymentForm" action="#" method="POST">
		<button type="button"
			class="AcceptUI"
			data-billingAddressOptions='{"show":true, "required":false}'
			data-apiLoginID="#{App.version.nes.payment.authnet_tn}"
			data-clientKey="#{App.version.nes.payment.authnet_id}"
			data-acceptUIFormBtnTxt="Submit"
			data-acceptUIFormHeaderTxt="Card Information"
			data-paymentOptions='{"showCreditCard": true, "showBankAccount": true}'
			data-responseHandler="responseHandler">Save Card</button>
	</form>
	<script type="text/javascript"
		src="#{jslib}"
		charset="utf-8">
	</script>
	<script>
		setTimeout(function() {
			$("#authnet_form .AcceptUI").click();
		}, 1500);

		function responseHandler(response) {
			window.paycardHandler(response);
		}
	</script>
</div>
"""

	@form_btree: ->
		"""
<div id="dropin-container"></div>
<button id="submit-button" class="btn btn-tertiary">Save Card</button>
<script>
	var button = document.querySelector('#submit-button');
	braintree.dropin.create({
		authorization: '#{App.version.nes.payment.btree}',
		selector: '#dropin-container'
	}, function (err, instance) {
		button.addEventListener('click', function () {
			instance.requestPaymentMethod(function (err, payload) {
				if(payload) {
					window.paycardHandler(payload);
				}
			});
		})
	});
</script>
"""

	@form_stripe: ->
		"""
<form id="stripe_form">
	<div id="card-element"></div>
	<button class="btn btn-tertiary" type="submit">Save Card</button>
</form>
<script>
	const stripe = Stripe('#{App.version.nes.payment.stripe}');
	const elements = stripe.elements({
		currency: 'usd',
	});
	const card = elements.create('card', {
		disableLink: true,
	});
	card.mount('#card-element');

	$('#stripe_form').on('submit', async (event) => {
		event.preventDefault();
		const { token, error } = await stripe.createToken(card);
		if (token) {
			window.paycardHandler(token);
		}
	});
</script>
"""

	@get_card: ->
		t = $(this)
		d = t.siblings("textarea")

		# clear if card exists
		if t.html() is 'X'
			DSLFields.value_set d, ''
			d.siblings('.paycard-label').html('')
			d.siblings('.paycard-btn').html('New Card')
			return

		v = d.data('v')
		html = FieldPaycard['form_' + v.view.reference]()

		if v.view.reference is 'authnet'
			# this gets overwritten each click
			window.paycardHandler = (resp) =>
				DSLFields.value_set d, JSON.stringify(resp)
				lb = FieldPaycard.label(v, resp)
				d.siblings('.paycard-label').html(lb)
				d.siblings('.paycard-btn').html('X')
				$("#authnet_form").remove()

			$("#authnet_form").remove()
			d.after(html)
		else
			fopt =
				action: 'FlyoutHTMLView'
				label: 'Add ' + v.view.label
				html: html

			# this gets overwritten each click
			window.paycardHandler = (resp) =>
				fopt.flyout.views.flyout_root.cancel()
				DSLFields.value_set d, JSON.stringify(resp)
				lb = FieldPaycard.label(v, resp)
				d.siblings('.paycard-label').html(lb)
				d.siblings('.paycard-btn').html('X')

			Flyout.open fopt
		return

	@load_all: (fa) ->
		# only need to do this once per site-load
		if not @jsloaded
			@jsloaded = true
			loadScript "https://js.braintreegateway.com/web/dropin/1.43.0/js/dropin.js"
			loadScript "https://js.stripe.com/v3/"

		fa.find('.paycard-btn').on('click', @get_card)
		return

	@unload_all: (fa) ->
		fa.find('.paycard-btn').off('click')
		return

	@validate: (d) ->
		false

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_auto_name: ->
		null

	@value_get: (d) ->
		d.val()

	@value_set: (d, n) ->
		d.val(n).change()
		n

	@visible: (d) ->
		@is_visible(d)
