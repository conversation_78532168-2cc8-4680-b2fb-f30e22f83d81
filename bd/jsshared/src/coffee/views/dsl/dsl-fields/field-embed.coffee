class FieldEmbed extends DSLFields

	@clear: (d) ->
		return

	@draw: (form, view, id, k, v, df, mode) ->
		
		readonly = v.view.readonly

		attr =
			id: id
			class: 'embedded_table field_' + k + " " + if v.view.class then v.view.class else '' 
		attr.readonly = 'readonly' if readonly
		attr.disabled = 'disabled' if readonly
		attr.tabindex = if v.view.offscreen || v.view.readonly then '-1' else '0'
		attr.multiple = 'multiple' if v.model.multi

		d =
			html: '<div ' + @attr(attr) + '>Loading...</div>'
			data:
				fieldtype: 'embedded_table'
				df: df
				id: id
				v: v
				readOnly: readonly
				manage: false

	@value_auto_name: ->
		null

	@load_all: (fa) ->
		fa.find('.embedded_table').each ->
			d = $(this)
			props = d.data()
			props.dslFieldRef = (helper) ->
				d.data('helper', helper)
			loadComponent('DSLFieldEmbed', this, props.id, props )
		return

	@unload_all: (fa) ->
		fa.find('.embedded_table').each ->
			d = $(this)
			fldid = d.data('id')
			unloadComponent('DSLFieldEmbed', fldid)
			return
		return

	@validate: (d) ->
		if !d.data('helper')
			return false
		return d.data('helper').validateField()

	@value: (d, n) ->
		if n?
			@value_set d, n
		else if @visible(d)
			@value_get d
		else
			null

	@value_get: (d) ->
		if d.data('helper')
			return d.data('helper').getValue()
		df = d.data('df')
		pendingValue = d.data('pendingValue')
		if pendingValue
			return pendingValue
		return df
	
	@get_by_id: (d, id) ->
		if d.data('helper')
			return d.data('helper').getById(id)
		d =	
			data: null
			state: 'pending'
		return d

	@value_set: (d, n) ->
		if d.data('helper')
			d.data('helper').setValue(n)
		else
			d.data('pendingValue', n)
		return FieldEmbed.value_get(d)

	@visible: (d) ->
		@is_visible(d)

	@readonly: (d, ro) ->
		return false if not @can_make_readonly(d)
		if d.data('helper')
			d.data('helper').updateReadOnly(ro)
			return true
		return false
