class DSLFields

	@field_types: ['area', 'barcode', 'checkbox', 'datetime', 'esign', 'file', 'grid', 'input', 'link', 'paycard', 'radio', 'select', 'subform', 'embedded_table']

	@action: (c, d, n) ->
		if d and d.data?
			dd = d.data('dd')
			ft = d.data('fieldtype')
			f = @field(ft)
			if f and f[c]?
				v = f[c].call(f, d, n)
				@hopon_form_buss(d) if c is 'value_set' and !['barcode', 'link', 'paycard', 'subform'].includes(ft)
				return v
			else
				null
		else
			null
	
	@hopon_form_buss: (d, event_info) ->
		dd = d.data('dd')
		if dd and dd.formBuss
			dd.formBuss.hopOnFormBuss(d, event_info)

	@is_subform_field_visible: (formmap, parent_form, k) ->
		fshm = formmap[parent_form]?.shmap or null
		return true if !fshm
		if fshm?.fields
			return @is_field_visible(fshm.fields, k)
		return true

	@is_visible: (d) ->
		if d.attr('id')?.startsWith?('fnd_')
			return true
		return false if !d or !d.data?
		v = d.data()
		visible = false
		dd = v.dd
		return false if not dd
		if dd?.constructor?.name == 'DSLDrawFind'
			return true
		sec = v.section
		sh = dd.shmap
		return false if !@is_section_visible(sh.sections, sec)
		return @is_field_visible(sh.fields, v.k)

	@is_section_visible: (shmap, sec) ->
		sh = shmap[sec]
		return true if !sh or sh.current
		return false

	@is_field_visible: (shmap, k) ->
		sh = shmap[k]
		return true if !sh or sh.current
		return false

	@attr: (attr) ->
		$.map(attr, (v, k) -> k + '="' + v + '"').join(' ')

	@browser_use_html5: ->
		isBrowserMobile() or isBrowseriOS()

	@clear: (d) ->
		@action 'clear', d

	@field: (type = 'input') ->
		if type is 'area'
			FieldArea
		else if type is 'barcode'
			FieldBarcode
		else if type is 'checkbox'
			FieldCheckbox
		else if type is 'datetime'
			FieldDatetime
		else if type is 'esign'
			FieldESign
		else if type is 'file'
			FieldFile
		else if type is 'grid'
			FieldGrid
		else if type is 'input'
			FieldInput
		else if type is 'link'
			FieldLink
		else if type is 'paycard'
			FieldPaycard
		else if type is 'radio'
			FieldRadio
		else if type is 'select'
			FieldSelect
		else if type is 'subform'
			FieldSubform
		else if type is 'embedded_table'
			FieldEmbed

	@filter_url: (form, k, v, dd = null, qt = '', sk, sv, sfr = {}) ->
		config = getFormConfig(v.model.source)
		fetch_fields = ""
		if config?.selectConfig?.fields
			fetch_fields = config.selectConfig.fields
		qp = {}
		pform_search = ''
		qa = 'auto_name'
		if_blank = '!..'

		source = null
		sourceid = null
		sourcefilter = null
		ftype = 'text'

		if sk?
			source = if sv.source != null then sv.source else sv.dynamic
			sourceid = if sv.sourceid then sv.sourceid else "id"
			sourcefilter = sv.sourcefilter
		else
			source = if v.model.source != null then v.model.source else (v.model.dynamic.source or v.model.dynamic?.query)
			sourceid = v.model.sourceid
			sourcefilter = v.model.sourcefilter
			ftype = v.model.type
		if getType(source) is 'string' and source.includes('{')
			source = string_template(source, {}, dd)
		always_apply = getSourceFilterValues(form, v)
		if v.view.findunique
			qa = k
			qu = '/unique/' + form + '/' + k + '/?sort=' + qa + '&' + DSLDraw.default_params(true, MAX_FILTER_ROWS)
		else
			qf = if sourceid in ['id', 'code', 'mcr_ref'] then 'min' else 'all'
			query_code = v.model.query or v.model.dynamic?.query
			qf = if (v.view.class.includes("select_prefill") or (query_code and query_code isnt '')) then 'all' else qf
			field_source = source
			if fetch_fields and qf isnt 'all'
				qf = fetch_fields
			qu = '/form/' + field_source + '/?fields=' + qf + '&sort=' + qa + '&' + DSLDraw.default_params(true, MAX_FILTER_ROWS)
			qu = if (query_code and query_code isnt '') then '/query/' + query_code + '/?' else qu
			if $.trim(sourceid) isnt '' and sourceid isnt 'id' and if_blank isnt ''
				qp[sourceid] = if_blank

		if qt isnt ''
			qp[qa] = (if App.company.filter_wc is 'Yes' or v.view.findwildcard then '*' else '') + qt.replace(/\s/g, '*') + '*'
		else if if_blank isnt ''
			qp[qa] = if_blank
		# handle static/dynamic filtering
		# NOTE: while this works on fields with multi=true
		#       it does not handle linked sourcefields with multi=true
		if getType(sourcefilter) is 'object'
			for qk,qv of sourcefilter
				tqvs = getType(qv.static)
				tqvd = getType(qv.dynamic)
				vf = null
				if tqvs in ['array', 'number', 'string']
					vf = qv.static
				if tqvd is 'string'
					if qv.dynamic
						if qv.dynamic.substr(0, 2) is '{{' and qv.dynamic.substr(-2) is '}}'
							vf = DSLDraw.parse_template_field(qv.dynamic, ftype)
						else if dd
							vv = qv.dynamic.replace(/[{}]/g, "")
							vf = sfr[vv] || dd.value_field(vv)
							if not vf and vv is 'patient_id'
								vf = dd.preset.patient_id
				if $.trim(vf) isnt ''
					if getType(qv.source) is 'array' and (not (vf.toLowerCase() in qv.source.join('|').toLowerCase().split('|')))
						vf = if $.trim(qv.default) is '' then 'null' else qv.default
					if qk is 'parent_id' or qk is 'parent_form'
						pform_search = pform_search + "&#{qk}=#{vf}"
					else
						qp[qk] = vf
		if(!_.isEmpty(always_apply))
			for fk, fv of always_apply
				qp[fk] = fv
		if DSL[source]?.fields['site_id'] and !qp['site_id']
			qp['site_id'] = '!1000'
		# source url filter
		qu + (if _.isEmpty(qp) then '' else '&') + joinFilters(qp) + pform_search

	@load: (fa) ->
		for f in @field_types
			@field(f).load_all(fa)
		return

	@load_all: (fa) ->
		return

	@unload: (fa) ->
		for f in @field_types
			@field(f).unload_all(fa)
		return

	@unload_all: (fa) ->
		return

	@validate: (d) ->
		@action 'validate', d

	@value: (d, n) ->
		@action 'value', d, n

	@value_auto_name: (d) ->
		@action 'value_auto_name', d

	@value_get: (d) ->
		@action 'value_get', d

	@readonly: (d, ro) ->
		@action 'readonly', d, ro

	@can_make_readonly: (d) ->
		v = d.data('v')
		return false if v.view.readonly or v.view.offscreen
		true

	@value_set: (d, n) ->
		@action 'value_set', d, n

	@visible: (d) ->
		@action 'visible', d