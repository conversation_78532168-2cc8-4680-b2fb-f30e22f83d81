
DSLFx.Validators.PopulateEventEndTime = (form, dd, vld, f, k) ->
	dd.handler?.populate_event_end_time?(form, dd, vld, f, k)
	return

DSLFx.Validators.PopulateEventEndDate = (form, dd, vld, f, k) ->
	dd.handler?.populate_event_end_date?(form, dd, vld, f, k)
	return

class Schedule_eventHandlerView extends CRView

	nodes: =>
		@parent.field_nodes

	populate_event_end_date: (form, dd, vld, f, k) ->
		return if @mode is 'read'
		reoccur = DSLFields.value(@parent.field_nodes.series)
		sd = dd.value_field('event_start_date')
		cal_id = dd.value_field('calendar_id')
		return if not sd
		if not cal_id
			@add_end_date_using_company(sd)
			return
		g = Ajax.async
			url: '/form/calendar/' + cal_id
		g.done (data, status, xhr) =>
			if data.length > 0 and data[0].default_event_span
				@increment_end_date_by(sd, data[0].default_event_span)
			else
				@add_end_date_using_company(sd)
		g.fail (xhr, status, data) ->
			Auth.fail 'Unable to load Modules.'
			return

	add_end_date_using_company: (st) ->
		h = Ajax.async
			url: '/form/company/?limit=1&fields=list&sort=-id&page_number=0'
		h.done (comp, status, xhr) =>
			if comp.length > 0 and comp[0].default_event_span
				@increment_end_date_by(st, comp[0].default_event_span)
			else
				@increment_end_date_by(st, 12)
				console.log("No Company Configuration Found")
		h.fail (xhr, status, comp) ->
			console.log("Request Failed Company Config")
	
	increment_end_date_by: (st, inc) ->
		et = moment(st, 'MM/DD/YYYY').add(inc, 'month').format('MM/DD/YYYY') #check from doc
		@parent.value_field('event_end_date', et)

	populate_event_end_time: (form, dd, vld, f, k) ->
		return if @mode is 'read'
		st = dd.value_field('event_start_time')
		cal_id = dd.value_field('calendar_id')
		return if not st

		if not cal_id
			@add_end_time_using_company(st)
			return

		g = Ajax.async
			url: '/form/calendar/' + cal_id
		g.done (data, status, xhr) =>
			if data.length > 0 and data[0].default_event_duration
				@increment_end_time_by(st, data[0].default_event_duration)
			else
				@add_end_time_using_company(st)
		g.fail (xhr, status, data) ->
			Auth.fail 'Unable to load Modules.'
			return

	add_end_time_using_company: (st) ->
		h = Ajax.async
			url: '/form/company/?limit=1&fields=list&sort=-id&page_number=0'
		h.done (comp, status, xhr) =>
			if comp.length > 0 and comp[0].default_event_duration
				@increment_end_time_by(st, comp[0].default_event_duration)
			else
				@increment_end_time_by(st, 240)
				console.log("No Company Configuration Found")
		h.fail (xhr, status, comp) ->
			console.log("Request Failed Company Config")
	
	increment_end_time_by: (st, inc) ->
		et = moment('01/01/2000 ' + st, 'MM/DD/YYYY hh:mm a').add(inc, 'minutes').format('hh:mm a')
		@parent.value_field('event_end_time', et)

	handle: (dfd) ->
		dfd.resolve()
		if @parent?.preset?.event_series_id
			sl = @nodes()['series']
			sl.find("input").attr("disabled", true)
			sl.find("label").removeClass("enabled").addClass("disabled")
		if !['add', 'addfill'].includes(@mode)
			sl = @nodes()['event_start_date']
			sl.find("input").attr("disabled", true)
			sl.find("label").removeClass("enabled").addClass("disabled")
		return

class ScheduleCardFormBaseView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		# @ddf.options.form = 'schedule_event_series'
		form_data = @ddf.get_formdata()

		today = moment().format('MM/DD/YYYY')
		eff_date = moment(form_data?.effective_start_date, 'MM/DD/YYYY')
		final_date = ""

		if eff_date.isAfter(today)
			final_date = eff_date
		else if eff_date.isBefore(today)
			final_date = today

		final_date = moment(final_date).format('MM/DD/YYYY')
	
		is_yes = false
		ref = @
		if form_data.event_series_id and form_data.event_series_id != ""
			data = Ajax.sync
				url: '/form/schedule_event?filter=dirty:Yes&filter=effective_start_date:' + final_date  + '..&filter=event_series_id:' + form_data.event_series_id
				type: 'GET'
			if data.length > 0
				message = '\n'
				data.forEach((item, index) ->
					message += item.patient_id_auto_name + " has sechdule on " + item.effective_start_date + "\n"
				)
				prettyYesNo 'Update following events?', 'Your changes will be applied to following overriden events. Do you wish to proceed' + message, ->
					ref.confirmed(ref)
					# fxtrue()
					return
			else
				ref.confirmed(ref)
		else
			ref.confirmed(ref)

	confirmed: () ->
		@ddf.options.form_override_url =  '/schedule/event'
		form_data = @ddf.get_formdata()
		dfd = @ddf.save()
		dfd.done (rt) =>
			if rt? and rt.record? and rt.values?
				if @parent.options.isFlyout or  @parent.parent.tabid is 'flyout_root'
					@parent.save(rt.values, rt.record, @dom, @domcols, @record is null, rt.rowhtml)
					return
				@record = rt.record
				@parent.mode('read', @record)
				@parent?.parent?.onSaved?(rt, @parent.tabData, @parent)
			return
		return


# extend from this class to make any form require
# all the fields to be filled, including all the subfroms
class ScheduleCardFormView extends ScheduleCardFormBaseView
