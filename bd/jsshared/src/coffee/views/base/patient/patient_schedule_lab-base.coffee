DSLFx.Validators.PastScheduledValidate = (form, dd, vld, f, k) ->
	dd.handler?.backdated_schedule?(form, dd, vld, f, k)
	return

DSLFx.Validators.LoadNPINumber = (form, dd, vld, f, k) ->
	dd.handler?.load_npi?(form, dd, vld, f, k)
	return

DSLFx.Validators.PastScheduleEndDate = (form, dd, vld, f, k) ->
	dd.handler?.backdated_end_date?(form, dd, vld, f, k)
	return

class Patient_schedule_labHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	sh_reoccur: (v) ->
		return if not @parent.field_nodes?.reoccur?
		if v? and v is true and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'No'
		else if (v is null or v is false) and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'Yes'
		return

	backdated_schedule: (form, dd, vld, f, k) ->
		return if not @parent.field_nodes?.reoccur?
		forms = @parent.options.wrapper.subforms.formmap
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)
		schedule_start = if DSLFields.value(@parent.field_nodes.schedule_start) is null then "" else DSLFields.value(@parent.field_nodes.schedule_start)

		now = moment(new Date())
		field_date =  moment(new Date(schedule_start))

		@schedule_end(form, dd, vld, f, k)

		if field_date.diff(now,'days') < 0 and forms?.patient_schedule_lab?.field_nodes?.schedule_start
			@parent.value_field 'reoccur', 'No'
			DSLFields.value_set forms?.patient_schedule_lab?.field_nodes?.schedule_start , ''
			@sh_reoccur(true)
		else
			@sh_reoccur(false)

		return

	backdated_end_date: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		schedule_start = if DSLFields.value(@parent.field_nodes.schedule_start) is null then "" else DSLFields.value(@parent.field_nodes.schedule_start)
		appointment_end = if DSLFields.value(@parent.field_nodes.appointment_end) is null then "" else DSLFields.value(@parent.field_nodes.appointment_end)

		if moment(appointment_end).diff(moment(schedule_start),'days') < 0 and not _.isEmpty(schedule_start) and not _.isEmpty(appointment_end)
			DSLFields.value_set forms?.patient_schedule_lab?.field_nodes?.appointment_end , ''

	schedule_end: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		schedule_start = DSLFields?.value(@parent.field_nodes?.schedule_start)
		schedule_end = forms?.patient_sch_lab_rpt_appt?.value_field('schedule_end')
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)

		start = moment(new Date(schedule_start))
		end = moment(new Date(schedule_end))

		if schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'Yes'
			DSLFields.value @parent.field_nodes?.schedule_start, ''
		if  schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'No'
			DSLFields.value forms?.patient_sch_lab_rpt_appt?.field_nodes?.schedule_end, ''

	load_npi: (form, dd, vld, f, k) ->
		phy_id = DSLFields.value(@parent.field_nodes['ordered_by'])
		return if not phy_id

		tc = Ajax.async
			url: '/form/physician/' + phy_id 
		tc.done (data, status, xhr) =>
			phy = data[0]
			@parent.value_field 'npi_number', phy.npi_number, true, true

		tc.fail (xhr, status, data) =>
			if data.error?
				console.log(data.error)
				return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_labHandlerView extends Patient_schedule_labHandlerBaseView

class Patient_schedule_labCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_labCardView extends Patient_schedule_labCardBaseView