
DSLFx.Validators.LabValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_lab?(dd.value_field("lab"))
	return


class Patient_labresultHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	validate_lab: (id) ->
		return if (@mode is 'read') or (@parent.preset?.lab is parseInt(id)) or (not id)
		tc = Ajax.async
			url: '/form/labresult/'+ id
		tc.done (data, status, xhr) =>
			@parent.value_field 'type', data[0].type, true
			@clear_components =>
				@add_components(data[0])
			return
		tc.fail (xhr, status, data) =>
			dfd.reject 'Cannot load lab components data. Please try again.'
			return
		return

	nodes: =>
		@parent.field_nodes

	clear_components: (fxtrue) =>
		d = @nodes()['components']
		if DSLFields.value(@nodes()['components']).length is 0
			DSLFields.clear(d)
			fxtrue()
		else
			prettyYesNo 'Replace all items?', 'Are you sure you want to replace all lab components?\n\nNOTE: This action cannot be reversed!', ->
				DSLFields.clear(d)
				fxtrue()
				return
		return

	add_components: (data) ->
		for c in data.components
			tc = Ajax.async
				url: '/form/labcomponent/'+ c
			tc.done (data, status, xhr) =>
				@add_component(data)
		return

	add_component: (data) ->
		d = @nodes()['components']
		r = {}
		r.component = data[0].short_name
		r.unit = data[0].unit
		r.range = data[0].norm_low + r.unit + " - " + data[0].norm_high + r.unit
		FieldGrid.add_row(d, r)

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_labresultHandlerView extends Patient_labresultHandlerBaseView
