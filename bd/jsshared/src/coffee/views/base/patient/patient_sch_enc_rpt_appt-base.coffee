
DSLFx.Validators.ScheduledEndValidate = (form, dd, vld, f, k) ->
	dd.handler?.schedule_end?(form, dd, vld, f, k)
	return

class Patient_sch_enc_rpt_apptHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	schedule_end: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		appointment_date = forms.patient_schedule_encounter.value_field('appointment_date')
		schedule_end = DSLFields.value(@parent.field_nodes.schedule_end)
		start = moment(new Date(appointment_date))
		end = moment(new Date(schedule_end))

		if schedule_end? and end.diff(start,'days') <= 0
			prettyAlert false, 'End Date cannot be earlier then Schedule Date'
			DSLFields.value @parent.field_nodes.schedule_end, ''

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_sch_enc_rpt_apptHandlerView extends Patient_sch_enc_rpt_apptHandlerBaseView

class Patient_sch_enc_rpt_apptCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_sch_enc_rpt_apptCardView extends Patient_sch_enc_rpt_apptCardBaseView
