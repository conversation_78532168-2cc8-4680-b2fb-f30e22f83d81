
DSLFx.Validators.BradenScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_bradenscore?()
	return


class Clinical_bradenHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_bradenscore()
		return

	validate_bradenscore: ->
		sckey = ['sensory_score', 'moisture_score', 'activity_score', 'mobility_score',
		'nutrition_score', 'friction_score']
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			sc += parseInt(cv)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_bradenHandlerView extends Clinical_bradenHandlerBaseView


