
DSLFx.Validators.MaterialValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_materials?()
	return

DSLFx.Validators.BundleValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_bundle?(dd.value_field("bundle"))
	return


class Patient_educationHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@add_preview_col()
		return

	add_preview_col: =>
		if @$el.find('.patient_education_materials').find('th.subfield-preview').length is 0
			@$el.find('.patient_education_materials').find('th.subfield-education').after('<th class="subfield-preview">Preview</th>')

		@$el.find('.patient_education_materials').find('tbody').find('tr').each (id, el) =>
			edC = $(el).find('td.subfield-education')
			if $(el).find('td.subfield-preview').length is 0
				edC.after('<td class="subfield-preview"></td>')

			if edC.find('.select2-chosen').length > 0
				edV = edC.find('.select2-chosen').html()
				if edV is 'Select Item'
					$(el).find('td.subfield-preview').html ''
					return

				epC = $(el).find('td.subfield-preview')
				aj = Ajax.async
					url: '/form/education/?limit=100&filter=auto_name:' + edV
				aj.done (data, status, xhr) =>
					epC.html '<a id="education-preview" href="' + data[0].url + '" target="_blank">Preview</a>'
				aj.fail (xhr, status, data) =>
					log('Failed to render preview with status [' + status + ']')

	validate_materials: ->
		return if (@mode is 'read')
		@add_preview_col()
		d = @nodes()['materials']
		d.each (id, el) =>
			log(id + ' ' + el)

	validate_bundle: (id) ->
		return if (@mode is 'read') or (@parent.preset?.bundle is parseInt(id)) or (not id)
		tc = Ajax.async
			url: '/form/educationbundle/'+ id
		tc.done (data, status, xhr) =>
			@clear_materials =>
				@add_materials(data[0])
			return
		tc.fail (xhr, status, data) =>
			dfd.reject 'Cannot load education bundle data. Please try again.'
			return
		return

	nodes: =>
		@parent.field_nodes

	clear_materials: (fxtrue) =>
		d = @nodes()['materials']
		if DSLFields.value(@nodes()['materials']).length is 0
			DSLFields.clear(d)
			fxtrue()
		else
			prettyYesNo 'Replace all items?', 'Are you sure you want to replace all education items?\n\nNOTE: This action cannot be reversed!', ->
				DSLFields.clear(d)
				fxtrue()
				return
		return

	add_materials: (data) ->
		for c in data.items
			tc = Ajax.async
				url: '/form/education/'+ c
			tc.done (data, status, xhr) =>
				@add_item(data)
				@add_preview_col()
		return

	add_item: (data) ->
		d = @nodes()['materials']
		r = {}
		r.education = data[0].auto_name
		r.required = "Yes"
		r.reviewed = "No"
		FieldGrid.add_row(d, r)

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_educationHandlerView extends Patient_educationHandlerBaseView
