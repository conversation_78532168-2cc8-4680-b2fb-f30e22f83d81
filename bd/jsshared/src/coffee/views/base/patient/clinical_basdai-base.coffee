
DSLFx.Validators.BASDAIScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_basdaiscore?()
	return


class Clinical_basdaiHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_basdaiscore()
		return

	validate_basdaiscore: ->
		sckey = ['fatigue', 'pain', 'swelling', 'discomfort_pressure',
		'discomfort_wakeup', 'stiffness']

		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			sc += parseInt(cv)
		if sc > 0
			sc = sc / 6.0
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_basdaiHandlerView extends Clinical_basdaiHandlerBaseView
