
DSLFx.Validators.PADQOLScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_padqolscore?()
	return


class Clinical_padqolHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_padqolscore()
		return

	validate_padqolscore: ->
		sckey =
			padqol_infections:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_tired:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_cough:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_flareups:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_unscheduled_visit:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_nausea:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_infections_trouble:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_effects_wear_off:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_shortness_breath:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_keepup:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_trouble_sleeping:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_depressed:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_missed_days:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_burden:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_require_help:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
			padqol_avoid:
				'Rarely/never': 0
				'Sometimes': 1
				'Often/always': 2
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_padqolHandlerView extends Clinical_padqolHandlerBaseView
