DSLFx.Validators.validateLabsListOther = (form, dd, vld, f, k) ->
	dd.handler?.validate_labs_other?(k)
	return

class Medicare_hitHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@$el.find('.medicare_hit_med_list button').addClass('hide')
		@$el.find('.medicare_hit_infusion_meds button').addClass('hide')
		pid = @options?.linkid?.patient
		@map_allergy(pid)
		@map_medication(pid)
		return

	nodes: =>
		@parent.field_nodes

	map_allergy: (pid) ->
		if (@mode != 'addfill')
			return 
		return if (not pid)

		ff = @nodes()['patient_allergy']
		dfd = $.Deferred()

		aj = Ajax.async
			url: '/form/patient_allergy?filter=patient_id:' + pid
		aj.done (data, status, xhr) ->
			if data.length > 0
				fdata = []
				for d in data
					fdata.push({
						id: d.allergen_id
						text: d.allergen_id_auto_name
					})
				DSLFields.value(ff, fdata)
				dfd.resolve()
		aj.fail (xhr, status, data) =>
			dfd.reject 'Cannot find patient allergy data. Please try again.'
		dfd

	map_medication: (pid) =>
		if (@mode != 'addfill')
			return 
		return if (not pid) or (pid.length <= 0)

		vals = DSLFields.value(@nodes()['med_list']) or []
		ff = @nodes()['med_list']
		aj = Ajax.async
			url: '/form/patient_medication?filter=patient_id:' + pid + '&sort=-created_on'
		aj.done (data, status, xhr) ->
			count = 0
			for v in data
				dup = false
				if status == 'Pending' and reported_by != 'Patient Reported'
					continue
				if status == 'Discontinued'
					continue
				for val in vals
					if parseInt(val._meta) == parseInt(v.id)
						dup = true
						break
				if dup
					continue
				r =
					_meta: parseInt(v.id)
					name: v.medication_id_auto_name
					dose: v.medication_dose
					freq: v.medication_frequency
					start: v.start_date
				FieldGrid.add_row(ff, r)
				count++
			return
		return

	validate_labs_other: (k) ->
			ko = 'labs_list_other'
			val = DSLFields.value @nodes()[k]
			sel = '.' + @form + '_' + ko
			f = DSL[@form].fields.labs_list_other
			p = @parent
			dfd = $.Deferred()
			aj = Ajax.async
				url: '/form/patient_lab?filter=name:Other'
			aj.fail (data, status, xhr) ->
				dfd.reject 'Cannot find Other lab data.'
			aj.always (data, status, xhr) ->
				if data.length > 0
					other_id = data[0]?.id;
					if val.includes(other_id)
						$(sel).parent().removeClass('offscreen')
						f.model.required = true
						f.view.offscreen = false
						if @$el.find('.medicare_hit_labs_list_other').find('span.help-block.note').has('b').length < 1
							@$el.find(sel).find('span.help-block.note').append('<b>REQUIRED</b>')
					else
						$(sel).parent().addClass('offscreen')
						f.model.required = false
						f.view.offscreen = true
						p.value_field ko, "", true, true
				dfd.resolve()
			dfd

class Medicare_hitHandlerView extends Medicare_hitHandlerBaseView

class Medicare_hitCardReadBaseView extends CRCardReadView

	print: ->
		setTimeout =>
			filename = 'Medicare_HIT_' + @record
			params = filename
			ENPrint.print_backend '/bi/print/medicare_hit/' + @record + '/' + params, filename
		, 50
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Medicare_hitCardReadView extends Medicare_hitCardReadBaseView
