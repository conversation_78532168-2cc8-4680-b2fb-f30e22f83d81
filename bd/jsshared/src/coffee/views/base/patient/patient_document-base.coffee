
# all patient_document_* forms will auto extend this
class Patient_documentCardReadBaseView extends CRCardReadView

	print: ->
		setTimeout =>
			filename = 'PD_' + @options.form + '_' + @record
			params = filename
			CRPrint.print_backend '/print/patient_document/' + @options.form + '/' + @record + '/' + params, filename
		, 50
		return


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_documentCardReadView extends Patient_documentCardReadBaseView
