
DSLFx.Validators.POEMScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_poemscore?()
	return


class Clinical_poemHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_poemscore()
		return

	validate_poemscore: ->
		sckey = ['itchy', 'sleep', 'bleed', 'weeping', 'cracked', 'flaking', 'dry']

		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			sc += parseInt(cv)
		@parent.value_field 'poem_score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_poemHandlerView extends Clinical_poemHandlerBaseView
