
DSLFx.Validators.ProgramValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_program?(dd.value_field("schedule_followup_program"))
	return


class Patient_schedule_taskHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	validate_program: (id) ->
		return if (@mode is 'read') or (@parent.preset?.lab is parseInt(id)) or (not id)
		tc = Ajax.async
			url: '/form/followupprogram/'+ id
		tc.done (data, status, xhr) =>
			@clear_program =>
				@add_program(data[0])
			return
		tc.fail (xhr, status, data) =>
			dfd.reject 'Cannot load follow-up program data. Please try again.'
			return
		return

	nodes: =>
		@parent.field_nodes

	clear_program: (fxtrue) =>
		d = @nodes()['schedule_breakdown']
		if DSLFields.value(@nodes()['schedule_breakdown']).length is 0
			DSLFields.clear(d)
			fxtrue()
		else
			prettyYesNo 'Replace program details?', 'Are you sure you want to replace the program details?\n\nNOTE: This action cannot be reversed!', ->
				DS<PERSON>ields.clear(d)
				fxtrue()
				return
		return

	add_program: (data) ->
		for d in data.schedule_breakdown
				@add_day(d)
		return

	add_day: (data) ->
		d = @nodes()['schedule_breakdown']
		r = {}
		r.day = data.day
		r.comment = data.comment
		FieldGrid.add_row(d, r)

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_taskHandlerView extends Patient_schedule_taskHandlerBaseView
