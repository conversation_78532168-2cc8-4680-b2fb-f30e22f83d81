
DSLFx.Transforms.MarkCompDate = (form, dd, vld, f, k) ->
	dd.handler?.mark_compdate?(vld.field)
	return

DSLFx.Validators.LoadCareplanTemplates = (form, dd, vld, f, k) ->
	dd.handler?.load_careplan_templates?()
	return

class CareplanHandlerBaseView extends CRView

	tab_can_add: ->
		false

	handle: (dfd) ->
		dfd.resolve()
		return

	nodes: =>
		@parent.field_nodes

	mark_compdate: (field) ->
		return if (@mode is 'read')

		vs = @parent.values()
		v = vs[field]

		updated = false
		for r in v
			if r.compeleted == 'yes' && (not r.compeleted_date or r.compeleted_date.length == 0)
				r.compeleted_date = (new Date()).format(FORMAT_DATETIME_SAVE)
				updated = true
			if r.compeleted == 'no' && (r.compeleted_date or r.compeleted_date.length > 0)
				r.compeleted_date = ''
				updated = true

		if updated
			d = @nodes()[field]
			DSLFields.clear(d)
			for r in v
				FieldGrid.add_row(d, r)

	load_careplan_templates: ->
		# return true
		return if (@mode is 'read')

		vs = @parent.values()
		v = vs['template_id']

		eqAr = _.isEmpty(_.difference(@parent.preset?.template_id, v)) && _.isEmpty(_.difference(v, @parent.preset?.template_id))

		return if (eqAr) or not v
		__this = this

		alert = false
		cpitems = 
			'problems': 'problem'
			'goals': 'goal'
			'interventions': 'intervention'
		for k,v of cpitems
			for val in vs[k]
				if val[v]?.trim().length > 0
					alert = true

		if alert
			prettyYesNo 'Replace all problems,goals, and interventions?', 'Are you sure you want to replace all items?\n\nNOTE: This action cannot be reversed!', ->
				for k in ['problems', 'goals', 'interventions']
					d = __this.nodes()[k]
					DSLFields.clear(d)
				v = __this.parent.values()['template_id']
				for vid in v
					aj = Ajax.async
						url: '/form/careplan_template/'+vid
					aj.done (data, status, xhr) =>
						if data.length > 0
							__this.add_items(data[0])
						return
					aj.fail (xhr, status, data) =>
						return
		else
			for k in ['problems', 'goals', 'interventions']
				d = @nodes()[k]
				DSLFields.clear(d)
			for vid in v
				aj = Ajax.async
					url: '/form/careplan_template/'+vid
				aj.done (data, status, xhr) =>
					if data.length > 0
						__this.add_items(data[0])
					return
				aj.fail (xhr, status, data) =>
					return

		@parent.block_validation = false
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CareplanHandlerView extends CareplanHandlerBaseView


class CareplanCardReadBaseView extends CRCardReadView

	print: ->
		BootstrapDialog.show
			title: 'Select Print View'
			message: 'Do you want to print CMS-485 form or the default form?'
			buttons: [
				label: 'Print CMS-485'
				action: (dialog) =>
					dialog.close()
					setTimeout =>
						filename = 'CMS-485_' + @record
						params = filename +
							'?pharm_name=' + App.company.name +
							'&pharm_phone=' + App.company.phone +
							'&pharm_addr=' + App.company.street + '|' +
							App.company.street2 + '|' + App.company.city +
							', ' + App.company.state + ' ' + App.company.zip
						CRPrint.print_backend '/print/cms-485/' + @record + '/' + params, filename
					, 50
					return
			,
				label: 'Print Default Form'
				action: (dialog) =>
					dialog.close()
					super()
					return
			]
			type: BootstrapDialog.TYPE_PRIMARY
			animate: false
			size: BootstrapDialog.SIZE_LARGE
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CareplanCardReadView extends CareplanCardReadBaseView