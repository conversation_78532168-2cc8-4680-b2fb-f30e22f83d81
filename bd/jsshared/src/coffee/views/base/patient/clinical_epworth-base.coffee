
DSLFx.Validators.EPWORTHScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_epworthscore?()
	return


class Clinical_epworthHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_epworthscore()
		return

	validate_epworthscore: ->
		sckey = ['reading', 'watching_tv', 'sitting_public', 'passenger_car',
		'lying_down', 'sitting_talking', 'sitting_quietly']
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			sc += parseInt(cv)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_epworthHandlerView extends Clinical_epworthHandlerBaseView
