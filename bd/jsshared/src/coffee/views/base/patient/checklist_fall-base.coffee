
DSLFx.Validators.FallScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_fallscore?()
	return


class Checklist_fallHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_fallscore()
		return

	validate_fallscore: ->
		sckey =
			fall_history:
				'yes': 25
				'no': 0
			secondary_dx:
				'yes': 15
				'no': 0
			iv_patient:
				'yes': 20
				'no': 0
			fall_ambulatory:
				'bed': 0
				'crutches': 15
				'furniture': 30
			fall_gait:
				'normal': 0
				'weak': 10
				'impaired': 20
			fall_mental:
				'oriented': 0
				'forgets': 15
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v).toLowerCase()
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Checklist_fallHandlerView extends Checklist_fallHandlerBaseView
