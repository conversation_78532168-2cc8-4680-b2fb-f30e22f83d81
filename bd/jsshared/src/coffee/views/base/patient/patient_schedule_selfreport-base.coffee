
DSLFx.Validators.PastScheduledValidate = (form, dd, vld, f, k) ->
	dd.handler?.backdated_schedule?(form, dd, vld, f, k)
	return

class Patient_schedule_selfreportHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	sh_reoccur: (v) ->
		return if not @parent.field_nodes?.reoccur?
		if v? and v is true and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'No'
		else if (v is null or v is false) and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'Yes'
		return

	backdated_schedule: (form, dd, vld, f, k) ->
		return if not @parent.field_nodes?.reoccur?
		forms = @parent.options.wrapper.subforms.formmap
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)
		schedule_start = if DSLFields.value(@parent.field_nodes.schedule_start) is null then "" else DSLFields.value(@parent.field_nodes.schedule_start)

		now = moment(new Date())
		field_date =  moment(new Date(schedule_start))

		@schedule_end(form, dd, vld, f, k)

		if field_date.diff(now,'days') < 0
			@parent.value_field 'reoccur', 'No'
			DSLFields.value_set forms.pat_sch_selfrp_rpt_appt.field_nodes.schedule_end, ''
			@sh_reoccur(true)
		else
			@sh_reoccur(false)

		return

	schedule_end: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		schedule_start = DSLFields.value(@parent.field_nodes.schedule_start)
		schedule_end = forms.pat_sch_selfrp_rpt_appt.value_field('schedule_end')
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)

		start = moment(new Date(schedule_start))
		end = moment(new Date(schedule_end))

		if schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'Yes'
			DSLFields.value @parent.field_nodes.schedule_start, ''
		if  schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'No'
			DSLFields.value forms.pat_sch_selfrp_rpt_appt.field_nodes.schedule_end, ''

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_selfreportHandlerView extends Patient_schedule_selfreportHandlerBaseView


class Patient_schedule_selfreportCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_selfreportCardView extends Patient_schedule_selfreportCardBaseView