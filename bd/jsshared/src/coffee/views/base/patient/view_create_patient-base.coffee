class view_create_patientCardBaseFormView extends CRCardFormView

	tab_label_save: ->
		"Create"
	
	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return

		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		dfd = @ddf.save()
		dfd.done (rt) =>
			if rt? and rt.values?
				if @parent.options.isFlyout or @parent.parent.tabid is 'flyout_root'
					@parent.save(rt.values, rt.record, @dom, @domcols, @record is null, rt.rowhtml)
					return
				@record = rt.record
				@parent.tabData.form = "patient"
				@parent?.parent?.onSaved?(rt, @parent.tabData, @parent)
			return
			
		return

class view_create_patientCardFormView extends view_create_patientCardBaseFormView
