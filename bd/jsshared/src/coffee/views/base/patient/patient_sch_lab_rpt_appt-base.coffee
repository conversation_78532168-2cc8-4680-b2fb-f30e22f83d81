
DSLFx.Validators.ScheduledEndValidate = (form, dd, vld, f, k) ->
	dd.handler?.schedule_end?(form, dd, vld, f, k)
	return

class Patient_sch_lab_rpt_apptHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	schedule_end: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		schedule_start = forms.patient_schedule_lab.value_field('schedule_start')
		schedule_end = DSLFields.value(@parent.field_nodes.schedule_end)
		start = moment(new Date(schedule_start))
		end = moment(new Date(schedule_end))

		if schedule_end? and end.diff(start,'days') <= 0
			DSLFields.value @parent.field_nodes.schedule_end, ''

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_sch_lab_rpt_apptHandlerView extends Patient_sch_lab_rpt_apptHandlerBaseView

class Patient_sch_lab_rpt_apptCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_sch_lab_rpt_apptCardView extends Patient_sch_lab_rpt_apptCardBaseView
