
DSLFx.Validators.BundleValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_bundle?(dd.value_field("bundle"))
	return


class Patient_supplyHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	validate_bundle: (id) ->
		return if (@mode is 'read') or (@parent.preset?.bundle is parseInt(id)) or (not id)
		tc = Ajax.async
			url: '/form/supplybundle/'+ id
		tc.done (data, status, xhr) =>
			@clear_supplies =>
				@add_therapy(data[0])
				@add_supplies(data[0])
			return
		tc.fail (xhr, status, data) =>
			dfd.reject 'Cannot load supply bundle data. Please try again.'
			return
		return

	nodes: =>
		@parent.field_nodes

	clear_supplies: (fxtrue) =>
		d = @nodes()['supplies']
		t = @nodes()['therapy']
		if DSLFields.value(@nodes()['supplies']).length is 0
			DSLFields.clear(d)
			DSLFields.clear(t)
			fxtrue()
		else
			prettyYesNo 'Replace all items?', 'Are you sure you want to replace all supply items?\n\nNOTE: This action cannot be reversed!', ->
				DSLFields.clear(d)
				DSLFields.clear(t)
				fxtrue()
				return
		return

	add_supplies: (data) ->
		for c in data.items_auto_name
			@add_item(c)
		return

	add_therapy: (data) ->
		if data.therapy and data.therapy.length > 0
			@parent.value_field 'therapy', data.therapy, true
		return

	add_item: (data) ->
		d = @nodes()['supplies']
		r =
			supply: data
			count: 1
		FieldGrid.add_row(d, r)
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_supplyHandlerView extends Patient_supplyHandlerBaseView
