
DSLFx.Validators.RODSScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_rodsscore?()
	return


class Clinical_rodsHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_rodsscore()
		return

	validate_rodsscore: ->
		sckey = ['read', 'eat', 'brush', 'wash_upper', 'sit', 'make_sandwich', 'dress', 'wash_lower',
			'move_chair', 'turn_key', 'go_to_dr', 'take_shower', 'do_dishes', 'catch_ball', 'do_shopping', 'bend_down',
			'walk_up_stairs', 'travel', 'walk', 'walk_outside', 'carry_object', 'dance', 'stand', 'run']

		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			if cv.search(/Possible, but with some difficulty/gi) == 0
				sc += 1
			else if cv.search(/Possible, without any difficulty/gi) == 0
				sc += 2

		sc = (sc/48) * 100
		@parent.value_field 'score', sc, true

		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_rodsHandlerView extends Clinical_rodsHandlerBaseView
