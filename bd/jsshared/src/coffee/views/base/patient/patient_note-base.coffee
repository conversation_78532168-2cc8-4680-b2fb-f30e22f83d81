
DSLFx.Validators.CopySubjectLine = (form, dd, vld, f, k) ->
	dd.handler?.copy_subject?()
	return

class Patient_noteHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@copy_subject()
		@resize_note()
		return

	copy_subject: ->
		return if not @parent.field_nodes?.dynamic_subject?
		sub = DSLFields.value(@parent.field_nodes.dynamic_subject)
		if sub?.length > 0
			@parent.value_field 'subject', sub, true

		note = DSLFields.value(@parent.field_nodes.note)
		return if not sub or sub.length == 0 
		return if (@mode is 'read')
		return if note and note.length > 0

		tc = Ajax.async
			url: '/form/note_template/?limit=1&filter=subject:'+ sub
		tc.done (data, status, xhr) =>
			if data and data[0]
				@parent.value_field 'note', data[0].body, true, true

			return
		tc.fail (xhr, status, data) =>
			dfd.reject 'Cannot load lab components data. Please try again.'
			return

		return

	resize_note: ->
		return if not @parent.field_nodes?.note?
		note = @parent.field_nodes.note?[0]
		return if not note?
		mode = @parent.options.mode
		return if not mode
		# Add a 500ms delay to allow text to be saved first
		setTimeout =>
			note.style.height = 'auto'
			if mode is 'read'
				note.style.height = (note.scrollHeight / 2) + 'px'
			else
				note.style.height = note.scrollHeight + 'px'
		, 500

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_noteHandlerView extends Patient_noteHandlerBaseView


class Patient_noteCardReadBaseView extends CRCardReadView

	load: ->
		dfd = super()
		if not Auth.is_admin()
			dfd.done (args...) =>
				# disable editing for forms created by other users
				@disable_editing = true
				if @ddr.subforms?.forms?[0]?.preset?
					r = @ddr.subforms.forms[0].preset
					if parseInt(r.user_id) is parseInt(App.user.id)
						@disable_editing = false
				@parent.setheader() if @parent.setheader?
				return
		dfd

	tab_can_archive: ->
		# cannot archive forms created by other users
		(not @disable_editing) and Auth.can_update_any(@form)


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_noteCardReadView extends Patient_noteCardReadBaseView
