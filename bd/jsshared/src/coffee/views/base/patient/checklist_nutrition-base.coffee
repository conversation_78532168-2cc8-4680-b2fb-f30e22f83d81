
DSLFx.Validators.NoteScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_notescore?()
	return


class Checklist_nutritionHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_notescore()
		return

	validate_notescore: ->
		sc = 0
		for k,v of @parent.values()
			continue if $.trim(v).toLowerCase() isnt 'yes'
			continue if not DSL[@form]?.fields[k]?
			f = DSL[@form].fields[k]
			n = f.view.note
			continue if n.toLowerCase().indexOf('point') is -1
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Checklist_nutritionHandlerView extends Checklist_nutritionHandlerBaseView
