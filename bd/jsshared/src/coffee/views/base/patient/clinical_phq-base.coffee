
DSLFx.Validators.PHQScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_phqscore?()
	return


class Clinical_phqHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_phqscore()
		return

	validate_phqscore: ->
		sckey =
			phq_doing_things:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_depressed:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_sleeping:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_tired:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_appetite:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_selfimage:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_concentrating:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_moving_change:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
			phq_suicidal:
				'Not at all': 0
				'Several days': 1
				'More than half the days': 2
				'Nearly every day': 3
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_phqHandlerView extends Clinical_phqHandlerBaseView
