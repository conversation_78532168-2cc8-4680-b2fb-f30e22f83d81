
DSLFx.Validators.INCATScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_incatscore?(k)
	return


class Clinical_incatHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	validate_incatscore: (sk) ->

		if @parent.block_validation
			return

		sckey =
			'0 = Normal': 0.0
			'1 = Minor symptoms or signs in one or both arms but not affecting any of the funtions listed': 1.0
			'2 = Moderate symptoms or signs in one or both arms affecting but not preventing any of the functions listed': 2.0
			'3 = Severe symptoms or signs in one or both arms preventing at least one but not all functions listed': 3.0
			'4 = Severe symptoms or signs in both arms preventing all functions listed but some purposeful movements still possible': 4.0
			'5 = Severe symptoms and signs in both arms preventing all purposeful movements': 5.0
			'0 = Walking is not affected': 0.0
			'1 = Walking is affected but does not look abnormal': 1.0
			'2 = Walks independently but gait looks abnormal': 2.0
			'3 = Usually uses unilateral support to walk 10 meters (25 feet) (stick, single crutch, one arm)': 3.0
			'4 = Usually uses bilateral support to walk 10 meters (25 feet) (sticks, crutches, two arms)' : 4.0
			'5 = Usually uses wheelchair to travel 10 meters (25 feet)': 5.0
			'6 = Restricted to wheelchair, unable to stand and walk few steps with help but able to make some purposeful leg movement': 6.0
			'7 = Restricted to wheelchair or bed most of the day, preventing all purposeful movements of the legs (eg. unable to reposition legs in bed)': 7.0

		sc = 0
		vs = @parent.values()
		v = vs['arm_grade']
		if v and v.length > 0
			sc = sckey[v]

		v = vs['leg_grade']
		if v and v.length > 0
			sc += sckey[v]

		@parent.value_field 'score', sc, true
		
		@parent.block_validation = false

		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_incatHandlerView extends Clinical_incatHandlerBaseView
