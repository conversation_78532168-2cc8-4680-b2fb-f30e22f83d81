
DSLFx.Validators.EDSSScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_edssscore?(k)
	return


class Clinical_edssHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	validate_edssscore: (sk) ->

		if @parent.block_validation
			return

		sckey =
			'1.0 - No disability, minimal signs in one FS': 1.0
			'1.5 - No disability, minimal signs in more than one FS': 1.5
			'2.0 - Mi.0nimal disability in one FS': 2.0
			'2.5 - Mild disability in one FS or minimal disability in two FS': 2.5
			'3.0 - Moderate disability in one FS, or mild disability in three or four FS. No impairment to walking': 3.0
			'3.5 - Moderate disability in one FS and more than minimal disability in several others. No impairment to walking': 3.5
			'4.0 - Significant disability but self-sufficient and up and about some 12 hours a day. Able to walk without aid or rest for 500m': 4.0
			'4.5 - Significant disability but up and about much of the day, able to work a full day, may otherwise have some limitation of full activity or require minimal assistance. Able to walk without aid or rest for 300m': 4.5
			'5.0 - Disability severe enough to impair full daily activities and ability to work a full day without special provisions. Able to walk without aid or rest for 200m': 5.0
			'5.5 - Disability severe enough to preclude full daily activities. Able to walk without aid or rest for 100m': 5.5
			'6.0 - Requires a walking aid - cane, crutch, etc - to walk about 100m with or without resting' : 6.0
			'6.5 - Requires two walking aids - pair of canes, crutches, etc - to walk about 20m without resting': 6.5
			'7.0 - Unable to walk beyond approximately 5m even with aid. Essentially restricted to wheelchair; though wheels self in standard wheelchair and transfers alone. Up and about in wheelchair some 12 hours a day': 7.0
			'7.5 - Unable to take more than a few steps. Restricted to wheelchair and may need aid in transferring. Can wheel self but cannot carry on in standard wheelchair for a full day and may require a motorized wheelchair': 7.5
			'8.0 - Essentially restricted to bed or chair or pushed in wheelchair. May be out of bed itself much of the day. Retains many self-care functions. Generally has effective use of arms': 8.0
			'8.5 - Essentially restricted to bed much of day. Has some effective use of arms retains some self-care functions': 8.5
			'9.0 - Confined to bed. Can still communicate and eat': 9.0
			'9.5 - Confined to bed and totally dependent. Unable to communicate effectively or eat/swallow': 9.5
			'10.0 - Death due to MS': 10.0


		sc = 0
		vs = @parent.values()
		v = vs['edss_scores']
		if v and v.length > 0
			sc = sckey[v]

		@parent.value_field 'edss_score', sc, true
		
		@parent.block_validation = false

		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_edssHandlerView extends Clinical_edssHandlerBaseView
