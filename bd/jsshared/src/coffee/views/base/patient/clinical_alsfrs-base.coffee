
DSLFx.Validators.ALSFRSScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_alsfrsscore?()
	return


class Clinical_alsfrsHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_alsfrsscore()
		return

	validate_alsfrsscore: ->
		sckey = ['speech', 'salivation', 'swallowing', 'handwriting',
		'cutting_food', 'cutting_food_gastrostomy', 'dressing_and_hygiene',
		'turning_in_bed', 'walking', 'climbing_stairs', 'dyspnea',
		'orthopnea', 'respiratory_insufficiency']

		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if !(k in sckey) or (cv.length is 0)
			continue if not DSL[@form]?.fields[k]?
			sc += parseInt(cv)
		@parent.value_field 'alsfrs_score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_alsfrsHandlerView extends Clinical_alsfrsHandlerBaseView


class Clinical_alsfrsCardReadBaseView extends CRCardReadView

	print: ->
		BootstrapDialog.show
			title: 'Select Print View'
			message: 'Do you want to print ALS Functional Rating Scale Revised form or the default form?'
			buttons: [
				label: 'Print ALS-FRS-R'
				action: (dialog) =>
					dialog.close()
					setTimeout =>
						filename = 'ALS-FRS-R_' + @record
						params = filename
						CRPrint.print_backend '/print/alsfrs/' + @record + '/' + params, filename
					, 50
					return
			,
				label: 'Print Default Form'
				action: (dialog) =>
					dialog.close()
					super()
					return
			]
			type: BootstrapDialog.TYPE_PRIMARY
			animate: false
			size: BootstrapDialog.SIZE_LARGE
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_alsfrsCardReadView extends Clinical_alsfrsCardReadBaseView

