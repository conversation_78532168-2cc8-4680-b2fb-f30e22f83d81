DSLFx.Validators.InsuranceBlock = (form, dd, vld, vals, k) ->
	return !(k in vld.disabled_fields)

class Patient_insuranceCardReadBaseView extends CRCardReadView

	# Check Elig. Medical
	tab_can_special_event: ->
		return false

	# PDF View Elig. Results 
	tab_can_special_event_1: ->
		pi_id = @values()?.id || null
		last_verification_file = @values()?.last_verification_file || null
		if not last_verification_file
			console.log("No file hash found for this insurance record for id: " + pi_id)
			return false
		return true

	tab_label_special_event_1: ->
		"View Elig. Results"

	tab_do_special_event_1: ->
		pi_id = @ddr.dr.options.record
		CRPrint.print_backend('/pverify/eligibility/report?ins_id=' + pi_id)
	
	# E1 Pharmacy Eligibility Check
	tab_can_special_event_2: ->
		false

	# Pharmacy Test Claim
	tab_can_special_event_3: ->
		false


class Patient_insuranceCardReadView extends Patient_insuranceCardReadBaseView