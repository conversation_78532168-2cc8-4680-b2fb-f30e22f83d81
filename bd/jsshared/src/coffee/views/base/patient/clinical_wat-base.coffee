
DSLFx.Validators.WATScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_watscore?()
	return


class Clinical_watHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_watscore()
		return

	validate_watscore: ->
		sckey =
			loose_stool:
				'No': 0
				'Yes': 1
			vomiting:
				'No': 0
				'Yes': 1
			high_temp:
				'No': 0
				'Yes': 1
			sbs:
				'SBS < 0 or asleep/awake/calm': 0
				'SBS > +1 or awake/distressed': 1
			tremor:
				'None/mild': 0
				'Moderate/severe': 1
			sweating:
				'No': 0
				'Yes': 1
			movement:
				'None/mild': 0
				'Moderate/severe': 1
			yawning:
				'None or 1': 0
				'>1': 1
			touch:
				'None/mild': 0
				'Moderate/severe': 1
			muscle:
				'Normal': 0
				'Increased': 1
			calm:
				'< 2min': 0
				'2 - 5min': 1
				'> 5 min': 2
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_watHandlerView extends Clinical_watHandlerBaseView
