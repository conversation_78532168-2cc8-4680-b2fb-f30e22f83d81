
DSLFx.Validators.CopyBillSubjectLine = (form, dd, vld, f, k) ->
	dd.handler?.copy_subject?()
	return

class Patient_bill_noteHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@copy_subject()
		return

	copy_subject: ->
		return if not @parent.field_nodes?.dynamic_subject?
		sub = DSLFields.value(@parent.field_nodes.dynamic_subject)
		if sub?.length > 0
			@parent.value_field 'subject', sub, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_bill_noteHandlerView extends Patient_bill_noteHandlerBaseView


class Patient_bill_noteCardReadBaseView extends CRCardReadView

	tab_can_archive: ->
		# cannot archive forms created by other users
		(not @disable_editing) and Auth.can_update_any(@form)


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_bill_noteCardReadView extends Patient_bill_noteCardReadBaseView
