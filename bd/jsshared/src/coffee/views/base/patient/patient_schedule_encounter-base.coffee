DSLFx.Validators.PastScheduledValidate = (form, dd, vld, f, k) ->
	dd.handler?.backdated_schedule?(form, dd, vld, f, k)
	return


DSLFx.Validators.ShowHideResendLink = (form, dd, vld, f, k) ->
	dd.handler?.hide_show_resend_link_field?(form, dd, vld, f, k)
	return

class Patient_schedule_encounterHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	sh_reoccur: (v) ->
		return if not @parent.field_nodes?.reoccur?
		if v? and v is true and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'No'
		else if (v is null or v is false) and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'Yes'
		return

	hide_show_resend_link_field:(form, dd, vld, f, k) ->
		if ['add', 'addfill'].includes(dd.handler.mode)
			@parent.field_nodes['resend_link'].closest('.form-horizontal').addClass('offscreen')

	backdated_schedule: (form, dd, vld, f, k) ->
		return if not @parent.field_nodes?.reoccur?
		forms = @parent.options.wrapper.subforms.formmap
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)
		appointment_date = DSLFields.value(@parent.field_nodes.appointment_date)

		now = moment(new Date())
		field_date =  moment(new Date(appointment_date))

		@schedule_end(form, dd, vld, f, k)

		if field_date.diff(now,'days') < 0
			@parent.value_field 'reoccur', 'No'
			if forms.patient_sch_enc_rpt_appt
				DSLFields.value_set forms.patient_sch_enc_rpt_appt.field_nodes.schedule_end, ''
			else
				DSLFields.value_set @parent.field_nodes.schedule_end, ''
			@sh_reoccur(true)
		else
			@sh_reoccur(false)

		return

	schedule_end: (form, dd, vld, f, k) ->
		forms = @parent.options.wrapper.subforms.formmap
		appointment_date = DSLFields.value(@parent.field_nodes.appointment_date)
		if forms.patient_sch_enc_rpt_appt?
			schedule_end = forms.patient_sch_enc_rpt_appt.value_field('schedule_end')
		else
			schedule_end = DSLFields.value(@parent.field_nodes.schedule_end)
		reoccur = DSLFields.value(@parent.field_nodes.reoccur)

		start = moment(new Date(appointment_date))
		end = moment(new Date(schedule_end))

		if schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'Yes'
			prettyAlert false, 'Schedule Date cannot be later then End Date'
			DSLFields.value @parent.field_nodes.appointment_date, ''
		if  schedule_end? and start.diff(end,'days') >= 0 and reoccur is 'No'
			if forms.patient_sch_enc_rpt_appt
				DSLFields.value forms.patient_sch_enc_rpt_appt.field_nodes.schedule_end, ''
			else
				DSLFields.value @parent.field_nodes.schedule_end, ''

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_encounterHandlerView extends Patient_schedule_encounterHandlerBaseView


class Patient_schedule_encounterCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_schedule_encounterCardView extends Patient_schedule_encounterCardBaseView
