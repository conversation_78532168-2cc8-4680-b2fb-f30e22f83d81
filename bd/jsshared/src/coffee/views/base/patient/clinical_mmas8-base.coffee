
DSLFx.Validators.MMAS8ScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_mmas8score?()
	return


class Clinical_mmas8HandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_mmas8score()
		return

	validate_mmas8score: ->
		sckey =
			forget:
				'No': 1.0
				'Yes': 0.0
			did_not_take:
				'No': 1.0
				'Yes': 0.0
			se_stopped_taking:
				'No': 1.0
				'Yes': 0.0
			forget_travel:
				'No': 1.0
				'Yes': 0.0
			taken_yesterday:
				'No': 0.0
				'Yes': 1.0
			well_stopped_taking:
				'No': 1.0
				'Yes': 0.0
			feel_hassled:
				'No': 1.0
				'Yes': 0.0
			remembering:
				'Never/Rarely': 1.0
				'Once in a while': 0.75
				'Sometimes': 0.50
				'Usually': 0.25
				'All the time': 0.0
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseFloat(n)
		@parent.value_field 'score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_mmas8HandlerView extends Clinical_mmas8HandlerBaseView
