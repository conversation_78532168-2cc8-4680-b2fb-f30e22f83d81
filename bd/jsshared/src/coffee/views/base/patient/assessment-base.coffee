
class AssessmentHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	sh_reoccur: (v) ->
		return if not @parent.field_nodes?.reoccur?
		if v? and v is true and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'No'
		else if (v is null or v is false) and @mode in ['edit', 'add', 'addfill']
			DSLFields.value @parent.field_nodes['sh_reoccur'], 'Yes'
		return



# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class AssessmentHandlerView extends AssessmentHandlerBaseView


class AssessmentCardBaseView extends CRCardView

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class AssessmentCardView extends AssessmentCardBaseView