
DSLFx.Validators.FLACCScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_flaccscore?()
	return

class Checklist_painHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_flaccscore()
		return

	validate_flaccscore: ->
		sckey =
			flacc_face:
				'No particular expression or smile': 0
				'Occasional grimace or frown, withdrawn, uninterested': 1
				'Frequent to constant quivering chin, clenched jaw': 2
				'N/A': 0
			flacc_legs:
				'Normal position or relaxed': 0
				'Uneasy, restless, tense': 1
				'Kicking, or legs drawn up': 2
				'N/A': 0
			flacc_activity:
				'Lying quietly, normal position, moves easily': 0
				'Squirming, shifting, back and forth, tense': 1
				'Arched, rigid or jerking': 2
				'N/A': 0
			flacc_cry:
				'No cry (awake or asleep)': 0
				'Moans or whimpers; occasional complaint': 1
				'Crying steadily, screams or sobs, frequent complaints': 2
				'N/A': 0
			flacc_consolability:
				'Content, relaxed': 0
				'Reassured by occasional touching, hugging or being talked to, distractible': 1
				'Difficult to console or comfort': 2
				'N/A': 0
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'flacc_score', sc, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Checklist_painHandlerView extends Checklist_painHandlerBaseView
