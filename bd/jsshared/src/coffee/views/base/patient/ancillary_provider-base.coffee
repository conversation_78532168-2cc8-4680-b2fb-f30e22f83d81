DSLFx.Validators.AgencyCalendarCheck = (form, dd, vld, f, k) ->
	return dd.handler?.agency_calendar_check?(dd.value_field('calendar_id'))


class Ancillary_providerHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	nodes: =>
		@parent.field_nodes

	agency_calendar_check: (calendar_id) ->
		return if (@mode is 'read')
		return if !calendar_id
		return if @parent.options.parent.ddf.count_missing_required_fields() > 0
		return if @parent.block_validatione
		id = @parent.preset.id
		id_flt = ''
		ag = Ajax.sync
			url: """/form/ancillary_provider/?limit=1&fields=min&filter=calendar_id:#{calendar_id}#{ if id then ('&filter=id:!'+id) else '' }"""
		return if (getType(ag) != 'object' or _.isEmpty(ag))
		return if (getType(ag) == 'array' and ag.length == 0)
		err = """Selected calendar is already assigned to '#{ag.auto_name}' nursing agency."""
		prettyError 'Error', err
		return { 'error': err }

class Ancillary_providerHandlerView extends Ancillary_providerHandlerBaseView
