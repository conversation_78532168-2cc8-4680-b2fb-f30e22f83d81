
DSLFx.Validators.MHAQScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_mhaqscore?()
	return


class Clinical_mhaqHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_mhaqscore()
		return

	validate_mhaqscore: ->
		sckey =
			mhaq_dress:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_bed:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_glass:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_walk:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_wash:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_bend:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_faucet:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
			mhaq_vehicle:
				'easy': 0
				'some': 1
				'moderate': 2
				'impossible': 3
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc/8.0, true
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_mhaqHandlerView extends Clinical_mhaqHandlerBaseView
