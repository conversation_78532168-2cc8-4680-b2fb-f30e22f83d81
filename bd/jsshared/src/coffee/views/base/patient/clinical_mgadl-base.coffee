
DSLFx.Validators.MGADLScoreValidate = (form, dd, vld, f, k) ->
	dd.handler?.validate_mgadlscore?()
	return

DSLFx.Validators.MGADLScoreEqualizer = (form, dd, vld, f, k) ->
	dd.handler?.mgadl_score_equalizer?()
	return

class Clinical_mgadlHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		@validate_mgadlscore()
		@mgadl_score_equalizer()
		return

	validate_mgadlscore: ->
		sckey =
			talking:
				'Normal': 0
				'Intermittent slurring or nasal speech': 1
				'Constant slurring or nasal speech, but can be understood': 2
				'Difficult-to-understand speech': 3
			chewing:
				'Normal': 0
				'Fatigue with solid food': 1
				'Fatigue with soft food': 2
				'Gastric tube': 3
			swallowing:
				'Normal': 0
				'Rare episode of choking': 1
				'Frequent choking necssitating changes in diet': 2
				'Gastric tube': 3
			breathing:
				'Normal': 0
				'Shortness of breath with exertion': 1
				'Shortness of breath at rest': 2
				'Ventilator dependence': 3
			grooming:
				'None': 0
				'Extra effort, but no rest periods needed': 1
				'Rest periods needed': 2
				'Cannot do one of these functions': 3
			arise:
				'None': 0
				'Mild, sometimes uses arms': 1
				'Moderate, always uses arms': 2
				'Severe, requires assistance': 3
			vision:
				'None': 0
				'Occurs, but not daily': 1
				'Daily, but not constant': 2
				'Constant': 3
			eyelid:
				'None': 0
				'Occurs, but not daily': 1
				'Daily, but not constant': 2
				'Constant': 3
		sc = 0
		for k,v of @parent.values()
			cv = $.trim(v)
			continue if not sckey[k] or cv.length is 0
			continue if not DSL[@form]?.fields[k]?
			n = sckey[k][cv]
			sc += parseInt(n)
		@parent.value_field 'score', sc, true
		return

	mgadl_score_equalizer: () ->
		prev_score = @parent.preset['score']
		sc = @parent.value_field 'score'
		if prev_score != sc
			@parent.value_field 'score', prev_score, true
			return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Clinical_mgadlHandlerView extends Clinical_mgadlHandlerBaseView
