
class Patient_labCardFormBaseView extends CRCardFormView

	gridrow: (htmlrow, row, cols, fields) =>
		htmlrow.prefilled = @status(row)
		htmlrow

	status: (v) ->
		if v.prefilled is 'Y'
			'Filled'
		else if dateYMD() > dateYMD(v.draw_date?.substr(0, 10))
			'<span class="label label-danger">Past Due</span>'
		else
			'Upcoming'

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_labCardFormView extends Patient_labCardFormBaseView

class Patient_labCardListBaseView extends CRCardListView

	gridload: (options) =>
		it = []
		rseen = {}
		for k,v of options.items
			if v.prefilled is 'Upcoming' # already set via gridrow/@status
				continue if rseen[v.rtag]?
				rseen[v.rtag] = true
			delete v.rtag
			it.push v
		options.items = it

		cl = []
		for k,v of options.columns
			cl.push v if v.property isnt 'rtag'
		options.columns = cl

		@ddg.callback options
		return

	gridrow: (htmlrow, row, cols, fields) =>
		htmlrow.prefilled = @status(row)
		htmlrow

	status: (v) ->
		if v.prefilled is 'Y'
			'Filled'
		else if dateYMD() > dateYMD(v.draw_date?.substr(0, 10))
			'<span class="label label-danger">Past Due</span>'
		else
			'Upcoming'

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Patient_labCardListView extends Patient_labCardListBaseView
