
class CompanyHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		
		@parent.field_nodes?.bg_color?.on 'input.bg_color', (e) ->
			document.querySelector("#application").style.backgroundColor = e.target.value
			return
		@parent.field_nodes?.fg_color?.on 'input.fg_color', (e) ->
			console.log(e.target.value)
			return
		return

	unload: ->
		@parent.field_nodes?.bg_color?.off('input.bg_color')
		@parent.field_nodes?.fg_color?.off('input.fg_color')
		super()
		return
# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class CompanyHandlerView extends CompanyHandlerBaseView
