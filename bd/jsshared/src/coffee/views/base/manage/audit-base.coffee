class auditCardBaseView extends CRCardView

	can_archive: ->
		false


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class auditCardView extends auditCardBaseView


class auditCardListBaseView extends CRCardListView

	tab_can_add: ->
		false

	tab_can_edit: ->
		false


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class auditCardListView extends auditCardListBaseView


class auditCardReadBaseView extends CRCardReadView

	tab_can_archive: ->
		false

	tab_can_edit: ->
		false

	tab_can_review: ->
		false


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class auditCardR<PERSON>View extends auditCardReadBaseView
