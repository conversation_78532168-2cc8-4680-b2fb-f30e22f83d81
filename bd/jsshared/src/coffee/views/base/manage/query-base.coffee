class QueryHandlerBaseView extends CRView

	nodes: =>
		@parent.field_nodes

	handle: (dfd) ->
		dfd.resolve()
		disabled = @mode is 'read'
		uid = window.next_id('dsl-editor')
		@parent.render_id_editor = uid
		@nodes()['report_sql'].parent().parent().parent().after("<div class='form-horizontal form-col-1' id=#{uid}></div>")
		loadComponent('CoffeeQueryDSLEditor',@$("##{uid}")[0], uid, {handle: @, disabled: disabled})
		return

	unload: ->
		unloadComponent('CoffeeQueryDSLEditor', @parent.render_id_editor)
		super()
		return

class QueryHandlerView extends QueryHandlerBaseView