class QueryCardFormView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return
		currentData = @ddf.get_formdata()
		if currentData.create_view == 'Yes'
			@ddf.options.form_override_url = '/query/save'
		dfd = @ddf.save()
		onSavedCallBack(@, dfd)