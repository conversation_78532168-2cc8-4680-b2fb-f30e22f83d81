class careplan_dt_cmpdCardFormBaseView extends CRCardFormView

	tab_can_archive: ->
		return false # should only void delivery ticks, not archive

	tab_label_special_event_1: ->
		return "Print Label"

	tab_can_special_event_1: ->
		return true

	tab_do_special_event_1: ->
        fd = @ddf.values()
		label = fd.auto_name or fd.order_id_auto_name or null
		lbl = _.cloneDeep(fd?.subform_lbl?[0] || {})
		lbl = [_.defaults(lbl, fd)]
		dt = @?.options?.parent?.parent?.subform_parent?.parent?.ddf?.get_formdata();
		data =
			dataSource:
				lbl: duplicateEachElement(lbl, 2)
				dt: dt
		params =
			site_id: dt.site_id or -1
			inventory_id: fd.inventory_id or @preset.inventory_id or -1
			order_item_id: fd.order_item_id or @preset.order_item_id or -1
			user_id: App.user.id or -1
		window.openARJsPopover(joinValid(['Compound Label', label], ' | '), 'dt_cmpd_lbl', params, data)
	
class careplan_dt_cmpdCardFormView extends careplan_dt_cmpdCardFormBaseView


class careplan_dt_cmpdCardReadBaseView extends CRCardReadView

	tab_can_archive: ->
		return false # should only void delivery ticks, not archive

	tab_label_special_event_1: ->
		return "Print Label"

	tab_can_special_event_1: ->
		return true

	tab_do_special_event_1: ->
        fd = @ddr.values()
		label = fd.auto_name or fd.order_id_auto_name or null
		lbl = _.cloneDeep(fd?.subform_lbl?[0] || {})
		lbl = [_.defaults(lbl, fd)]
		dt = @?.options?.parent?.parent?.subform_parent?.parent?.ddr?.get_formdata();
		data =
			dataSource:
				lbl: duplicateEachElement(lbl, 2)
				dt: dt
		params =
			site_id: dt.site_id or -1
			inventory_id: fd.inventory_id or @preset.inventory_id or -1
			order_item_id: fd.order_item_id or @preset.order_item_id or -1
			user_id: App.user.id or -1
		window.openARJsPopover(joinValid(['Compound Label', label], ' | '), 'dt_cmpd_lbl', params, data)
	
class careplan_dt_cmpdCardReadView extends careplan_dt_cmpdCardReadBaseView