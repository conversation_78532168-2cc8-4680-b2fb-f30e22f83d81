
DSLFx.Validators.AddStartStopDates = (form, dd, vld, f, k) ->
	dd.handler?.add_start_stop?(dd.value_field('written_date'))
	return

DSLFx.Validators.OrderBlock = (form, dd, vld, vals, k) ->

	allowed_fields = vld.allowed_fields
	if (vals.void and vals.void is 'Yes')
		k in allowed_fields
	else
		true

class careplan_orderHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	nodes: =>
		@parent.field_nodes

	add_start_stop: (order_date) ->
		return if (@mode is 'read') or not order_date

		order_date = moment(order_date)

		if App.company.default_order_days?
			@parent.value_field 'start_date', order_date.format("MM/DD/YYYY"), true
			@parent.value_field 'stop_date', order_date.add(App.company.default_order_days, 'days').format("MM/DD/YYYY"), true

		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class careplan_orderHandlerView extends careplan_orderHandlerBaseView

class careplan_orderCardReadBaseView extends CRCardReadView

	tab_can_archive: ->
		# should only void orders, not archive
		return false

class careplan_orderCardReadView extends careplan_orderCardReadBaseView