class view_create_dtCardBaseFormView extends CRCardFormView

	tab_label_save: ->
		"Create"
	
	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return

		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		@ddf.options.form_override_url =  '/dispense/?func=generate_delivery_ticket&'
		dfd = @ddf.save()
		dfd.done (rt) =>
			if @parent?.tabViewActions?.openTab and rt?.values?.careplan_delivery_tick
				@parent.tabViewActions.openTab(rt.values.careplan_delivery_tick, "Delivery Ticket", "edit", "careplan_delivery_tick", {}, null, {subtitle: @parent?.tabData?.subtitle or "Patient", showAssessment: true})
				this.tab_do_cancel(true)
				return
			if rt? and rt.values?
				if @parent.options.isFlyout or @parent.parent.tabid is 'flyout_root'
					@parent.save(rt.values, rt.record, @dom, @domcols, @record is null, rt.rowhtml)
					return
				@record = rt.record
				@parent?.parent?.onSaved?(rt, @parent.tabData, @parent)
				@tab_do_cancel(true)
			return
			
		return

class view_create_dtCardFormView extends view_create_dtCardBaseFormView