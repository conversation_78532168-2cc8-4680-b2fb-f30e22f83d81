class careplan_delivery_tickCardReadBaseView extends CRCardReadView

	tab_can_edit: ->
		sd = @values()
		if sd?.status?.toLowerCase() != 'billed'
			return true
		return false

	tab_can_print: ->
		return false

	tab_can_archive: ->
		return false # should only void delivery ticks, not archive

	tab_can_add: ->
		false

	tab_can_addfill: ->
		false

	tab_label_special_event_1: ->
		return "Work Ticket"

	tab_can_special_event_1: ->
		#return false
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true
		return false

	tab_do_special_event_1: =>
		@print_wt('Work Ticket', 'Pharmacy_PO_Work_Order')

	tab_label_special_event_2: ->
		#return false
		return "Labels"

	tab_label_special_event: ->
		return "Print"

	tab_do_special_event: =>
		fd = @ddr.get_formdata()
		return if not fd
		ticket_no = "x1=#{fd.ticket_no}"
		rx_id = "x1=#{fd.rx_id[0]}" 
		window.openPrintQuantityPopover(fd, form_name: 'careplan_delivery_tick', query_parameters: {delivery_ticket_info: ticket_no, po_label: fd.rx_id, Pharmacy_PO_Work_Order: rx_id, rx_ids: fd.rx_id} )

	tab_can_special_event: ->
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true
		return false

	tab_can_special_event_2: ->
		#return false
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true
		return false

	make_data_for_labels: (dt, subfom=[]) ->
		label = dt.order_id_auto_name or null
		labels = []
		for r in subfom
			lbl = _.cloneDeep(r?.subform_lbl?[0] || {})
			lbl = _.defaults(lbl, r)
			lbl.refills_remaining = lbl.refills_remaining || lbl.doses_remaining
			lbl.print_date = moment().format('MM/DD/YY')
			earliest_expiry = dt.subform_wt_pulled.filter (e) -> 
				e.delivery_ticket_item_uuid == lbl.uuid
			.sort (a, b) -> 
				new Date(a.expiration_date) - new Date(b.expiration_date)
			lbl.expiration_date = earliest_expiry[0]?.expiration_date
			inv = Ajax.sync 
					url: "/form/inventory/" + r.inventory_id
					type: 'GET'
			if !Array.isArray(inv)
				lbl.ndc = inv.ndc
				lbl.manufacturer_id_auto_name = inv.manufacturer_id_auto_name
			prs = Ajax.sync 
					url: "/form/careplan_ordera_item/" + r.order_item_id
					type: 'GET'
			if !Array.isArray(prs)
				lbl.written_date = moment(prs.written_date).format('MM/DD/YY')
			labels.push(lbl)
		duplicateEachElement(labels, 2)

	add_shipping_address: (dt) ->
		shipping_log = dt.subform_delivery_log?[0] || {}
		return dt if _.isEmpty(shipping_log)
		dt.ship_to = shipping_log.ship_to
		dt.ship_street = shipping_log.ship_street
		dt.ship_city = shipping_log.ship_city
		dt.ship_state = shipping_log.ship_state_id
		dt.ship_zip = shipping_log.ship_zip
		pat = Ajax.sync 
				url: "/form/patient/" + dt.patient_id
				type: 'GET'
		if !Array.isArray(pat)
			dt.patient_dob = moment(pat.dob).format('MM/DD/YY')
		dt

	tab_do_special_event_2: ->
		@print_po_label('PO Label', 'po_label')

	tab_label_special_event_3: ->
		return "Delivery Ticket"

	tab_can_special_event_3: ->
		#return false
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true

	tab_do_special_event_3: ->
		@print_dt('Delivery Ticket', 'delivery_ticket_info')

	print_dt: (header, report) ->
		default_header_label = 'Delivery Ticket'
		prettyNotify 'Printing Delivery Ticket...'
		fd = @ddr.get_formdata()
		fro =
			url: """/api/query/delivery_ticket_info?x1=#{fd.ticket_no}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {id: fd.id, printer: 'pharmacy_work_order_printer', patient_id: fd.patient_id}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return


	print_wt: (header, report) =>
		default_header_label = 'Work Order'
		prettyNotify 'Printing Work Ticket...'
		fd = @ddr.get_formdata()
		fro =
			url: """/api/query/work_order?x1=#{fd.rx_id[0]}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {id: fd.id, printer: 'pharmacy_work_order_printer', patient_id: fd.patient_id}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

	updateSupplyDetails: (fd, resp) ->
		if !fd?.subform_supply?.length or !fd?.subform_wt_pulled?.length
			return resp

		if !Array.isArray(resp)
			return []

		pulledObjects = []

		for supply in fd.subform_supply
			fd.subform_wt_pulled
			.filter (i) -> i.delivery_ticket_item_uuid == supply.uuid
			.filter Boolean
			.forEach (i) ->
				pulledObjects.push(
					name: i.inventory_id_auto_name
					lot_no: i.lot_no
					ndc: null
					expiration_date: if moment(i.expiration_date).isValid() then moment(i.expiration_date).format('MM/DD/YYYY') else null
					manufacturer: null
					id: i.inventory_id
					quantity: i.quantity
				)


		primaryResp = resp.find (item) -> item.type_id == 'Primary'

		if primaryResp
			primaryResp.supply_details = pulledObjects

		return resp

	print_po_label: (header, report) ->
		prettyNotify 'Printing PO Label...'
		fd = @ddr.get_formdata()
		fro =
			url: """/api/query/po_labels?x1=#{fd.rx_id[0]}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

	tab_can_special_event_4: ->
		fd = @values?() || {}
		return if not fd?.id
		delivery_log = fd.subform_delivery_log?[0] || {}
		return if not delivery_log?.id
		shipment_status = delivery_log.shipment_status_id
		if shipment_status == 'PENDING'
			return true
		return false

	tab_do_special_event_4: ->
		fd = @ddr.get_formdata()
		return if not fd?.id
		delivery_log = fd.subform_delivery_log?[0] || {}
		return if not delivery_log?.id
		street = delivery_log.ship_street
		city = delivery_log.ship_city
		state = delivery_log.ship_state_id
		zip = delivery_log.ship_zip
		if not street or not city or not state or not zip
			prettyError 'Unable to create shipment label', 'Please enter a valid shipping address.'
			return
		window.openShipmentPopup(fd)
		return

	tab_label_special_event_4: ->
		return "Create Shipment Label"

class careplan_delivery_tickCardReadView extends careplan_delivery_tickCardReadBaseView

class careplan_delivery_tickHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		disabled = @mode is 'read'
		uid = window.next_id('delivery-ticket-scanner')
		@parent.render_id_editor = uid
		@nodes()['embed_item'].parent().parent().parent().after("<div class='delivery-ticket-scanner' id=#{uid}></div>")
		loadComponent('DeliveryTicketScanner',@$("##{uid}")[0], uid, {handle: @, disabled: disabled})
		return

	unload: ->
		unloadComponent('DeliveryTicketScanner', @parent.render_id_editor)
		super()
		return

	nodes: =>
		@parent.field_nodes

	pull_payer_info: (insurance_id) ->
		return if @mode is 'read' or not insurance_id

		tc = Ajax.async
			url: '/form/patient_insurance/' + insurance_id
		tc.done (data, status, xhr) =>
			insur = data[0]
			@parent.value_field 'pri_method_id', insur.billing_method_id, true, true
			@check_secondary_payer_types('secondary_insurance_id', insur.billing_method_id)
			@check_secondary_payer_types('tertiary_insurance_id', insur.billing_method_id)

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class careplan_delivery_tickHandlerView extends careplan_delivery_tickHandlerBaseView

class careplan_delivery_tickCardBaseFormView extends CRCardFormView

	check_payer_settings: (delivery_ticket) ->

		prettyNotify 'Verifying payer rules...'

		data = Ajax.sync
				url: '/dispense?func=check_dispense_payer_settings'
				type: 'POST'
				data: delivery_ticket

		prettyNotify()

		if data.error?.length > 0
			prettyError 'Error checking payer rules', data.error
			return false
		else if data.warnings?.length > 0
			warnings = data.warnings
			block = data?.block == 'Yes'
			if block
				label = 'Dispense Blocked By Payer Rules'
			else
				label = 'Please review the warnings prior to continuing'

			data = {
				dataSource: {
					alert: {
						label: label,
						warnings: warnings
					}
				}
			}
			save = @save_me
			popover = window.openARJsPopover("Payer Rules Warnings", "dt_create_payer_warning", {}, data)
			popover.then (resp) ->
				if !block
					@save_me()
			return false
		else
			return true

	check_items_verified: (delivery_ticket) ->
		data = Ajax.sync
				url: '/dispense?func=check_pulled_items_verified'
				type: 'POST'
				data: delivery_ticket
		if data.error?.length > 0
			return false
		else
			return true

	values: ->
		if @ddf?.subforms?.forms?[0]?.preset?
			@ddf.subforms.forms[0].preset
		else
			null

	save_me: ->
		saved = $.Deferred()
		dfd = @ddf.save()
		dfd.done (rt) =>
			if rt? and rt.values?
				if @parent.options.isFlyout or @parent.parent.tabid is 'flyout_root'
					@parent.save(rt.values, rt.record, @dom, @domcols, @record is null, rt.rowhtml)
					return
				@record = rt.record
				@parent.mode('read', @record)#<===BND
				@parent?.parent?.onSaved?(rt, @parent.tabData, @parent)
				saved.resolve(true)
			else
				saved.resolve(false)
		dfd.fail (err) =>
			saved.reject(false)
		return saved

	save: ->
		form_data = @form_values || @ddf.get_formdata()
		preset = @options.preset
		if !preset
			preset = @ddf?.subforms?.formmap?['careplan_delivery_tick']?.preset
		if !preset
			preset = {}
		was_verified = preset['verified'] == 'Yes'
		if not @ddf?.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return
		try 
			dtItems = this.ddf.subforms.formmap['careplan_delivery_tick'].field_nodes.embed_item.data('helper').getAllData().data
			if (dtItems.length == 0)
				prettyError 'Unable to save', 'There should be at least one drug, supply, compound or rental in this delivery ticket to save it.'
				return
		catch e
			console.error(e)

		if (form_data.was_verified == 'Yes' && !was_verified && !form_data.confirmed == 'Yes')
			if @check_payer_settings(form_data)
				saved = @save_me()
				that = @
				saved.done (data, status, xhr) =>
					return
				return
		else if (form_data.status == 'order_ver' && !@check_items_verified(form_data))
			prettyError 'Unable to save', 'All pulled items must be scanned and verified.\nPlease verify each item by scanning or manually checking.'
			return
		else
			@save_me()
			return
	
	tab_label_special_event_1: ->
		return "Work Ticket"

	tab_can_special_event_1: ->
		return false
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true
		return false

	tab_do_special_event_1: =>
		@print_wt('Work Ticket', 'Pharmacy_PO_Work_Order')

	tab_label_special_event_2: ->
		return "Ready To Fill"

	tab_can_special_event_2: ->
		fd = @values?() || {}
		if ['delivery_ticket'].includes(fd.status)
			return true
		return false

	tab_label_special_event: ->
		return "Print"

	checkPrintItems: (ticket_no) ->
		fro =
			url: """/form/careplan_dt_item?filter=ticket_no:#{ticket_no}"""
			method: "GET"
		fr = request(fro)
		
		fr.then (resp) =>
			if resp?.data?.length
				# Filter out rx_ids where print is null
				filtered_rx_ids = []
				for item in resp.data
					if item.print == 'Yes' && item.rx_id
						filtered_rx_ids.push(item.rx_id)
				return filtered_rx_ids
			return []

	tab_do_special_event: =>
		fd = @ddf.get_formdata()
		return if not fd
		@checkPrintItems(fd.ticket_no).then (filtered_rx_ids) =>
			ticket_no = "x1=#{fd.ticket_no}"
			rx_id = if filtered_rx_ids.length then "x1=#{filtered_rx_ids[0]}" else "x1=#{fd.rx_id[0]}"
			
			window.openPrintQuantityPopover(fd,
				form_name: 'careplan_delivery_tick'
				query_parameters: {
					delivery_ticket_info: ticket_no
					po_label: ticket_no
					Pharmacy_PO_Work_Order: rx_id
					rx_ids: if filtered_rx_ids.length then filtered_rx_ids else fd.rx_id
				}
			)

	tab_can_special_event: ->
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true
		return false

	make_data_for_labels: (dt, subfom=[]) ->
		label = dt.order_id_auto_name or null
		labels = []
		for r in subfom
			lbl = _.cloneDeep(r?.subform_lbl?[0] || {})
			lbl = _.defaults(lbl, r)
			lbl.refills_remaining = lbl.refills_remaining || lbl.doses_remaining
			lbl.print_date = moment().format('MM/DD/YY')
			earliest_expiry = dt.subform_wt_pulled.filter (e) -> 
				e.delivery_ticket_item_uuid == lbl.uuid
			.sort (a, b) -> 
				new Date(a.expiration_date) - new Date(b.expiration_date)
			lbl.expiration_date = earliest_expiry[0]?.expiration_date
			inv = Ajax.sync 
					url: "/form/inventory/" + r.inventory_id
					type: 'GET'
			if !Array.isArray(inv)
				lbl.ndc = inv.ndc
				lbl.manufacturer_id_auto_name = inv.manufacturer_id_auto_name
			prs = Ajax.sync 
					url: "/form/careplan_ordera_item/" + r.order_item_id
					type: 'GET'
			if !Array.isArray(prs)
				lbl.written_date = moment(prs.written_date).format('MM/DD/YY')
			labels.push(lbl)
		duplicateEachElement(labels, 2)

	add_shipping_address: (dt) ->
		shipping_log = dt.subform_delivery_log?[0] || {}
		return dt if _.isEmpty(shipping_log)
		dt.ship_to = shipping_log.ship_to
		dt.ship_street = shipping_log.ship_street
		dt.ship_city = shipping_log.ship_city
		dt.ship_state = shipping_log.ship_state_id
		dt.ship_zip = shipping_log.ship_zip
		pat = Ajax.sync 
				url: "/form/patient/" + dt.patient_id
				type: 'GET'
		if !Array.isArray(pat)
			dt.patient_dob = moment(pat.dob).format('MM/DD/YY')
		dt

	tab_do_special_event_2: ->
		@ready_to_fill()

	tab_label_special_event_3: ->
		return "Delivery Ticket"

	tab_can_special_event_3: ->
		return false
		fd = @values?() || {}
		if ['ready_to_fill', 'order_ver', 'pending_conf', 'ready_to_bill', 'billed'].includes(fd.status)
			return true

	tab_do_special_event_3: ->
		@print_dt('Delivery Ticket', 'delivery_ticket_info')

	print_dt: (header, report) ->
		default_header_label = 'Delivery Ticket'
		prettyNotify 'Printing Delivery Ticket...'
		fd = @ddf.get_formdata()
		fro =
			url: """/api/query/delivery_ticket_info?x1=#{fd.ticket_no}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {id: fd.id, printer: 'pharmacy_work_order_printer', patient_id: fd.patient_id}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return


	print_wt: (header, report) =>
		default_header_label = 'Work Order'
		prettyNotify 'Printing Work Ticket...'
		fd = @ddf.get_formdata()
		fro =
			url: """/api/query/work_order?x1=#{fd.rx_id[0]}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {id: fd.id, printer: 'pharmacy_work_order_printer', patient_id: fd.patient_id}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

	updateSupplyDetails: (fd, resp) ->
		if !fd?.subform_supply?.length or !fd?.subform_wt_pulled?.length
			return resp

		if !Array.isArray(resp)
			return []

		pulledObjects = []

		for supply in fd.subform_supply
			fd.subform_wt_pulled
			.filter (i) -> i.delivery_ticket_item_uuid == supply.uuid
			.filter Boolean
			.forEach (i) ->
				pulledObjects.push(
					name: i.inventory_id_auto_name
					lot_no: i.lot_no
					ndc: null
					expiration_date: if moment(i.expiration_date).isValid() then moment(i.expiration_date).format('MM/DD/YYYY') else null
					manufacturer: null
					id: i.inventory_id
					quantity: i.quantity
				)


		primaryResp = resp.find (item) -> item.type_id == 'Primary'

		if primaryResp
			primaryResp.supply_details = pulledObjects

		return resp

	print_po_label: (header, report) ->
		prettyNotify 'Printing PO Label...'
		console.log('print_po_label')
		fd = @ddf.get_formdata()
		console.log('fd', fd)
		fro =
			url: """/api/query/po_labels?x1=#{fd.rx_id[0]}"""
			method: "GET"
		fr = request(fro)

		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			label = fd.auto_name or fd.order_id_auto_name or null
			window.openARJsPopover(joinValid([header, label], ' | '), report, {}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

	ready_to_fill: ->
		fd = @ddf?.get_formdata?()
		return if not fd
		@ddf.subforms.formmap.careplan_delivery_tick.value_field 'ready_to_fill', 'Yes', true, true
		@save()

	tab_can_special_event_4: ->
		fd = @values?() || {}
		return if not fd?.id
		delivery_log = fd.subform_delivery_log?[0] || {}
		return if not delivery_log?.id
		shipment_status = delivery_log.shipment_status_id
		if shipment_status == 'PENDING'
			return true
		return false

	tab_do_special_event_4: ->
		fd = @ddf.get_formdata()
		return if not fd?.id
		delivery_log = fd.subform_delivery_log?[0] || {}
		return if not delivery_log?.id
		street = delivery_log.ship_street
		city = delivery_log.ship_city
		state = delivery_log.ship_state_id
		zip = delivery_log.ship_zip
		if not street or not city or not state or not zip
			prettyError 'Unable to create shipment label', 'Please enter a valid shipping address.'
			return
		window.openShipmentPopup(fd)
		return

	tab_label_special_event_4: ->
		return "Create Shipment Label"

class careplan_delivery_tickCardFormView extends careplan_delivery_tickCardBaseFormView