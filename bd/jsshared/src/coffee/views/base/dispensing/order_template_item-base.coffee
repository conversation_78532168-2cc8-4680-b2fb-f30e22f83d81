DSLFx.Validators.SetTemplateItemInfo = (form, dd, vld, f, k) ->
	dd.handler?.set_item_info?(dd.value_field('inventory_id'))
	return

class Order_template_itemHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	set_item_info: (inv_id) ->
		return if (@mode is 'read') or not inv_id

		dfd = $.Deferred()
		tc = Ajax.async
			url: '/form/inventory/' + inv_id
		tc.done (data, status, xhr) =>
			invi = data[0]
			@parent.value_field 'unit_id', invi.dispense_unit_id, true, true
			@parent.value_field 'therapy_id', invi.therapy_id, true, true
			@parent.value_field 'brand_name_id', invi.brand_name_id, true, true
			@parent.value_field 'storage', invi.storage, true, true
			@parent.value_field 'route_id', invi.route_id, true, true

			dfd.resolve()

		tc.fail (xhr, status, data) ->
			dfd.reject 'Unable to fetch inventory details. Please try again.'

		dfd

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Order_template_itemHandlerView extends Order_template_itemHandlerBaseView