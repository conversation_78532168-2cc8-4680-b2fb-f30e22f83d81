class careplan_order_rxCardBaseFormView extends CRCardFormView

	tab_label_special_event_1: ->
		return "Print Verbal Order"

	tab_do_special_event_1: ->
		@print_careplan_order_rx('Prescription Verbal Order RX', 'pres_verbal_order_rx')

	tab_can_special_event_1: ->
		return if not @ddf.form_rendered
		fd = @ddf.get_formdata?() || {}
		if not fd?.id
			return false
		resp = Ajax.sync 
				url: """/query/pres_verbal_order_rx?x1=#{fd.id}"""
				type: 'GET'
		if ['rxform_add1', 'rxform_add2', 'rxform_add3'].every (key) -> resp?[key]?
			return true
		return false

	print_careplan_order_rx: (header, report) =>
		prettyNotify 'Printing Prescription Verbal Order RX...'
		fd = @ddf.get_formdata()
		if not fd?.id
			prettyError 'Error While Printing', 'No Pres Verbal Order RX ID found.'
			prettyNotify()
			return

		fro =
			url: """/api/query/pres_verbal_order_rx?x1=#{fd.id}"""
			method: "GET"
		fr = request(fro)
		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			window.openARJsPopover(header, report, {}, { ...data, printer: UserPreference?.subform_printers?[0]?.verbal_order_printer or '' });

		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return


class careplan_order_rxCardFormView extends careplan_order_rxCardBaseFormView

class careplan_order_rxBaseView extends CRView

	nodes: =>
		@parent.field_nodes

	handleWrapLineFields: (subform_fields_nodes, wrapLineFields) ->
		wrapLineFields.forEach (field, index) =>
			field.node.on 'input', =>
				currentValue = DSLFields.value_get(field.node)
				if field.transform?.max and currentValue?.length >= field.transform?.max
					lastSpaceIndex = currentValue.lastIndexOf(' ')
					if lastSpaceIndex is -1 or lastSpaceIndex is currentValue.length - 1
						subform_fields_nodes[field.transform?.next_field]?.focus()
						return
					else
						nextField = subform_fields_nodes[field.transform?.next_field]
						if nextField
							wordToMove = currentValue.slice(lastSpaceIndex + 1)
							DSLFields.value_set(field.node, currentValue.slice(0, lastSpaceIndex + 1))
							nextFieldValue = DSLFields.value_get(nextField) or ''
							DSLFields.value_set(nextField, wordToMove + (if nextFieldValue then ' ' + nextFieldValue else ''))
							nextField.focus()
						return

				if currentValue?.length is 0 and index > 0 and event?.inputType is 'deleteContentBackward'
					prevField = wrapLineFields[index - 1].node
					prevValue = DSLFields.value_get(prevField)
					prevField.focus()
					if prevValue?.length > 0
						prevField[0].setSelectionRange(prevValue.length, prevValue.length)
					return
				return

	handle: (dfd) ->
		dfd.resolve()
		Object.values(@nodes()).forEach (field_node) =>
			tranform_data = field_node.data('v')?.view?.transform.find (item) -> item?.name is 'WrapLine'
			if tranform_data? and Object.keys(tranform_data).length > 0
				field_node.on 'input', =>
					if tranform_data?.max and DSLFields.value_get(field_node)?.length >= tranform_data?.max
						subform_fields_nodes[tranform_data?.next_field]?.focus()
						return
					return

		subform = @parent?.subform.field_source?.label_form
		if !subform
			return

		subform_fields_nodes = @parent.options.wrapper.subforms.formmap[subform].field_nodes
		wrapLineFields = []
		Object.entries(subform_fields_nodes).forEach ([field_name, field_node]) =>
			tranform_data = field_node.data('v')?.view?.transform.find (item) -> item?.name is 'WrapLine'
			if tranform_data? and Object.keys(tranform_data).length > 0
				wrapLineFields.push({
					name: field_name,
					node: field_node,
					transform: tranform_data
				})

		@handleWrapLineFields(subform_fields_nodes, wrapLineFields)
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class careplan_order_rxHandlerView extends careplan_order_rxBaseView