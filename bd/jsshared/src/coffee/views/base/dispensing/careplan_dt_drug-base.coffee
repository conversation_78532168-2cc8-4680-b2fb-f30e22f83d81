class careplan_dt_drugCardFormBaseView extends CRCardFormView

	tab_can_archive: ->
		return false # should only void delivery ticks, not archive

	tab_label_special_event_1: ->
		return "Print Label"

	tab_can_special_event_1: ->
		return true

	tab_do_special_event_1: ->
		fd = @ddf.values()
		label = fd.auto_name or fd.order_id_auto_name or null
		lbl = _.cloneDeep(fd?.subform_lbl?[0] || {})
		lbl = [_.defaults(lbl, fd)]
		dt = @?.options?.parent?.parent?.subform_parent?.parent?.ddf?.get_formdata()
		data =
			dataSource:
				lbl: duplicateEachElement(lbl, 2)
				dt: dt
		params =
			site_id: dt.site_id or -1
			user_id: App.user.id or -1
		console.log data, params if not App.version.nes.is_prod
		window.openARJsPopover(joinValid(['Drug Label', label], ' | '), 'dt_drug_lbl', params, data)

class careplan_dt_drugCardFormView extends careplan_dt_drugCardFormBaseView



class careplan_dt_drugCardReadBaseView extends CRCardReadView

	tab_can_archive: ->
		return false # should only void delivery ticks, not archive

	tab_label_special_event_1: ->
		return "Print Label"

	tab_can_special_event_1: ->
		return true

	tab_do_special_event_1: ->
		fd = @ddr.values()
		label = fd.auto_name or fd.order_id_auto_name or null
		lbl = _.cloneDeep(fd?.subform_lbl?[0] || {})
		lbl = [_.defaults(lbl, fd)]
		dt = @?.options?.parent?.parent?.subform_parent?.parent?.ddr?.get_formdata()
		data =
			dataSource:
				lbl: duplicateEachElement(lbl, 2)
				dt: dt
		params =
			site_id: dt.site_id or -1
			user_id: App.user.id or -1
		console.log data, params if not App.version.nes.is_prod
		window.openARJsPopover(joinValid(['Drug Label', label], ' | '), 'dt_drug_lbl', params, data)

class careplan_dt_drugCardReadView extends careplan_dt_drugCardReadBaseView