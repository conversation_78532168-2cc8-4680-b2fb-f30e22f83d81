class verbal_orderCardBaseFormView extends CRCardFormView

	tab_label_special_event_1: ->
		return "Print"

	tab_do_special_event_1: ->
		@print_verbal_order('Print Verbal Order', 'verbal_order')

	tab_can_special_event_1: ->
		return false unless @ddf.form_rendered and (@ddf.get_formdata?()?.id?)
		return true

	print_verbal_order: (header, report, res_data) =>
		prettyNotify 'Printing Verbal Order...'
		fd = if Object.keys(res_data ? {}).length then res_data else @ddf.get_formdata()
		if not fd?.id
			prettyError 'Error While Printing', 'No Verbal Order ID found.'
			prettyNotify()
			return
		fro =
			url: """/api/query/verbal_order?x1=#{fd.id}"""
			method: "GET"
		fr = request(fro)
		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			window.openARJsPopover(header, report, {}, { ...data, printer: UserPreference?.subform_printers?[0]?.verbal_order_printer or '' });

		fr.catch (resp) ->
			prettyNotify()
			console.error("Error while printing verbal order", resp)
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return

		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		dfd = @ddf.save()
		dfd.done (res_data) =>
			prettyYesNo 'Print Verbal Order', 'Do you want to print verbal order?', =>
				@print_verbal_order('Print Verbal Order', 'verbal_order', res_data.values)
				return
			, =>
				@tab_do_cancel(true)
				return		
			return

		return

class verbal_orderCardFormView extends verbal_orderCardBaseFormView


class verbal_orderCardReadBaseView extends CRCardReadView

	tab_label_special_event_1: ->
		return "Print"

	tab_can_special_event_1: ->
		return true

	tab_do_special_event_1: ->
		@print_verbal_order('Print Verbal Order', 'verbal_order')

	print_verbal_order: (header, report) =>
		prettyNotify 'Printing Verbal Order...'
		fd = @ddr.values()
		if not fd?.id
			prettyError 'Error While Printing', 'No Verbal Order ID found.'
			prettyNotify()
			return
		fro =
			url: """/api/query/verbal_order?x1=#{fd.id}"""
			method: "GET"
		fr = request(fro)
		fr.then (resp) =>
			prettyNotify()
			data =
				dataSource:
					apiform: resp.data
			window.openARJsPopover(header, report, {}, { ...data, printer: UserPreference?.subform_printers?[0]?.verbal_order_printer or '' });

		fr.catch (resp) ->
			prettyNotify()
			console.error("Error while printing verbal order", resp)
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

class verbal_orderCardReadView extends verbal_orderCardReadBaseView