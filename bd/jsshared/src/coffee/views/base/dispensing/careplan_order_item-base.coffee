DSLFx.Validators.OrderPullPriPayerInfo = (form, dd, vld, f, k) ->
	dd.handler?.pull_payer_info?(dd.value_field("insurance_id"))
	return

DSLFx.Validators.AutoGenLabels = (form, dd, vld, f, k) ->
	dd.handler?.auto_gen_label?(dd.value_field('auto_gen_label'))
	return

class Careplan_order_itemHandlerBaseView extends CRView

	handle: (dfd) ->

		if not ((@mode is 'addfill') or (@mode is 'add'))
			dfd.resolve()
			return

		# Default fill number. DO NOT CHANGE, used in billing
		@parent.value_field 'next_fill_no', 1, true, true
		dfd.resolve()
		return

	nodes: =>
		@parent.field_nodes

	pull_payer_info: (insurance_id) ->
		return if @mode is 'read' or not insurance_id

		tc = Ajax.async
			url: '/form/patient_insurance/' + insurance_id
		tc.done (data, status, xhr) =>
			insur = data[0]
			@parent.value_field 'pri_method_id', insur.billing_method_id, true, true
			@check_secondary_payer_types('secondary_insurance_id', insur.billing_method_id)
			@check_secondary_payer_types('tertiary_insurance_id', insur.billing_method_id)

	check_secondary_payer_types: (field, billing_method) ->
		insurance_id = @parent.value_field(field)

		return if @mode is 'read' or not insurance_id

		tc = Ajax.async
			url: '/form/patient_insurance/' + insurance_id
		tc.done (data, status, xhr) =>
			insur = data[0]
			if insur.billing_method_id != billing_method
				@parent.value_field field, '', true, true
		tc.fail (xhr, status, data) =>
			@parent.value_field field, '', true, true

	auto_gen_label: (auto_gen_label) ->
		return if (@mode is 'read') # or auto_gen_label not 'Yes'

		#TODO - Add logic to auto generate label
		return

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class Careplan_order_itemHandlerView extends Careplan_order_itemHandlerBaseView

class Careplan_order_itemCardFormView extends CRCardFormView
