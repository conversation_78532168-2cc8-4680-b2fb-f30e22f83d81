class billing_postingHandlerBaseView extends CRView

	nodes: =>
		@parent.field_nodes

	handle: (dfd) ->
		dfd.resolve()
		uid = window.next_id('outstanding-ar')
		@nodes()['charge_line_postings'].parent().parent().parent().after("<div class='form-horizontal form-col-1' id=#{uid}></div>")
		if ['add', 'addfill'].includes(@mode)
			loadComponent('OutstandingAR',@$("##{uid}")[0], @parent.options.id, {handle: @})
		else
			data = @parent?.preset?.charge_line_postings or []
			loadComponent('OutstandingARReadOnly',@$("##{uid}")[0], @parent.options.id, {data: data})
			that = @ 
			setTimeout =>
				that.parent.value_field("charge_line_postings", JSON.stringify(data), true, true); # JSON field lose there data if they are not control grid
			, 1000
		return

	unload: ->
		if ['add', 'addfill'].includes(@mode)
			unloadComponent('OutstandingAR', @parent.options.id)
		else
			unloadComponent('OutstandingARReadOnly', @parent.options.id)
		super()
		return

class billing_postingHandlerView extends billing_postingHandlerBaseView