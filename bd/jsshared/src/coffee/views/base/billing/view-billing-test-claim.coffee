window.get_ncpdp_data = (d) ->
	return {} if !d
	cd = d
	if getType(cd) is 'object'
		return cd
	if(Array.isArray(cd))
		if cd.length > 0
			cd = cd[0]
		else
			cd = {}
	return cd



window.open_ncpdp = (rt, overrides={}) ->
	ncpdp_id = rt.id or rt.values.id
	if not ncpdp_id
		prettyError 'Error Running Eligiblity Check', "Unable to fetch NCPDP ID"
		return
	response_preset = {}
	fd = rt.values
	
	if fd.subform_response and fd.subform_response.length
		response_preset = fd.subform_response.reduce (max, current) ->
			if current.id > max.id then current else max
	if fd?.segment_patient?.length and fd.segment_patient[0].preview_response and fd.segment_patient[0].preview_response?.length
		response_preset = fd.segment_patient[0].preview_response.reduce (max, current) ->
			if current.id > max.id then current else max
	if fd.preview_response and fd.preview_response.length
		response_preset = fd.preview_response.reduce (max, current) ->
			if current.id > max.id then current else max
		
	if getType(response_preset) isnt 'object'
		response_preset = {}
	if !DSL['view_ncpdp_response']
		cp = _.cloneDeep(DSL.ncpdp_response)
		cp.model.save = false
		for k, v of cp.fields
			cp.fields[k].view.readonly = true
		DSL['view_ncpdp_response'] = cp
	Flyout.open
		action: 'PreviewFillCardView'
		form: 'ncpdp'
		card: 'edit'
		tab_can_special_event_override: overrides.tab_can_special_event
		tab_can_special_event_1_override: overrides.tab_can_special_event_1
		record: ncpdp_id
		autoRecoverEnabled: false
		preview:
			type: 'DSLCardView'
			form: 'view_ncpdp_response'
			preset: response_preset
			card: 'add'
			autoRecoverEnabled: false
	resp.catch (error) =>
		console.log error
		prettyError 'Error Opening NCPDP Entery', error

class view_billing_claim_testCardBaseFormView extends CRCardFormView

	tab_label_cancel: ->
		"Close"

class view_billing_claim_testCardFormView extends view_billing_claim_testCardBaseFormView
