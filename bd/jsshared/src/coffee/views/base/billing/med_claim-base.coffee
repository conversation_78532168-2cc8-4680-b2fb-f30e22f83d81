
class med_claimCardFormBaseView extends CRCardFormView
	tab_label_special_event: ->
		return "CMS 1500"

	tab_can_special_event: ->
		return  true

	tab_do_special_event: ->
		return if not @ddf.form_rendered

		prettyNotify 'Printing CMS 1500...'
		fd = @ddf.get_formdata()
		fro =
			url: """/api/print/1500/med_claim"""
			method: "POST"
			data: fd
		fr = request(fro)

		fr.then (resp) ->
			prettyNotify()

			if !resp.success or resp.data?.error
				prettyError 'Error Generating PDF', resp.data?.error
				return

			data =
				dataSource:
					cms: formatForReporting(resp.data, ["f24"]) or {}
			window.openARJsPopover("CMS 1500", "cms1500",{}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

class med_claimCardFormView extends med_claimCardFormBaseView

class med_claimCardReadBaseView extends CRCardReadView
	tab_label_special_event: ->
		return "CMS 1500"

	tab_can_special_event: ->
		return  true

	tab_do_special_event: ->
		return if not @ddr.form_rendered

		id = @ddr.dr.options.record
		fro =
			url: """/api/print/1500/#{id}"""
			method: "GET"
		fr = request(fro)
		prettyNotify 'Printing CMS 1500...'
		fr.then (resp) ->
			prettyNotify()
			if !resp.success or resp.data?.error
				prettyError 'Error Generating PDF', resp.data?.error
				return
			data =
				dataSource:
					cms: formatForReporting(resp.data, ["f24"]) or {}
			window.openARJsPopover("CMS 1500", "cms1500", {}, data)
		fr.catch (resp) ->
			prettyNotify()
			prettyError 'Error While Printing', resp.data?.error or resp.data?.response?.data?.error or "Unexpect error occurred."
			return

class med_claimCardReadView extends med_claimCardReadBaseView
