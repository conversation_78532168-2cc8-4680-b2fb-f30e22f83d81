#TODO: remove this file @Osama
DSLFx.Validators.BIPullPriPayerInfo = (form, dd, vld, f, k) ->
	dd.handler?.pull_payer_info?(dd.value_field("insurance_id"))
	return

class Billing_itemHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()

		return if not ((@mode is 'addfill') or (@mode is 'add'))
		DSLFields.value(@nodes()['unit_id'], 'EA') # Dispensed quantity is always per each

		return

	nodes: =>
		@parent.field_nodes

	pull_payer_info: (insurance_id) ->
		return if @mode is 'read' or not insurance_id

		tc = Ajax.async
			url: '/form/patient_insurance/' + insurance_id
		tc.done (data, status, xhr) =>
			insur = data[0]
			@parent.value_field 'pri_method_id', insur.billing_method_id, true, true

class Billing_itemHandlerView extends Billing_itemHandlerBaseView
