DSLFx.Validators.WriteoffBlock = (form, dd, vld, vals, k) ->

	if vals.close_id
		false # Never edit a closed invoice that has already had revenue booked
	allowed_fields = vld.allowed_fields
	if (vals.void and vals.void is 'Yes')
		k in allowed_fields
	else
		true

class billing_writeoffHandlerBaseView extends CRView

	nodes: =>
		@parent.field_nodes

	handle: (dfd) ->
		dfd.resolve()
		uid = window.next_id('writeoff-ar')
		@nodes()['charge_lines_applied'].parent().parent().parent().after("<div class='form-horizontal form-col-1' id=#{uid}></div>")
		if ['add', 'addfill'].includes(@mode)
			patient_id = @parent.preset.patient_id or @parent.value_field('patient_id')
			invoice_no = @parent.preset.invoice_no or @parent.value_field('invoice_no')
			loadComponent('WriteOffsGrid',@$("##{uid}")[0], @parent.options.id, {handle: @, patient_id: patient_id, invoice_no: invoice_no})
		else
			data = @parent?.preset?.charge_lines_applied or []
			loadComponent('WriteOffsGridReadOnly',@$("##{uid}")[0], @parent.options.id, {data: data})
			that = @ 
			setTimeout =>
				that.parent.value_field("charge_lines_applied", JSON.stringify(data), true, true); # JSON field lose there data if they are not control grid
			, 1000
		return

	unload: ->
		if ['add', 'addfill'].includes(@mode)
			unloadComponent('WriteOffsGrid', @parent.options.id)
		else
			unloadComponent('WriteOffsGridReadOnly', @parent.options.id)
		super()
		return

class billing_writeoffHandlerView extends billing_writeoffHandlerBaseView