class Ss_messageCardBaseFormView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return
		form_data = @ddf.get_formdata()
		return if form_data.direction == 'IN' && (super.save() || true)
		return if not @ddf.verify() && ($.notify 'Missing required fields.' || true)

		prettyNotify 'Sending message...'
		@ddf.options.form_override_url = '/surescripts/outbound'
		closeNotify = ->
			setTimeout ->
				prettyNotify()
			, 3000
		dfd = @ddf.save()
		dfd.done (data) =>
			return if data?.error
				closeNotify()
				prettyError 'Error Sending Message', data?.error
				false
			else
				closeNotify()
				prettyNotify 'Message Sent Successfully'
				@tab_do_cancel(true)
		return

class Ss_messageCardFormView extends Ss_messageCardBaseFormView
