DSLFx.Validators.SSCheckEventFilters = (form, dd, vld, f, k) ->
	dd.handler?.check_filter_dependencies?()

class View_ss_order_generatorHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		setTimeout =>
			@handle_filter_source_filter()
		,1000
		return

	handle_filter_source_filter: () =>
		filters = @parent.value_field('filter') || @parent.preset.filter || []
		_.forEach @parent.field_nodes['event'].find('input[type="checkbox"]'), (t) ->
			v = $(t).attr('value')
			if filters.includes(v)
				$(t).parent().show()
			else
				$(t).parent().hide()
			return

	check_filter_dependencies: () ->
		return if (@mode is 'read')
		dependencies =
				'intake': [{
						field: 'patient_insurance'
						event: 'insurance',
						error: 'Must have an existing insurance selected or generating an insurance record for intake generation.'
						},{
						event: 'careplan',
						error: 'Must also generate a careplan if generating an intake' }],
				'careplan': [{
						field: 'patient_id',
						event: 'patient',
						error: 'Must have an existing patient selected or generating a patient record for careplan generation.' }],
				'prescriber': [{
						field: 'physician_id',
						event: 'physician',
						error: 'Must have an existing physician selected or generating a physician record for patient prescriber generation.' }],
				'order': [{
						field: 'patient_insurance'
						event: 'insurance',
						error: 'Must have an existing insurance selected or generating an insurance record for order generation.'
						},{
						field: 'patient_prescriber_id',
						event: 'prescriber',
						error: 'Must have an existing prescriber selected or generating a prescriber record for order generation.'
						},{
						field: 'patient_diagnosis_id',
						event: 'diagnosis',
						error: 'Must have an existing diagnosis selected or generating a diagnosis record for order generation.'
						},{
						event: 'careplan',
						error: 'Must have also generate careplan with order generation.'
						},{
						event: 'intake',
						error: 'Must have also generate intake with order generation.'
						}]
				'pa': [{
						field: 'patient_insurance'
						event: 'insurance',
						error: 'Must have an exiting insurance selected or generating an insurance record for PA generation.'
						},{
						field: 'pharmacy_order_id',
						event: 'intake',
						error: 'Must have an exiting order selected or generating an intake record for PA generation.'
						}]
				'insurance': [{
						field: 'payer_id'
						error: 'Must have an exiting payer selected for generating an insurance record'
						}]

		ext_events = @parent.value_field('event') || @parent.preset.event || []
		filters = @parent.value_field('filter') || @parent.preset.filter || []
		events = _.cloneDeep(ext_events)
		errors = []

		for key, val of dependencies
			if events.includes(key)
				for depend in val
					err = @check_dependency(depend, events)
					errors.push err if err

		show_alerts = if errors.length > 0 then 'Yes' else 'No'
		@parent.value_field 'show_event_alerts', show_alerts, true, true

		if errors.length > 0
			events = events.filter((event) -> filters.includes(event))
			alerts = errors.join '\n'
			@parent.value_field 'event_alerts', alerts, true, true
			@parent.value_field 'event', events, true, true
			'Errors found when checking record dependencies. Please see Generation Event Alerts for details'
		else
			false

	check_dependency: (dependency, events, filters) ->
		field = dependency?.field || null
		value = @parent.value_field(field) || null
		event = dependency?.event || null
		error = dependency?.error || 'Invalid Selection'
		field_check = if field then (value != null) else false
		event_check = if event then events.includes(event) else false
		events.push event if not field_check and not event_check and event
		return error if not field_check and not event_check

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class View_ss_order_generatorHandlerView extends View_ss_order_generatorHandlerBaseView

class View_ss_order_generatorCardBaseFormView extends CRCardFormView

	tab_label_save: ->
		"Generate"

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		prettyNotify 'Generating workflow...'
		@ddf.options.form_override_url =  '/surescripts/?func=generate_records'
		dfd = @ddf.save()
		that = @
		dfd.done (rt) =>
			if rt?.values?.error
				prettyNotify()
				prettyError 'Error Generating Workflow', rt?.values?.error
				return
			else
				prettyNotify()
				if rt.values?
					if that.parent.options.isFlyout or that.parent.parent.tabid is 'flyout_root'
						that.parent.save(rt.values, rt.record, that.dom, that.domcols, that.record is null, rt.rowhtml)
						return
					that.record = rt.record
					that.parent?.parent?.onSaved?(rt, that.parent.tabData, that.parent)
					@tab_do_cancel(true)
		dfd.fail (err) => 
			prettyNotify()
		return

class View_ss_order_generatorCardFormView extends View_ss_order_generatorCardBaseFormView
