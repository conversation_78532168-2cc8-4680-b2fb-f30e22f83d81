class shipmentCardReadBaseView extends CRCardReadView

	tab_can_edit: ->
		return false
	
	tab_can_print: ->
		return false
	
	tab_can_archive: ->
		sd = @values()
		if sd?.shipment_status?.toLowerCase() is 'success'
			return true
		return false

	tab_do_archive: ->
		mode = 'Void'
		txt = ""

		prettyYesNo mode.proper() + ' record?', 'Are you sure you want to ' + mode + ' this ' + pluralize.singular(@formname) + '?'+ txt, =>
			aj = Ajax.async
				type: 'DELETE'
				url: '/label/' + @record
			aj.done (data, status, xhr) =>
				@parent.archive(@record, @dom, @domcols)
			aj.fail (xhr, status, data) ->
				prettyError false, 'Sorry! Cannot ' + mode + ' this record.<br/><br/>' + ERR_CONTACT
			return
		, =>
			@cancel() if @err_loading
			return
		return
	
	tab_label_archive: ->
		return "Void Shipment"


class shipmentCardReadView extends shipmentCardReadBaseView