
DSLFx.Validators.ValidateAdjustmentAmount = (form, dd, vld, f, k) ->
	dd.handler?.check_adjustment_amount?(dd, dd.value_field('site_id'), dd.value_field('inventory_id'), dd.value_field('lot_id'), dd.value_field('serial_id'), dd.value_field('quantity'))
	return

class View_inventory_adjustmentHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()

	check_adjustment_amount: (dd, site_id, inventory_id, lot_id, serial_id, quantity) ->
		return if (@mode is 'read') or not site_id or not inventory_id or not quantity

		if quantity == 0
			return DSLFx.ValidateFieldError 'quantity', 'Quantity must be greater than 0.'

		if quantity > 0
			return # No need to check for positive quantity

		dfd = $.Deferred()

		url = '/inventory?func=check_stock&site_id=' + site_id + '&inventory_id=' + inventory_id
		if lot_id
			url += '&lot_id=' + lot_id
		if serial_id
			url += '&serial_id=' + serial_id

		$.notify 'Checking Inventory Stock...'

		quantity = Math.abs(quantity)
		aj = Ajax.async
			url: url
		aj.done (data, status, xhr) ->
			$.notify()
			if data?.results?.inv_quantity?
				inv_quantity = data.results.inv_quantity
				lot_quantity = data.results.lot_quantity
				serial_quantity = data.results.sni_quantity
				
				if (serial_id and (quantity > serial_quantity))
					dfd.resolve()
					return DSLFx.ValidateFieldError dd.field_nodes['quantity'], 'Quantity in stock is less than adjustment amount. (Serial Stock: ' + serial_quantity + ')'
				else if (lot_id and (quantity > lot_quantity))
					dfd.resolve()
					return DSLFx.ValidateFieldError dd.field_nodes['quantity'], 'Quantity in stock is less than adjustment amount. (Lot Stock: ' + lot_quantity + ')'
				else if (quantity > inv_quantity)
					dfd.resolve()
					return DSLFx.ValidateFieldError dd.field_nodes['quantity'], 'Quantity in stock is less than adjustment amount. (Stock: ' + inv_quantity + ')'
			else
				prettyError 'Error Fetching In-Stock', 'We encountered an error fetching the stock information. Please try again and contact support if the problem persists.'
				dd.value_field 'quantity', '', false, false

			dfd.resolve()
		aj.fail (xhr, status, error) ->
			$.notify()
			prettyError 'Error Fetching Stock', 'We encountered an error fetching the stock information. Please try again and contact support if the problem persists.'
			dd.value_field 'quantity', '', false, false

			dfd.resolve()

		dfd

# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class View_inventory_adjustmentHandlerView extends View_inventory_adjustmentHandlerBaseView

class View_inventory_adjustmentCardBaseFormView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
	
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		prettyNotify 'Adjusting Inventory...'

		@ddf.options.form_override_url = '/inventory/adjustment'
		dfd = @ddf.save()
		dfd.done (data) =>
			if data?.error || data?.values?.error
				setTimeout ->
					prettyNotify()
				, 3000
				prettyError 'Error Saving Adjustment', data?.error || data?.values?.error
				return
			else
				setTimeout ->
					prettyNotify()
				, 3000
				prettyNotify 'Adjustment successful'
				@tab_do_cancel(true)
				@parent?.tabViewActions?.openTab?(data?.values?.receipt_id, 'Adjustment Receipt', 'read', 'receipt_adjustment', {})

		return

class View_inventory_adjustmentCardFormView extends View_inventory_adjustmentCardBaseFormView
