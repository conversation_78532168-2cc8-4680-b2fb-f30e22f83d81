class View_inventory_transferCardBaseFormView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		
		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		prettyNotify 'Transferring items into inventory...'
		@ddf.options.form_override_url =  '/inventory/transfer'
		dfd = @ddf.save()
		dfd.done (data) =>
			if data?.error || data?.values?.error
				setTimeout ->
					prettyNotify()
				, 3000
				prettyError 'Error Saving Transfer', data?.error || data?.values?.error
				return
			else
				setTimeout ->
					prettyNotify()
				, 3000
				prettyNotify 'Transferring successful'
				@tab_do_cancel(true)
				@parent?.tabViewActions?.openTab?(data?.values?.receipt_id, 'Transfer Receipt', 'read', 'receipt_transfer', {})
		return

class View_inventory_transferCardFormView extends View_inventory_transferCardBaseFormView
