class View_inventory_lotsCardBaseFormView extends CRCardFormView

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return

		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return

		prettyNotify 'Loading items into inventory...'
		@ddf.options.form_override_url =  '/inventory/receive'
		dfd = @ddf.save()
		dfd.done (data) =>
			if data?.error || data?.values?.error
				setTimeout ->
					prettyNotify()
				, 3000
				prettyError 'Error Saving Inventory', data?.error || data?.values?.error
				return
			else
				setTimeout ->
					prettyNotify()
				, 3000
				prettyNotify 'Inventory loaded successfully'
				@tab_do_cancel(true)
				@parent?.tabViewActions?.openTab?(data?.values?.receipt_id, 'PO Receipt', 'read','receipt_po', {})

		return

class View_inventory_lotsCardFormView extends View_inventory_lotsCardBaseFormView
