class CustomGridBaseView extends CRView

	###
		can init with:
			sampleData
			colmuns
			source_url
			parent
	###

	tpl: 'card-list'
	
		
	load: ->

		@views = {}
		@channels = {}
		@repeater = @$('.repeater')
		@repeater.attr('id','as0grid')
		@repeater.repeater
					dataSource:	@data_source
					staticHeight: true
			
		@repeater.data(
						force_resize: true
					)
		@repeater.data('view', @)
		@repeater.data('pagination')

		setInterval =>
			@$('.repeater').find('.repeater-footer').removeClass('hide')
		, 600


	parse_source_url: (options) ->
		@options.source_url + '/?' + @params(options).join('&')
	
	params: (options) ->
		p = []
		if options.sortProperty?
			p.push 'sort=' + (if options.sortDirection is 'asc' then '' else '-') + options.sortProperty

		p.push 'page_number=' + options.pageIndex
		p.push 'page_size=' + options.pageSize

		p

	data_source:(options,callback) => 

			if getType(@options?.sampleData) not in ['null','undefined']
				@static_data_source(options,callback) 
			else
				@dynamic_data_source(options,callback)

	static_data_source: (options, callback) ->

		columns = [
			label: "Name"
			property: "name"
			sortable: true
		,
			label: "Description"
			property: "description"
			sortable: true
		,
			label: "Status"
			property: "status"
			sortable: true
		,
			label: "Category"
			property: "category"
			sortable: false
		]

		items = []
		count = 0
		while count < 60
			obj = 
				name: "Name "+count
				description: "Description "+count
				status: "Status "+count
				category: "Category "+count

			items.push(obj)
			count++

		pageIndex 	= options.pageIndex
		pageSize 	= options.pageSize
		totalItems 	= items.length
		totalPages 	= Math.ceil(totalItems / pageSize)
		startIndex 	= (pageIndex * pageSize) + 1
		endIndex 	= (startIndex + pageSize) - 1

		if endIndex > items.length
			endIndex = items.length

		rows 		= items.slice(startIndex - 1, endIndex)

		dataSource = 
			page:		pageIndex
			pages:		totalPages
			count:		totalItems
			start:		startIndex
			end:		endIndex
			columns:	columns
			items:		rows
		
		callback(dataSource)
		return


	dynamic_data_source: (options, callback) ->

		url = @parse_source_url(options)
		aj = Ajax.async
				url:url
		aj.done (data, status, xhr) =>

			getData = data
			data = getData.concat(data)

			pageIndex 	= options.pageIndex
			pageSize 	= options.pageSize
			totalItems 	= data.length
			totalPages 	= Math.ceil(totalItems / pageSize)
			startIndex 	= (pageIndex * pageSize) + 1
			endIndex 	= (startIndex + pageSize) - 1
			rows 		= data.slice(startIndex - 1, endIndex)

			dataSource = 
				page:		pageIndex
				pages:		totalPages
				count:		totalItems
				start:		startIndex
				end:		endIndex
				columns:	@options.cols
				items:		rows

			callback(dataSource)
			return

		aj.fail (xhr, status, data) =>

	
			dataSource = 
				page:		options.pageIndex
				pages:		options.pageIndex
				count:		0
				start:		0
				end:		0
				columns:	@options.cols
				items:		[]

			callback(dataSource)

	