
DSLFx.Validators.BVCheckINNOON = (form, dd, vld, f, k) ->
	dd.handler?.check_inn_oon?(dd.value_field('payer_id'), dd.value_field("site_id"))
	return

class Patient_intake_itemHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	check_inn_oon: (payer_id, site_id) ->
		return if @mode is 'read' or not payer_id or not site_id

		tc = Ajax.async
			url: '/form/payer/' + insurance_id
		tc.done (data, status, xhr) =>
			payerrec = data[0]
			if payerec.inn_oon == 'INN'	and payerrec.site_id.contains(site_id)
				@parent.value_field 'inn_or_oon', 'INN', true, true
			else
				@parent.value_field 'inn_or_oon', 'OON', true, true

		tc.fail (xhr, status, data) ->
			prettyError('Cannot fetch payer record', 'Sorry! There was an error trying to fetch the payer record. Please try again or contact support if problem persist.')
			@parent.value_field 'insurance_id', '', true, true
			dfd.reject 'Unable to fetch payer details. Please try again.'

class Patient_intake_itemHandlerView extends Patient_intake_itemHandlerBaseView
