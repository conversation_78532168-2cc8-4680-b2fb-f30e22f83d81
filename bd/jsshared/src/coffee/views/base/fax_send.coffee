class Fax_sendCardBaseFormView extends CRCardFormView

	tab_label_save: ->
		"Create & Send"
	
	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return

		if !@ddf.verify()
			$.notify 'Missing required fields.'
			return
		form_data = @ddf.get_formdata()
		that = this
		openARJsPopover(
						"Hello",
						"pick_list",
						{ printer: "" },
						{
							onPrint: (data) ->
								if "error" of data
									console.error(data.error)
									return
								file = new File([data.pdfRaw.data], data.filename + ".pdf", {
									type: "application/pdf"
								})

								window.prettyConfirm(
									"Send Fax",
									"Do you want to send fax?",
									"Send",
									"Close",
									->
										window.prettyNotify("Sending fax...")
										try
											hash = await uploadToS3(file)
											console.log(hash, "<<<<<<<<<<<")
											window.prettyNotify()
											form_data.fax_file = hash
											# Post Request to send fax
						
											that.tab_do_cancel(true)
											try
												window._ARJPopover?.onCancel?()
											catch e
												console.error(e)
											window.prettyAlert("Fax sent successfully")
										catch err
											console.error(err)
											window.prettyNotify()
											window.prettyError("Error sending fax")
									,
									->
										console.log("close")
									,
									window.BootstrapDialog.TYPE_SUCCESS
								)
						}
					)

class Fax_sendCardFormView extends Fax_sendCardBaseFormView