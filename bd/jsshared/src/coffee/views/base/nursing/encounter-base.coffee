
DSLFx.Validators.EncounterBlock = (form, dd, vld, vals, k) ->
	if (vals.ready_to_bill is 'Yes' or vals.approved is 'Yes' or vals.locked is 'Yes')
		false
	else
		true

class EncounterHandlerBaseView extends CRView

	handle: (dfd) ->
		dfd.resolve()
		return

	nodes: =>
		@parent.field_nodes

class EncounterCardFormBaseView extends CRCardFormView

	load: ->
		super()


# this must be blank by definition as any shared code goes above
# client repos can redefine this to extend the base class
class EncounterCardFormView extends EncounterCardFormBaseView
