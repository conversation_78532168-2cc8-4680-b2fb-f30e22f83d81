class CRCardReadView extends CRView

	###
		can init with:
			form
			el
			parent
			viewid
			link
			links
			linkid
			fields
			formname
			record
			preset
			mode
			dom
			domcols
	###

	tpl: 'card-read'

	events:
		'click .print': 'print'
		'click .edit': 'edit'
		'click .cancel': 'cancel'
		'click #card-bars': 'action_menu'
		'click #card-log-icon' : 'action_log'

	autounload: ['ddr']
	disable_editing: true
	err_loading: false
	unarchivable_forms: ['user']

	action_menu: ->
		buttons = [
			label: 'Print Blank Template'
			action: (dialog) =>
				dialog.close()
				prettyNotify 'Generating printout...'
				setTimeout =>
					dm = {}
					op = {}
					dfd = CRPrint.print_blank @ddr.dr, dm, op, 'Displaying preview...'
					dfd.done ->
						prettyNotify()
						return
					dfd.fail ->
						prettyNotify()
						prettyError 'Cannot print blank template!', 'Sorry! There was an error trying to print the blank template form.<br/><br/>' + ERR_CONTACT
						return
					return
				, 50
				return
		]
		if @tab_can_archive()
			buttons.push
				label: 'Archive'
				cssClass: 'archive-action btn-default',
				action: (dialog) =>
					dialog.close()
					@tab_do_archive()
					return
		else if @tab_can_unarchive()
			buttons.push
				label: 'Unarchive'
				cssClass: 'unarchive-action btn-default',
				action: (dialog) =>
					dialog.close()
					@tab_do_unarchive()
					return
		BootstrapDialog.show
			title: 'Action Menu'
			message: 'Select the action you want to perform for this form:'
			buttons: buttons
			type: BootstrapDialog.TYPE_INFO
			animate: false
			size: BootstrapDialog.SIZE_LARGE
		return
	
	tab_can_archive: ->
		if @audit_mode
			return false
		if @values() and @values()?.archived != true
			return Auth.can_update_any(@form) and Auth.not_blocked_update(@form, @values())
		return false
	
	tab_can_unarchive: ->
		if @audit_mode
			return false
		if App.user.role == "admin" and @values() and @values()?.archived
			return Auth.can_update_any(@form) and Auth.not_blocked_update(@form, @values())
		return false

	tab_do_archive: ->
		mode = 'archive'
		txt = ""
		that = @
		if App.user.role != "admin"
			txt = "\n\nNOTE: This action cannot be reversed!"
		prettyYesNo mode.proper() + ' record?', 'Are you sure you want to ' + mode + ' this ' + pluralize.singular(@formname) + '?'+ txt, =>
			aj = Ajax.async
				type: 'PUT'
				url: '/form/' + @form + '/' + @record + '/archive/'
				data:
					archived: true
					updated_by : App.user.id
					updated_by_auto_name : App.user.auto_name
					updated_on : dateTimeNow()
			aj.done (data, status, xhr) =>
				# parent stuff here
				cv = that?.options?.parent
				if cv.options.onArchive
					cv.options.onArchive(data, cv.tabData, cv)

			aj.fail (xhr, status, data) ->
				prettyError false, 'Sorry! Cannot ' + mode + ' this record.<br/><br/>' + ERR_CONTACT
			return
		, =>
			@cancel() if @err_loading
			return
		return

	tab_do_unarchive: ->
		mode = 'unarchive'
		that = @
		prettyYesNo mode.proper() + ' record?', 'Are you sure you want to ' + mode + ' this ' + pluralize.singular(@formname) + '?', =>
			aj = Ajax.async
				type: 'PUT'
				url: '/form/' + @form + '/' + @record + '/unarchive/'
				data:
					archived: false
			aj.done (data, status, xhr) =>
				cv = that?.options?.parent
				if cv.options.onArchive
					cv.options.onUnArchive(data, cv.tabData, cv)
				cv.mode('read', that.record)
			aj.fail (xhr, status, data) ->
				prettyError false, 'Sorry! Cannot ' + mode + ' this record.<br/><br/>' + ERR_CONTACT
			return
		, =>
			@cancel() if @err_loading
			return
		return
	
	action_log: ->
		fld_id = "#{@form}_#{@record}"
		loadComponent("FormAuditHistory", @el.find('.card-log-poppover')[0], fld_id, {form:@form, fid:@record, fld_id, el:@el})
		return
	
	cancel: ->
		@parent.cancel()
		@parent?.parent?.onCancel?(@record or @parent.xid, @parent.tabData, @parent)
		return

	edit: ->
		@parent.edit(@record, @dom, @domcols)
		return

	load: (data = {}) ->
		@mode = 'read' # overwrite no matter what
		@ddr = new DSLDrawRead(form: @form, id: @viewid, el: @$el, parent: @, link: @link, links: @links, linkid: @linkid, fields: @fields, record: @record, preset: @preset, mode: @mode, domcols: @domcols, menutitle: 'View ' + @formname, audit_mode: @audit_mode, show_sections: @show_sections, formBuss: @formBuss)
		dfd = @ddr.draw(if @audit_mode then @preset else {})
		dfd.done (args...) =>
			@disable_editing = false
			# disable editing for forms linked to inactive intakes
			r = @values()
			if r
				if (r.archived) or @lock_after_review(r)
					@disable_editing = true
				else if @link and (linkkey = @link + '_id') and r[linkkey]?
					oid = getLinkID(@link, @linkid, @$el) or @record
					@disable_editing = true if parseInt(r[linkkey]) isnt parseInt(oid)
			@parent.setheader() if @parent.setheader?
			return
		dfd.fail (err) =>
			@err_loading = true
			@parent.skip_activation = true
			return @cancel() if not err

			errmsg = $.trim(err)
			if getType(err) isnt 'string'
				if err?.error?
					errmsg = err.error
				else if err?.message?
					errmsg = err.message

			if @tab_can_archive()
				errmsg += "\n\nDo you want to archive this record?"
				prettyYesNo "ERROR: Cannot open " + @formname, errmsg, =>
					@tab_do_archive()
					return
				, =>
					@cancel() # make sure this doesn't mess with patient loading
					return
			else
				errmsg += "\n\nPlease contact your system administrator."
				prettyError false, errmsg, =>
					@cancel() # make sure this doesn't mess with patient loading
					return
			return
		dfd

	lock_after_review: (r) ->
		false
		#if Auth.can_rereview(@form)
		#	false
		#else
		#	(r.reviewed? and r.reviewed is 'Yes') or (r.reviewed_on and r.reviewed_by)

	print: ->
		prettyNotify 'Generating printout...'
		setTimeout =>
			dm = findClosest(@, 'snapshot_data')()
			op = {}
			dfd = CRPrint.print_form @ddr.dr, dm, op, 'Displaying preview...'
			dfd.done ->
				prettyNotify()
				return
			dfd.fail ->
				prettyNotify()
				return
			return
		, 50
		return
	
	tab_can_special_event: ->
		return false

	tab_can_special_event_1: ->
		return false
	
	tab_can_special_event_2: ->
		return false
	
	tab_can_special_event_3: ->
		return false

	tab_can_special_event_4: ->
		return false

	
	tab_can_cancel: ->
		true

	tab_can_patient: ->
		false

	tab_can_edit: ->
		if @audit_mode
			return
		(not @disable_editing) and Auth.can_update_any(@form) and Auth.not_blocked_update(@form, @values())

	tab_can_list: ->
		true

	tab_can_print: ->
		false
		# Auth.not_blocked_print(@form, @values())

	tab_can_review: ->
		return false # currently its not working better not show it to use
		if @audit_mode
			return
		(DSL[@form].model.access.review?.length > 0) and Auth.can_review(@form)
	
	tab_do_refresh: ->
		@parent.mode("read", @record)
		# refresh
		return

	tab_do_cancel: ->
		@cancel()
		return

	tab_do_edit: ->
		@edit()
		return

	tab_do_list: ->
		@cancel()
		return

	tab_do_print: ->
		@print()
		return
	
	tab_do_special_event: ->
		console.log 'Special Event 0', @form, @record
		return

	tab_do_special_event_1: ->
		console.log 'Special Event 1', @form, @record
		return
	
	tab_do_special_event_2: ->
		console.log 'Special Event 2', @form, @record
		return
	
	tab_do_special_event_3: ->
		console.log 'Special Event 3', @form, @record
		return

	tab_do_special_event_4: ->
		console.log 'Special Event 4', @form, @record
		return


	tab_do_review: ->
		aj = Ajax.async
			type: 'PUT'
			url: '/form/' + @form + '/' + @record + '/review/'
		aj.done (data, status, xhr) =>
			$.notify 'Marked as \'Reviewed\' successfully.'
			g = Ajax.async
				url: '/form/' + @form + '/' + @record
			g.done (gdata, gstatus, gxhr) =>
				gdata = gdata[0] if getType(gdata) is 'array' and gdata?[0]?.id?
				if gdata?.id?
					rowhtml = @ddr.root().make_row_html(gdata, @domcols, DSL[@form].fields).fr
					@parent.review(@record, @dom, @domcols, rowhtml)
				else
					prettyError false, 'Sorry! Cannot mark this record as \'Reviewed\'.<br/><br/>' + ERR_CONTACT
				return
			g.fail (xhr, status, data) ->
				prettyError false, 'Sorry! Cannot mark this record as \'Reviewed\'.<br/><br/>' + ERR_CONTACT
				return
			return
		aj.fail (xhr, status, data) ->
			prettyError false, 'Sorry! Cannot mark this record as \'Reviewed\'.<br/><br/>' + ERR_CONTACT
			return
		return

	tab_label_cancel: ->
		'Close'

	tab_label_archive: ->
		'Archive'
	
	tab_label_special_event: ->
		'SPECIAL EVENT 0'

	tab_label_special_event_1: ->
		'SPECIAL EVENT 1'
	
	tab_label_special_event_2: ->
		'SPECIAL EVENT 2'
	
	tab_label_special_event_3: ->
		'SPECIAL EVENT 3'

	tab_label_special_event_4: ->
		'SPECIAL EVENT 4'

	values: ->
		if @ddr?.subforms?.forms?[0]?.preset?
			@ddr.subforms.forms[0].preset
		else
			null
