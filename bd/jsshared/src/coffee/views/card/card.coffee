class CRCardView extends CRView

	###
		can init with:
			form
			el
			parent
			viewid
			link
			links
			linkid
			fields
			record
			preset
			card
			menu
			gridparams
	###

	tpl: 'card'

	autounload: ['cardlist', 'cardform', 'cardread']
	sub_type: ['ready']
	card: 'list'
	cardlist: false
	cardform: false
	cardread: false
	curview: false
	record: null
	skip_activation: false
	source: null # used for switching back to source view from manage view

	add: (dom, cols) ->
		@mode('add', null, dom, cols)
		return

	addfill: (dom, cols) ->
		@mode('addfill', null, dom, cols)
		return

	archive: (record, dom, cols) ->
		if dom
			dom.addClass('archived')
			@mode('list')
		else
			@mode('read', record, dom, cols)
		return

	unarchive: (record, dom, cols) ->
		if dom
			dom.removeClass('archived')
			@mode('list')
		else
			@mode('read', record, dom, cols)
		return

	can_archive: ->
		DSL[@form].view.open is 'read' and Auth.can_update_any(@form) and Auth.not_blocked_update(@form)

	cancel: (record = false, dom = false, cols = false) ->
		# record, dom, cols are required only if swiching to read mode
		@mode('list')
		App.appview.select_submenu @source if @source
		@source = null
		return

	subscribe: (event, callback) =>
		return if !@sub_type.includes(event)
		if !@subs
			@subs = {}
		id = generateUUID()
		@subs["#{event}.#{id}"] = callback
		return id
	
	publish: (event) =>
		return if !@sub_type.includes(event)
		return if !@subs
		Object.keys(@subs).forEach (key) =>
			if key.startsWith(event)
				@subs[key](@)
		return
	
	unsubscribe: (event, id) =>
		return if !@sub_type.includes(event)
		return if !@subs
		if id
			delete @subs["#{event}.#{id}"]
		else
			Object.keys(@subs).forEach (key) =>
				if key.startsWith(event)
					delete @subs[key]
		return

	delete: (record, dom, cols) ->
		dom.addClass('archived')
		@mode('list')
		dom.find('td').animate({'line-height': 0, 'padding': 0}, 600)
		setTimeout =>
			if dom.closest('tbody').find('tr').length is 1 # Fix for SC-183
				@cardlist.clear()
			else
				dom.remove()
			return
		, 600
		return
		
	focus_in: ->
		# Ticket #: 4320, 4346 
		@cardlist.focus_in() if @cardlist?.focus_in? # only if clicked

	edit: (record, dom, cols) ->
		@mode('edit', record, dom, cols)
		return

	load: ->
		if not DSL[@form]?
			prettyError false, 'Sorry! There was a problem accessing form: ' + @form + '<br/><br/>' + ERR_CONTACT
			return
		@$cardlist = @$('.cardlist').empty()
		@$cardform = @$('.cardform').empty().hide()
		if @inline
			@$cardform = @$('.cardform').closest('tr')
		@$cardread = @$('.cardread').empty().hide()
		@formname = if DSL[@form].view.label is '' then @form.proper() else DSL[@form].view.label
		@mode @card, @record
		return

	mode: (view, record = null, dom = null, cols = null) ->
		if view is 'list' and record
			view = 'read'
		if @curview and view and record and @parent.renderDSLCardCoffee?
			@parent.renderDSLCardCoffee(view, record, @)
			return
		@curview = view
		@$cardlist.hide()
		@$cardlist?.removeClass?('flex-list')
		if @cardform
			@cardform.unload()
			@cardform = false
		if @cardread
			@cardread.unload()
			@cardread = false
		@$cardform.empty().hide()
		@$cardread.empty().hide()

		viewid = @viewid + '_' + @form
		card = false

		@formBuss.clear() if @formBuss
		@formBuss = createFormBuss() if not @formBuss

		if view is 'list'
			if not @cardlist
				DynamicClass = getDynamicClass(@form, 'CardListView')
				@cardlist = new DynamicClass(
													form:      @form
													el:        @$cardlist
													parent:    @
													viewid:    viewid
													link:      @link
													links:     @links
													linkid:    @linkid
													formname:  @formname
													gridparams: @gridparams
												)
			@$cardlist.html('<div className="cr-error-boundary">⚠️ Something went wrong!</div>')
			@$cardlist.show()
			@$cardlist?.addClass?('flex-list')
			card = 'list'
		else if view in ['add', 'addfill', 'edit']
			DynamicClass = getDynamicClass(@form, 'CardFormView')
			@cardform = new DynamicClass(
												form:     @form
												el:       @$cardform
												parent:   @
												viewid:   viewid
												link:     @link
												links:    @links
												linkid:   @linkid
												fields:   @fields
												formname: @formname
												record:   record
												preset:   @preset
												mode:     view
												dom:      dom
												domcols:  cols
												show_sections: @showSections
												form_override_url: @form_override_url
												formBuss: @formBuss 
											)
			@$cardform.show()
			card = 'form'
			@changeMode?(view, record or @xid, @tabData, @)
		else if view in ['read']
			DynamicClass = getDynamicClass(@form, 'CardReadView')
			@cardread = new DynamicClass(
												form:     @form
												el:       @$cardread
												parent:   @
												viewid:   viewid
												link:     @link
												links:    @links
												linkid:   @linkid
												fields:   @fields
												formname: @formname
												record:   record
												preset:   @preset
												mode:     view
												dom:      dom
												domcols:  cols
												audit_mode: @audit_mode
												show_sections: @showSections
												form_override_url: @form_override_url
												formBuss: @formBuss
											)
			@$cardread.show()
			card = 'read'
			@changeMode?(view, record or @xid, @tabData, @)

		return if @curview isnt view # above operation didnt succeed (e.g. add aborted)
		@card = card                 # only set if successful

		if @menu? and @parent.state? # e.g. used in patient-card
			@parent.state(@menu, view)

		if @skip_activation
			@skip_activation = false
		else
			@['card' + card].activate() if @['card' + card]?.activate?
		@setheader()

		if @tabData and (view != @tabData.mode or (record or @xid) != @tabData.id)
			@changeMode?(view, record or @xid, @tabData, @)
		return

	open: (id = null) ->
		return if not id
		@mode 'list' if @card is 'read'
		if @card isnt 'list'
			prettyError false, 'You already currently editing a form for this ' + DSL.patient.view.label.toLowerCase() + '. Please save or cancel this form and then try again.'
			return

		if @cardlist.cnt? # check if row count has been set i.e. grid loaded successfully
			@cardlist.open id
		else
			@cardlist.startup_id = id
		return

	read: (record, dom, cols) ->
		@mode('read', record, dom, cols)
		return

	review: (record, dom, cols, rowhtml) ->
		if dom
			@mode('list')
			tds = dom.find('td')
			for k in cols
				continue if not rowhtml?[k]?
				td = $(tds[cols.indexOf k])
				td.html rowhtml[k]
			tds.effect('highlight', {color: '#efd'}, 2000)
		else
			@mode('read', record, dom, cols)
		return

	save: (values, record, dom, cols, add, rowhtml) -> # this gets overridden in flyout, patient-add etc.

		if record           
			always_refresh = false
			for trf in DSL[@form].model.transform
				if trf.name in ['EnsureUnique']
					always_refresh = true
					break

			tf = dom?.find('tr:first')
			# clear '0 items' from grid if empty
			if @cardlist and tf?.html()?.indexOf('0 items') > -1
				always_refresh = true
				tf.html ''

			if always_refresh
				@cardlist.clear() # implicit find()
			else if _.isEmpty(values) # do not have access to the original row
				if dom
					tds = dom?.find('td')
					tds.css({backgroundColor: '#daa'})
					tds.effect('highlight', {color: '#fcc'}, 800)
					setTimeout ->
						tds?.remove()
					, 850
			else if add and @cardlist.ddg?.table_grid?
				# New Datatabled grid instead of manually append dom element add using gridview api
				rowNode = @cardlist.ddg.table_grid.row.add(rowhtml).node()
				$(@cardlist.ddg.dg).prepend(rowNode)
				$(rowNode).effect('highlight', {color: '#efd'}, 2000)
				text = @$('.dataTables_info').text()
				if text.trim().split('-').length is 2
					split_arr = text.trim().split('-')
					end = parseInt(split_arr[1].trim())
					if end >= 0
						total = end + 1
						text = split_arr[0] + " - " +split_arr[1].replace(String(end),String(total))
						@$('.dataTables_info').text(text)
			else
				tds = dom?.find('td')
				if (cols?.length > 0)
					for k in cols
						continue if not rowhtml?[k]?
						td = $(tds[cols?.indexOf k])
						td?.html rowhtml[k]
				tds?.effect('highlight', {color: '#efd'}, 2000)
			@parent.child_saved(@form, record, add) if @parent.child_saved?
			@cardlist.saved(@form, record, add) if @cardlist.saved?
			@cancel(record, dom, cols)
		return

	setheader: ->
		@parent.setheader() if @parent.setheader?
		return

	sublabel: ->
		if @['card' + @card]?.sublabel?
			@['card' + @card].sublabel()
		else
			''

	contextlabel: ->
		if @['card' + @card]?.contextlabel?
			@['card' + @card].contextlabel()
		else
			''

	tab_action: (fx, action) =>
		card = @['card' + @card]
		tabaction = 'tab_' + fx + '_' + action
		taboverride = findClosest(@, tabaction + '_override', false)
		if taboverride # this allows parent class to override tab_[fx]
			taboverride()
		else if card[tabaction]?
			card[tabaction]()
		else
			false

	tab_can: (action) ->
		@tab_action 'can', action

	tab_do: (action) ->
		@tab_action 'do', action

	tab_label: (action) ->
		action = 'tab_label_' + action
		card = @['card' + @card]
		if card[action]?
			card[action]()
		else if action == 'tab_label_addfill'
			"PREFILL"
		else
			false

