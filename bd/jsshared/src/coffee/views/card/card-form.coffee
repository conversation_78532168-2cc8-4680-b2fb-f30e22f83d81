class CRCardFormView extends CRView

	###
		can init with:
			form
			el
			parent
			viewid
			link
			links
			linkid
			fields
			formname
			record
			preset
			mode
			dom
			domcols
	###


	tpl: 'card-form'

	events:
		'click .save': 'call_save'
		'click .cancel': 'call_cancel'
		'click #card-bars': 'action_menu'
		'click #card-log-icon': 'action_log'

	autounload: ['ddf']
	missing_field_count: 0
	slave_missing_field_count: 0

	# fields that should be on top for DSLText notes #HB-2959
	#   set this in {DSL-FORM}BaseCardForm's load()
	#   e.g. EncounterBaseCardForm
	sections_fields_text_override: {}


	action_menu: ->
		buttons = [
			label: 'Print Blank Template'
			action: (dialog) =>
				dialog.close()
				prettyNotify 'Generating printout...'
				setTimeout =>
					dm = {}
					op = {}
					dfd = CRPrint.print_blank @ddf.dr, dm, op, 'Displaying preview...'
					dfd.done ->
						prettyNotify()
						return
					dfd.fail ->
						prettyNotify()
						prettyError 'Cannot print blank template!', 'Sorry! There was an error trying to print the blank template form.<br/><br/>' + ERR_CONTACT
						return
					return
				, 50
				return
		]
		if @tab_can_archive()
			buttons.push
				label: 'Archive'
				cssClass: 'btn-default archive-action',
				action: (dialog) =>
					dialog.close()
					@tab_do_archive()
					return
		BootstrapDialog.show
			title: 'Action Menu'
			message: 'Select the action you want to perform for this form:'
			buttons: buttons
			type: BootstrapDialog.TYPE_INFO
			animate: false
			size: BootstrapDialog.SIZE_LARGE
		return

	action_log: ->
		fld_id = "#{@form}_#{@record}"
		loadComponent("FormAuditHistory", @el.find('.card-log-poppover')[0], fld_id, {form:@form, fid:@record, fld_id, el:@el})
		return

	tab_can_archive: ->
		vls = @ddf?.values?() 
		if vls and vls?.archived != true
			return Auth.can_update_any(@options.form) and Auth.not_blocked_update(@options.form, vls)
		return false
	
	tab_do_archive: ->
		mode = 'archive'
		txt = ""
		that = @
		if App.user.role != "admin"
			txt = "\n\nNOTE: This action cannot be reversed!"
		prettyYesNo mode.proper() + ' record?', 'Are you sure you want to ' + mode + ' this ' + pluralize.singular(@formname) + '?'+ txt, =>
			aj = Ajax.async
				type: 'PUT'
				url: '/form/' + @form + '/' + @record + '/archive/'
				data:
					archived: true
					updated_by : App.user.id
					updated_by_auto_name : App.user.auto_name
					updated_on : dateTimeNow()
			aj.done (data, status, xhr) =>
				# parent stuff here
				cv = that?.options?.parent
				if cv.options.onArchive
					cv.options.onArchive(data, cv.tabData, cv)
			aj.fail (xhr, status, data) ->
				prettyError false, 'Sorry! Cannot ' + mode + ' this record.<br/><br/>' + ERR_CONTACT
			return
		, =>
			@cancel() if @err_loading
			return
		return

	call_cancel: ->
		@cancel()

	call_save: ->
		@save()

	cancel: ->
		@parent.cancel(@record, @dom, @domcols)
		@parent?.parent?.onCancel?(@record or @parent.xid, @parent.tabData, @parent)
		return
		if ['add', 'addfill'].includes(@mode)
			@parent?.parent?.onCancel?(@record or @parent.xid, @parent.tabData, @parent)
			return
		@parent.mode('read', @record, @dom, @domcols)
		return

	#	allows modifying each row as needed
	#		return update htmlrow
	#		overridden by jshomebase/Patient_labCardFormView
	#	gridrow: (htmlrow, row, cols, fields) =>
	#		return htmlrow

	load: ->
		return if not @mode? # add, addfill, edit
		@start_time = new Date().getTime()
		@mark_required_if(@form)
		menutitle = (if @mode in ['add', 'addfill'] then '_Add ' else '_Edit ') + @formname
		@ddf = new DSLDrawForm(form: @form, id: @viewid, el: @$el, parent: @, link: @link, links: @links, linkid: @linkid, fields: @fields, record: @record, preset: @preset, mode: @mode, domcols: @domcols, menutitle: menutitle, audit_mode: @audit_mode, show_sections: @show_sections, form_override_url: @form_override_url, formBuss: @formBuss)
		dfd = @ddf.draw()
		# enabled for all browser
		dfd.done =>
			return if @parent?.parent?.options?.is_subform? # subform data recovery is managed by parent form
		if @ddf.autorecover_enabled()
			dfd.done =>
				setTimeout @ddf.autorecover_set, 1000  # inital caching for formdata for detecting changes
				@autorecover = setInterval =>
					@ddf.autorecover_set()
				, 1000
				return
		dfd.fail (err) =>
			if err
				if getType(err) is 'string'
					prettyError false, err
				else if err?.error?
					prettyError false, err.error
				else if err?.message?
					prettyError false, err.message
				@cancel() # make sure this doesn't mess with patient loading
			return
		dfd


	mark_required_if: (form) ->
		return if (not DSL[form]?.model?) or (not DSL[form].model.required_if) or DSL[form].model.required_if_fields
		req = []
		for k, v of DSL[form].fields
			continue if v.view.requireif_bypass
			if v.model.type is 'subform'
				@mark_required_if(v.model.source) if getType(v.model.source) is 'string'
				for sf, _ of v.model.sourcefilter
					@mark_required_if(sf)
			else if v.model.required
				req.push k
				DSL[form].fields[k].model.required = false
		DSL[form].model.required_if_fields = req
		return

	save: ->
		if not @ddf.form_rendered
			prettyError false, 'Please wait while this form is loaded completely before you try to save it.'
			return
		dfd = @ddf.save()
		dfd.done (rt) =>
			if rt? and rt.values?
				if @parent.options.isFlyout or @parent.parent.tabid is 'flyout_root'
					@parent.save(rt.values, rt.record, @dom, @domcols, @record is null, rt.rowhtml)
					return
				@record = rt.record
				@parent.mode('read', @record)#<===BND
				@parent?.parent?.onSaved?(rt, @parent.tabData, @parent)
			return
		return

	tab_can_cancel: ->
		true


	tab_can_special_event: ->
		return false

	tab_can_special_event_1: ->
		return false

	tab_can_special_event_2: ->
		return false

	tab_can_special_event_3: ->
		return false

	tab_can_special_event_4: ->
		return false

	tab_can_unarchive: ->
		false

	tab_can_save: ->
		Auth.can_write_any(@form)

	tab_can_verify: ->
		@missing_field_count > 0 and Auth.can_write_any(@form)
	
	tab_do_refresh: ->
		@parent.mode("edit", @record)
		return

	tab_do_cancel: (cwe = false) ->
		if @options?.parent?.options?.closeWithOutAck
			cwe = true
		if not @tab_can_save() or cwe
			log('Canceled without user prompt')
			return @cancel()
		show_modal = false
		if @start_time
			current_time = new Date().getTime()
			elapsed_econds = (current_time - @start_time) / 1000
			if elapsed_econds > 10
				@show_popup()
				return
			else
				data = @ddf.get_formdata()
				delete data._meta;
				# after 10
				cache_data = @ddf.autorecover_get()
				if ((cache_data != null and data != cache_data) || @ddf.form_edited)
					@show_popup()
					return
				else
					return @cancel()
		else
			return @cancel()

		return

	show_popup: ->
		is_add = ['add', 'addfill'].includes(@mode)
		msg = 'this ' + ( is_add and 'new ' or 'editing ') + @formname
		prettyYesNo 'Close ' + (is_add and 'New ' or 'Edit ') + @formname + '?', 'Are you sure you want to cancel ' + msg + '?', =>
			@cancel()
			return
		return
	tab_do_save: ->
		@save()
		return

	tab_do_verify: ->
		@verify()
		return

	tab_do_special_event: ->
		console.log 'Special Event 0', @form, @record
		return

	tab_do_special_event_1: ->
		console.log 'Special Event 1', @form, @record
		return

	tab_do_special_event_2: ->
		console.log 'Special Event 2', @form, @record
		return

	tab_do_special_event_3: ->
		console.log 'Special Event 3', @form, @record
		return

	tab_do_special_event_4: ->
		console.log 'Special Event 4', @form, @record
		return

	tab_label_verify: ->
		total_count = @missing_field_count + @slave_missing_field_count
		total_count + ' FIELD' + if total_count isnt 1 then 'S' else ''

	tab_label_special_event: ->
		'SPECIAL EVENT 0'

	tab_label_special_event_1: ->
		'SPECIAL EVENT 1'

	tab_label_special_event_2: ->
		'SPECIAL EVENT 2'

	tab_label_special_event_3: ->
		'SPECIAL EVENT 3'

	tab_label_special_event_4: ->
		'SPECIAL EVENT 4'

	tab_label_cancel: ->
		'Close'

	set_missing_field_count: (missing) ->
		@missing_field_count = missing
		@parent.setheader() if @parent.setheader?
		return

	set_slave_missing_field_count: (missing) ->
		@slave_missing_field_count = missing or 0
		return

	verify: ->
		if @ddf.verify()
			$.notify 'Form verified.'
		return

	unload: ->
		if @autorecover? and @autorecover
			clearInterval @autorecover
			@ddf.autorecover_clear()

		if _.isEmpty(@cache_data?[@form]?[@record or 'add'])
			delete @cache_data?[@form]?[@record or 'add']
		super()


# extend from this class to make any form require
# all the fields to be filled, including all the subfroms
class CRCardRequiredView extends CRCardFormView

	load: ->
		@make_required(@form)
		super()

	make_required: (form) ->
		return if (not DSL[form]?.model?) or DSL[form].model.required_all
		req = []
		for k, v of DSL[form].fields
			if v.model.type is 'subform'
				@make_required(v.model.source) if getType(v.model.source) is 'string'
				for sf, _ of v.model.sourcefilter
					@make_required(sf)
			else if not (v.model.required or v.view.offscreen or v.view.readonly)
				# do not require special field types
				if not v.view.requireall_bypass and not (v.view.control in ['area', 'esign', 'file', 'grid', 'inline', 'subform'] or v.view.note is 'check all that apply')
					DSL[form].fields[k].model.required = true
					req.push k
		DSL[form].model.required_all = true
		if DSL[form].model.required_if_fields? and req?.length > 0
			DSL[form].model.required_if_fields = req
		return
