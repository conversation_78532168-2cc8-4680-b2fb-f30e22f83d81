class MaxRowsOneCardView extends CRCardView

	archive: (record, dom, cols) ->
		@cardlist.clear() # implicit find()
		@mode('list')
		return


class MaxRowsOneCardListView extends CRCardListView

	activate: ->
		# called every time when switching to list view (after save/cancel)
		@loaded(@cnt) if @cnt?
		return

	loaded: (cnt) =>
		@cnt = cnt
		if cnt > 0 # fake a DOM click event with target
			@edit(target: @$('.dataTable tbody tr td:first'))
		else if cnt is 0 and Auth.can_create_any(@form)
			@addfill()
		return

	tab_can_add: ->
		@cnt? and (@cnt is 0) and Auth.can_create_any(@form)

	tab_can_addfill: ->
		false

	tab_can_print: ->
		false

	tab_can_refresh: ->
		false

	tab_do_add: ->
		@addfill() # we want to prefill instead of regular, but call it 'add'
		false


class MaxRowsOneCardReadView extends CRCardReadView

	tab_can_list: ->
		false
