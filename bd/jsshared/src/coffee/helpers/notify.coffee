notifyTm = 0

window.geoLocation = null

$.notify = (message = false) ->
	clearInterval notifyTm if notifyTm
	if message
		$('#notify').html('<span class="label label-default">' + message + '</span>').show()
		notifyTm = setTimeout ->
			$('#notify').fadeOut(500)
		, 2000
	else
		$('#notify').hide()
	return


window.prettyAlert = (title = false, msg = false, err = false, fxok = null, showCopyButton = true ) ->
	App.appview?.navigation?.hidemenu()
	dfd = $.Deferred()
	buttons = [
		label: 'OK'
		action: (dialog) ->
			dialog.close()
			dfd.resolve()
			fxok() if fxok
			return
	]
	if showCopyButton
		buttons.push
			label: ''
			cssClass: "btn-copy"
			action: (dialog) ->
				textToCopy = if err then err else title
				navigator.clipboard.writeText(textToCopy)
				$('.btn-copy').addClass('copied') if textToCopy
				setTimeout ->
					$('.btn-copy').removeClass('copied')
				, 1000
				return
	BootstrapDialog.show
		title: if title then title else (if msg then 'Notice' else 'ERROR!')
		message: if msg then msg else err
		buttons: buttons
		type: if title == 'Success' then BootstrapDialog.TYPE_SUCCESS else ( if msg? and msg isnt false and typeof msg is "string" and msg.trim() isnt "" then BootstrapDialog.TYPE_INFO else BootstrapDialog.TYPE_DANGER)
		size: BootstrapDialog.SIZE_LARGE
		closeByBackdrop: false
	dfd

window.prettyAlertCustom = (title = false, msg = false, err = false, fxok = null) ->
	App.appview?.navigation?.hidemenu()
	dfd = $.Deferred()
	BootstrapDialog.show
		title: if title then title else (if msg then 'Notice' else 'ERROR!')
		message: if msg then msg else err
		buttons: [
			label: 'OK'
			action: (dialog) ->
				dialog.close()
				dfd.resolve()
				fxok() if fxok
				return
		]
		type: if msg then BootstrapDialog.TYPE_INFO else BootstrapDialog.TYPE_DANGER
		animate: false
		closable: false
		size: BootstrapDialog.SIZE_LARGE
		closeByBackdrop: false
		closeByKeyboard: false
	dfd

window.prettyConfirm = (title = false, msg = false, txtrue = 'OK', txfalse = 'Close', fxtrue, fxfalse, type=BootstrapDialog.TYPE_DANGER) ->
	App.appview?.navigation?.hidemenu()
	BootstrapDialog.show
		title: if title then title else 'Please Select'
		message: msg.replace(/\n/g,'<br/>')
		buttons: [
			label: txtrue
			cssClass: "btn-true"  # Add a class for true button
			action: (dialog) ->
				log('User selected: ' + txtrue)
				dialog.close()
				fxtrue(txtrue) if fxtrue
				return
		,
			label: txfalse
			cssClass: "btn-false"  # Add a class for false button
			action: (dialog) ->
				log('User selected: ' + txfalse)
				dialog.close()
				fxfalse(txfalse) if fxfalse
				return
		]
		type: type
		animate: false
		size: BootstrapDialog.SIZE_LARGE
		closable: false
		closeByBackdrop: false
		closeByKeyboard: false
	return

window.prettyError = (title = false, err = false, fxerr = null) ->
	prettyAlert (if title then title else false), false, err, fxerr

window.prettyForm = (title = false, html = false, txtrue = 'Save', txfalse = 'Close', fxinit, fxtrue, fxfalse,type=BootstrapDialog.TYPE_INFO) ->
	App.appview?.navigation?.hidemenu()
	getLoc = true
	BootstrapDialog.show
		title: if title then title else 'Please Fill Form'
		message: $('<div></div>').html(html)
		buttons: [
			label: txtrue
			cssClass: "btn-true"  # Add a class for true button
			action: (dialog) ->
				log('User selected: ' + txtrue)
				if txtrue is 'Close'
					dialog.close()
				else if fxtrue
					fxtrue(txtrue, dialog)
				else
					dialog.close()
				return
		,
			label: txfalse
			cssClass: "btn-false"  # Add a class for false button
			action: (dialog) ->
				log('User selected: ' + txfalse)
				if txfalse is 'Confirm Location'
					fxtrue(txfalse, dialog)
				else if fxfalse
					fxfalse(txfalse, dialog) # fx must close dialog
				else
					dialog.close()
				return
		]
		type: type
		animate: false
		size: BootstrapDialog.SIZE_LARGE
		closable: false
		closeByBackdrop: false
		closeByKeyboard: false
		onshown: (dialog) ->
			fxinit(dialog)		
			return
	return

window.prettyFormCustom = (title = false, html = false, txtrue = 'Save', txfalse = 'Close', fxinit, fxtrue, fxfalse,type=BootstrapDialog.TYPE_DANGER) ->
	App.appview?.navigation?.hidemenu()
	BootstrapDialog.show
		title: if title then title else 'Please Fill Form'
		message: $('<div></div>').html(html)
		buttons: [
			label: txtrue
			cssClass: "btn-true"  # Add a class for true button
			action: (dialog) ->
				log('User selected: ' + txtrue)
				if fxtrue
					fxtrue(txtrue, dialog) # fx must close dialog
				else
					dialog.close()
				return
		,
			label: txfalse
			cssClass: "btn-false"  # Add a class for false button
			action: (dialog) ->
				log('User selected: ' + txfalse)
				if fxfalse
					fxfalse(txfalse, dialog) # fx must close dialog
				else
					dialog.close()
				return
		]
		type: type
		animate: false
		size: BootstrapDialog.SIZE_LARGE
		closable: true
		closeByBackdrop: true
		closeByKeyboard: true
		onshown: (dialog) ->
			fxinit(dialog)
			return
	return

window.prettyMessage = (title = false, msg = false, fxmsg = null) ->
	prettyAlert title, msg, false, fxmsg

window.prettyNotify = (title = false) ->
	if title
		App.appview?.navigation?.hidemenu()
		if window.prettyNotifyDialog?
			window.prettyNotifyDialog.$modalHeader.find('.bootstrap-dialog-title').html(title)
		else
			window.prettyNotifyDialog = BootstrapDialog.show
				title: title
				message: '<span class="formload"><i class="fa-duotone fa-solid fa-circle-notch fa-spin" style="margin-right: 10px;" aria-hidden="true"></i> Please wait while this operation completes...</span>'
				type: BootstrapDialog.TYPE_INFO
				animate: false
				size: BootstrapDialog.SIZE_LARGE
				closable: false
				closeByBackdrop: false
				closeByKeyboard: false
	else if window.prettyNotifyDialog?
		window.prettyNotifyDialog.close()
		delete window.prettyNotifyDialog
	return

window.prettyYesNo = (title = false, msg = false, fxtrue, fxfalse) ->
	prettyConfirm title, msg, 'Yes', 'No', fxtrue, fxfalse
	return
