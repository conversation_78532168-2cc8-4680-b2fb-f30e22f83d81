# handles console functions
Function::property = (prop, desc) ->
	Object.defineProperty @prototype, prop, desc

class CRConsole

	constructor: ->
		@help
		return

	@property 'debug',
		get: ->
			App.feature.debug = true
			App.feature.wss = false
			Auth.stop_check = true
			'Enabled debug mode.'
	
	@property 'qa',
		get: ->
			App.feature.qa = true
			'Enabled qa mode.'
	
	@property 'wip',
		get: ->
			App.feature.wip = true
			'Enabled wip mode.'

	@property 'help',
		get: ->
			console.log("%c ClaraRX ", "font-size: 32px; font-weight: bold; color: white; background: linear-gradient(90deg, #668eba, #4a6e94); padding: 10px 10px; border-radius: 10px;")
			# console.log("%c ClaraRX ", "font-size: 32px; font-weight: bold; color: white; background: linear-gradient(to bottom, #e40303, #ff8c00, #ffed00, #008026, #24408e, #732982); padding: 10px 20px; border-radius: 10px;")
			console.log '%cCommands:', 'font-weight: bold; color: #ffcc00; font-size: 14px;'
			commands = [
				{ cmd: 'cl.debug', desc: 'Enable debug mode', color: '#e40303' }   # Red
				{ cmd: 'cl.help', desc: 'This help message', color: '#ff8c00' }   # Orange
				{ cmd: 'cl.reload', desc: 'Reload configuration', color: '#ffed00' } # Yellow
				{ cmd: 'cl.version', desc: 'Print version', color: '#008026' }  # Green
				{ cmd: 'cl.qa', desc: 'Enable QA mode', color: '#24408e' }  # Blue
				{ cmd: 'cl.wip', desc: 'Enable WIP mode', color: '#732982' }  # Purple
			]

			for command in commands
				console.log "%c #{command.cmd} %c #{command.desc} ",
					"background: #{command.color}; color: white; padding: 2px 8px; border-radius: 5px; font-weight: bold;",
					"color: #ddd; font-size: 12px;"

	@property 'no_wss',
		get: ->
			App.feature.wss = false
			'Disabled WebSockets.'

	@property 'reload',
		get: ->
			App.start_dsl()
			'Sent reload DSL command.'

	@property 'stop_checks',
		get: ->
			Auth.stop_check = true
			'Status checks stopped.'

	@property 'version',
		get: ->
			'Version: ' + JSON.stringify(App.version)
