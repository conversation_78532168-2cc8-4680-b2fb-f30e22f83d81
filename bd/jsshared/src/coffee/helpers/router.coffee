class CRRouter extends B.Router

	appview: false
	company: CRBaseCompany
	feature:
		debug: false
		nes:   false
		wss:   false
		monitor: false
	user: false
	version: false

	handle_nes_failure: (ver,error) ->
		if (not CRLocal) and ver?.app?.type? and (ver.app.type is 'jshomebase')
			Auth.fail error + '<br/><br/>' + ERR_CONTACT
			true
		else
			false

	launch_ui: (user = false, site_ids = []) ->
		#CM:2014-07-31 - this can be safely removed once the user_role table is linked
		#                to the user table's role field
		if not user.role_auto_name?
			if user.job_title?
				user.role_auto_name = user.job_title
			else
				user.role_auto_name = DSL.user?.fields?.role?.model?.source[user.role] or user.role.proper()
		@user = user
		if not @appview
			@appview = true
			p = getUserPreference()
			p.then (data) =>
				window.UserPreference = data
			p.catch (err) =>
				console.error(err)
			p.finally () =>
				load_app = (site_ids) =>
					window.sitesSelected = site_ids
					loadComponent('App',document.getElementById('application'), 'root', {})
				if site_ids.length == 0
					openSiteSelector(load_app, true, false)
				else
					load_app(site_ids)
			return
		return

	start: ->
		Log.start()
		g = $.ajax # get static file 'version.json'. note: not an API call
			url: 'public/conf/version.json?rnd=' + Date.now()
		g.done (data, status, xhr) =>
			data = JSON.parse(data) if getType(data) is 'string'
			return @start_version_get_nes(data)
		g.fail (xhr, status, data) =>
			if CRLocal
				@start_version_set {}
			else
				Auth.fail 'Sorry! Cannot determine application version.<br/><br/>' + ERR_CONTACT
			return

	start_app: ->
		if not @started?
			g = Ajax.async
				url: '/form/module/?limit=100&fields=list&sort=sort_order&page_number=0'
			g.done (data, status, xhr) =>
				window.MODULES = data
				@started = true
				@company = fillDSLDefaults("company",  @company)
				# Ajax.start()
				Auth.authenticate()
				return
			g.fail (xhr, status, data) =>
				Auth.fail 'Unable to load Modules.'
				return
		return

	start_dsl: (init = false)->
		return @start_app() if init and window.DSL

		g = Ajax.async
			url: '/dsl/'
		g.done (data, status, xhr) =>
			if data.autoinsert?
				window.DSL_AutoInsert = DSLParse.auto_insert(data.autoinsert)
				delete(data.autoinsert)
			window.DSL = data
			@start_app() if init
			return
		g.fail (xhr, status, data) =>
			Auth.fail 'Your password has expired, please click ok to reset your password.'
			return
		return

	start_version_get_nes: (ver) ->
		g = Ajax.async
			url: '/version/?type=' + ver.app.type + '&version=' + ver.app.version
		g.done (data, status, xhr) =>
			g2 = Ajax.async # No NES or DB if this fails. Failure means there is no way to access the system
				url: '/form/company/1'
			g2.done (data2, status2, xhr2) =>
				@company = _.defaultsDeep(CRCompany, @company) if CRCompany?
				@feature.nes = data.active = not data2.error?
				@feature.wss = data.wss? and data.wss and @feature.nes
				@feature.monitor = data.monitor? and data.monitor and @feature.wss
				data2 = _.head(data2)
				@company = _.merge(@company, data2)
				document.querySelector("#application").style.backgroundColor = @company?.bg_color || "#F8F1EC"
				@start_version_set(ver, data)
				return
			g2.fail (xhr2, status2, data2) =>
				return if @handle_nes_failure(ver,"You must be logged in to view this page")
				@feature.nes = data.active = false
				@feature.wss = false
				@feature.monitor = false
				@start_version_set(ver, data)
				return
			return
		g.fail (xhr, status, data) =>
			return Auth.fail 'Your session has expired. Please login again.'
			return

	start_version_set: (ver, nes = null) ->
		ver.nes = nes if nes
		@version = ver
		window.cl = new CRConsole() if console?.log?
		g = Ajax.async
			url: '/my/access/'
		g.done (data, status, xhr) =>
			window.ACCESS = data
			@start_dsl(true)
			return
		g.fail (xhr, status, data) =>
			Auth.fail 'Sorry! There was a problem accessing Security Access.'
			return
		return
