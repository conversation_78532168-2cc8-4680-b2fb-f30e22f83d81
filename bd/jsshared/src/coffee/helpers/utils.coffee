String.prototype.alphanum = ->
	this.replace(/[^a-z0-9]/gi, "").toLowerCase()

String.prototype.chop = (len) ->
	this.substr(0, len) + (if this.length > len then '&hellip;' else '')

String.prototype.chopText = (len) ->
	this.substr(0, len) + (if this.length > len then '...' else '')

String.prototype.matches = (that) ->
	$.trim(this.toLowerCase()) is $.trim(that.toLowerCase())


String.prototype.proper = ->
	this.substr(0, 1).toUpperCase() + this.substr(1)

# string repeat
String.prototype.repeat = (num) ->
	new Array(num + 1).join(this)

# string replace all
String.prototype.replaceAll = (s, r) ->
	x = this
	while x.indexOf(s) isnt -1
		x = x.replace(s, r)
	x

# string trim for IE
if typeof String.prototype.trim isnt 'function'
	String.prototype.trim = ->
		$.trim(this)

# scale image to fit within a boundary and preserve aspect ratio
window.boundBox = (mw, mh, iw, ih) ->
	ir = iw / ih
	if ir < mw/mh
		ih = Math.min(ih, mh)
		iw = Math.round(ih * ir)
	else
		iw = Math.min(iw, mw)
		ih = Math.round(iw / ir)
	[iw, ih]

window.cleanup_name = (nm, trim_only = false) ->
	nm = $.trim(nm)
	return nm if trim_only
	nn = $.trim(nm.replace(/\((.*)\)/, ''))
	if nn then nn else $.trim(nm.replace('(', '').replace(')', ''))

window.getSourceFilterValues = (form, v) ->
	sourceFilterForms = [
		"list_ncpdp_ecl",
		"list_ncpdp_ext_ecl",
		"inventory_sk_item",
		"list_med_claim_ecl",
    ]
	always_apply = {}
	if v?.model?.sourcefilter? && form && sourceFilterForms.indexOf(form) > -1
		srcf = v?.model?.sourcefilter || {}
		for fk, fv of srcf
			if not fv.static
				continue
			always_apply[fk] = fv.static
	return always_apply

window.composeHTMLattr = (attr) ->
	$.map(attr, (v, k) -> k + '="' + v + '"').join(' ')

# convert image to data:base64 string
window.dataImage = (id, url, outputFormat) ->
	dfd = $.Deferred()
	img = new Image()
	img.crossOrigin = 'Anonymous'
	img.onload = ->
		canvas = document.createElement('CANVAS')
		ctx = canvas.getContext('2d')
		canvas.height = this.height
		canvas.width = this.width
		ctx.drawImage(this, 0, 0)
		dataURL = canvas.toDataURL(outputFormat)
		dfd.resolve(id, dataURL, this.width, this.height)
		canvas = null
	img.onerror = ->
		dfd.resolve(id, false, 0, 0)
	img.src = url
	dfd

# convert Data URI to Blob
window.dataURItoBlob = (dataURI) ->
	# convert base64 to raw binary data held in a string
	byteString = atob(dataURI.split(',')[1])
	# separate out the mime component
	mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
	# write the bytes of the string to an ArrayBuffer
	arrayBuffer = new ArrayBuffer(byteString.length)
	_ia = new Uint8Array(arrayBuffer)
	i = 0
	while i < byteString.length
		_ia[i] = byteString.charCodeAt(i)
		i++
	dataView = new DataView(arrayBuffer)
	blob = new Blob([ dataView ], type: mimeString)
	blob

# Check if two date ranges overlap
# all dates should be String in 'YYYY-MM-DD' (dateYMD) format
window.dateRangeOverlaps = (start_a, end_a, start_b, end_b) ->
	a_start = moment(start_a, 'YYYY-MM-DD')
	a_end = moment(end_a, 'YYYY-MM-DD')
	b_start = moment(start_b, 'YYYY-MM-DD')
	b_end = moment(end_b, 'YYYY-MM-DD')
	if a_start.isSameOrBefore(b_start) and b_start.isSameOrBefore(a_end)
		return true
	if a_start.isSameOrBefore(b_end) and b_end.isSameOrBefore(a_end)
		return true
	if b_start.isBefore(a_start) and a_end.isBefore(b_end)
		return true
	false

window.dateTimeFromNow = (dt) ->
	moment(new Date(dateTimeToUTC(dt))).local().fromNow()

window.dateTimeFromTZ = (dt, fmt = FORMAT_DATETIME_GRID) ->
	dateFormat(new Date(dt), fmt)

window.dateTimeFromUTC = (dt, fmt = FORMAT_DATETIME_GRID) ->
	dateTimeFromTZ(dateTimeToUTC(dt), fmt)

window.dateTimeInUTC = ->
	d = new Date()
	new Date(d.getTime() + d.getTimezoneOffset() * 60 * 1000)

window.dateTimeNow = ->
	(new Date()).format(FORMAT_DATETIME_SAVE)

window.dateTimeToUTC = (dt) ->
	dt + ' UTC'

window.dateTimetoLocal = (dt) ->
	offset = new Date().getTime()- window.dateTimeInUTC().getTime()
	new Date(new Date(dt).getTime() + offset).format(FORMAT_DATETIME_SAVE)

# return YYYY-MM-DD for any date
window.dateYMD = (dt = (new Date())) ->
	if dt then (new Date(dt)).format('yyyy-mm-dd') else dateYMD() # if dt = '' return today

# safely escape html chars and quotes in text
window.escapeHTML = (val) ->
	$('<i>').html(val).text().replace(/\"/g, '&quot;')

# safely escape html chars and quotes for use in grid view
window.escapeHTMLGrid = (val) ->
	$('<i>').html(val).text().replace(/\"/g, '&quot;').replace(/\n/g, '<br>')

window.esignJSONLabel = (data, html = false, obj = false) ->
	if data and getType(data) is 'string'
		try
			jdata = JSON.parse(data)
			data = jdata if jdata and jdata.name
		catch e
			return data
	position = data?.position
	positionString = if position then (if position != "0 x 0" then "GPS : #{position}" else "GPS : N/A") else null
	signtime = if data?.timestamp then (new Date(data.timestamp)).format(FORMAT_DATETIME_GRID) else null
	if data and data.name and html
		if data.image?.svg
			imgsrc = data.image.svg
		else # smallest blank image - https://stackoverflow.com/questions/6018611/smallest-data-uri-image-possible-for-a-transparent-image
			imgsrc = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
		nametitle = joinValid([data.name, data.title])
		usertitle = joinValid([data.user, data.usertitle])
		if obj
			v =
				name: nametitle
				user: usertitle
				time: signtime
				position: position
				image: data.image
		else
			ut = if usertitle then ' title="User: ' + usertitle + '"' else ''
			'<a class="esign-link' +
			(if data.image?.svg then " has-signed" else "") + '"' + ut + ' href="#esign"><div class="esign-text-container ' +
			(if not data.image?.svg then (if getUserSignature()? and Object.keys(getUserSignature()).length > 0 then 'esign-prefill' else 'click-to-sign') else '') + '" >' +
				(if nametitle then '<div>' + nametitle + '</div>' else '') +
				(if signtime then '<div>' + signtime + '</div>' else '') +
				(if positionString then '<div>' + positionString + '</div>' else '') +
				'</div><img class="esign-image" src="' + imgsrc + '"><span class="edit-esign-icon' + (if data.image?.svg then " edit-exist-esign" else "") + '" />' +
			'</a>'
	else if data
		joinValid([
			data.name
			data.title
			position
			signtime
		])
	else
		''

window.getUserSignature = ->
		user_signature = UserPreference?.user_signature

		if user_signature and typeof user_signature is 'string'
			try
				user_signature = JSON.parse(user_signature)
			catch error
				console.log 'Invalid signature format. Please ensure it is correctly saved in your preferences.'
				return

		if not user_signature or not user_signature?.image
			return

		return user_signature  # Return valid user signature

# Check for Falsy Value
window.falsy = (value) ->
	if value == null or value == false or value == 0 or value == [] or value == '' or value == {}
		true
	else
		false

window.fileJSONLabel = (file, link = false, temp_access = false) ->
	if file and getType(file) is 'string'
		try
			jfile = JSON.parse(file)
			file = jfile if jfile and jfile.filehash? and jfile.filesize?
		catch e
			return file

	if file and file.filehash? and file.filesize?
		fs = ' (' + filesize(file.filesize, {round: 1}) + ')'
		if link and temp_access == false
			'<a target="_blank" href="/api/file/secure/' + file.filehash + '">' +
				file.filename + '</a>' + '<div class="delete-file-icon" />'
		else if temp_access == true
			'<a target="_blank" href="/api/file_temp/' + file.filehash + '/' + btoa(App.user.id) + '">' +
				file.filename + '</a>' + fs
		else
			file.filename + fs
	else
		'-'

window.fileUploaded = (id, file) ->
	setTimeout ->
		if file.error?
			FieldFile.draw_file $('#' + id), ''
			prettyNotify()
			prettyError 'File Upload Error!', file.error
		else
			dv = FieldFile.draw_file $('#' + id), file
			if dv? and dv.d?
				dv.d.closest('.fileupload').addClass('has-file').find('.file-upload-area-container').first().remove()
			prettyNotify()
	, 100
	return


window.string_template = (template, data = {}, dd = null) ->
    missing_keys = false
	if typeof template isnt 'string'
		return template
	return template if not template.includes('{')
	if not template.match(/{([^}]+)}/g)
		return template
	if dd
		data = dd?.values?() or dd?.preset or data or {}
    result = template.replace /{([^}]+)}/g, (match, key) ->
        if data.hasOwnProperty(key) and data[key]
            return data[key]
        else
            missing_keys = true
            return ""
    if missing_keys
        return ""
    return result

# clear empty values in array
window.filterArray = (arr) ->
	$.grep(arr, (n) -> n)

# useful for finding objects with specific keys in an array e.g. transforms & validators
window.findArrayObject = (arr, key, val) ->
	for t in arr
		if t[key]? and t[key] is val
			return t
	false

# goes up the parent chain to find a function
window.findClosest = (obj, fx, alt = null) ->
	if obj[fx]?
		obj[fx]
	else if obj.parent?
		findClosest(obj.parent, fx, alt)
	else if obj.options?.parent?
		findClosest(obj.options.parent, fx, alt)
	else if alt isnt null
		alt
	else
		->

window.fixTrailingDecimalZeros = (val, rounding) ->
	return '' if (not val?) or ($.trim(val) is '')
	value = val
	index_of_dot = rounding.toString().indexOf('.')
	if index_of_dot > -1
		num_decimals = rounding.toString().substring(index_of_dot).length-1
		if num_decimals > 0
			value = value.toFixed(num_decimals)
	return value

# flatten all nested properties & converts numeric keys to f# keys for use with ARJS
window.flattenForReporting = (obj, parentKey = '') ->
	result = {}
	for key, value of obj
		newKey = if parentKey then "#{parentKey}_#{key}" else "#{if Number.isInteger(+key) then "f#{key}" else key}"
		if typeof value == 'object' and not Array.isArray(value) and value isnt null
			result = { ...result, ...flattenForReporting(value, newKey) }
		else
			result[newKey] = value
	result

# flatten & create 1-level deep arrays for ARJS tables
window.formatForReporting = (raw, fieldPrefixes) ->
	data = flattenForReporting(raw)

	for fieldPrefix in fieldPrefixes
		fieldArray = []

		maxIndex = Math.max(...Object.keys(data).map((key) ->
			match = key.match(new RegExp("^" + fieldPrefix + "_(\\d+)_"))
			return parseInt(match?[1] ? -1)
		))

		for i in [0..maxIndex]
			entry = {}
			for key, value of data
				if key.startsWith("#{fieldPrefix}_#{i}_")
					newKey = key.replace("#{fieldPrefix}_#{i}_", "")
					entry[newKey] = value
					delete data[key]
			fieldArray.push(entry) unless Object.keys(entry).length is 0

		data[fieldPrefix] = fieldArray

	data


# round & show right-padded zeros
window.formatDecimal = (n, z) ->
	n = roundTo(n, 1/Math.pow(10, z))
	n.toFixed(z)

window.duplicateEachElement = (arr=[], n=1) ->
	newArr = []
	for el in arr
		for i in [1..n]
			duplicate = _.cloneDeep(el)
			duplicate._page = i
			newArr.push(duplicate)
	newArr

# format as: lastname, firstname initial
window.formatFullName = (lb) ->
	return '' if joinValid(lb, '') is ''
	if (lb.length is 3) and !!lb[2]
		if !!lb[1] and !!lb[0]
			$.trim(lb[2] + ', ' + lb[0] + ' ' + lb[1])
		else if !!lb[1]
			$.trim(lb[2] + ', ' + lb[1])
		else
			$.trim(lb[2] + ', ' + lb[0])
	else
		joinValid(lb, ' ')

# allow basic newline/formatting in section/field notes
window.formatNote = (n) ->
  formatted = n.replace(/\n/g, '<br/>').replace(/\s\s/g, '&nbsp;')
  formatted.charAt(0).toUpperCase() + formatted.slice(1)

# unique identifiers (e.g. used for reporting)
window.generateUUID = ->
	d = (new Date).getTime()
	uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) ->
		r = (d + Math.random() * 16) % 16 | 0
		d = Math.floor(d / 16)
		(if c is 'x' then r else r & 0x7 | 0x8).toString 16
	)
	uuid

# calculate age from string as of today
window.getAge = (dateString, today = new Date()) ->
	birthDate = new Date(dateString)
	age = today.getFullYear() - birthDate.getFullYear()
	m = today.getMonth() - birthDate.getMonth()
	age-- if (m < 0 or (m is 0 and today.getDate() < birthDate.getDate()))
	age

# get dynamic class if exists
window.getDynamicClass = (form, base = '') ->
	dyn = false
	try
		eval('dyn = ' + form + base)
	return dyn if dyn

	# only classes beginning with upper-case letter are matched partially
	fp = form.proper()
	try
		eval('dyn = ' + fp + base)
	return dyn if dyn

	if fp.indexOf('_') > -1
		fe = fp.split('_')
		for i in [fe.length..1]
			fj = fe.slice(0, i).join('_')
			try
				eval('dyn = ' + fj + base)
			return dyn if dyn

	if DSL[form]?.view.max_rows? and (DSL[form].view.max_rows is 1)
		try
			eval('dyn = MaxRowsOne' + base)
		return dyn if dyn

	try
		eval('dyn = CR' + base)
	dyn

# get link id via array or dom
window.getLinkID = (link, linkid) ->
	if linkid?[link]?
		return linkid[link]

window.getParentFormRef = (that, form) ->
	forms = that?.parent?.options?.wrapper?.subforms?.formmap
	parent = forms?[form]
	if not parent
		parent = that?.parent?.get_subform_parent?()?.wrapper?.subforms?.formmap?[form]
	return parent

window.get_text_from_formdata = (preset={}, fields=[]) ->
	val = []
	for k in fields
		v = ''
		if preset[k+'_auto_name']
			v = preset[k+'_auto_name']
		else if preset[k]
			v = preset[k]
		vtp = getType(v)
		if vtp is 'object'
			v = ''
		else if vtp is 'array'
			v = v.join(', ')
		if v
			val.push $.trim(v)
	val.filter(Boolean).join('\n')

# get proper JS type
window.getType = (val) ->
	return 'null' if val is null
	return 'boolean' if typeof val is 'boolean'
	return 'string' if typeof val is 'string'
	return 'number' if typeof val is 'number'
	return 'array' if (typeof val is 'object') and (val instanceof Array)
	return 'object' if (typeof val is 'object') and not (val instanceof Array)
	return 'undefined'

window.isBrowserFirefox = ->
	(navigator.userAgent) and (
		(navigator.userAgent.indexOf('Firefox') > -1)
	)

window.isBrowseriOS = ->
	navigator.platform.substr(0, 1) is 'i'

window.isBrowserIE = ->
	(navigator.userAgent) and (
		(navigator.userAgent.indexOf('MSIE ') > -1) or
		(navigator.userAgent.indexOf('Trident/') > -1) or
		(navigator.userAgent.indexOf('Edge/') > -1)
	)

window.isBrowserMobile = ->
	/Mobi/.test(navigator.userAgent) and (not /iPad/.test(navigator.userAgent))

window.isBrowserIpad = ->
	isIOS = /iPad/.test(navigator.platform) || (navigator.platform == 'MacIntel' && navigator.maxTouchPoints > 1)
	if isIOS
		return true
	else
		return false

window.isBrowserSafari = ->
	/^((?!chrome|android).)*safari/i.test(navigator.userAgent)

window.ical = (data) ->
        start = moment(data['dtstart'] + ' ' + data['appointment_start'])
        interval = 1
        if data['freq'].indexOf('2') > -1
            interval = 2
        else if data['freq'].indexOf('3') > -1
            interval = 3
        else if data['freq'].indexOf('4') > -1
            interval = 4

        ical_str = 'DTSTART:' + start.format('YYYYMMDDTHHmmss') + 'Z\nRRULE:FREQ=WEEKLY;INTERVAL=' + interval + ';'

        if data['until'] != null
            if !data['appointment_end'] != null
                untill = moment(data['until'] + ' ' + data['appointment_end'])
            else
                untill = moment(data['until'])
            untill = untill.add('days',1)
            ical_str += 'UNTIL=' + untill.format('YYYYMMDDTHHmmss') + 'Z;'

        return ical_str.slice(0,-1)


# return concat of object with filter=
window.joinFilters = (obj) ->
	joinParams obj, 'filter='

# return concat of object with field delims
window.joinParams = (obj, pfx = '', sep = ':', dl = '&') ->
	jv = []
	if obj? and getType(obj) is 'object'
		for k,v of obj
			continue if v is null or v is ''
			if getType(v) is 'array'
				for vv in v
					vv = $.trim(vv)
					jv.push(pfx + k + sep + vv) if vv isnt ''
			else
				v = $.trim(v)
				jv.push(pfx + k + sep + v) if v isnt ''
	jv.join(dl)

# return concat of array, ignoring blanks
window.joinValid = (ar, dl = ', ') ->
	jv = []
	if ar?
		if getType(ar) is 'array'
			for a in ar
				continue if a is null or a is ''
				a = $.trim(a)
				jv.push(a) if a isnt ''
		else
			jv.push(ar)
	jv.join(dl)

# returns 'a, b, and c' for ['a','b','c']
window.joinWords = (array) ->
	if array.length == 1
		array[0]
	else if array.length == 2
		array.join(' and ')
	else if array.length > 2
		head = array.slice(0, -1)
		tail = array[array.length - 1]
		head.join(', ') + ', and ' + tail
	else
		''

window.linkJSONLabel = (data, link = false) ->
	if data and getType(data) is 'string'
		try
			jdata = JSON.parse(data)
			data = jdata if jdata and jdata.url
		catch e
			return data

	title = data?.title or data?.url or '-'
	icon = data?.icon or ""
	if data and data.url and link
		target = if data.url isnt '#' then ' target="_blank"' else ''
		className = if data.url.startsWith(CRProtocolPrefix) then 'clararx-link' else ''
		'<a href="' + data.url + '"' + target + ' class="' + className + '"><i class="' + icon + '"></i> ' + title + '</a>'
	else
		title

# load a <script> tag dynamically
window.loadScript = (url) ->
	s = document.createElement('script')
	s.src = url
	document.body.appendChild(s)
	return

window.loaderOptions = ->
	data = {size: 32,fontColor: '#000', title: 'Processing', bgOpacity: '0.6',autoCheck: 32, bgColor: '#FFF'}
	return data

# get timestamp
window.microtime = (get_as_float = false) ->
	now = new Date().getTime() / 1000
	s = parseInt(now, 10)
	if (get_as_float) then now else (Math.round((now - s) * 1000) / 1000) + ' ' + s

# get random id
window.microrand = ->
	Math.random() + '.' + microtime(true)

# get next id for a series
window.next_id = (series) ->
	if not window.next_id_series?
		window.next_id_series = {}
	if not window.next_id_series[series]?
		window.next_id_series[series] = 0
	series + '_' + (++window.next_id_series[series])

# return plain text without any text from child nodes
window.nodeText = (n) ->
	v = n.clone()
	v.children().remove()
	v.text()

window.openWindow = (link) ->
	wind = window.open(link, '_blank')
	wind.DSL = window.DSL
	wind.WF = window.WF
	return wind

# escape dobule-quote for input/textarea fields
window.quote = (text) ->
	el = document.createElement('div')
	el.innerText = el.textContent = text
	text = el.innerHTML
	text


window.fillDSLDefaults = (form, og) ->
	dsl = window.DSL[form]
	data = _.cloneDeep(og)
	return data unless dsl

	for field, fld of dsl.fields
		continue unless fld.model.default
		if fld.model.multi
			data[field] ?= []
			if data[field].length == 0
				if Array.isArray(fld.model.default)
					data[field] = fld.model.default
				else
					data[field].push fld.model.default
			continue
		if not data[field]
			data[field] = fld.model.default
	return data

# parse barcode - gs1, upc etc.
window.parseBarcode = (raw) ->
	if (raw.includes('(') and raw.includes(')')) or raw.includes('\x1d') or raw.includes('\u001d') # GS1 barcode
		# convert HID-plain-text GS1 containing () to use FNC1 \u001d
		bc = raw.trim().replace /\x1d/g, '\u001d'
		bc = bc.substr(1) if bc.startsWith('(')
		matches = bc.match /\(\d{2}\)/g
		if matches and matches.length > 0
			for match in matches
				code = match.slice 1, -1
				bc = bc.replace match, "\u001d#{code}"
		bc = bc.replaceAll('(', '').replaceAll(')', '')

		try
			parseGS1Barcode(bc)
		catch e
			false

	else if raw.length > 4 # maybe GS1
		try
			bc = raw.trim()
			parseGS1Barcode(bc)
		catch
			raw.trim()
	else
		false

# round to
window.roundTo = (val, rounding) ->
	return '' if (not val?) or ($.trim(val) is '')
	v = parseFloat(val)
	return 0 if isNaN(v)
	r = Math.round(v / rounding) * rounding
	r = Math.round(r * 100000) / 100000
	index_of_dot = rounding.toString().indexOf('.')
	if index_of_dot > -1
		num_decimals = rounding.toString().substring(index_of_dot).length-1
		if num_decimals > 0
			r = r.toFixed(num_decimals)
	r

window.sanitizeJSON = (json) ->
	for k,v of Object.entries json
		if v[1] is null
			delete json[v[0]]
	return json

window.scrollTop = ->
	$('html,body').scrollTop 0
	$(window).scroll()
	return

# return sorted array of object keys
window.sortedKeys = (obj) ->
	ret = []
	for k,v of obj # make array
		ret.push(k)
	ret.sort()
	ret

# lodash template from html
window.templateRaw = (tpl, opt = {}) ->
	_.template($('#template-' + tpl).html())(opt)

# safely remove all tags from html
window.textAll = (val) ->
	$('<i>').html(val).text()

# safely remove all child tags from html
window.textOnly = (val) ->
	i = $('<i>').html(val)
	j = i.text()
	i.children().remove().end().text() or j

# return window vertical height for scaling content
window.verticalHeight = ->
	if (window.innerHeight and window.innerHeight < $(window).height())
		window.innerHeight
	else
		$(window).height()

# local storage, no cookie fallback
store =
	clear: ->
		window.localStorage.clear()
	enabled: ->
		try
			store.set('store', 'enabled')
			if store.get('store') isnt 'enabled'
				false
			else
				store.remove('store')
				true
		catch error
			false
	get: (key, def = '') ->
		window.localStorage.getItem(key) or def
	remove: (key) ->
		window.localStorage.removeItem key
		return
	set: (key, value) ->
		window.localStorage.setItem key, value
		return
	user_get: (key, def = '') ->
		store.get store.user_key() + key, def
	user_get_json: (key, def = null) ->
		try
			JSON.parse(CRCrypto.decrypt(store.user_key(), store.user_get(key, '')) or def)
		catch e
			def
	user_key: ->
		if parseInt(store.get('userid', 0)) isnt App.user.id
			store.clear()
			store.set 'userid', App.user.id
		uk = 'u' + App.user.id + '.'
		store.user_key = -> uk # overwrite self for perf.
		uk
	user_remove: (key) ->
		store.remove store.user_key() + key
	user_set: (key, value) ->
		store.set store.user_key() + key, value
		return
	user_set_json: (key, value) ->
		store.user_set key, CRCrypto.encrypt(store.user_key(), JSON.stringify(value))
		return

$.fn.fullscreen = (mode = false) ->
	if mode
		f = @[0]
		fl = f.requestFullScreen or f.requestFullscreen or f.webkitRequestFullscreen or f.webkitRequestFullScreen or f.mozRequestFullScreen or f.msRequestFullscreen
		fl.call(f)
	else
		f = document
		fl =  f.fullscreenElement or f.webkitFullscreenElement or f.mozFullScreenElement or f.msFullscreenElement
		if fl then true else false

$.fn.loop = (block) ->
	for i in @
		element = $(i)
		res = block.call element, element
		break if res is false

$.fn.removeClassPattern = (regex) ->
	$(@).removeClass (index, classes) ->
		classes.split(/\s+/).filter (c) ->
			regex.test c
		.join ' '

$.fn.template = (tpl, opt = {}) ->
	$(this).html(templateRaw(tpl, opt))

window.sanitizeJSON = (json) ->
	for k,v of Object.entries json
		if v[1] is null
			delete json[v[0]]
	return json

window.generateRedisKey = (form, record, preset, linkid) ->
	record = record || preset?.id || 'null' # should only be null for add new
	if linkid
		return [ form, App.user.id, linkid, record ].join("_")
	else
		return [ form, App.user.id, record ].join("_")
# Check for Falsy Value
window.falsy = (value) ->
	if value == null or value == false or value == 0 or value == [] or value == '' or value == {}
		true
	else
		false

window.onSavedCallBack = (that, dfd, ovr=false, okcb=null, failcb=null, allcb=null) ->
	dfd.done (rt) =>
		if !rt.record
			prettyError "No Record Returned", "Error Saving #{DSL[that.form]?.view?.label  or that.form} Record"
			failcb(rt) if failcb?
			return
		if ovr and rt?.error
			prettyError "Error Saving #{DSL[that.form]?.view?.label  or that.form} Record", data?.error
			failcb(rt) if failcb?
			return
		if rt? and rt.values?
			if that.parent.options.isFlyout or that.parent.parent.tabid is 'flyout_root'
				that.parent.save(rt.values, rt.record, that.dom, that.domcols, that.record is null, rt.rowhtml)
				return
			that.record = rt.record
			that.parent.mode('read', that.record)#<===BND
			that.parent?.parent?.onSaved?(rt, that.parent.tabData, that.parent)
		okcb(rt) if okcb?
	dfd.always =>
		allcb() if allcb?

window.get_string_value = (val) ->
	if typeof val == 'string'
		return val
	if Array.isArray(val)
		return val.filter(Boolean).join(', ').trim()
	if typeof val == 'object'
		return Object.values(val).filter(Boolean).join(', ').trim()
	return val

window.getNumericKeys = (obj={}) ->
	keys = Object.keys(obj).map (key) -> 
		if key == 'null' || key == 'undefined' || key == '' || key == 'NaN' || key == null
			return null
		if getType(obj[key]) != 'object'
			return null
		if _.isEmpty(obj[key])
			return null
		if key and getType(key) == 'string' and key.startsWith('__index_')
			key = parseInt(key.replace('__index_', '').replace("'", ''))
			if isNaN(key)
				return null
			return key		
		return null
	keys

window.get_grid_fields = (sf, v) ->
	gridfields = sf.view?.grid?.fields or []
	if v.view.grid?.fields?.length > 0
		gridfields = v.view.grid.fields
	return gridfields

window.get_grid_labels = (sf, v) ->
	flabels = sf.view?.grid?.label or []
	if v.view.grid?.fields?.length > 0
		flabels = v.view.grid?.label or []
	return flabels

window.get_grid_width = (sf, v) ->
	fwidth = sf.view?.grid?.width or []
	if v.view.grid?.fields?.length > 0
		fwidth = v.view.grid?.width or []
	return fwidth

window.getParentFormRef = (that, form) ->
	forms = that?.parent?.options?.wrapper?.subforms?.formmap
	parent = forms?[form]
	if not parent
		parent = that?.parent?.get_subform_parent?()?.wrapper?.subforms?.formmap?[form]
	return parent