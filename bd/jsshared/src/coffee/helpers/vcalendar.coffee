window.VCalenderGenerate = (val) ->
	SEPARATOR = '\n'
	rules = []
	days = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA']
	calendarEvents = []
	calendarStart = [
		'BEGIN:VCALENDAR'
		'VERSION:2.0'
	].join(SEPARATOR)
	calendarEnd = SEPARATOR + 'END:VCALENDAR'

	#Dates
	start_date    = new Date(val.dtstart)
	end_date    = new Date(val.dtstart)
	if !_.isEmpty(val.appointment_start)
		start_time = val.appointment_start.split(" ")
		am_pm = start_time[1]
		time = start_time[0].split(":")
		if am_pm == 'pm'
			time[0] = parseInt(time[0])%12 + 12
		start_date.setHours(time[0],time[1], time[1])
	
	if !_.isEmpty(val.appointment_end)
		end_time = val.appointment_end.split(" ")
		am_pm = end_time[1]
		time = end_time[0].split(":")
		if am_pm == 'pm'
			time[0] = parseInt(time[0])%12 + 12
		end_date.setHours(time[0],time[1], time[1])
	
	until_end_date      = new Date(val.until)
	until_end_date.setDate(until_end_date.getDate()+1)
	start_year    = ('0000' + (start_date.getFullYear().toString())).slice(-4)
	start_month   = ('00'   + ((start_date.getMonth() + 1).toString())).slice(-2)
	start_day     = ('00'   + ((start_date.getDate()).toString())).slice(-2)
	start_hours   = ('00'   + (start_date.getHours().toString())).slice(-2)
	start_minutes = ('00'   + (start_date.getMinutes().toString())).slice(-2)
	start_seconds = '00'
	end_year    = ('0000' + (end_date.getFullYear().toString())).slice(-4)
	end_month   = ('00'   + ((end_date.getMonth() + 1).toString())).slice(-2)
	end_day     = ('00'   + ((end_date.getDate()).toString())).slice(-2)
	end_hours   = ('00'   + (end_date.getHours().toString())).slice(-2)
	end_minutes = ('00'   + (end_date.getMinutes().toString())).slice(-2)
	end_seconds = ('00'   + (end_date.getMinutes().toString())).slice(-2)

	until_end_year      = ('0000' + (until_end_date.getFullYear().toString())).slice(-4)
	until_end_month     = ('00'   + ((until_end_date.getMonth() + 1).toString())).slice(-2)
	until_end_day       = ('00'   + ((until_end_date.getDate()).toString())).slice(-2)
	until_end_hours     = ('00'   + (until_end_date.getHours().toString())).slice(-2)
	until_end_minutes   = ('00'   + (until_end_date.getMinutes().toString())).slice(-2)
	until_end_seconds   = ('00'   + (until_end_date.getMinutes().toString())).slice(-2)
	until_end_time      = ''

	unless start_minutes + start_seconds + until_end_minutes + until_end_seconds is 0
		start_time  = 'T' + start_hours + start_minutes + start_seconds + 'Z'
		end_time  = 'T' + end_hours + end_minutes + end_seconds + 'Z'
		until_end_time    = 'T' + until_end_hours + until_end_minutes + until_end_seconds + 'Z'
	start 			= start_year + start_month + start_day + start_time
	end 			= end_year + end_month + end_day + end_time
	until_end 		= until_end_year + until_end_month + until_end_day + until_end_time

	# calendarEvents.push 'BEGIN:VEVENT'
	# calendarEvents.push 'CLASS:PUBLIC'
	calendarEvents.push 'DTSTART:' + start

	#Build RRule
	rules.push 'RRULE:FREQ=' + val.freq.toUpperCase()
	rules.push 'INTERVAL=' + val.interval  if val.interval > 1

	#Weekly
	if val.freq is 'Weekly'
		rules.push 'WKST=MO'
		days = []
		i = 0

		if val.byweekday?
			while i < val.byweekday.length
				days.push val.byweekday[i].substr(0, 2).toUpperCase()
				i++
		if days.length > 1
			rules.push 'BYDAY=' + days.join(',')

	#Monthly
	if val.freq is 'Monthly'
		if val.monthweekday is 'Day of the Week'
			day = days[start_date.getDay()];
			# https://envoy.myjetbrains.com/youtrack/issue/HB-3034
			# som = start of month -- Need to adjust day count for week
			som = new Date(start_date.getFullYear(), start_date.getMonth(), 1)
			somd = som.getDay()
			wkn = Math.ceil((start_date.getDate() + som.getDay())/7)
			if(start_date.getDay() < somd)
				wkn = wkn - 1
			total_weeks = weekCount(start_date.getFullYear(), start_date.getMonth())
			
			if wkn > 3
				wkn = wkn - (total_weeks + 1)
			
			rules.push 'BYDAY=' + wkn + day
		else
			lastDayOfMonth = new Date(start_date.getFullYear(), start_date.getMonth() + 1, 0)
			if lastDayOfMonth.getDate() is start_date.getDate()
				rules.push 'BYMONTHDAY=-1'
			else
				rules.push 'BYMONTHDAY=' + start_date.getDate()

	if val.until? and val.until != ""
		rules.push 'UNTIL=' + until_end

	calendarEvents.push rules.join(';')
	# calendarEvents.push 'DTEND:' + end
	# calendarEvents.push 'END:VEVENT'

	calendarStart + SEPARATOR + calendarEvents.join(SEPARATOR) + calendarEnd
	calendarEvents.join(SEPARATOR)



window.VCalenderGenerateLab = (val) ->
	SEPARATOR = '\n'
	rules = []
	days = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA']
	calendarEvents = []
	calendarStart = [
		'BEGIN:VCALENDAR'
		'VERSION:2.0'
	].join(SEPARATOR)
	calendarEnd = SEPARATOR + 'END:VCALENDAR'

	#Dates
	start_date    = new Date(val.dtstart + ' 09:00')
	end_date    = new Date(val.dtstart + ' 17:00')
	if !_.isEmpty(val.appointment_start)
		start_time = val.appointment_start.split(" ")
		am_pm = start_time[1]
		time = start_time[0].split(":")
		if am_pm == 'pm'
			time[0] = parseInt(time[0])%12 + 12
		start_date.setHours(9,0,0)
	
	if !_.isEmpty(val.appointment_end)
		end_time = val.appointment_end.split(" ")
		am_pm = end_time[1]
		time = end_time[0].split(":")
		if am_pm == 'pm'
			time[0] = parseInt(time[0])%12 + 12
		end_date.setHours(17,0,0)
	
	until_end_date      = new Date(val.until)
	until_end_date.setDate(until_end_date.getDate()+1)
	start_year    = ('0000' + (start_date.getFullYear().toString())).slice(-4)
	start_month   = ('00'   + ((start_date.getMonth() + 1).toString())).slice(-2)
	start_day     = ('00'   + ((start_date.getDate()).toString())).slice(-2)
	start_hours   = ('00'   + (start_date.getHours().toString())).slice(-2)
	start_minutes = ('00'   + (start_date.getMinutes().toString())).slice(-2)
	start_seconds = '00'
	end_year    = ('0000' + (end_date.getFullYear().toString())).slice(-4)
	end_month   = ('00'   + ((end_date.getMonth() + 1).toString())).slice(-2)
	end_day     = ('00'   + ((end_date.getDate()).toString())).slice(-2)
	end_hours   = ('00'   + (end_date.getHours().toString())).slice(-2)
	end_minutes = ('00'   + (end_date.getMinutes().toString())).slice(-2)
	end_seconds = ('00'   + (end_date.getMinutes().toString())).slice(-2)

	until_end_year      = ('0000' + (until_end_date.getFullYear().toString())).slice(-4)
	until_end_month     = ('00'   + ((until_end_date.getMonth() + 1).toString())).slice(-2)
	until_end_day       = ('00'   + ((until_end_date.getDate()).toString())).slice(-2)
	until_end_hours     = ('00'   + (until_end_date.getHours().toString())).slice(-2)
	until_end_minutes   = ('00'   + (until_end_date.getMinutes().toString())).slice(-2)
	until_end_seconds   = ('00'   + (until_end_date.getMinutes().toString())).slice(-2)
	until_end_time      = ''

	unless start_minutes + start_seconds + until_end_minutes + until_end_seconds is 0
		start_time  = 'T' + start_hours + start_minutes + start_seconds + 'Z'
		end_time  = 'T' + end_hours + end_minutes + end_seconds + 'Z'
		until_end_time    = 'T' + until_end_hours + until_end_minutes + until_end_seconds + 'Z'
	start 			= start_year + start_month + start_day + start_time
	end 			= end_year + end_month + end_day + end_time
	until_end 		= until_end_year + until_end_month + until_end_day + until_end_time

	# calendarEvents.push 'BEGIN:VEVENT'
	# calendarEvents.push 'CLASS:PUBLIC'
	calendarEvents.push 'DTSTART:' + start

	#Build RRule
	rules.push 'RRULE:FREQ=' + val.freq.toUpperCase()
	rules.push 'INTERVAL=' + val.interval  if val.interval > 1

	#Weekly
	if val.freq is 'Weekly'
		rules.push 'WKST=MO'
		days = []
		i = 0

		if val.byweekday?
			while i < val.byweekday.length
				days.push val.byweekday[i].substr(0, 2).toUpperCase()
				i++
		if days.length > 1
			rules.push 'BYDAY=' + days.join(',')

	#Monthly
	if val.freq is 'Monthly'
		if val.monthweekday is 'Day of the Week'
			day = days[start_date.getDay()];
			# https://envoy.myjetbrains.com/youtrack/issue/HB-3034
			# som = start of month -- Need to adjust day count for week
			som = new Date(start_date.getFullYear(), start_date.getMonth(), 1)
			somd = som.getDay()
			wkn = Math.ceil((start_date.getDate() + som.getDay())/7)
			if(start_date.getDay() < somd)
				wkn = wkn - 1
			total_weeks = weekCount(start_date.getFullYear(), start_date.getMonth())
			
			if wkn > 3
				wkn = wkn - (total_weeks + 1)
			
			rules.push 'BYDAY=' + wkn + day
		else
			lastDayOfMonth = new Date(start_date.getFullYear(), start_date.getMonth() + 1, 0)
			if lastDayOfMonth.getDate() is start_date.getDate()
				rules.push 'BYMONTHDAY=-1'
			else
				rules.push 'BYMONTHDAY=' + start_date.getDate()

	if val.until? and val.until != ""
		rules.push 'UNTIL=' + until_end

	calendarEvents.push rules.join(';')
	# calendarEvents.push 'DTEND:' + end
	# calendarEvents.push 'END:VEVENT'

	calendarStart + SEPARATOR + calendarEvents.join(SEPARATOR) + calendarEnd
	calendarEvents.join(SEPARATOR)





window.weekCount = (year, month_number, startDayOfWeek) ->
  # month_number is in the range 1..12

  # Get the first day of week week day (0: Sunday, 1: Monday, ...)
  firstDayOfWeek = startDayOfWeek || 0

  firstOfMonth = new Date(year, month_number-1, 1)
  lastOfMonth = new Date(year, month_number, 0)
  numberOfDaysInMonth = lastOfMonth.getDate()
  firstWeekDay = (firstOfMonth.getDay() - firstDayOfWeek + 7) % 7

  used = firstWeekDay + numberOfDaysInMonth

  return Math.ceil( used / 7)
