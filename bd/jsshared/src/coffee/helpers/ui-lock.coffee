
class UILockView extends CRView

	last_active: 0
	last_auth: 0

	activity_check: ->
		return if Auth.stop_check or Auth.is_dev()
		return if not (App.user.authentication_type is 'password') #HB-4293
		return if microtime(true) - (@last_active or microtime(true)) < App.company.ui_lock
		return if (document.webkitCurrentFullScreenElement or
								document.webkitFullscreenElement or
								document.msFullscreenElement or
								document.mozFullScreenElement or
								document.fullScreenElement or
								document.fullscreenElement
								)

		App.company.user.is_session_locked = true
		@uilockdialog.open()
		return

	activity_reset: =>
		@last_active = microtime(true)
		return

	auth_success: ->
		store.user_set('application.last_auth', microtime(true))
		@activity_reset()
		return

	check_last_auth: ->
		# HB-2055 - ensure refreshing window does not unlock UI
		@last_auth = Math.max(
			parseInt(store.user_get('application.last_auth', 0)),
			App.user.session.login_time
		)
		if (not Auth.is_dev()) and (getType(@last_auth) is 'number') and (@last_auth > 0) and
				(App.user.authentication_type is 'password') and
				(microtime(true) - @last_auth > App.company.ui_lock)
			@uilockdialog.open()
		return

	load: ->
		$('body').append templateRaw('ui-lock-dialog') if $('#ui-lock-dialog').length is 0
		@uilockdialog = new UILockDialogView(parent: @, el: $('body'))

		$(document).keypress(@activity_reset)
		$(document).mouseup(@activity_reset)
		$(document).scroll(@activity_reset)

		setInterval =>
			@activity_check()
		, 1000

		@activity_reset()
		@check_last_auth()
		return

class UILockDialogView extends CRView

	events:
		'click #logout': 'logout'
		'click #unlock': 'unlock'
		'keyup #unlock-password': 'unlock_enter'

	close: ->
		$('body').removeClass('ui-lock-enable')
		App.company.user.is_session_locked = false
		@parent.auth_success()
		return

	logout: ->
		Auth.logout()
		false

	open: ->
		return if $('body.ui-lock-enable').length isnt 0

		App.company.user.is_session_locked = true
		@$('.username').html $('#nav_user').html() + ' (' + $('#nav_role').html() + ')'
		@$('.navhead').html $('#navigation .brand').html()
		@$('.errormsg').html '&nbsp;'
		@$('#unlock-password').attr('autocomplete', 'off')
		@$('#unlock-password').val('')
		$('body').addClass('ui-lock-enable')
		return

	unlock: (e) ->
		@$('.errormsg').html 'Verifying...'
		password = @$('#unlock-password').val()
		@$('#unlock-password').focus()

		aj = Ajax.async
			url: '/auth/'
			type: 'POST'
			data:
				username: App.user.username
				password: password
		aj.done (data, status, xhr) =>
			if data.refresh
				aj = Ajax.async
					url: "/validate_session/"+data.url.substring(data.url.lastIndexOf('/') + 1);
					type: 'POST'
				aj.done (data, status, xhr) =>
					$(window).off('beforeunload');
					window.location.href=data.url;
				aj.fail (data, status, xhr) =>
					console.log(status)
					return
			@close()
			return
		aj.fail (xhr, status, data) =>
			return @close() if xhr.status is 201
			if xhr.responseJSON?.error?
				err = xhr.responseJSON.error
			else if getType(data) is 'object' and data?.error?
				err = data.error
			else
				err = 'Incorrect password entered.'
			@$('.errormsg').html err
			return

		false

	unlock_enter: (e) ->
		@unlock(e) if e.keyCode is 13
		true
