# handles all logging
class CRLog extends CRModel

	lasterr: 0


	build: (subject, stack = null) ->
		u = v = 'N/A'
		if App?.user?.username?
			u = App.user.username + '/' + App.user.role + ' (' + App.user.firstname + ' ' + App.user.lastname + ')'
		if App?.version?
			v = JSON.stringify(App.version)
		[
			subject + (if stack then '\n\n' + stack + '\n' else '')
			'Environment:'
			'  Base: ' + CRBaseURL
			'  User: ' + u
			'  Time: ' + (new Date())
			'  Ver : ' + v
		].join('\n')

	error: (message) ->
		Log.post_error
			message: message
			exception: true
		return

	message: (message) ->
		Log.post_error
			message: message
			exception: false
		return

	post_error: (data) ->
		type = if data.exception then 'error' else 'log'
		if typeof(console) isnt 'undefined' and typeof(console[type]) isnt 'undefined'
			console[type](data.message)
		return

	start: ->
		window.log=Log.message
		return
