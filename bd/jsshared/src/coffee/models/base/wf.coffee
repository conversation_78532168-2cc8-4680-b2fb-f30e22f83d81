# handles all workflow
class CRWorkflow extends CRModel

	initialize: ->
		@hook = {}
		@rules = {}
		return

	run: (method, $) ->
		eval("("+method.toString()+")()")

	set: (@rules) ->
		@hook = {}
		for k,v of @rules
			continue if (v.board isnt 'client') or (not v.type.includes('client'))
			@run(v.subscribe, new CRWorkflowDollar(@, k))
		return

	subscribe: (type, key, action, rule) ->
		@hook[type] = {} if not @hook[type]?
		@hook[type][key] = {} if not @hook[type][key]?
		@hook[type][key][action] = [] if not @hook[type][key][action]?
		@hook[type][key][action].push rule if not @hook[type][key][action]?.includes(rule)
		return

	trigger: (type, key, mode, operation, context, data, callback = false) ->
		return if _.isEmpty(@hook)

		action = false
		if type is 'form'
			if mode in ['add', 'addfill']
				action = 'create'
			else if mode is 'edit'
				action = 'update'
			else if mode in ['open', 'read']
				action = mode
			if operation in ['save', 'snap', 'show']
				action += '.' + operation

		if action and @hook[type]?[key]?[action]?
			for k in @hook[type][key][action]
				@trigger_hook type, key, action, context, data, callback, k
		return

	trigger_hook: (type, key, action, context, data, callback, k) ->
		v = @rules[k]
		dollar = new CRWorkflowDollar(@, k)

		if type is 'form'
			dollar.context.form =
				name: key
				method: action
				data: data
			dollar.context.ui =
				callback: callback
				dom: context
				snap: findClosest(context, 'snapshot_data')()
				subforms: context.subforms?.formmap

		@run(v.action, dollar) if @run(v.condition, dollar)


class CRWorkflowDollar extends CRModel

	initialize: (@parent, @rule) ->
		@context =
			environment:
				name: 'client'
				client: App.version.app.type
				version: App.version.app.version
			user: App.user
			form: {}
			options: {}
			ui:
				dom: false
				snap: false
				subforms: false

		@dsl = (fm) =>
			DSL[fm]

		@form = (fm) =>
			open: (type, id, mode) ->
				App.appview.navigation.navto([type, id, fm, mode], 'Loading...')
				return
			read: (flt) =>
				if getType(flt) is 'number'
					u = '/form/' + fm + '/' + flt
				else
					dflt =
						sort: [['asc', 'id']]
						limit: MAX_DATA_ROWS
						fields: 'all'
					flt = _.defaults(flt, dflt)
					p = [
						'fields=' + flt.fields
						'limit=' + flt.limit
					]

					for f in flt.sort
						p.push 'sort=' + (if f[0] is 'desc' then '-' else '') + f[1]

					for f in flt.filter
						[op, k, v] = f
						continue if (k is 'archived') or (k is 'deleted')
						v = $.trim(v)
						continue if (v is '')
						if op is 'between'
							v = v[0] + '..' + v[1]
						else if op is 'like'
							v = v + '*'
						else if op is 'gt'
							v = v + '..'
						else if op is 'lt'
							v = '..' + v
						p.push 'filter=' + k + ':' + v

					if flt.archived?
						if flt.archived is 'true'
							p.push 'archived'
						else if flt.archived is 'all'
							p.push 'archived=all'

					if flt.deleted?
						if flt.deleted is 'true'
							p.push 'deleted'
						else if flt.deleted is 'all'
							p.push 'deleted=all'

					u = '/form/' + fm + '/?' + p.join('&')

				dfd = $.Deferred()
				aj = Ajax.async
					url: u
				aj.done (data, status, xhr) =>
					dfd.resolve(data, status, xhr)
					return
				aj.fail (xhr, status, data) ->
					log(['WF Read Error', u, data, status, xhr])
					dfd.reject(data, status, xhr)
					return
				dfd
			update: (flt,data) =>
				if getType(flt) is not 'number'
					return dfd
				u = '/form/' + fm + '/' + flt
				dfd = $.Deferred()
				aj = Ajax.async
					url: u
					type: 'PUT'
					data: data
				aj.done (data, status, xhr) =>
					dfd.resolve(data, status, xhr)
					return
				aj.fail (xhr, status, data) ->
					log(['WF Read Error', u, data, status, xhr])
					dfd.reject(data, status, xhr)
					return
				dfd

		@g =
			logger:
				exception: (e) ->
					Log.error e
				error: (e) ->
					Log.error e
				warning: (e) ->
					Log.message e
				info: (e) ->
					Log.message e
				debug: (e) ->
					Log.message e
			notification:
				alert:   window.prettyAlert
				confirm: window.prettyConfirm
				error:   window.prettyError
				message: window.prettyMessage
				notify:  window.prettyNotify
				yesno:   window.prettyYesNo

		@subscribe =
			form: (f, t) =>
				if DSL[f]?
					@parent.subscribe('form', f, t, @rule)
				else
					log('Invalid workflow subscription form: ' + f)
				return

		@vars = {}
		return
