# handles ajax & websocket calls
class CRAjax extends CRModel

	ws: false
	wd: {}
	wm: {}
	wc: 0
	wclimit: 30

	add_ie_param: (opt) ->
		if (not opt.type?) or (opt.type is 'GET')
			(if opt.url.indexOf('?') > -1 then '&' else '?') + 'rnd=' + (new Date).getTime()
		else
			''

	ajax: (opt) ->
		opt = _.defaults(opt, @ajax_defaults)
		if opt.data? and getType(opt.data) in ['', 'object']
			opt.data = JSON.stringify(opt.data)
		if CRLocal
			# note - only GET allowed
			opt.crossDomain = true
			opt.dataType = 'jsonp'
			opt.jsonp = 'jsonp'
		opt.headers = {} if not opt.headers?

		if App?.version?.app?
			opt.headers['Clara-client-app'] = App.version.app.type
			opt.headers['Clara-client-version'] = App.version.app.version

		over_ws = false
		if App.feature.nes
			if opt.type is 'GET'
				if opt.url.substr(0, 6) is '/form/'
					opt.url = '/form/' + opt.url.substr(6)
				else if (
						(opt.url.substr(0, 8) is '/unique/') or
						(opt.url.substr(0, 4) is '/dsl')
				)
					opt.url =  opt.url
				if App.feature.wss and (not CRLocal) and opt.dataType isnt 'jsonp' and
						(@ws and @ws.readyState == @ws.OPEN) and
						opt.url.indexOf('/print/') < 0 and
						opt.url.indexOf('/form/audit') < 0 and
						opt.url.indexOf('/') >= 0
					over_ws = true
			else if opt.url.substr(0, 6) is '/comm/'
				opt.url = opt.url

		# Ajax.sync calls should never go over wss
		if opt.async? and (not opt.async) and over_ws
			over_ws = false

		if over_ws
			opt.url = CRBackend + opt.url.substr(3)
			@ws_call opt
		else
			opt.url = CRBackend + opt.url
			$.ajax opt

	ajax_defaults:
		type: 'GET'
		dataType: 'json'
		async: true
		contentType: 'application/json; charset=utf-8'

	async: (opt) ->
		opt.url += @add_ie_param(opt) if isBrowserIE()
		@ajax opt

	get_put: (opt) ->
		dfd = $.Deferred()
		aj = Ajax.async
			url: opt.url
		aj.done (data, status, xhr) ->
			if not data?[0]?
				dfd.reject(data, status, xhr)
				return
			data = _.defaults(opt.data, data[0])
			ak = Ajax.async
				type: 'PUT'
				url: opt.url
				data: data
			ak.done (data, status, xhr) ->
				dfd.resolve(data, status, xhr)
			ak.fail (xhr, status, data) ->
				dfd.reject(data, status, xhr)
			return
		aj.fail (xhr, status, data) ->
			dfd.reject(data, status, xhr)
			return
		dfd

	monitor: (form, key, interval, force_refresh, callback) ->
		if App.feature.monitor and (not CRLocal)
			newmon = not @wm[form]?
			@wm[form] = {} if newmon
			@wm[form][key] =
				interval: interval
				callback: callback
			@ws_monitor form if newmon
			setInterval callback, force_refresh
		else
			setInterval callback, interval
		return

	start: ->
		@ws_connect()
		setInterval =>
			@ws_connect()
		, 1000
		return

	# ONLY USED FOR SAVING - DO NOT USE FOR ANY GET OPERATIONS!
	sync: (opt) ->
		opt.async = false
		r = @ajax opt
		if r.status is 200
			if r isnt ''
				r = JSON.parse(r.responseText)
			if getType(r) is 'array' and r.length is 1 and getType(r[0]) is 'object'
				r = r[0]
			return r
		r.responseJSON or r.responseText

	ws_call: (opt) ->
		id = generateUUID()
		dfd = $.Deferred()
		@wd[id] =
			url: opt.url
			dfd: dfd
		@ws.send JSON.stringify(
			id: id
			method: (if opt.type? then opt.type else 'GET')
			url: opt.url
			body: if opt.data? then opt.data else {}
		)
		dfd

	ws_connect: ->
		return if window?.disableWS
		return if not (App?.version?.nes?.wss? and App.version.nes.wss)
		return if CRLocal or !window.WebSocket? or (@ws and @ws.readyState != @ws.CLOSED)
		return if @wc > @wclimit

		@ws = new WebSocket(CRBackend.replace('https', 'wss') + '/')

		@ws.onclose = (event) =>
			# only increment error count if websockets has never been connected
			@wc++ if @wc >= 0
			return

		@ws.onerror = (event) =>
			toastr.options.preventDuplicates = true
			toastr.error("No Internet Available")
			App.company.user.is_internet_avail = false
			return

		@ws.onopen = (event) =>

			# websockets connected successfully at least once, set count to negative
			@wc = -1

			toastr.options.preventDuplicates = false
			toastr.clear()
			App.company.user.is_internet_avail = true

			# queue monitors
			for form,_ of @wm
				@ws_monitor form

			# handle data
			@ws.onmessage = (event) =>
				try
					data = JSON.parse(event.data)
					if data.monitor?
						if @wm[data.monitor]?
							for key, mdata of @wm[data.monitor]
								mdata.callback(data)
					else if data.id? and @wd[data.id]?
						w = @wd[data.id]
						xhr =
							responseJSON: data.body
						if data.body?.error?
							w.dfd.reject(xhr, data.status, data.body)
						else
							w.dfd.resolve(data.body, data.status, xhr)
						delete(@wd[data.id])
					else
						#//TODO - relay message all observers
						# log(['no id', data.body, data.status])
				catch e
					console.error(e)
					return
				return
			return
		return

	ws_monitor: (form) ->
		if (@ws and @ws.readyState == @ws.OPEN)
			@ws.send JSON.stringify(monitor: form)
		return
