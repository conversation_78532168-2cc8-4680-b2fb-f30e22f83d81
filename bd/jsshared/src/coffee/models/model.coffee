
class CRModel

	constructor: (args...) ->
		@initialize args...
		return

	initialize: ->
		return

	generate_child_parent_map: (rowdata={}, parent_form) ->
		map = {}
		fields =  DSL[parent_form].fields
		for k, v of fields
			continue if v.model.type isnt 'subform' 
			source_form = DSLRecordSubform.get_subform_source(parent_form, k, rowdata)
			continue if not source_form
			[source_form, sfs] = source_form
			continue if !window.DSL[source_form] #need to handle assessment{therapy_id} fields
			continue if parent_form is source_form
			map[source_form] = parent_form
			sfv = rowdata[k]
			if !v.model.multi
				map = _.merge(map, @generate_child_parent_map(sfv?[0] or {}, source_form))
		return map

	get_child_parent_map: ()->
		map = @generate_child_parent_map(@root().get_record_data(), @options.form)
		return map

	get_flattend: (type, form) ->
		if !@flattend
			@flattend =
				prefill: null
				rowdata: null
				compare: null
		if type is 'prefill'
			if @flattend.prefill == null
				df = _.cloneDeep(@root().prefill)
				@flattend.prefill = @flattend_prefill(df, @options.form) or {}
			return @flattend.prefill[form] or {}
		else if type is 'rowdata'
			if @flattend.rowdata == null
				df = _.cloneDeep(@root().get_record_data())
				@flattend.rowdata = @flattend_rowdata(df, @options.form) or {}
			return @flattend.rowdata[form] or {}
		else if type is 'compare'
			if @flattend.compare == null
				df = _.cloneDeep(@root().compare)
				@flattend.compare = @flattend_rowdata(df, @options.form) or {}
			return @flattend.compare[form] or {}
		else
			console.error("Invalid type #{type} for get_flattend")
			return {}
	
	flattend_prefill: (prefill, parent_form) ->
		sval = {}
		fields =  DSL[parent_form].fields
		for k, v of fields
			continue if v.model.type isnt 'subform' 
			source_form = DSLRecordSubform.get_subform_source(parent_form, k, prefill)
			continue if not source_form
			[source_form, sfs] = source_form
			continue if !window.DSL[source_form] #need to handle assessment{therapy_id} fields
			continue if parent_form is source_form
			sfv = prefill[k]
			if v.model.multi
				continue
			else
				if sfv
					sval[source_form] = sfv
					sval = _.merge(sval, @flattend_prefill(sfv, source_form))
		return sval
	
	flattend_rowdata: (rowdata, parent_form) ->
		sval = {}
		if !rowdata
			return sval

		fields =  DSL[parent_form].fields
		for k, v of fields
			continue if v.model.type isnt 'subform' 
			source_form = DSLRecordSubform.get_subform_source(parent_form, k, rowdata)
			continue if not source_form
			[source_form, sfs] = source_form
			continue if !window.DSL[source_form] #need to handle assessment{therapy_id} fields
			continue if parent_form is source_form
			sfv = rowdata[k]
			if sfv
				sval[source_form] = sfv
			else
				sval[source_form] = []
				continue
			if v.model.multi
				continue
			else
				if sfv and sfv?[0]
					sval = _.merge(sval, @flattend_rowdata(sfv[0], source_form))
		return sval
