class DSLParse extends B.View

	# DSL_Specification, and DSL_AutoInsert should already be defined and available
	E: false
	L: false
	X: false

	@auto_insert: (au) ->
		allowed =
			autoinsert: true
			label: ''
			readonly: true
			type: ''
		r = {}
		for k,v of au.fields
			r[k] = {}
			for t in ['model', 'view']
				r[k][t] = {}
				for kk,vv of v[t]
					if kk is 'type' and vv in ['boolean', 'string']
						vv = 'text'
					if allowed[kk]?
						r[k][t][kk] = vv
		fields: r

	# exit with error
	err: (msg) ->
		@E.push msg
		return

	initialize: ->
		@E = []
		@L = {forms: [], transform: [], validate: []}
		@X = {}
		return

	# check if type is valid
	isValidType: (types, val) ->
		types = [types] if getType(types) is 'string'
		getType(val) in types

	# merge & validate custom hand-written DSL with specifications & auto-insert fields
	mergeDSL: (parents, key, edsl, spec, auto) ->
		ret = {}

		# add all the keys from spec if not a default value
		if not ('def' of spec)
			for k, v of spec
				continue if k is 'DEFAULTS'
				if getType(v) is 'object'
					if 'def' of v
						ret[k] = v.def
					else
						ret[k] = {}
				else
					ret[k] = v

		# add all the keys from auto and hand-written dsl
		for src in [auto, edsl]
			for k, v of src
				keyparent = parents + '.' + key
				keypath = keyparent + '.' + k
				# check for reserved keywords
				if k in ['def', 'DEFAULTS']
					@err keyparent + ' cannot use the reserved keyword: ' + k
				# check for valid key names
				if not (k of ret) and # only type:objects can have dynamic key names
						not ('DEFAULTS' of spec) and
						not ('def' of spec and 'type' of spec and ((spec.type is 'object') or ('object' in spec.type)))
					@err keyparent + ' has unknown key: ' + k

				if getType(v) is 'object'
					ret[k] = {}
				else if 'DEFAULTS' of spec
					@err keypath + ' must be type:object instead of type:' + getType(v)
				else
					if (k of spec)
						if ('def' of spec[k])
							# check if type is valid
							if ('type' of spec[k]) and not @isValidType(spec[k].type, v)
								@err keypath + ' must be type:' + spec[k].type + ' instead of type:' + getType(v)
							# check if value is within approved source list
							if ('source' of spec[k]) and not (v in spec[k].source)
								@err keypath + ' must have value:' + spec[k].source + ' instead of value:' + v
						else if getType(spec[k]) is 'object'
							# check if type is object
							if getType(v) isnt 'object'
								@err keypath + ' must be type:object instead of type:' + getType(v)
					ret[k] = v

		# merge dsl sub-tree
		for k, v of ret
			if getType(v) is 'object'
				p1 = if k of edsl then edsl[k] else {}
				if k of spec
					p2 = spec[k]
				else if 'DEFAULTS' of spec
					p2 = spec['DEFAULTS']
				else
					p2 = {}
				p3 = if k of auto then auto[k] else {}
				ret[k] = @mergeDSL parents + '.' + key, k, p1, p2, p3
		ret

	parse: (code, json) ->
		@parseDSL(code, json)
		#@showLists()
		if @L.forms.length > 0
			for k in @uniqueSort(@L.forms)
				@err 'Unavailable form: ' + k
		return json: @X, err: @E

	parseDSL: (k, v) ->

		if (getType(v) isnt 'object') or not ('fields' of v)
			@err 'DSL.' + k + ' does not have any fields!'

		@X[k] = @mergeDSL 'DSL', k, v, DSL_Specification, DSL_AutoInsert

		for fn, fv of @X[k].fields
			# set appropriate view.control
			if (fv.view.control is '')
				if fv.model.source isnt null
					@X[k]['fields'][fn].view.control = 'select'
				else if fv.model.type in ['date', 'datetime', 'time']
					@X[k]['fields'][fn].view.control = 'picker'
				else
					@X[k]['fields'][fn].view.control = 'input'
			if (fv.model.type is 'subform')
				@X[k]['fields'][fn].view.control = if fv.model.multi then 'subform' else 'inline'

			# find existence of all sources
			if getType(fv.model.source) is 'string' and fv.model.source.indexOf('{') is -1
				if not (fv.model.source of DSL)
					@L.forms.push(fv.model.source)
				else if fv.model.sourceid isnt 'id' and not (fv.model.sourceid of DSL[fv.model.source].fields)
					@L.forms.push(fv.model.source + '.' + fv.model.sourceid)
			if fv.model.sourceid not in ['id', 'code',  'mcr_ref']
				@err 'DSL.fields.' + fn + '.model.sourceid is invalid: ' + fv.model.sourceid

			# verify <field>.model.if.<selection>: fields/sections exist and are active
			if fv.model.active
				for ifk, ifv of fv.model.if
					@verifyIfSelections k + '.fields.' + fn + '.model.if.' + ifk, @X[k], ifv

			# verify that offscreen fields using if + prefill are readonly
			if (fv.model.active) and (fv.view.offscreen) and (not fv.view.readonly) and
			(fv.model.prefill.length > 0) and (getType(fv.model.if) is 'object')
				@err 'DSL.fields.' + fn + '.view.readonly must be true since it is offscreen and uses if/prefill.'

			# setup sorting of subfields
			if fv.model.subfields? and fv.model.subfields_sort.length is 0
				fv.model.subfields_sort = for sfk, _ of fv.model.subfields
					sfk

			# list all field transformers and validators
			@queueList k + '.' + fn + '.model', 'field.model', 'transform', fv.model.transform
			@queueList k + '.' + fn + '.model', 'field.model', 'validate',  fv.model.validate
			@queueList k + '.' + fn + '.view',  'field.view',  'transform', fv.view.transform
			@queueList k + '.' + fn + '.view',  'field.view',  'validate',  fv.view.validate

		# ensure model.name fields exists IF model.name is array
		if getType(@X[k].model.name) is 'array'
			for f in @X[k].model.name
				if not (f of @X[k].fields)
					@err 'DSL.model.name uses non-existent form field: ' + f

		# autofill view.find.advanced if needed
		if (@X[k].view.find.advanced.length is 0) and (@X[k].view.find.basic.length > 0)
			@X[k].view.find.advanced = @X[k].view.find.basic

		# verify all fields in view.find exist and are active
		for ff in ['basic', 'advanced']
			node = fields: @X[k].view.find[ff], sections: []
			@verifyIfSelections k + '.view.find.' + ff, @X[k], node

		# verify all fields in view.grid exist and are active
		for f in @X[k].view.grid.fields
			continue if (getType(f) isnt 'string') or (f.indexOf('.') > -1)
			if not (f of @X[k].fields)
				@err k + '.view.grid.fields' + ' uses non-existent form field: ' + f
			else if not @X[k].fields[f].model.active
				@err k + '.view.grid.fields' + ' uses inactive form field: ' + f

		# list all form transformers and validators
		@queueList k + '.model', 'model', 'transform', @X[k].model.transform
		@queueList k + '.model', 'model', 'validate',  @X[k].model.validate
		@queueList k + '.view' , 'view',  'transform', @X[k].view.transform
		@queueList k + '.view' , 'view',  'validate',  @X[k].view.validate

		@setSectionsOrder k
		@verifySections k, @X[k]

		return

	queueList: (parent, modelview, type, list) ->
		keypath = parent + '.' + type
		if getType(list) isnt 'array'
			@err keypath + ' must be type:array instead of type:' + getType(list)
		for i in list
			if (getType(i) is 'object')
				if 'name' of i
					@L[type].push(modelview + '.' + type + '.' + i.name)
				else
					@err keypath + ' is missing key:name'
			else
				@err keypath + ' must contain only type:object instead of type:' + getType(i)

	setSectionsOrder: (k) ->
		@X[k].model.sections_order = []
		if @X[k].model.sections_group? and @X[k].model.sections_group.length > 0
			@X[k].model.sections = {}
			for gr in @X[k].model.sections_group
				for kk,vv of gr
					if vv.fields?
						vv.group =
							label: kk
							note: if vv.note? then vv.note else ''
							hide_header: vv.hide_header or false
						@X[k].model.sections[kk] = vv
						@X[k].model.sections_order.push(kk)
					else if vv.sections?
						for sc in vv.sections
							for sk,sv of sc
								sv.group =
									label: kk
									note: if vv.note? then vv.note else ''
									hide_header: vv.hide_header or false
								@X[k].model.sections[sk] = sv
								@X[k].model.sections_order.push(sk)
		else if @X[k].model.sections?
			@X[k].model.sections_order = for kk,vv of @X[k].model.sections
				kk
		return

	showLists: ->
		# show results of parsing
		log('Transform[]:')
		for k in @uniqueSort(@L.transform)
			log(' ' + k)
		log('\nValidate[]:')
		for k in @uniqueSort(@L.validate)
			log(' ' + k)
		return

	uniqueSort: (arr) ->
		obj = {}
		for v in arr
			obj[v] = 1
		ret = []
		for k,v of obj
			ret.push(k)
		ret.sort()
		ret

	verifyIfSelections: (parents, form, node) ->
		for f in node.fields
			if not (f of form.fields)
				@err parents + ' uses non-existent form field: ' + f
			else if not form.fields[f].model.active
				@err parents + ' uses inactive form field: ' + f
		if not form.model.sections_group?
			for f in node.sections
				if not (f of form.model.sections)
					@err parents + ' uses non-existent form section: ' + f
		return

	verifySections: (parents, form) ->
		for k, v of form.model.sections
			if not (getType(v.prefill) in ['null', 'string', 'undefined'])
				@err parents + '.model.sections.\'' + k + '\'.prefill must be string'
			for f in v.fields
				if not (f of form.fields)
					@err parents + '.model.sections.\'' + k + '\'.fields uses non-existent form field: ' + f
				else if not form.fields[f].model.active
					@err parents + '.model.sections.\'' + k + '\'.fields uses inactive form field: ' + f
				else if v.fields.length > 1 and form.fields[f].model.type is 'subform'
					@err parents + '.model.sections.\'' + k + '\'.fields must have exactly one subform field: ' + f
		return
