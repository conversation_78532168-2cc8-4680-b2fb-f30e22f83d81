class DSLText extends CRModel

	@hidden_sections: ['demographics']

	@field_map: (dd) ->
		fm = {}
		for grp in dd.dom
			continue if not grp.sections or grp.sections.length is 0
			frm = grp.form or dd.options.form
			fm[frm] = {} if not fm[frm]?
			for sec in grp.sections
				for f in sec.fields
					fm[frm][f.id] = f
		fm

	@text: (dd, val) ->
		tx = []
		fm = @field_map(dd)
		dr = dd.dr.subforms
		for grp in dd.dom
			continue if not grp.sections or grp.sections.length is 0
			if grp.multi # multi-subform
				continue if not val[dr.fieldrev[grp.form]?.toString()]?.length > 0
				stx = []
				fval = val[dr.fieldrev[grp.form]]
				cnt = 0
				for kval, sval of fval
					[sftx, sffr] = @text_sections(dd, sval, fm, grp.sections, grp.multi)
					continue if sftx.length is 0
					stx.push grp.group + ' (Form #' + (++cnt) + ' of ' + fval.length + '):'
					stx = stx.concat sftx
					for k,v of sffr
						continue if not val[dr.fieldrev[k]]?
						val[dr.fieldrev[k]][kval]._meta = {} if not val[dr.fieldrev[k]][kval]?._meta?
						val[dr.fieldrev[k]][kval]._meta.text = $.trim(v.join('\n'))
			else # root form and inline subforms
				[stx, sfr] = @text_sections(dd, val, fm, grp.sections, grp.multi)
				continue if stx.length is 0
				kval = 0
				for k,v of sfr
					continue if not val[dr.fieldrev[k]]?
					val[dr.fieldrev[k]][kval]._meta = {text: ''} if not val[dr.fieldrev[k]][kval]?._meta?
					val[dr.fieldrev[k]][kval]._meta.text = $.trim(val[dr.fieldrev[k]][kval]._meta.text + '\n' + v.join('\n'))
			tx = tx.concat stx if stx.length > 0

		# return fully populated values
		val._meta = {} if not val._meta?
		val._meta.text = $.trim(tx.join('\n'))
		val._meta.tzoffset = (new Date().getTimezoneOffset()) # Send client TZ Offset, to get exact datetime mapped to server datetime
		val

	@text_field_grid: (dd, val, fld, ind = '') ->
		tx = []
		v = fld.fieldgen.v
		vf = val[fld.id]
		cnt = 0
		for gval in vf
			gtx = []
			for s in v.model.subfields_sort
				sv = $.trim(gval[s])
				continue if sv is ''
				gtx.push ind + '    ' + v.model.subfields[s].label + ': ' + sv
			continue if gtx.length is 0
			tx.push ind + '  ' + v.view.label + ' (#' + (++cnt) + ' of ' + vf.length + '):'
			tx = tx.concat gtx
		tx

	@text_field_input: (dd, val, fld, ind = '') ->
		v = fld.fieldgen.v
		val = val[fld.id + '_auto_name'] or val[fld.id]
		if v.model.multi
			return [] if _.isEmpty(val) or getType(val) isnt 'array' or (val.length is 1 and _.isEmpty(val[0]))
			if getType(v.model.source) is 'object'
				val = for r in val
					v.model.source[r] or r
			val = val.join(', ')
		else
			return [] if $.trim(val) is ''
			if getType(v.model.source) is 'object' and v.model.source[val]?
				val = v.model.source[val]
			if v.model.type is 'datetime' # make datetime field data to be in localized datetime HB-5586
				val = moment(val).format('MM/DD/YYYY hh:mm A')
		[ind + '  ' + v.view.label + ': ' + $.trim(val)]

	@text_field_value: (dd, val, f, ind = '') ->
		if f.fieldgen.v.view.offscreen
			[]
		else if f.fieldgen.v.model.type is 'subform' and f.fieldgen.v.model.multi
			[] # don't print grid for multi-subform since we will expand it
		else if (not val[f.id]?) or ($.trim(val[f.id]) is '')
			[] # all non-multi-subform fields must have value
		else if f.fieldgen.v.model.type is 'json' and f.fieldgen.v.view.control is 'grid'
			@text_field_grid(dd, val, f, ind)
		else
			@text_field_input(dd, val, f, ind)

	@text_fields: (dd, val, fm, sec, ovfld, ind = '') ->
		tx = []
		flused = {}

		frm = sec.form or dd.options.form
		if frm and fm[frm]? and ovfld?
			for fd in ovfld
				f = fm[frm][fd]
				continue if not f?
				flused[f.id] = true
				vtx = @text_field_value(dd, val, f, ind)
				continue if vtx.length is 0
				tx = tx.concat vtx

		for f in sec.fields
			continue if not f.id
			continue if flused[f.id]? # do not repeat fields already printed
			vtx = @text_field_value(dd, val, f, ind)
			continue if vtx.length is 0
			tx = tx.concat vtx

		tx

	@text_sections: (dd, val, fm, secs, multi) ->
		tx = []
		fr = {}
		dr = dd.dr.subforms
		ind = if multi then '  ' else ''

		loop_sections = (override) =>
			for s in secs
				continue if s.section.toLowerCase() in DSLText.hidden_sections
				fval = val
				if not multi
					if dr.recmap[s.form]?.shmap?.sections[s.section]?.current?
						cur = dr.recmap[s.form]?.shmap?.sections[s.section]?.current
					else
						cur = true
					continue if not cur
					if val[dr.fieldrev[s.form]?.toString()]?.length is 1 # inline subform
						fval = val[dr.fieldrev[s.form]][0]

				ovfld = dd.options.parent.sections_fields_text_override?[s.section]
				continue if not (override is ovfld?)
				stx = @text_fields(dd, fval, fm, s, ovfld, ind)
				continue if stx.length is 0
				tx.push ind + s.section + ':'
				tx = tx.concat(stx)
				tx.push ''

				# text for subforms
				if dd.options.form isnt s.form
					fr[s.form] = [] if not fr[s.form]?
					fr[s.form].push s.section + ':'
					fr[s.form] = fr[s.form].concat(stx.map((x) -> x.substr(ind.length)))
					fr[s.form].push ''

		# first loop through all the custom sections-fields, then the standard
		#   this will put the custom fields at the top
		loop_sections true
		loop_sections false
		[tx, fr]
