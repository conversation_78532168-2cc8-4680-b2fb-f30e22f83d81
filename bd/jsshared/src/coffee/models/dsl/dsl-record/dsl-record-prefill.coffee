class DSLRecordPrefill extends CRModel

	###
		same @options as caller
	###

	initialize: (options) ->
		@options = options
		@dfd = $.Deferred()
		@filldata = {}
		@formdata = {}
		@force_prefill_parent = false

		flyout_card = @options?.parent?.parent
		flyout_form = flyout_card?.form
		if flyout_form? and flyout_card?
			parent_subforms = flyout_card?.subform_parent?.wrapper?.subforms
			if parent_subforms
				parent_form = parent_subforms.child[flyout_form]
				if parent_form and parent_subforms.formmap[parent_form]
					@formdata['parent'] = parent_subforms.formmap[parent_form].values()
					@force_prefill_parent = true
				else
					@formdata['parent'] = parent_subforms.forms[0].values()
					@force_prefill_parent = true
		return

	get_links: (rowdata) ->
		return false if not Auth.can_read_any(@options.form)
		return false if (getType(@options.links) isnt 'array') or (@options.links.length is 0)
		p = if @options.el? then @options.el.parents('.navtabitem') else []
		return false if (p.length is 0) and (_.isEmpty(rowdata)) and (not @options.linkid?)
		lks = {}
		for link in @options.links
			lid = getLinkID(link, @options.linkid, @options.el)
			lid = rowdata[link + '_id'] if (not lid) and rowdata[link + '_id']? # used when editing recurring forms
			lks[link + '_id'] = lid if lid
		lks

	parse: ->
		# parse root @options.form first, then read+parse all of its subforms
		fd = if @formdata[@options.form]? then @formdata[@options.form] else {}
		pd = @parse_form(null, @options.form, fd)
		$.when.apply($, pd).done =>
			drf = @read_subforms(@options.form, @filldata)
			drf.done =>
				@dfd.resolve
					data: @filldata
					formdata: @formdata
					mode: @options.mode
			drf.fail (err) =>
				@dfd.reject(err)
			return
		return

	parse_chained: (root, form, k, fl, k1, v1, k2, k3) ->
		cdfd = $.Deferred()
		ldfd = @parse_chained_lookup(fl, k1, v1, [k2, k3])
		ldfd.done (data) =>
			if form is @options.form
				@filldata[k] = data
			else if root
				root[k] = data
			cdfd.resolve(data)
			return
		cdfd

	parse_chained_lookup: (form, key, val, keys) ->
		ldfd = $.Deferred()

		lfld = DSL[form].fields[key].model
		lform = lfld.source
		if lfld.sourceid is 'id' and getType(lform) is 'string'
			lval = val + '?'
		else
			lval = '?filter=' + lfld.sourceid + ':' + val + '&'

		lform = form if getType(lform) isnt 'string'
		lkey = keys[0]
		lkeys = keys.slice(1)

		aj = Ajax.async
			# prefilling is not a real parameter, just sending it to make debugging easier
			url: '/form/' + lform + '/' + lval + 'prefilling=2'
		aj.done (data, status, xhr) =>
			data = data[0] if data and data.length > 0
			if data and data[lkey]?
				if lkeys.length > 0 and lkeys[0]
					pdfd = @parse_chained_lookup(lform, lkey, data[lkey], lkeys)
					pdfd.done (data2) ->
						ldfd.resolve(data2)
						return
				else
					ldfd.resolve(data[lkey])
			else
				ldfd.resolve()
			return
		aj.fail (xhr, status, data) ->
			ldfd.resolve()
			return

		ldfd

	parse_form: (root, form, data) ->
		filldata = {}
		chained = []

		pushdata = (ak, da, bk) ->
			return false if not da
			bk = ak if not bk
			av = da[bk]
			aw = da[bk + '_auto_name']
			if ((getType(av) is 'array') and (av.length is 0)) or
					((getType(av) is 'object') and _.isEmpty(av))
				false
			else if av
				filldata[ak] = av
				filldata[ak + '_auto_name'] = aw if aw
				true
			else
				false

		srcdata = (fln, kn) =>
			if @formdata[fln]?[kn]?
				# use global data first e.g. qol from any parent instead of current root's subform
				@formdata[fln]
			else if (fln is form) and data[kn]?
				data
			else
				false

		# fields.prefill
		for k,v of DSL[form].fields
			continue if v.model.type is 'subform'
			for fl in v.model.prefill
				fl = form if fl is 'self'
				if DSL[fl]?
					ds = srcdata(fl, k) # data source to use for filling data
					break if ds and pushdata(k, ds)
				else
					[fl1, k1, k2, k3] = fl.split('.')
					fl1 = form if fl1 is 'self'
					if fl1 is "parent"
						ds = srcdata(fl1, k1) # data source to use for filling data
						break if ds and pushdata(k, ds)
					if fl1 and k1 and k2
						if DSL[fl1]?.fields[k1]?
							ds = srcdata(fl1, k1)
							chained.push @parse_chained(root, form, k, fl1, k1, ds[k1], k2, k3) if ds?[k1]
					else if fl1
						k1 = k if not k1
						if k1 and DSL[fl1]?.fields[k1]?
							ds = srcdata(fl1, k1)
							break if pushdata(k, ds, k1)
						else
							log('Invalid prefill in form ' + form + ' for field ' + k + ': ' + fl)

		# sections.prefill
		for ks,v of DSL[form].model.sections
			continue if not v.prefill
			fl = if v.prefill is 'self' then form else v.prefill
			continue if not DSL[fl]?
			for k in v.fields
				continue if (not DSL[fl].fields[k]?) or (DSL[fl].fields[k].model.type is 'subform')
				ds = false # data source to use for filling data
				if (fl is form) and data[k]?
					ds = data
				else if @formdata[fl]? and @formdata[fl][k]?
					ds = @formdata[fl]
				pushdata(k, ds) if ds

		if not _.isEmpty(filldata)
			if root
				root = _.merge(root, filldata)
			else
				for k,v of filldata
					@filldata[k] = v

		chained

	parse_subforms: (form, parent_filldata)->
		parse_dfd = $.Deferred()
		# parse prefill-able fields/sections mentioned in form
		fd = if @formdata[form]? then @formdata[form] else {}
		chained = {}

		# parse first row of subforms of form for prefill-able fields/sections
		key_form_map = {}
		for k,fv of DSL[form].fields
			v = _.cloneDeep(fd[k])
			continue if not fv? # _auto_name etc. fields
			continue if fv.model.type isnt 'subform' or fv.model.multi # only inline subform
			continue if getType(fv.model.source) isnt 'string'
			if !parent_filldata[k]?
				parent_filldata[k] = {}
			if !v or !v?.length
				src = DSLRecordSubform.get_subform_source(form, k, parent_filldata)
				continue if not src
				[src, sfs] = src
				key_form_map[k] = src
				continue
			if v.length < 1
				src = DSLRecordSubform.get_subform_source(form, k, parent_filldata)
				if src
					v = [
						_meta:
							source: src[0]
					]
				else
					continue
			key_form_map[k] = v[0]._meta.source
			sf = @parse_form(parent_filldata[k], v[0]._meta.source, v[0])
			chained[k] = sf if not _.isEmpty(sf)

		cp = []
		for k,v of chained
			continue if v.length is 0
			for c in v
				cp.push c

		if cp.length > 0
			$.when.apply($, cp).done(=>
				drf = @read_subforms_childs(parent_filldata, key_form_map)
				drf.done =>
					parse_dfd.resolve()
				drf.fail (err) =>
					parse_dfd.reject(err)
			)
		else
			drf = @read_subforms_childs(parent_filldata, key_form_map)
			drf.done =>
				parse_dfd.resolve()
			drf.fail (err) =>
				parse_dfd.reject(err)
		return parse_dfd

	read_subforms_childs: (filldata, key_form_map={}) ->
		dfd = $.Deferred()
		pds = []
		for k,f of key_form_map
			if !filldata[k]?
				filldata[k] = {}
			fd = if @formdata[f]? then @formdata[f] else {}
			pd = @parse_form(filldata[k], f, fd)
			pds.push pd
		if pds.length > 0
			$.when.apply($, pds).done(=>
				cp = []
				for k,f of key_form_map
					cp.push @read_subforms(f, filldata[k])
				if cp.length > 0
					$.when.apply($, cp).done(=>
						dfd.resolve()
					).fail((err) =>
						dfd.reject(err)
						return
					)
				else
					dfd.resolve()
			).fail((err) =>
				dfd.reject(err)
				return
			)
		else
			dfd.resolve()
		dfd
		
	read: (rowdata = {}) ->
		# prefill if add/addfill
		do_prefill = @options.mode in ['add', 'addfill']
		# also prefill if edit and prefilled field exists in DSL and edit.prefilled isnt 'Y'
		if @options.mode in [DSL[@options.form].fields.force_prefill?.model?.default] and DSL[@options.form].fields.prefilled? and rowdata.prefilled?.toUpperCase() isnt 'Y'
			do_prefill = true
		# dont prefill if there is nothing to prefill
		if (not DSL[@options.form].model.prefill?) or _.isEmpty(DSL[@options.form].model.prefill)
			do_prefill = false
		# dont prefill if already prefilled
		if rowdata and rowdata.id and rowdata.updated_on
			do_prefill = false
		# force prefill if set in DSL
		if @options.mode in [DSL[@options.form].fields.force_prefill?.model?.default] and DSL[@options.form].fields.force_prefill?
			do_prefill = true

		# get links used for filtering forms
		links = if do_prefill then @get_links(rowdata) else false
		if do_prefill and not links and DSL[@options.form].model.force_prefill
			links = {}
		if not links and not @force_prefill_parent 
			@dfd.resolve({})
			return
		else
			@links = links
		include_self = true
		prefill_map = DSL[@options.form].model.prefill
		if !prefill_map or _.isEmpty(prefill_map)
			if !@force_prefill_parent
				@dfd.resolve({})
				return
			else
				include_self = false
		
		# ui update
		mode1 = if @options.mode in ['add', 'addfill'] then 'Creating ' else 'Loading '
		mode2 = if @options.mode is 'add' then 'new ' else 'prefilled '
		$.notify mode1 + mode2 + 'form...' if not @options.silent

		# prefill each form in model.prefill
		source = []
		for k,v of DSL[@options.form].model.prefill
			continue if not DSL[k].model.save
			source.push @read_form k, v, links

		# dynamically include self form if not in model.prefill
		if not DSL[@options.form].model.prefill[@options.form]? and include_self
			if DSL[@options.form].fields.patient_id?
				l = patient_id: 'patient_id'
			else
				l = false
			if l
				v =
					filter: {}
					link: l
					max: 'created_on'
					min: null
			if DSL[@options.form].model.save
				source.push @read_form @options.form, v, links

		if source.length > 0 or @force_prefill_parent
			$.when.apply($, source).done(=>
				@parse()
				return
			).fail((err) =>
				@dfd.reject(err)
				return
			)
		else
			@dfd.resolve({})

		return

	read_form: (form, prefill, links) ->
		return if not prefill?.link
		dfd = $.Deferred()

		# prefilling is not a real parameter, just sending it to make debugging easier
		params = ['limit=1', 'prefilling=1']
		form = @options.form if form is 'self'
		f = DSL[form].fields

		# prefill links
		for k,v of prefill.link
			params.push 'filter=' + k + ':' + links[v] if f[k]? and links[v]?

		# filters
		for k,v of prefill.filter
			if getType(v) is 'array'
				for av in v
					params.push 'filter=' + k + ':' + av
			else
				params.push 'filter=' + k + ':' + v if f[k]?

		# sort
		params.push 'sort=-' + prefill.max if $.trim(prefill.max) isnt ''
		params.push 'sort=' + prefill.min if $.trim(prefill.min) isnt ''
		params.push 'sort=-id' if ($.trim(prefill.min) is '') and ($.trim(prefill.max) is '')
		dfds = []
		aj = Ajax.async
			url: '/form/' + form + '/?' + params.join('&')
		aj.done (data, status, xhr) =>
			if data and data.length is 1
				@formdata[form] = data[0]
			else if form is @options.form
				# initialize dummy form data (with all subforms)
				@formdata[form] = {}
				for k,v of DSL[form].fields
					if v.model.type is 'subform'
						@formdata[form][k] = []
			dfd.resolve()
		aj.fail (xhr, status, data) =>
			if xhr?.responseJSON?.error?
				err = 'Sorry! Cannot create form ' + DSL[form].view.label + ' because:<br/><br/><div class="inset">' + xhr.responseJSON.error + '</div><br/>' + ERR_CONTACT
			else
				err = 'Sorry! Cannot create new ' + DSL[form].view.label + ' form at this time.<br/><br/>' + ERR_CONTACT
			dfd.reject(err)
		dfd

	read_subforms: (form, parent_filldata) ->
		source = []
		srdfd = $.Deferred()

		# determine which subforms still need to be read via API so they can be prefilled
		for k,v of DSL[form].fields
			continue if v.model.type isnt 'subform' or v.model.multi # only inline subform
			continue if getType(v.model.source) isnt 'string'
			src = DSLRecordSubform.get_subform_source(form, k, parent_filldata)
			continue if not src
			[src, sfs] = src
			continue if @formdata[src]?

			for kk,vv of DSL[src].model.prefill
				continue if @formdata[kk]?
				continue if not DSL[kk].model.save
				source.push @read_form kk, vv, @links

		if source.length > 0
			$.when.apply($, source).done(=>
				p_dfd = @parse_subforms(form, parent_filldata)
				p_dfd.done(=>
					srdfd.resolve()
				)
				p_dfd.fail((err) =>
					srdfd.reject(err)
				)
			).fail((err) =>
				srdfd.reject(err)
				return
			)
		else
			p_dfd = @parse_subforms(form, parent_filldata)
			p_dfd.done(=>
				srdfd.resolve()
			)
			p_dfd.fail((err) =>
				srdfd.reject(err)
			)
		srdfd
