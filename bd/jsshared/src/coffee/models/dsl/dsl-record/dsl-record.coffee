class DSLRecord extends CRModel

	###
		same @options as parent:
			form
			id
			el
			parent
			link
			links
			linkid
			record
			preset
			compare
			mode
			domcols
			menutitle
			# added by read()
			silent
			dfd
			# for template printing
			noif
			show_sections
			show_nested

		@dom[]
			group (label)
			note
			sections[]
				section (label)
				secid (form + section)
				id (sec_num)
				note
				form
				area
				fields
					html
					data

		@subforms{}
			recs
			recmap
			forms
			formmap
			field
			fieldrev
	###

	get_multi_subrecords: (fld) ->
		if not @subforms.field[fld]?
			return []
		sf = @subforms.field[fld]
		if not @subforms.recmap[sf]?.subform?.subrecords?
			return []
		@subforms.recmap[sf].subform.subrecords

	initialize: (options) ->
		@options = _.clone(options)
		@options.wrapper = @
		@dom = []
		@subforms =
			records:  [] # model
			recmap:   {} # model-map
			forms:    [] # view
			formmap:  {} # view-map
			field:    {}
			fieldrev: {}
			child: {}
		@prefill_formdata = {}
		return

	insert_field: (dd, seck, secv, fld, tg=null, allow_modal=false, parent_tab=null) ->
		v = DSL[dd.options.form].fields[fld]
		if v.model.type is 'subform'
			form = dd.get_subform(fld)
			return not v.model.required if not form # fail if model.required = true, else ok
			return true if not DSL[form]? # silently fail
			return false if form is @options.form # cannot insert self as subform

			if @subforms.fieldrev[form]? and fld isnt @subforms.fieldrev[form]
				console.log 'Cannot insert the same subform more than once per form.', form
				return true # silently fail when same subform is included by another field

			if @subforms.recmap[form]?
				return true if v.model.multi # cannot insert same multi-sub more than once per form
				sfrm = @subforms.recmap[form]
			else
				options =
					el: dd.options.el
					form: form
					parent: dd.options.parent
					record: null
					wrapper: @
					noif: if dd.options.noif? then dd.options.noif else false
				sfrm = @insert_subform options
				return false if not sfrm

				@subforms.field[fld] = form
				@subforms.fieldrev[form] = fld
				rowdata = {} # see set_record_data for usage
				prefill = {} #   of these
				preset  = {} #   four
				compare = {} #   objectsxw

				rowdata = @get_flattend('rowdata', form)
				compare = @get_flattend('compare', form)
				prefill = @get_flattend('prefill', form)
				if rowdata
					if v.model.multi # handle multiple rows
						rowdata = rowdata 
						compare = compare
					else if @options.mode is 'add'
						rowdata = if rowdata?[0]? then rowdata[0] else rowdata
					else if @options.mode is 'addfill'
						prefill =
							data: prefill
							mode: @options.mode
						rowdata = if rowdata?[0]? then rowdata[0] else rowdata
					else
						rowdata = if rowdata?[0]? then rowdata[0] else rowdata
						compare = if compare?[0]? then compare[0] else compare
				sfrm.set_record_data rowdata, prefill, preset, compare

			@push_group secv.group, tg
			if v.model.multi
				# use form label
				seck = DSL[form].view.label
				# insert the single header/field
				@push_section form, seck, secv, tg, parent_tab
				@push_field form, seck, fld, v
				return true if not @options.show_nested
				# insert the exploded form - needed for printing and other operations
				msecv = _.cloneDeep(secv)
				msecv.group.form = form
				msecv.group.label = seck
				msecv.group.multi = true
				@push_group msecv.group, tg
				@push_section form, seck, msecv, tg, parent_tab
				sfrm.explode_form(msecv.area or 'header', true, seck, tg, allow_modal, parent_tab, tg?.parent_tab_level)
			else
				sfrm.explode_form(secv.area or 'header', true, seck, tg, allow_modal, parent_tab, tg?.parent_tab_level)
		else
			lbl = v.view.label
			dyn = lbl.match(/\{(\w*)\}/gi)
			if dyn?.length > 0

				# Handle dynamic labels
				for tk in dyn
					tb = tk.replace('{', '').replace('}', '')
					if not DSL[dd.options.form].fields[tb]?
						log('Cannot find the field: ' + tb + ' for source: ' + src + '!')
					srcvl = false
					if (dd.preset?[tb]? and dd.preset[tb] isnt '')
						srcvl = dd.preset[tb]
					else
						vs = DSL[dd.options.form].fields[tb]
						if getType(vs.model.default) is 'string' and $.trim(vs.model.default).length > 0
							srcvl = vs.model.default
					if not srcvl
						if v.model.required
							log('Field: ' + tb + ' for source: ' + src + ' cannot be blank!')
					lbl = lbl.replace(tk, srcvl)
				v.view.label = lbl
			
			form = dd.options.form
			@push_group secv.group, tg
			@push_section form, seck, secv, tg, parent_tab
			@push_field form, seck, fld, v
		true

	insert_subform: (options) ->
		opt = _.clone(options)
		opt.link = @linkMap.link
		opt.links = @linkMap.links
		opt.linkid = @linkMap.linkid
		opt.me = @subforms.records.length
		nf = new DSLRecordSubform(opt)
		@subforms.records.push nf
		@subforms.recmap[options.form] = nf

	is_empty: (obj) ->
		return true if not obj? or obj.length is 0

		return false if obj.length? and obj.length > 0

		for key of obj
			return false if Object.prototype.hasOwnProperty.call(obj,key) 

		return true

	process_record: (rowdata = {}, prefill = {}) ->
		@linkMap = updateDSLLinkMap(@, rowdata)
		frm = @insert_subform(@options)
		frm.set_record_data rowdata, prefill, @options.preset, @options.compare
		allow_modal = @options?.parent?.parent?.options?.allowModal or false
		@subforms.child = @get_child_parent_map()
		if not frm.explode_form(false, true, false, null, allow_modal) # all other subforms would get loaded by this
			if @force_readonly
				@options.dfd.reject 'This form has not been filled in yet.'
			else
				err = 'There was an error trying to load this form.'
				if DSL[@options.form]?.fields?.careplan_id?
					err += '<br><br>'
					err += 'This is often caused by missing therapy/diagnosis in the Care Plan form. '
					err += 'If that is the case, please enter the correct values in the Treatment > Intake form and try again.'
				@options.dfd.reject err
			return false
		for frm in @subforms.records
			preset = @get_flattend('rowdata', frm.options.form)
			if (Array.isArray(frm.preset) and frm.preset?.length>0) && (Array.isArray(preset) && preset?.length>0)
				frm.preset=preset
			else if !Array.isArray(frm.preset) && (Array.isArray(preset) && preset?.length>0)
				frm.preset=preset[0]
			frm.show_hide_init()
		@options.dfd?.resolve @dom, @subforms
		return

	push_field: (form, sec, fld, v) ->
		return if not _.last(_.last(@dom)?.sections)?.fields?
		_.last(_.last(@dom).sections).fields.push
			form: form
			id: fld
			fieldgen:
				fld: fld
				v: v
				sec: sec
		return

	push_section_tab: (form, stabs) ->
		@dom.push
			group: ''
			note: ''
			form: form? and form
			multi: false
			tc: {}
			sections: []
			is_tabcontroller: true
		_.last(@dom).sections.push
			form: form
			section_tab: true
			secid: form + '__tg__' + stabs.tabgroup
			section: form + '__tg__' + stabs.tabgroup
			parent_tab: stabs.parent_tab
			id: window.next_id('sec')
			area: 'header'
			tabcontroller: stabs.tabgroup
			tc: stabs or {}
			fields: [{}]
		return

	push_group: (grp, tg=null) ->
		if not _.last(@dom)?.group? and not grp.label
			return @push_group
				label: @options.menutitle or DSL[@options.form].view.label
				note: ''
				tc: tg or {}
				form: grp.form? and grp.form
				hide_header: grp?.hide_header or false
				multi: grp.multi? and grp.multi
		return if (not grp.label)
		if _.last(@dom)?.group.toLowerCase() is grp.label.toLowerCase()
			if grp.multi? and grp.multi and (not _.last(@dom).group.multi)
				#HB-3484 - multi-subform DSL.view.label is same as parent section (group) label
				_.last(@dom).group = grp.label + ' Details'
			else
				return
		@dom.pop() if _.last(@dom)?.sections.length is 0
		@dom.push
			group: grp.label
			note: grp.note
			form: grp.form? and grp.form
			multi: grp.multi? and grp.multi
			hide_header: grp?.hide_header or false
			tc: tg or {}
			sections: []
		return

	push_section: (form, seck, secv, tg=null, parent_tab) ->
		return if not seck
		secid = form + '_' + seck
		_.last(@dom).sections.pop() if _.last(_.last(@dom)?.sections)?.fields.length is 0
		return if _.last(_.last(@dom)?.sections)?.secid.toLowerCase() is secid.toLowerCase()
		_.last(@dom).sections.push
			section: seck
			id: window.next_id('sec')
			hide_header: secv.hide_header or false
			indent: secv.indent? and secv.indent
			compact: secv.compact? and secv.compact
			secid: secid
			tc: tg or {}
			note: secv.note
			parent_tab: parent_tab
			form: form
			area: if secv.area? then secv.area else 'header'
			fields: []
		return

	read: (rowdata, silent = false) ->
		@options.dfd = $.Deferred()
		@options.silent = silent
		if (not silent) and (not Auth.is_dev()) and @options.parent?.ddf?.autorecover_get? and @options.parent?.ddf?.autorecover_enabled?() and (autorecover = @options.parent.ddf.autorecover_get())
			prettyYesNo 'Recover unsaved changes?', 'It appears that you made changes to this form but the browser was unexpectedly closed before your data could be saved.\n\n<b>Do you want to automatically recover the unsaved changes</b>?', =>
				if !autorecover?['id'] and @options?.record # data coming from cache don't contain id
					autorecover['id'] = parseInt(@options?.record)
				@process_record autorecover
				return
			, =>
				@options.parent?.ddf?.autorecover_clear?()
				@read_mode(rowdata)
				return
		else
			@read_mode(rowdata)

		@options.dfd

	read_edit: (rowdata) ->
		insertData = (data) =>
			rowdata = false
			if getType(data) is 'string'
				@options.dfd.reject data
			else if getType(data) is 'array' and data.length is 1 and getType(data[0]) is 'object'
				rowdata = data[0]
			else if getType(data) is 'object' 
				rowdata = data
			else
				@options.dfd.reject 'Invalid data!'
			ofd = @options?.parent?.parent?.override_field_data
			@options?.parent?.parent?.onServerActions?(rowdata?._meta?.actions or [], rowdata?._meta?.warning)
			if rowdata and ofd
				rowdata = _.merge(rowdata, ofd)
			@read_prefill rowdata if rowdata
			return
		if @is_empty(rowdata)
			$.notify 'Loading form data...' if not @options.silent
			aj = Ajax.async
				url: @record_url(true)
			aj.done (data, status, xhr) =>
				insertData(data)
			aj.fail (xhr, status, data) =>
				console.error("Action Request Failed: ", @record_url(true), "Opening form without server actions")
				toast({type:'error', position: "bottom-center", theme: "dark", message: 'Unable to fetch server actions buttons.', prodIgnore: true, autoClose: 2000})
				rty = Ajax.async
					url: @record_url(false)
				rty.done (data, status, xhr) =>
					insertData(data)
				rty.fail (xhr, status, data) =>
					@options.dfd.reject data
					return
				return
		else
			insertData(rowdata)
		return

	read_mode: (rowdata) ->
		if @options.mode in ['add', 'addfill']
			@read_prefill({})
		else
			@read_edit(rowdata)
		return

	read_prefill: (rowdata = {}) ->
		drp = new DSLRecordPrefill(@options)
		drp.dfd.done (prefill) =>
			@prefill_formdata = prefill?.formdata or {}
			@process_record rowdata, prefill
			return
		drp.dfd.fail (err) =>
			@options.dfd.reject err
			return
		drp.read(rowdata)
		return

	record_url: (actions = true) ->
		'/form/' + @options.form + '/' + @options.record + if actions then '?get_actions=true' else ''

	root: ->
		@subforms.records[0]
