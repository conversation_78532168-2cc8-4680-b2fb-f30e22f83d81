class DSLRecordSubform extends CRModel

	###
		root will have same @options as parent, sub will have * marked:
			form             *
			id
			el               *
			parent           *
			link
			links
			linkid
			record           *
			preset
			compare
			mode
			domcols
			menutitle
			wrapper          *
			# added by wrapper.read()
			silent
			dfd
			# for template printing
			noif             *
			# added by wrapper.insert_subform()
			show_sections        
			me               *

		shmap{}
			fields{}
				show_by{}
				hide_by{}
				current: true
			sections{}
				show_by{}
				hide_by{}
				current: true

		subform{}
			subrecords[]   - DSLRecordSubform for multi-row subforms
			if_fields[]    - show/hide fields on init
			if_fieldmap{}  - map
			field_source{} - original DSL source for subform
			if_subarea{}   - used to show/hide sections with areas from subforms
	###

	explode_form: (area = false, insert = true, seck = false, ptc = null, allow_modal=false, parent_tab=null, parent_tab_level=0) ->
		tabsgroups = {}
		stg = {}
		if insert and !allow_modal
			last_has_tab = -1
			first = ""
			first_tab = ""
			last_tab = ""
			tgid = ""
			tabs = {}
			tab_toggles = {}

			push_tab_toggle = (k, v) =>
				if not tab_toggles[k]?
					tab_toggles[k] = []
				tab_toggles[k].push v

			push_tab_group = () =>
				return if _.isEmpty(tabs) or tabsgroups[tgid]
				tabsgroups[tgid] =
					first: first	
					tabs: tabs
					first_tab: first_tab
					last_tab: last_tab
					tabgroup: tgid
					parent_tab_level: parent_tab_level + 1
					tab_toggles: tab_toggles
					parent_tabgroup: ptc.tabgroup if ptc
					parent_tab: parent_tab
					last: _.last(_.last(Object.values(tabs)))
				first = ""
				tabs = {}
				tab_toggles = {}

			for k in DSL[@options.form].model.sections_order
				v = DSL[@options.form].model.sections[k]
				if v.modal and v.fields.length > 0
					continue
				has_tab = if v.tab then 1 else 0
				if has_tab isnt last_has_tab and last_has_tab isnt -1
					push_tab_group()
					first_tab = ""
					last_tab = ""
				if has_tab
					can_push = true
					if ((@options.mode is 'add') or (@options.mode is 'addfill')) and Auth.can_create_any(@options.form)
						if not (Auth.section_can_read(@options.form, k) or Auth.section_can_create(@options.form, k))
							can_push = false
					else if (@options.mode is 'edit') and Auth.can_update_any(@options.form)
						if not (Auth.section_can_read(@options.form, k) or Auth.section_can_update(@options.form, k))
							can_push = false
					else
						if not Auth.section_can_read(@options.form, k)
							can_push = false
					if _.isEmpty(tabs)
						first = k
						first_tab = v.tab
						tgid = window.next_id('tabgroup')
					if not tabs[v.tab]? and can_push
						tabs[v.tab] = []
						last_tab = v.tab
					if can_push
						push_tab_toggle(v.tab, k) if v.tab_toggle
						tabs[v.tab].push k

					stg[k] = tgid
				last_has_tab = has_tab
			push_tab_group()
	
		for k in DSL[@options.form].model.sections_order
			v = DSL[@options.form].model.sections[k]
			if v.modal 
				if not allow_modal or v.fields.length == 0
					continue
			tg = null 
			if stg[k] and tabsgroups[stg[k]]?
				tg = _.cloneDeep(tabsgroups[stg[k]])
				if tg.first is k
					@options.wrapper.push_section_tab(@options.form, tg) if tg
			continue if area and (v.area or 'header') isnt area
			for kk in v.fields
				if ((@options.mode is 'add') or (@options.mode is 'addfill')) and Auth.can_create_any(@options.form)
					continue if not (Auth.field_can_read(@options.form, kk, @preset) or Auth.field_can_create(@options.form, kk, @preset))
				else if (@options.mode is 'edit') and Auth.can_update_any(@options.form)
					continue if not (Auth.field_can_read(@options.form, kk, @preset) or Auth.field_can_update(@options.form, kk, @preset))
				else
					continue if not Auth.field_can_read(@options.form, kk, @preset)
				vv = DSL[@options.form].fields[kk]
				continue if not vv.model.active
				if (vv.model.type is 'subform') and (not vv.model.multi)
					@subform.if_tabcontroller[k] = (ptc or tg) if not @subform.if_tabcontroller[k]?
				if insert and (not @options.wrapper.insert_field @, k, v, kk, tg or ptc, allow_modal, (v.tab or parent_tab), (tg?.parent_tab_level or parent_tab_level))
					return false

				if v.area and (vv.model.type is 'subform') and (not vv.model.multi)
					@subform.if_subarea[k] = {} if not @subform.if_subarea[k]?
					@subform.if_subarea[k][kk]= [] if not @subform.if_subarea[k][kk]?
					@subform.if_subarea[k][kk].push v.area

				# create list of fields with if: logic to show/hide in subforms or this form
				if (vv.model.type is 'subform' and vv.model.multi)
					for sr in @options.wrapper.get_multi_subrecords(kk)
						sr.explode_form area, false, seck, tg or ptc, allow_modal, (v.tab or parent_tab), (tg?.parent_tab_level or parent_tab_level)
				else if @options.noif
					continue
				else if not _.isEmpty(vv.model.if)
					continue if @subform.if_fieldmap[kk]?
					@subform.if_fields.push kk
					@subform.if_fieldmap[kk] = 1
		true

	get_record_data: (k = null) ->
		if k
			@preset[k]
		else
			@preset

	get_subform: (k) ->
		src = DSLRecordSubform.get_subform_source(@options.form, k, @preset)
		if src
			[src, sfs] = src
			for k,v of sfs
				@subform.field_source[k] = v
		src

	@get_subform_source: (form, k, preset) ->
		# select the right subform based on dynamic field values
		v = DSL[form].fields[k]
		src = v.model.source
		sfs = {} # subform-field-source
		dyn = src.match(/\{(\w*)\}/gi)
		if dyn?.length > 0
			for tk in dyn
				tb = tk.replace('{', '').replace('}', '')
				if not DSL[form].fields[tb]?
					log('Cannot find the field: ' + tb + ' for source: ' + src + '!')
					return false
				srctb = false
				if (preset?[tb]? and preset[tb] isnt '')
					srctb = preset[tb]
				else
					vs = DSL[form].fields[tb]
					if getType(vs.model.default) is 'string' and $.trim(vs.model.default).length > 0
						srctb = vs.model.default
				if not srctb
					if v.model.required
						log('Field: ' + tb + ' for source: ' + src + ' cannot be blank!')
					return false
				src = src.replace(tk, srctb)
				sfs[tb] = srctb
			if not v.model.sourcefilter[src.toLowerCase()]?
				if window.DSL[src.toLowerCase()]
					v.model.sourcefilter[src.toLowerCase()] = {}
				else
					log('Invalid source table: ' + src.toLowerCase() + ' for field: ' + k)
					return false
		[src.toLowerCase(), sfs]

	@if_keys: (v,fv) ->
		mif = v.model.if
		# sort if: logic keys so that null & wildcard are checked last
		# moves selected `if` to the end of execution order.
		mifk = Object.keys(mif)
		if mifk.includes(fv) and mifk.length > 1 and !mifk[fv]?.sections?.length
			mifk.push(mifk.splice(mifk.indexOf(fv), 1)[0]);
		sif = mifk.sort((x,y) ->
			if x is '*'
				1
			else if y is '*'
				-1
			else if x is '!'
				-1
			else if y is '!'
				1
			else
				x > y
		)
		[mif, sif]

	@handle_if: (sr, form, k, d = null, sm = true) ->
		return if sr.options.noif
		v = form.fields[k]
		return if v.view.control in ['grid', 'inline'] # ignore grid & subforms

		fn = v.view.note

		srcdata = (sr, fln, kn) =>
			pt = if sr.options.mode == "read" then "ddr" else "ddf"
			if fln == sr.options.form
				data = sr?.values?()
				if !data
					data = sr?.preset 
				if data?[kn]?
					return data[kn]
			formdata = sr.options.parent[pt].dr.prefill_formdata
			if formdata[fln]?[kn]?
				formdata[fln][kn]
			else
				""
		if d # dom-dependent
			fv = sr.field_node_value(v, d)
			fva = null
		else if sr.preset? and sr.preset[k]? and sr.preset[k] # dom-free & preset
			fv = sr.preset[k]
			fva = sr.preset[k + '_auto_name'] or null
		else # dom-free & default
			fv = v.model.default or (if v.model.multi then [] else '')
			fva = null

		recur =
			fields: {}
			sections: {}

		[mif, sif] = @if_keys(v,fv)

		# get current visiblity status
		if sm and sr.shmap.fields[k]?
			sf = sr.shmap.fields[k]
			sm = not _.isEmpty(sf.show_by) or _.isEmpty(sf.hide_by)

		rangematched = false
		for fc in sif
			fl = mif[fc]
			if rangematched
				rangematch = false
			else if sm # show me i.e. is current visible
				rangematch = DSLRecordSubform.in_range(v, fc, fv, d, fva)
				# support dictionary lookups by key & value
				if (not rangematch) and (getType(v.model.source) is 'object') and v.model.source[fc]?
					rangematch = DSLRecordSubform.in_range(v, v.model.source[fc], fv, d, fva)
				# only match the first true condition if not multi-value field
				rangematched = true if rangematch and not v.model.multi
			else
				rangematch = false

			fn = fl.note if rangematch and fl.note isnt ''
			rck = k + '_' + fc
			for ff in fl.fields
				if DSLRecordSubform.show_hide_mark_field(sr, ff, rck, rangematch)
					recur.fields[ff] = rangematch
			for fs in fl.sections
				if DSLRecordSubform.show_hide_mark_section(sr, fs, rck, rangematch)
					recur.sections[fs] = rangematch
			if rangematch and sr.field_nodes and fl.trigger and fl.trigger.length > 0
				for tfx in fl.trigger
					continue if not sr?.field_nodes?[tfx]
					sr.value_field(tfx, sr.value_field_get(tfx, true), true, true, true)
			if rangematch and fl.prefill and sr.field_nodes
				for pk, pvr of fl.prefill
					continue if not sr.field_nodes[pk]
					if typeof pvr is 'string' and pvr.startsWith("{{") and pvr.endsWith("}}")
						tfcv = sr.value_field_get(pk, true)
						if !tfcv or !areEqual(tfcv, sr.options?.preset?[k])
							tft = window.DSL[sr.options.form].fields[pk].model.type
							tfv = DSLDraw.parse_template_field(pvr, tft)
							sr.value_field pk, tfv, true, true, true
						continue
					if !Array.isArray(pvr)
						pvr = [pvr]
					valf = []
					for pv in pvr
						if pv and pv.includes(".")
							[fl1, k1, k2, k3] = pv.split('.')
							fl1 = sr.options.form if fl1 is 'self'
							if fl1 and k1
								if DSL[fl1]?.fields[k1]?
									pv = srcdata(sr, fl1, k1)
								if pv
									valf.push(pv)
						else
							if typeof pv is 'string' and pv.startsWith("{") and pv.endsWith("}")
								pvrm = pv.replace("{", "").replace("}", "")
								valf.push(srcdata(sr, sr.options.form, pvrm))
							else
								valf.push(pv)
					vset = ''
					valf = valf.filter(Boolean)
					if DSL[sr.options.form]?.fields[pk]?.model?.multi
						vset = valf
					else
						if valf.length == 1
							vset = valf[0]
						else if valf.length > 1
							vset = valf.join(" ")
					sr.value_field pk, vset, true, true, true
			if rangematch and fl.field_label and sr.field_nodes
				for pk, pvr of fl.field_label
					continue if not sr.field_nodes[pk]
					fnrx = sr.field_nodes[pk]
					lbl_over = ''
					if pvr == 'default'
						lbl_over = fnrx.data('v').view.label 
					else
						lbl_over = pvr
					fnrx.closest('.form-group').find('.control-label>span').html(lbl_over)
			if fl.require_fields? and sr.field_map?
				for fr in fl.require_fields
					markFieldRequiredIf(sr, fr, k, rangematch)
					fc = if sr.field_map[fr]? then sr.field_map[fr] else false
					for fd in fc
						el = $(fd.closest('.form-group')[0].getElementsByClassName('help-block')[0])
						ell = $(fd.closest('.form-group').find('label.control-label'))
						if rangematch
							if ell.find('.required-label').length is 0
								if ell.find('.audit-trail') and !(sr.options?.mode is 'add' or sr.options?.mode is 'addfill')
									ell.find('.audit-trail').before('<div class="required-label">*</div>')
								else
									ell.append('<div class="required-label">*</div>')
							el.html('<span>' + DSL[sr.options.form].fields[fr].view.note + '<span>')
						else
							ell.find('.required-label').remove()
							el.html('<span>' + DSL[sr.options.form].fields[fr].view.note + '<span>')

			if fl.readonly?.fields? and sr.field_map?
				for fro in fl.readonly.fields
					dd = sr?.options?.wrapper?.subforms?.formmap?[sr.options.form]
					if sr.field_map[fro]
						if dd
							for fa in sr.field_map[fro]
								fieldReadOnly(dd, fa, rangematch, k + '_' + fc)
					else
						dd = sr?.options?.wrapper?.subforms?.formmap?[sr.options.form]
						continue if !dd
						secName = null
						continue if form.fields[fro].model.type != 'subform'
						continue if form.fields[fro].model.multi
						for sec in form.model.sections_order
							if form.model.sections[sec].fields.includes(fro)
								secName = sec
								break
						continue if !secName
						sectionReadOnly(dd, secName, rangematch, k + '_' + fc)							

			dd = sr?.options?.wrapper?.subforms?.formmap?[sr.options.form]
			if fl.readonly?.sections? and sr.field_map? and dd
				for fro in fl.readonly.sections
					sectionReadOnly(dd, fro, rangematch, k + '_' + fc)

			if fl.highlight? and sr.field_map?
				for fr in fl.fields
					for fa in sr.field_map[fr]
						if rangematch
							e = fa.closest('.form-group')
							e.addClass('highlight')
							e.css('background-color', fl.highlight)
						else
							e = fa.closest('.form-group')
							e.removeClass('highlight')
							e.css('background-color', '')

		for ff,ffv of recur.fields
			if d
				if not sr.field_map[ff]?
					vsf = DSL[sr.options.form]?.fields?[ff]
					if vsf and vsf?.model?.type is 'subform'
						prefillSubFormIf(sr.options.wrapper?.subforms?.formmap, sr.options.form, ff, vsf, ffv)
						if !vsf.model?.multi
							if DSL[vsf.model.source]
								sections = []
								if ffv
									sections = DSL[vsf.model.source].model.sections_order
								subformSectionShowOnly(sr.options.wrapper?.subforms?.recmap, sr.options.wrapper?.subforms?.formmap, vsf?.model?.source, sections)
					continue
				for fd in sr.field_map[ff]
					DSLRecordSubform.handle_if sr, form, ff, fd, ffv
			else
				DSLRecordSubform.handle_if sr, form, ff, null, ffv

		for fs,fsv of recur.sections
			for ff in DSL[sr.options.form].model.sections[fs].fields
				if d
					continue if not sr.field_map[ff]?
					for fd in sr.field_map[ff]
						continue if fd.data('section') isnt fs
						DSLRecordSubform.handle_if sr, form, ff, fd, fsv
				else
					DSLRecordSubform.handle_if sr, form, ff, null, fsv

		d.data('group').find('.note span').html(formatNote(fn)) if d
		return

	initialize: (options) ->
		@options = options
		@preset = {}       # field key default
		@compare = {}      # compare old data
		@prefill = {}      # prefilled keys
		@shmap =           # show-hide field/section dictionary
			fields: {}
			sections: {}
		@required_if_map = {}
		@readonly_map = {}
		@subform =
			subrecords:   [] # for multi-row subforms
			if_fields:    [] # run the handle_if / showhide for these fields
			if_fieldmap:  {} # map for handle_if fields
			field_source: {} # field key => val for determining subform source {string}
			if_subarea:   {} # used to show/hide sections with areas from subforms
			if_tabcontroller: {}
		return

	@in_range: (f, r, v, d = null, va = null) ->

		parseType = (vl) ->
			if f.model.type is 'int'
				if getType(f.model.source) isnt 'string'
					parseInt(vl)
				else if d and d.select2?('val')? and d.select2?('data')?.text? and d.select2('val') is vl
					# dom-dependent code
					$.trim(d.select2('data').text).toLowerCase()
				else if va
					# dom-free code
					$.trim(va).toLowerCase()
				else
					$.trim(vl).toLowerCase()
			else if f.model.type is 'decimal'
				parseFloat(vl)
			else
				if getType(vl) is 'string' then vl.toLowerCase() else vl

		# handle match-any: wildcard
		if r is '*'
			return if f.model.multi or (getType(v) in ['array', 'object']) then not _.isEmpty(v) else !!v

		# handle match-none: null
		if r is '!'
			return if f.model.multi or (getType(v) in ['array', 'object']) then _.isEmpty(v) else !v

		# handle multi select dropdowns and checkboxes
		if f.model.multi
			if d # dom-dependent
				# handle multi select dropdowns with data from other tables
				if (getType(f.model.source) is 'string') and (f.view.control is 'select')
					for dt in v
						if r is dt.text or r is dt.id
							return true
					return false
				# handle multi select with data from source array/object
				if (getType(v) is 'array') and (f.view.control is 'select')
					for dt in v
						if r is dt
							return true
					return false
				# handle multi select checkboxes
				if (f.view.control is 'checkbox')
					for dt in v
						if r is dt
							return true
					return false
			else # dom-free
				if getType(v) is 'array'
					for dt in v
						if r is dt
							return true
				if va and getType(va) is 'array'
					for dt in va
						if r is dt
							return true
			return false
		else if (f.view.control is 'checkbox') # handle single select checkboxes
			if d # dom-dependent
				for dt in v
					if r is dt
						return true
			else # dom-free
				if getType(v) is 'array'
					for dt in v
						if r is dt
							return true
				if va and getType(va) is 'array'
					for dt in va
						if r is dt
							return true

		v = parseType(v)
		sides = r.split(/\.\.|\.\<|\>\.|\>\</)
		if sides.length is 1
			if ~sides[0].indexOf('!')
				return v isnt parseType(sides[0].replace('!', '')) # '!12'
			if ~sides[0].indexOf('>')
				return v > parseType(sides[0].replace('>', '')) # '>12' or '12>'
			if ~sides[0].indexOf('<')
				return v < parseType(sides[0].replace('<', '')) # '<14' or '14<'
			return true if v is parseType(r)
		else if sides.length is 2
			if sides[1] is '' and ~r.indexOf('..') # '12..'
				return v >= parseType(sides[0])
			if sides[0] is '' and ~r.indexOf('..') # '..14'
				return v <= parseType(sides[1])
			if sides[0] isnt '' and sides[1] isnt '' # 'x??y'
				if ~r.indexOf('..') # '12..14'
					return (v >= parseType(sides[0])) and (v <= parseType(sides[1]))
				if ~r.indexOf('>.') # '12>.14'
					return (v > parseType(sides[0])) and (v <= parseType(sides[1]))
				if ~r.indexOf('.<') # '12.<14'
					return (v >= parseType(sides[0])) and (v < parseType(sides[1]))
				if ~r.indexOf('><') # '12><14'
					return (v > parseType(sides[0])) and (v < parseType(sides[1]))
		false

	@pending_status_created_by_others: (d) ->
		d.change_type? and d.change_by? and d.change_type is 'create' and d.change_by isnt App.user.id

	@pending_status_change: (d) ->
		d?.change_data? and getType(d.change_data) is 'object' and not _.isEmpty(d.change_data) and d.change_by is App.user.id

	set_record_data: (rowdata, prefill, preset, compare) ->
		# rowdata - existing data from db
		# prefill - backend prefill api
		# preset  - initial values generated by front-end
		# compare - compare to old data in same row

		
		if prefill?.data? and prefill?.mode?
			if prefill.mode is 'addfill'
				prefill = prefill.data
			else # only prefill read-only fields for add/edit modes
				force_prefill = prefill.mode in [DSL[@options.form].fields.force_prefill?.model?.default] and DSL[@options.form].fields.force_prefill?
				fil = {}
				for k,v of prefill.data
					if DSL[@options.form].fields[k]?.view.readonly # read-only fields
						fil[k] = v
						# force prefill only if saved data is null
						delete rowdata[k] if force_prefill and (k of rowdata) and getType(rowdata[k]) is 'null'
					else if DSL[@options.form].fields[k]?.model.type is 'subform' # nest subform
						fil[k] =
							data: v
							mode: prefill.mode
					else if DSL[@options.form].fields.force_prefill?.model?.default == 'add'
						fil[k] = v
				prefill = fil
		linkMapPreset = getPresetFromLinkIds(@options.linkid) or {}
		@prefill = prefill
		@preset = _.defaults(rowdata, preset)
		@preset = _.defaults(@preset, prefill)
		if DSLRecordSubform.pending_status_change(@preset)
			@preset = _.defaults(@preset.change_data, @preset)
		@compare = compare
		if getType(@preset) is 'object'
			@preset = mergeIfFalsy(@preset, linkMapPreset)

		# load multiple rows for subforms
		if getType(@preset) is 'array'
			for v in @preset
				v = mergeIfFalsy(v, linkMapPreset)
				sr = new DSLRecordSubform(@options)
				sr.set_record_data v, {}, {}, {}
				@subform.subrecords.push sr
		return

	@show_hide: (sr, toggle = true, force = false) ->
		for k,v of sr.shmap.fields
			nc = not _.isEmpty(v.show_by) or _.isEmpty(v.hide_by)
			if toggle and (force or nc isnt v.current)
				sr.toggle_field k, nc
			else if DSL[sr.options.form].fields[k]?.model.type is 'subform'
				w = sr.options?.wrapper
				form = w?.subforms?.field[k]
				w.subforms.recmap[form].subform.hidden_by_root = not nc if form
			if sr.shmap.fields[k].hide_by['_force']?
				nc = false
			sr.shmap.fields[k].current = nc
		for k,v of sr.shmap.sections
			nc = not _.isEmpty(v.show_by) or _.isEmpty(v.hide_by)
			pc = v.current
			if sr.shmap.sections[k].hide_by['_force']?
				nc = false
			sr.shmap.sections[k].current = nc
			if toggle and (force or nc isnt pc)
				sr.toggle_section k, nc
		return

	show_hide_init: ->
		if @subform.subrecords.length
			for sr in @subform.subrecords
				sr.show_hide_init()
		else
			for f in @subform.if_fields
				# only handle fields that do not depend on if: logic of other fields
				if not @shmap?.fields[f]?
					DSLRecordSubform.handle_if @, DSL[@options.form], f
			DSLRecordSubform.show_hide @, false, false
		return

	@show_hide_mark_field: (sr, ff, k, showhide) ->
		chg = false
		if not sr.shmap.fields[ff]?
			sr.shmap.fields[ff] =
				hide_by: {}
				show_by: {}
				current: true
			chg = true
		if showhide
			if sr.shmap.fields[ff].hide_by[k]?
				delete sr.shmap.fields[ff].hide_by[k]
				chg = true
			else if not sr.shmap.fields[ff].show_by[k]?
				chg = true
			if k isnt "_force"
				sr.shmap.fields[ff].show_by[k] = 1
			if k is "_force"
				delete sr.shmap.fields[ff].hide_by[k]
				sr.shmap.fields[ff].current = not _.isEmpty(sr.shmap.fields[ff].show_by) or _.isEmpty(sr.shmap.fields[ff].hide_by)
		else
			if sr.shmap.fields[ff].show_by[k]?
				delete sr.shmap.fields[ff].show_by[k]
				chg = true
			else if not sr.shmap.fields[ff].hide_by[k]?
				chg = true
			sr.shmap.fields[ff].hide_by[k] = 1
		if sr.shmap.fields[ff].hide_by['_force']?
			sr.shmap.fields[ff].current = false
		chg

	@show_hide_mark_areas: (sr, areas, k, showhide) ->
		for sk in DSL[sr.options.form].model.sections_order
			sv = DSL[sr.options.form].model.sections[sk]
			continue if not areas.includes sv?.area
			DSLRecordSubform.show_hide_mark_section(sr, sk, k, showhide)
		return


	@show_hide_mark_section: (sr, fs, k, showhide) ->
		chg = false
		if not sr.shmap.sections[fs]?
			sr.shmap.sections[fs] =
				hide_by: {}
				show_by: {}
				current: true
			chg = true
		if showhide
			if sr.shmap.sections[fs].hide_by[k]?
				delete sr.shmap.sections[fs].hide_by[k]
				chg = true
			else if not sr.shmap.sections[fs].show_by[k]?
				return if ((Object.keys(sr.shmap.sections[fs].hide_by).length > 0 or Object.keys(sr.shmap.sections[fs].show_by).length > 0) and k.startsWith('root'))
				chg = true
			if sr.shmap.sections[fs].show_by[k]?
				sr.shmap.sections[fs].hide_by[k] = {} if sr.shmap.sections[fs].hide_by[k]
			if k isnt "_force"
				sr.shmap.sections[fs].show_by[k] = 1
			if k is "_force"
				delete sr.shmap.sections[fs].hide_by[k]
				sr.shmap.sections[fs].current = not _.isEmpty(sr.shmap.sections[fs].show_by) or _.isEmpty(sr.shmap.sections[fs].hide_by)
		else
			if sr.shmap.sections[fs].show_by[k]?
				delete sr.shmap.sections[fs].show_by[k]
				chg = true
			else if not sr.shmap.sections[fs].hide_by[k]?
				chg = true
			if sr.shmap.sections[fs].hide_by[k]?
				sr.shmap.sections[fs].show_by[k] = {} if sr.shmap.sections[fs].show_by[k]

			if not ((Object.keys(sr.shmap.sections[fs].hide_by).length > 0 or Object.keys(sr.shmap.sections[fs].show_by).length > 0) and k.startsWith('root'))
				sr.shmap.sections[fs].hide_by[k] = 1
		if sr.shmap.sections[fs].hide_by['_force']?
			sr.shmap.sections[fs].current = false
	
		if sr.subform.if_subarea?[fs]? and sr.options?.wrapper?.subforms?.formmap?
			sa = sr.subform.if_subarea[fs]
			ss = sr.options.wrapper.subforms
			for k,v of sa
				continue if v.length is 0
				continue if not ss.field[k]?
				ds = ss.formmap[ss.field[k]]
				continue if not ds?.drs?
				DSLRecordSubform.show_hide_mark_areas(ds.drs, v, 'root.' + fs + '.' + k, showhide)
				DSLRecordSubform.show_hide ds, true, true

		chg
