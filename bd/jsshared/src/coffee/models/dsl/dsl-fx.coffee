DSLFx =
	ValidateTransformSubForm: (cfv, dds) -> # cardview, drawsubform
		form = dds.get_parent_form() or cfv.form
		k = _.first(Object.keys(dds.field_map))
		return if not k
		dd = dds.options.wrapper.subforms.formmap[form]
		v = DSL[form].fields[k]
		return if not v
		f = dds.field_nodes[k]
		if v.model.transform.length > 0
			DSLFx.TransformField(DSL[form], dd, v.model.transform, f, k)
		if v.view.transform.length > 0
			DSLFx.TransformField(DSL[form], dd, v.view.transform, f, k)
		err = DSLFx.ValidateFieldRules(form, dd, v, f, k, true)
		return err

	TransformField: (form, dd, trns, f, k) ->
		for trn in trns
			if DSLFx.Transforms[trn.name]?
				DSLFx.Transforms[trn.name](form, dd, trn, f, k)
		return

	TransformForm: (form, dd) ->
		for trn in form.model.transform.concat(form.view.transform)
			if DSLFx.Transforms[trn.name]?
				DSLFx.Transforms[trn.name](form, dd, trn)

	TransformRequiredIf: (form, val) ->
		# do not send blank values for required fields if the toggle is not Yes
		# also see ValidateRequired
		if DSL[form]?.model?.required_if? and val[DSL[form].model.required_if] isnt 'Yes'
			if DSL[form].model.required_if_fields?
				for k in DSL[form].model.required_if_fields
					delete val[k] if not val[k]
		val


	Transforms:
		MomentPrefill: (form, dd, trn, f, k) ->
			cv = dd.value_field(k)
			return if cv
			fm = trn.format || 'MM/DD/YYYY'
			dd.value_field(k, moment().format(fm))

		BuildAddressLabel: (form, dd, trn, f, k) ->
			st1 = dd.value_field(trn.fields['street']) || ''
			st2 = dd.value_field(trn.fields['street2']) || ''
			cty = dd.value_field(trn.fields['city']) || ''
			st = dd.value_field(trn.fields['state']) || ''
			zp = dd.value_field(trn.fields['zip']) || ''
			
			addressLabel = st1
			addressLabel += "\n#{st2}" if st2
			addressLabel += "\n #{cty}, #{st} #{zp}"
			
			dd.value_field(trn.dest_field, addressLabel.trim())

		MergeFields: (form, dd, trn, f, k) ->
			# WARNING: Don't merge JSON or Array fields here.
			if trn.avoid_overwrite and trn.dest_field
				if dd.value_field(trn.dest_field)
					return
			merged_vals = _.map(trn.src_fields, (fk) -> DSLFields.value(dd.field_nodes[fk])?.toString()).filter((v) -> v).join(trn.separator || '|')
			DSLFields.value(dd.field_nodes[trn.dest_field], merged_vals)
			return

		UpdateFormLabel: (form, dd, trn, f, k) ->
			return if dd.options.form != dd.options.wrapper.options.form
			cond = trn.if
			return if !dd
			v = dd.value_field(k)
			label = form.view.label
			if !v and cond['!']
				label = cond['!']
			else if v and cond['*']
				label = cond['*']
			else if v and cond[v]
				label = cond[v]
			if dd.options.parent?.parent?.isFlyout
				dd.options.parent?.parent?.$el?.parent()?.parent().parent().find('.form-flyout-header-bar').find('span:not(.close-flyout)').html(label)
			header = dd.options.parent.$el.find('.cardarea').children().first()
			if header.is('h3')
				header.html(label)
			try
				title_cont = dd.options.parent.$el.parent().parent().parent().siblings('.window-manager-header').find('.window-manager-header-title')
				text_ext = title_cont.text()
				if text_ext.startsWith('New')
					title_cont.html(joinValid(['New', label], ' '))
				else
					title_cont.html(label)
			catch e
				# do nothing
				return
			return

		CopyData: (form, dd, trn, f, k) ->

			TruncateStringField = (d) ->
				v = d.data('v')
				dv1 = d.val()
				dv2 = $.trim(dv1)
				if getType(dv1) in ['string'] and getType(dv2) in ['string']
					d.val(dv2) if dv1 isnt dv2
					if v.model.max
						d.val(d.val().substring(0, v.model.max))

			id_field = trn.id_field
			id = dd.value_field(trn.id_field)
			if !(id)
				id = dd.drs.preset[id_field]
			source = trn.source
			fields = trn.copy
			if(!source && fields)
				# If Source is not defined, then copy data from within form and map it to destination fields.
				data = dd?.drs?.options?.parent?.ddf?.get_formdata()
				for own srcField, destField of fields
					dd.value_field destField, "", true, true
					dd.value_field(destField, data[srcField], true, true)
					# Truncating String Fields if max length is defined.
					TruncateStringField(dd.field_nodes[destField])
			return if !(id_field) or !(id) or !(source) or !(fields)
			return_modes = trn.return_modes
			return if return_modes and return_modes.includes(@mode)
			dfd = $.Deferred()
			tc = Ajax.async
				url: '/form/' + source + '/' + id
			tc.done (data, status, xhr) ->
				data = _.head(data)
				# if list of array is provided instead of {source: destination} mappings in form of object
				# then convert is to map on same name of fields for uniformity
				if _.isArray(fields)
					objFields = {}
					for k in fields
						objFields[k] = k
					fields = objFields
				for own srcField, destField of fields
					dd.value_field destField, "", true, true
					dd.value_field(destField, data[srcField], true, true)
				dfd.resolve(data)
			tc.fail (xhr, status, data) ->
				dfd.reject 'Unable to Copy data. Please try again.'
			dfd

		FetchScheduleDates: (form, dd, trn, f, k) ->
			pat_id = dd.value_field('patient_id')
			dates_taken = new Set()
			flt =
				patient_id: pat_id
			enc_url = '/form/encounter/?' + DSLDraw.default_params(true, MAX_DATA_ROWS) + '&' + joinFilters(flt)
			flt['active'] = 'Yes'
			sch_url = '/form/patient_schedule_encounter/?' + DSLDraw.default_params(true, MAX_DATA_ROWS) + '&' + joinFilters(flt)
			encs = Ajax.sync
				url: enc_url
			shc_encs = Ajax.sync
				url: sch_url
			if(!Array.isArray(encs))
				encs = [encs]
			if(!Array.isArray(shc_encs))
				shc_encs = [shc_encs]
			events = []
			dates = new Set()
			encs.forEach((enc) ->
				if enc?.encounter_schedule_date
					dates_taken.add enc.encounter_schedule_date
			)
			lower_range = moment.utc().subtract(14, "days")
			upper_range = moment.utc().add(14, "days")
			shc_encs.forEach((enc) ->
				if(enc?.reoccur == 'Yes' && enc?.ical != null)
					rule = rrule.rrulestr(enc.ical)
					allrules = rule.all()
					allrules.forEach((r) ->
						start_date = moment.utc(r).format("MM/DD/YYYY hh:mm A")
						if (!Array.from(dates_taken).includes(start_date) && moment.utc(r).isAfter(lower_range) && moment.utc(r).isBefore(upper_range)) || dd.preset[k] == start_date
							dates.add(start_date)
					)
				else
					start_date = moment.utc(enc?.appointment_date + ' ' + enc?.appointment_start).format("MM/DD/YYYY hh:mm A")
					if (!Array.from(dates_taken).includes(start_date)) || dd.preset[k] == start_date
						dates.add(start_date)
			)
			if(f.children().length == dates.size && dates.size > 1)
				return

			$(f).empty()
			dates.forEach((date) ->
				o = new Option(date, date)
				$(o).html(date)
				$(f).append(o)
			)
			if dd.preset[k]
				dd.value_field(k, dd.preset[k])
			return

		# string
		LowerCase: (form, dd, trn, f, k) ->
			return if f.length is 0
			f.val(f.val().toLowerCase())
			return

		MultiplyFields: (form, dd, trn, f, k) ->
			if trn.fields.length < 2
				return
			firstField = parseFloat(dd.value_field(trn.fields[0]))
			secondField = parseFloat(dd.value_field(trn.fields[1]))
			result = parseFloat(firstField * secondField)
			if isNaN(result)
				return
			dd.value_field(trn.destination, result, true, true)

		DivideFields: (form, dd, trn, f, k) ->
			if trn.fields.length < 2
				return
			firstField = parseFloat(dd.value_field(trn.fields[0]))
			secondField = parseFloat(dd.value_field(trn.fields[1]))
			if (secondField == 0)
				return
			result = parseFloat(firstField / secondField)
			if isNaN(result)
				return
			dd.value_field(trn.destination, result, true, true)

		Trim: (form, dd, trn, f, k) ->
			return if f.length is 0
			f.val(f.val().trim())
			return

		UpperCase: (form, dd, trn, f, k) ->
			return if f.length is 0
			f.val(f.val().toUpperCase())
			return

		# ajax address
		CityStateTransform: (form, dd, trn, f, k) ->
			return

		# calculations
		TimeDifference: (form, dd, trn, f, k) ->
			# accepts array of fields [start, end, (start, end)..., total]
			tt = 0
			for i in [0..trn.fields.length - 1] by 2
				ts = $.trim(dd.value_field(trn.fields[i])).toLowerCase()
				te = $.trim(dd.value_field(trn.fields[i+1])).toLowerCase()
				continue if ts is '' or te is ''
				if ((ts.indexOf('pm') > -1) and (te.indexOf('am') > -1))
					ts = '1/1/2001 ' + ts
					te = '1/2/2001 ' + te
				else
					ts = '1/1/2001 ' + ts
					te = '1/1/2001 ' + te
				ds = new Date(ts)
				de = new Date(te)
				tt += (de.getTime() - ds.getTime()) / 3600 / 1000 # hours
			tt = roundTo((tt + 24) % 24, 0.01)
			tt = '' if tt is 0
			dd.value_field(trn.fields[trn.fields.length - 1], tt)
			return

		DateTimeDifference: (form, dd, trn, f, k) ->
			# accepts array of fields [start, end, (start, end)..., total]
			tt = 0
			for i in [0..trn.fields.length - 1] by 2
				ts = $.trim(dd.value_field(trn.fields[i])).toLowerCase()
				te = $.trim(dd.value_field(trn.fields[i+1])).toLowerCase()
				continue if ts is '' or te is ''
				start = moment(ts)
				end = moment(te)
				if(start.isBefore(end))
					duration = moment.duration(end.diff(start))
					hours = duration.asHours()
					tt = parseFloat(roundTo(hours, 0.01))
			tt = '' if tt is 0
			dd.value_field(trn.fields[trn.fields.length - 1], tt, true)
			return

		# shipment tracking link
		TrackingLinkTransform: (form, dd, trn, f, k) ->
			shp = url = ''
			v = $.trim(f.val())
			u = f.next('.showunit')

			if v and TNV.isValid(v)
				shp = TNV.getCourierOne(v).toUpperCase()
				url = TNV.getTrackingUrl(v)

			if shp and url
				u.html '<a target="_blank" href="' + url + '">' + shp + '</a>'
			else if shp
				u.html shp
			else
				u.html ''
			return

		# dimensions
		HeightTransform: (form, dd, trn, f, k) ->

			# E.g.: 143, 143cm, 1.43m, 56in, 56", 4' 8", 4 8
			cmin = 0.393700787

			v = f.val().toLowerCase().replace(/\'/gi, 'f').replace(/\"/gi, 'i')
			u = f.next('.showunit')
			if v.replace(/[^0-9]/gi, ' ').replace(/(\s)+/gi, ' ').trim() is ''
				u.html('') if u.length isnt 0
				f.val('')
				return

			cm = 0
			# convert to cm
			if v.indexOf('c') > -1
				cm = parseFloat(v)
			else if v.indexOf('m') > -1
				cm = parseFloat(v) * 100
			else if (v.indexOf('f') > -1) and (v.indexOf('i') is -1)
				cm = parseFloat(v) * 12 / cmin
			else if (v.indexOf('i') > -1) and ((v.indexOf('f') is -1))
				cm = parseFloat(v) / cmin
			else if v.indexOf(' ') > -1 #ft in
				vv = v.split(' ')
				if vv.length is 1
					cm = parseFloat(vv[0]) / cmin
				else
					cm = ((parseFloat(vv[0]) * 12) + parseFloat(vv[1])) / cmin
			else
				cm = parseFloat(v)

			if isNaN cm
				u.html('') if u.length isnt 0
				f.val('')
				return

			fmin = form.fields[k].model.min
			fmax = form.fields[k].model.max
			cm = Math.max(cm, fmin) if fmin isnt null
			cm = Math.min(cm, fmax) if fmax isnt null

			fv = form.fields[k]
			if (fv.model.type is 'decimal') and (fv.model.rounding isnt null)
				f.val roundTo(cm, fv.model.rounding)
			else
				f.val roundTo(cm, 1)

			u = f.next('.showunit')
			if u.length isnt 0
				inch = (cm * cmin).toFixed(0)
				feet = Math.floor(inch / 12)
				inch %= 12
				u.html feet + 'ft ' + inch + 'in'

			return

		# Set on the DOB field to autocalculate the an age field
		AgeTransform: (form, dd, vld, f, k) ->
			if f.val()?
				field_date = moment(f.val(), 'M/D/YYYY', true)
				if field_date.isValid()
					d = new Date()
					bd = new Date(f.val())
					y = d.getFullYear()
					bdy = bd.getFullYear()
					m = d.getMonth()
					dt = d.getDate()
					bm = bd.getMonth()
					bdd = bd.getDate()
					age = y - bdy

					if m < bm
						age--

					if bm == m and dt < bdd
						age--

					dd.value_field 'age', age, true
				return

		# dimensions
		WeightTransform: (form, dd, trn, f, k) ->

			# E.g.: 78, 78kg, 172lbs, 171lbs 15oz
			kglb = 0.453592

			v = f.val().toLowerCase()
			u = f.next('.showunit')
			if v.replace(/[^0-9]/gi, ' ').replace(/(\s)+/gi, ' ').trim() is ''
				u.html('') if u.length isnt 0
				f.val('')
				return

			kg = 0
			# convert to kg
			if v.indexOf('k') > -1
				kg = parseFloat(v)
			else if v.indexOf('g') > -1
				kg = parseFloat(v) / 1000
			else if (v.indexOf('l') > -1) and (v.indexOf('o') > -1)
				# could be any format: "123 lbs 4.5 oz" / "123l 4.5o" / "123lb4.5o"
				v = v.split(/[^0-9\.]+/).slice(0, 2)
				kg = (parseFloat(v[0]) * kglb) + (parseFloat(v[1]) * kglb / 16)
			else if v.indexOf('l') > -1
				kg = parseFloat(v) * kglb
			else if v.indexOf('o') > -1
				kg = parseFloat(v) * kglb / 16
			else
				kg = parseFloat(v)

			if isNaN kg
				u.html('') if u.length isnt 0
				f.val('')
				return

			fmin = form.fields[k].model.min
			fmax = form.fields[k].model.max
			kg = Math.max(kg, fmin) if fmin isnt null
			kg = Math.min(kg, fmax) if fmax isnt null

			fv = form.fields[k]
			if (fv.model.type is 'decimal') and (fv.model.rounding isnt null)
				f.val roundTo(kg, fv.model.rounding)
			else
				f.val roundTo(kg, 1)

			u = f.next('.showunit')
			if u.length isnt 0
				oz = roundTo(((kg / kglb) - Math.floor(kg / kglb)) * 16, 1)
				if oz in [1..15]
					lb = Math.floor(kg / kglb)
					u.html lb + 'lb ' + oz + 'oz'
				else
					lb = roundTo(kg / kglb, 1)
					u.html lb + ' lbs'

			return

		# Timetransform - converts UTC field to local time
		Timetransform: (form, dd, vld, f, k) ->
			cv = $.trim(f.val())
			return if cv?.length > 0

			cp = vld.field
			v = dd.value_field(cp)

			if v? and v.length > 0
				nd = new Date(v + ' UTC')
				f.val(moment(nd.toString()).format('MM/DD/YYYY hh:mm A'))

		SetEventEffectiveDates: (form, dd, trn) ->
			vals = {}
			if dd.value_field('reoccur') != 'Yes'
				dd.value_field trn.destination, ''
				return
			for k, v of trn.source
				vals[k] = dd.value_field(v)
			vals.interval = vals['interval_' + vals.freq?.toLowerCase()]
			for k, v of vals
				if k.substr(0, 9) is 'interval_'
					delete vals[k]
			try
				vc = VCalenderGenerate vals
				if vc
					dd.value_field trn.destination, vc
			catch e
				# do nothing
			return

		# Recurring
		RRuleTransform: (form, dd, trn) ->
			vals = {}
			if dd.value_field('reoccur') != 'Yes'
				dd.value_field trn.destination, ''
				return
			for k, v of trn.source
				vals[k] = dd.value_field(v)
			vals.interval = vals['interval_' + vals.freq?.toLowerCase()]
			for k, v of vals
				if k.substr(0, 9) is 'interval_'
					delete vals[k]
			try
				vc = VCalenderGenerate vals
				if vc
					dd.value_field trn.destination, vc
			catch e
				# do nothing
			return

		SetEffectiveDateAndTime: (form, dd, trn) ->
			vals = {}
			for k, v of trn.source
				vals[k] = dd.value_field(v)
			dd.value_field trn.destination.effective_start_date, if vals.override_start_date then vals.override_start_date else vals.event_start_date
			dd.value_field trn.destination.effective_start_time, if vals.override_start_time then vals.override_start_time else vals.event_start_time
			dd.value_field trn.destination.effective_end_date, if vals.override_end_date then vals.override_end_date else vals.event_end_date
			dd.value_field trn.destination.effective_end_time, if vals.override_end_time then vals.override_end_time else vals.event_end_time
			return


		# Recurring
		RRuleTransformHeritage: (form, dd, trn) ->
			vals = {}
			if dd.value_field('reoccur') != 'Yes'
				dd.value_field trn.destination, ''
				return
			for k,v of trn.source
				vals[k] = dd.value_field(v)
			vals.interval = vals['interval_' + vals.freq?.toLowerCase()]
			for k,v of vals
				if k.substr(0, 9) is 'interval_'
					delete vals[k]
			try
				vc = window.ical(vals)
				if vc
					dd.value_field trn.destination, vc
			catch e
				# do nothing
			return

		RRuleTransformLab:(form, dd, trn) ->
			vals = {}
			if dd.value_field('reoccur') != 'Yes'
				dd.value_field trn.destination, ''
				return
			for k,v of trn.source
				vals[k] = dd.value_field(v)
				if(dd.value_field(v)?.indexOf('Week') != -1)
					vals[k] = 'Weekly'
				if(dd.value_field(v)?.indexOf('Month') != -1)
					vals[k] = 'Monthly'
				if(dd.value_field(v)?.indexOf('Year') != -1)
					vals[k] = 'Yearly'
			if(vals.freq == 'Monthly')
				if dd.value_field('lab_reoccurence').indexOf('3') != -1
					vals['interval'] = 3
				else
					vals['interval'] = 6
			if(vals.freq == 'Yearly')
				vals['interval'] = 1
			try
				vc = VCalenderGenerateLab vals
				if vc
					dd.value_field trn.destination, vc
			catch e
				# do nothing
			return

		# Role-based Field values
		RoleBasedFieldTransform: (form, dd, trn) ->
			return if not trn.role[App.user?.role]?
			for k,v of trn.role[App.user.role]
				dd.value_field_fixed k, v
			return

		JobTitleTransform: (form, dd, trn, f) ->
			role = dd.field_nodes[trn.fields[0]]
			job_title = dd.field_nodes[trn.fields[1]]
			if job_title.val().length == 0
				job_title.val(role.val())
			else
				if role.val()!= job_title.val()
					for v in role[0]
						if v.value == job_title.val()
							job_title.val(role.val())
							return
			return

	ValidateFieldView: (form, dd, vlds, f, k, flv) ->
		for vld in vlds
			if DSLFx.Validators[vld.name]?
				#CM:2019-03-03 - we do not want to call all the validators during verify/save
				#  all validators must be called when specific field handler event is triggered
				#  old style validators have 5 params and should not be called during verify/save
				#  new style validators have 6+ params and should be called during verify/save
				#  new style validators can decide if they need to re-validate via 6th param: flv
				if flv or DSLFx.Validators[vld.name].length > 5
					verr = DSLFx.Validators[vld.name](form, dd, vld, f, k, flv)

					# stop at first error, ignore promises
					if verr and not (typeof verr?.then is 'function' and typeof verr?.catch is 'function')
						return verr
		false

	ValidateFieldModel: (form, dd, vlds, f, k, flv) ->
		for vld in vlds
			form_name = dd.options.form
			field_name = vld.field
			field_vals = {}
			for fld, index in vld.fields
				fv = DSLFields.value(dd.field_nodes[fld])
				if vld.fields.length == 1
					field_vals['value'] = fv
				else
					field_vals[fld] = fv
			aj = Ajax.async
				url: '/form/' + form_name + '/validate'
				type: 'POST'
				data:
					name: vld.name
					field_vals: field_vals
			aj.done (data, status, xhr) ->
				if data?.error?.length > 0
					DSLFx.ValidateFieldError f, data.error
					if data?.clear_fields?.length > 0
						for fld, _ in data.clear_fields
							DSLFields.value(dd.field_nodes[fld], "")
				else
					DSLFx.ValidateFieldError f
			aj.fail (xhr, status, data) ->
				prettyError false, 'There was an error validating the field. Please try again.'

	ValidateFieldRules: (form, dd, v, f, k, flv) ->
		return if (dd.options.mode is 'read')
		# generic validator based on type of field
		ferr = DSLFx.ValidateFieldType(form, dd, v, f, k)
		# field-level validator: view
		if (not ferr) and (v.view.validate.length > 0)
			ferr = DSLFx.ValidateFieldView(form, dd, v.view.validate, f, k, flv)
		# field-level validator: model
		if (not ferr) and (v.model.validate.length > 0)
			ferr = DSLFx.ValidateFieldModel(form, dd, v.model.validate, f, k, flv)

		if (not ferr)
			DSLFx.ValidateFieldError f, ferr
		ferr

	ValidateFieldError: (f, err = false) ->
		if !f or getType(f) == 'string'
			console.error('Invalid field argument being passed to ValidateFieldError, must be jQuery object d')
			return
		d = f.data()
		if d and d.fieldtype and d.container is 'grid'
			errdiv = f.parent().siblings('.errormsg')
			fg = f.parent().parent()
			if err
				fg.addClass('has-error')
				err_str = get_string_value(err)
				errdiv.html err_str
				errdiv.attr('title', err_str)
			else
				fg.removeClass('has-error')
				errdiv.html ''
				errdiv.removeAttr('title')
			return err
		fg = f.closest('.form-group')
		if err
			fg.addClass('has-error')
			err_str = get_string_value(err)
			fg.find('.errormsg').html err_str
			fg.find('.errormsg').attr('title', err_str)
		else
			fg.removeClass('has-error')
			fg.find('.errormsg').html ''
			fg.find('.errormsg').removeAttr('title')
		err

	ValidateFieldWarning: (f, err = false) ->
		d = f.data()
		if d and d.fieldtype and d.container is 'grid'
			err = f.parent().siblings('.warningmsg')
			fg = f.parent().parent()
			if err
				err.html err
			else
				err.html ''
			return
		fg = f.closest('.form-group')
		if err
			fg.addClass('has-warning')
			fg.find('.warningmsg').html err
		else
			fg.removeClass('has-warning')
			fg.find('.warningmsg').html ''
		err

	ValidateFields: (form, dd, vals, vreq) ->
		vf = DSLFx.ValidateRequired(form, dd, vals, vreq)
		vf = {} if not vf
		for k, vv of vals
			continue if vf[k]? or (not (form.fields[k]? and dd.field_map[k]?))
			v = form.fields[k]
			for f in dd.field_map[k]
				continue if vf[k]?
				vfr = DSLFx.ValidateFieldRules(form, dd, v, f, k, false)
				vf[k] = vfr.replace(v.view.label + ' ', '') if vfr and getType(vfr) is 'string'
		if _.isEmpty(vf) then false else vf

	ValidateFieldType: (form, dd, v, f, k) -> # generic validator based on type of field
		err = DSLFields.validate(f)
		DSLFx.ValidateFieldError f, err if err
		err

	ValidateForm: (form, dd, vals) ->
		return if (dd.options.mode is 'read')
		vf = []
		vfva = DSLFx.ValidateFormView(form, dd, vals)
		vf = vf.concat(vfva) if vfva
		vfma = DSLFx.ValidateFormModel(form, dd, vals)
		vf = vf.concat(vfma) if vfma
		if vf.length is 0 then false else vf

	ValidateFormView: (form, dd, vals) ->
		if not form.view?.validate?.length > 0
			return false
		vf = []
		for vld in form.view.validate
			continue if not DSLFx.Validators[vld.name]?
			vfa = DSLFx.Validators[vld.name](form, dd, vld, vals)
			if vfa and not (typeof vfa?.then is 'function' and typeof vfa?.catch is 'function')
				vf = vf.concat(vfa) if vfa
		if vf.length is 0 then false else vf

	ValidateFormModel: (form, dd, vals) ->
		if not form.model?.validate?.length > 0
			return false

		vf = []
		form_name = dd.options.form
		for vld in form.model?.validate
			field_vals = {}
			if not vld.fields?
				field_vals = vals
			else
				for fld, index in vld.fields
					fv = DSLFields.value(dd.field_nodes[fld])
					if vld.fields.length == 1
						field_vals['value'] = fv
					else
						field_vals[fld] = fv

			aj = Ajax.sync
				url: '/form/' + form_name + '/validate'
				type: 'POST'
				async: false  # This makes the AJAX request synchronous
				data:
					name: vld.name
					field_vals: field_vals
				success: (data, status, xhr) ->
					if data?.error?.length > 0
						vf.push(data.error)
				error: (xhr, status, error) ->
					prettyError false, 'There was an error validating the form. Please try again.'
		if vf.length > 0
			return vf
		else
			return false

	ValidateNotBlocked: (form, dd, vals, k) ->
		for vld in DSL[form].view.block.validate
			continue if not DSLFx.Validators[vld.name]?
			vfa = DSLFx.Validators[vld.name](form, dd, vld, vals, k)
			return false if not vfa
		true

	ValidateRequired: (form, dd, vals, also_required = []) ->
		# also see TransformRequiredIf
		fname = dd.options.form
		mode = if ["add", "addfill"].includes(dd.options.mode) then "create" else "update"
		required_if_fields = []
		if form.model.required_if and vals[form.model.required_if] is 'Yes'
			required_if_fields = form.model.required_if_fields
		vf = false
		for k, v of vals
			continue if not form.fields[k]
			#continue if form.fields[k].view?.offscreen
			req = form.fields[k].model.required or (k in also_required) or (k in required_if_fields)
			continue if req and not Auth["field_can_#{mode}"](fname, k) # field is required but user cannot edit
			if dd.field_nodes?[k] and form.fields[k].model.type != 'subform'
				continue if !DSLFields.is_visible(dd.field_nodes[k])
			if req and (not _.isNumber(v)) and _.isEmpty(v)
				vf = {} if not vf
				vf[k] = 'is required'

			if form.fields[k].model?.required_all == true and form.fields[k].model?.multi == true and form.fields[k].view.control == 'checkbox'
				if v.length > 0 and form.fields[k].model.source.length != v.length
					vf = {} if not vf
					vf[k] = 'is required (All options must be select).'
		vf

	Validators:
		AddDateDays: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			field = vld.field
			days = vld.add_days
			val = dd.value_field(k)
			if (!field or !val or !days)
				return false
			DSLFields.value(dd.field_nodes[field], moment(val).add(days, 'days').format('MM/DD/YYYY'))
			false

		CopyForwardValue: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			field = vld.field
			val_field = vld.val_field || k
			ov = vld.overwrite || false
			val = dd.value_field(val_field)
			if (!dd.prev_val)
				dd.prev_val = {}
			pval = dd.prev_val[k] || dd.preset[k]
			if (!field or !val)
				return false
			if vld.condition
				cond = vld.condition?.cond
				cond_val = vld.condition?.value
				cond_field = vld.condition?.field || k
				if (cond_field != k)
					check_val = dd.value_field(cond_field)
				else
					check_val = val
				if (cond && cond_val)
					switch cond
						when 'eq'
							if (cond_val != check_val)
								return false
						when 'neq'
							if (cond_val == check_val)
								return false
						when 'gte'
							if (cond_val < check_val)
								return false
						when 'lte'
							if (cond_val > check_val)
								return false
						when 'gt'
							if (cond_val <= check_val)
								return false
						when 'lt'
							if (cond_val >= check_val)
								return false

			fields = if Array.isArray(field) then field else [field]
			for field in fields
				cval = dd.value_field(field)
				if (!cval or ov) or (pval is cval)
					dd.value_field(field, val, true, true)
					dd.prev_val[field] = val
			false

		UniqueValidator: (form, dd, vld, f, k, flv) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			fields = vld.fields
			emsg = vld.error or "All field values must be unique"
			if (val && fields?.length > 0)
				fieldValues = fields.map((field) -> DSLFields.value(field)).filter((value) -> value != null)
				if fieldValues.length > 0 && fieldValues.length != _.uniq(fieldValues).length
					return DSLFx.ValidateFieldError f, emsg
			false

		PrefillCurrentDate: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			cond = vld.condition
			dest = vld.dest || k
			overwrite = vld.overwrite || false
			dval = dd.value_field(dest)

			if (!cond)
				return false
			for ck of cond
				condv = cond[ck]
				cval = dd.value_field(ck)
				if (condv == '*' && cval)
					continue
				if (condv.startsWith('!'))
					if (cval == condv.slice(1))
						return false
				else
					if (Array.isArray(cval))
						if (!cval.includes(condv))
							return false
					else if (cval != condv)
						return false
			if (dval && !overwrite)
				return false
			dd.value_field(dest, moment().format('MM/DD/YYYY'), true, true)
			false

		PrefillCurrentDateTime: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			cond = vld.condition
			dest = vld.dest || k
			if (!cond)
				return false
			for ck of cond
				condv = cond[ck]
				cval = dd.value_field(ck)
				if (condv == '*' && cval)
					continue
				if (condv.startsWith('!'))
					if (cval == condv.slice(1))
						return false
				else
					if (Array.isArray(cval))
						if (!cval.includes(condv))
							return false
					else if (cval != condv)
						return false
			dval = dd.value_field(dest)
			if (dval)
				return false
			dd.value_field(dest, moment().format('MM/DD/YYYY hh:mm a'), true, true)
			false

		ConvertStringToInt: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			field = vld.field
			if (!val || !field)
				return false
			if !isNaN(parseInt(val)) && Number.isInteger(parseFloat(val))
				DSLFields.value(dd.field_nodes[field], parseInt(val), true, true)
			false

		PrefillFirstDayOfNextCalendarYear: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			if (!val)
				return false
			DSLFields.value(dd.field_nodes[k], moment().add(1, 'year').startOf('year').format('MM/DD/YYYY'), false, false)
			false

		PrefillReminderDate: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			if (!val)
				return false
			field = vld.field
			days = vld.subtract_days
			if (!field or !days or parseInt(days) <= 0)
				return false
			cval = dd.value_field(field)
			presetVal = dd.preset?[field] or ''
			if (presetVal == val)
				return false
			DSLFields.value(dd.field_nodes[field], moment(val).subtract(days, 'days').format('MM/DD/YYYY'), false, false)
			false

		PrefillCurrentUser: (form, dd, vld, f, k) ->
			mode = dd.options.mode
			return if mode is 'read'
			val = dd.value_field(k)
			cond = vld.condition
			dest = vld.dest || k
			if (!cond)
				return false
			for ck of cond
				condv = cond[ck]
				cval = dd.value_field(ck)
				if (condv == '*' && cval)
					continue
				if (condv.startsWith('!'))
					if (cval == condv.slice(1))
						return false
				else
					if (Array.isArray(cval))
						if (!cval.includes(condv))
							return false
					else if (cval != condv)
						return false
			dval = dd.value_field(dest)
			if (dval)
				return false
			dd.value_field(dest, App.user.id, true, true)
			false

		RequiredAny: (form, dd, vld, f, k, flv) ->
			cond = vld.if
			v = dd.value_field(vld.source_field)
			fields = []
			if v and cond[v]
				fields = cond[v]
			else if !v and cond['!']
				fields = cond['!']
			else if v and cond['*']
				fields = cond['*']
			return false if fields.length is 0
			for fl in fields
				if dd.value_field(fl)
					return false
			error_fields = []
			for fl in fields
				fn = dd.field_nodes[fl]
				if fn
					error_fields.push(form.fields[fl].view.label)
			if(error_fields.length > 0)
				return "At least one of the following fields is required: #{error_fields.join(', ')}"
			false

		ScheduledEndValidate: (form, dd, vld, f, k) ->
			appointment_date = dd.value_field('event_start_date')
			schedule_end = dd.value_field(k)
			start = moment(new Date(appointment_date))
			end = moment(new Date(schedule_end))

			if schedule_end? and end.diff(start,'days') <= 0
				prettyAlert false, 'End Date cannot be earlier than Schedule Date'
				DSLFields.value(dd.field_nodes[k], '', false, false)


		ScheduledEndValidateLab: (form, dd, vld, f, k) ->
			appointment_date = dd.value_field('schedule_start')
			schedule_end = dd.value_field(k)
			start = moment(new Date(appointment_date))
			end = moment(new Date(schedule_end))

			if schedule_end? and end.diff(start,'days') <= 0
				prettyAlert false, 'End Date cannot be earlier than Schedule Date'
				DSLFields.value(dd.field_nodes[k], '', false, false)

		#sup specific transform
		FetchSiteAccess: (form, dd, vld, f, k, flv) ->
			if f.val() == null || _.isEmpty(dd.options.site_access_transform)
				if App.user.site_access?.length > 0 && f.val() == null
					access_sites = _.cloneDeep(App.user.site_access)
					dd.options.site_access_transform = 'updated'
					f.val(access_sites).change()

		AlphaNumValidator: (form, dd, vld, f, kglb, flv) ->
			if f.val()? and f.val().toLowerCase() isnt f.val().alphanum()
				DSLFx.ValidateFieldError f, form.fields[k].view.label + ' must be alpha-numeric'

		DateOrderValidator: (form, dd, vld, fvals, k, flv) ->
			if k? # field-level
				f = fvals
				DSLFx.ValidateFieldError f
				v1 = $.trim(dd.value_field(vld.fields[0]))
				v2 = $.trim(dd.value_field(vld.fields[1]))
				if v1 is ''
					pf = dd.options?.parent?.ddf?.values()
					v1 = if pf then pf[vld.fields[0]] else ''
				if v2 is ''
					pf = dd.options?.parent?.ddf?.values()
					v2 = if pf then pf[vld.fields[1]] else ''
				return false if v1 is '' or v2 is ''
				d1 = dateYMD(v1)
				d2 = dateYMD(v2)
				DSLFx.ValidateFieldError f, (if d2 < d1 then vld.error else false)
			else # form-level
				vals = fvals
				v1 = $.trim(vals[vld.fields[0]])
				v2 = $.trim(vals[vld.fields[1]])
				if v1 is ''
					pf = dd.options?.parent?.ddf?.values()
					v1 = if pf then pf[vld.fields[0]] else ''
				if v2 is ''
					pf = dd.options?.parent?.ddf?.values()
					v2 = if pf then pf[vld.fields[1]] else ''
				return false if v1 is '' or v2 is ''
				d1 = dateYMD(v1)
				d2 = dateYMD(v2)
				if d2 < d1 then vld.error else false

		CompareValidator: (form, dd, vld, fvals, k, flv) ->
			if k? # field-level
				f = fvals
				DSLFx.ValidateFieldError f
				v1 = $.trim(dd.value_field(vld.fields[0]))
				v2 = $.trim(dd.value_field(vld.fields[1]))
				if v1 is ''
					pf = dd.options?.parent?.ddf?.values()
					v1 = if pf then pf[vld.fields[0]] else ''
				if v2 is ''
					pf = dd.options?.parent?.ddf?.values()
					v2 = if pf then pf[vld.fields[1]] else ''
				return false if v1 is '' or v2 is ''
				v1 = parseFloat(v1)
				v2 = parseFloat(v2)
				if (vld.require is 'gte') and (v1 < v2)
					DSLFx.ValidateFieldError f, vld.error
				else if (vld.require is 'gt') and (v1 <= v2)
					DSLFx.ValidateFieldError f, vld.error
				else if (vld.require is 'lte') and (v1 > v2)
					DSLFx.ValidateFieldError f, vld.error
				else if (vld.require is 'lt') and (v1 >= v2)
					DSLFx.ValidateFieldError f, vld.error
				else if (vld.require is 'eq') and (v1 != v2)
					DSLFx.ValidateFieldError f, vld.error

		StartEndDateValidation: (form, dd, vld, f, k, flv) ->
			start = dd?.field_nodes[vld.fields[0]]?.val()
			end = dd?.field_nodes[vld.fields[1]]?.val()

			mStart = moment(start)
			mEnd = moment(end)

			if mEnd.diff(mStart,'days') < 0
				DSLFx.ValidateFieldError f, 'Start date cannot be later than end date'

		# set require=past/future in DSL
		DateValidator: (form, dd, vld, f, k, flv) ->
			return if (dd.options.mode is 'read')
			if f.val()?
				today = moment().startOf('day')
				field_date = moment(f.val(), 'M/D/YYYY', true)
				if (vld.require is 'past') and (field_date > today)
					DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be later than today'
				else if (vld.require is 'future') and (field_date < today)
					DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be before today'

		DateTimeValidator: (form, dd, vld, f, k, flv) ->
			return if (dd.options.mode is 'read') # Don't run in readonly mode

			if vld.ignore
				return if App.user.role in vld.ignore
			if f.val()?
				today = moment().endOf('day')
				field_date = moment(f.val())
				if (vld.require is 'past') and (field_date > today)
					DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be later than today'
				else if (vld.require is 'future') and (field_date < today)
					DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be before today'

		DOBValidator: (form, dd, vld, f, k, flv) ->
			return if (dd.options.mode is 'read') # Don't run in readonly mode
			if f.val()?
				today = moment().startOf('day')
				field_date = moment(f.val(), 'M/D/YYYY', true)
				if field_date > today
					return DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be later than today'
				years_diff = today.diff(field_date, 'years')
				if years_diff > 120
					return DSLFx.ValidateFieldError f, form.fields[k].view.label + ' cannot be more than 120 years ago'

		PediatricCheck: (form, dd, vld, f, k, flv) ->
			return if (dd.options.mode is 'read') # Don't run in readonly mode
			val = dd.value_field(k)
			cutoff = vld.age
			if (val && cutoff)
				age = moment().diff(val, 'years')
				isped = age < cutoff
				DSLFields.value(dd.field_nodes['pediatric'], isped ? "Yes": "No")

		RegExValidator: (form, dd, vld, f, k, flv) ->
			fv = f.val()?.trim() || ''
			if fv.length > 0
				if !vld.pattern || !vld.error
					console.log 'RegExValidator in ' + form + ' for field ' + k + ' is missing pattern or error'
					return
				if Array.isArray(vld.pattern)
					for pattern of vld.pattern
						regex = new RegExp(pattern)
						if regex.test(f.val())
							return
					return DSLFx.ValidateFieldError f, vld.error
				else
					regex = new RegExp(vld.pattern)
					if !regex.test(f.val())
						return DSLFx.ValidateFieldError f, vld.error

		HighRiskWarning: (form, dd, vld, f, k, flv) ->
			checked = DSLFields.value(f) or []
			if checked.length > 0
				DSLFx.ValidateFieldError f, 'Warning: Patient is high risk and a care plan must be completed'

		# make sure that if an exclusive option is selected, it is exclusive of all other options
		ExclusiveValidator: (form, dd, vld, f, k, flv) ->
			checked = DSLFields.value(f) or []
			if checked.length > 1
				exclusivematches = _.intersection(vld.match, checked)
				if exclusivematches.length > 0
					DSLFx.ValidateFieldError f, joinWords(exclusivematches) + ' cannot be selected at the same time as other options'
				else
					false
			else
				false

		NameValidator: (form, dd, vld, f, k, flv) ->
			if f.val()?.length and not /^[a-zA-Z0-9'\\.\\-\s]+$/.test(f.val())
				return DSLFx.ValidateFieldError f, "can only contain Letters, numbers, single quotes('), sdots(.), backslashes(\), dashes(-) and spaces between"
			return

		# make sure url starts with https://
		SecureURLValidator: (form, dd, vld, f, k, flv) ->
			DSLFx.ValidateFieldError f
			if f.length > 0
				fv = f.val().trim()
				if fv.length > 0
					if fv.match(/^https:\/\//i)
						false
					else
						DSLFx.ValidateFieldError f, form.fields[k].view.label + ' must begin with https://'
				else
					false
			else
				false

		SSNValidator: (form, dd, vld, f, k, flv) ->
			if f.val()?.length and not /^\d{3}-\d{2}-\d{4}$/.test(f.val())
				return DSLFx.ValidateFieldError f, "Must be a valid Social Security Number and in the format ###-##-####."
			return

		# Simple validator to return current form id, for use in other dsl fields.
		GetFormidValidator: (form, dd, vld, f, k, flv) ->
			editmode = dd.preset?.id?
			fval = dd.value_field(k)
			if editmode and _.isEmpty(fval)
				DSLFields.value(dd.field_nodes[k], dd.preset.id)
				false
			else
				false

		# Simple validator copy field to another field.
		CopyLocalField: (form, dd, vld, f, k, flv) ->
			mode = dd.options.mode
			return if mode is 'read'
			if vld.fields.length? > 1
				false
			sfield = vld.fields[0]
			sval = dd.preset[sfield]
			editmode = dd.preset?.id?
			fval = dd.value_field(k)
			if fval
				false
			if mode is 'edit' and not fval and sval
				DSLFields.value(dd.field_nodes[k], sval)
				false
			else
				false

		BarcodeParseValidator: (form, dd, vld, f, k) ->
		# keys in fields must be one or more of these
			# fields:
			#	raw: 'e.g. any text field, no parsing'
			#	gtin_item: 'e.g. inventory_id'
			#	gtin: 'e.g. gtin'
			#	lot: 'e.g. lot_no'
			#	serial: 'e.g. serial_no'
			#	quantity: 'e.g. quantity'
			#	expiration: 'e.g. expiration_date'

			if not "fields" in vld
				console.error ["No fields specified in " + form + "." + k + " validator", vld]
				return

			bc = dd.value_field(k)
			return if not bc

			# if the barcode is plain string (not GS1)
			#   we just need to set the 'raw' and return
			# else
			#   parse GS1 and set values for each field
			if vld.fields.raw
				DSLFields.value(dd.field_nodes[vld.fields.raw], bc)
				return if _.keys(vld.fields).length is 1

			pb = parseBarcode(bc)
			return if not (getType(pb) is 'object' and pb.parsedCodeItems and pb.parsedCodeItems.length > 0)

			scan_values = {}
			for item in pb.parsedCodeItems
				switch item.ai
					when '01'
						scan_values.gtin = item.data
					when '10'
						scan_values.lot = item.data
					when '17'
						scan_values.expiration = item.data.format('mm/dd/yyyy')
					when '21'
						scan_values.serial = item.data
					when '30'
						scan_values.quantity = parseFloat(item.data)

			for vk, vv of vld.fields
				continue if not vk in scan_values
				DSLFields.value(dd.field_nodes[vv], scan_values[vk])

			if vld.fields.gtin_item and scan_values.gtin
				aj = Ajax.async
					url: '/form/inventory/?limit=1&filter=item_barcode:' + scan_values.gtin
				aj.done (data, status, xhr) =>
					if data and data[0] and data[0].id
						DSLFields.value(dd.field_nodes[vld.fields.gtin_item], data[0].id)
					return
			true

		# UI
		UpdateTabLabel: (form, dd, vld, f, k, flv) ->
			lb = for fl in vld.fields
				dd.value_field(fl)
			tx = formatFullName(lb)
			findClosest(dd, 'tablabel')(tx) if tx isnt ''
			false

		# FORM-LEVEL
		# pass/pin match
		PasswordMatchValidator: (form, dd, vld, vals) ->
			editmode = dd.preset?.id? # record id only exists in edit mode
			f1 = vld.fields[0]
			f2 = vld.fields[1]
			if editmode # if editing, no error if both fields are blank
				if vals[f1] != vals[f2] then vld.error else false
			else if vals[f1] is ''
				form.fields[f1].view.label + ' cannot be blank'
			else if vals[f1] != vals[f2]
				vld.error

		# valid field combinations
		CombinationValidator: (form, dd, vld, vals) ->
			fs = {}
			cmb = []
			# find all fields used in various combinations
			for fa in vld.combinations
				cmb.push fa.slice().sort().join('|')
				for f in fa
					fs[f] = f
			ne = []
			# get list of all combination fields filled in
			for k,v of fs
				if vals[k]? and $.trim(vals[k]) != ''
					ne.push k
			nd = ne.sort().join('|')
			# find exact match of filled-in fields in the allowed combination fields list
			if cmb.indexOf(nd) is -1
				[vld.error]
			else
				false

		EnsureUniqueFields: (form, dd, vld) ->
			id="#s2id_"
			error=false
			error_field=null
			for fld, index in vld.fields
				for fld1, index1 in vld.fields when index1>index
					if !_.isEmpty(dd.field_nodes[fld]) and dd.field_nodes[fld].val()!= ""
						if dd.field_nodes[fld].val() == dd.field_nodes[fld1].val()
							$(id+dd.field_nodes[fld1][0].id).select2("val", "")
							dd.field_nodes[fld1].val("")
							error=true
							error_field=dd.field_nodes[fld]
			if error
				return DSLFx.ValidateFieldError error_field, error_field[0].title + vld.error
			return

		ClearChildFields: (form, dd, vlds, f, k, flv) ->
			v = DSLFields.value(dd.field_nodes[k])
			if v == 'Yes'
				return

			for fld, index in vlds.fields
				fv = DSLFields.value(dd.field_nodes[fld])
				if !_.isEmpty(fv)
					DSLFields.value(dd.field_nodes[fld], "")
			false

		EmailValidator: (form, dd, vld, f, k, flv) ->
			if f.val()?.length!=0 and not f.val().match("^[^ \t\n]+@[^. \n\t]+(.[^. \t\n]+)+")
				return DSLFx.ValidateFieldError f, "address must be in the form of id@domain."

		# Validate that no range in provided in the JSON Grid overlaps each other and optionally check for missing dates between the ranges
		DateRangeOverlappingValidator: (form, dd, vld, vals) ->
			return if (dd.options.mode is 'read')
			err = false
			rows = vals[vld.fields[0]]
			if not rows or rows.length == 0
				return DSLFx.ValidateFieldError dd.field_nodes[vld.fields[0]], err
			if _.filter(rows, (r) -> falsy(r[vld.subfields.start_date])).length > 0
				return DSLFx.ValidateFieldError dd.field_nodes[vld.fields[0]], "Start Date must be present in all rows."
			sortedRows = rows.sort((a, b) ->
				moment(a[vld.subfields.start_date], 'YYYY-MM-DD').diff moment(b[vld.subfields.start_date], 'YYYY-MM-DD')
			)
			if sortedRows.length > 1
				i = 0
				while i < sortedRows.length - 1
					j = i + 1
					while j < sortedRows.length
						start_a = sortedRows[i][vld.subfields.start_date] || '1970-01-01'
						end_a = sortedRows[i][vld.subfields.end_date] || moment('2099-12-31').format('YYYY-MM-DD')
						start_b = sortedRows[j][vld.subfields.start_date] || '1970-01-01'
						end_b = sortedRows[j][vld.subfields.end_date] || moment('2099-12-31').format('YYYY-MM-DD')
						if dateRangeOverlaps(start_a, end_a, start_b, end_b)
							err = vld.overlapError || 'Date Ranges provided overlap each other.'
							break
						j += 1
					if err
						break
					i += 1
			if err == false and vld.allowMissingDates == false
				# check for missing dates
				i = 0
				while i < sortedRows.length - 1
					if Math.abs(moment(sortedRows[i][vld.subfields.end_date], 'YYYY-MM-DD').diff(sortedRows[i+1][vld.subfields.start_date], 'YYYY-MM-DD'))/1000 > 86400
						err = vld.missingDatesError || 'There should not be any gap between date ranges'
						break
					i += 1
			# Cannot display PrettyError because of save_form_values which triggers all Form-Level Validators pseudo randomly
			return DSLFx.ValidateFieldError dd.field_nodes[vld.fields[0]], err

		CustomUniqueValidator: (form, dd, vld, f, k, flv) -> # workaround for a glitch in DSL model.indexes.unique implementation (explanation here: https://stackoverflow.com/c/envoylabs/questions/12 )
			mode = dd.options.mode
			return if mode is 'read'
			vld.error ?= form.fields[k].view.label + " value already exists and must be unique"
			form_name = dd.options.form
			current_id = null
			if mode == 'edit'
				current_id = dd.drs.preset.id
			try
				data = Ajax.sync
					url: "/form/" + form_name + "/?limit=1000&filter=" + k + ":" + dd.value_field(k)
				if data instanceof Array and mode == 'edit'
					_.remove(data, {
						id: current_id
					})
				else if data instanceof Object and not(data instanceof Array) and mode == 'edit' and data.id == current_id
					data = null
				else if data instanceof Object and not(data instanceof Array)
					data = [data]
				if (data == null or (data instanceof Array and data.length == 0))
					return
				else
					DSLFx.ValidateFieldError f, vld.error
			catch
				DSLFx.ValidateFieldError f, 'Server Error. Please Try Again'
