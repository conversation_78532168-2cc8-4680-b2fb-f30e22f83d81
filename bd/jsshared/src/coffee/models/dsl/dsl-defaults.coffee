	DSL_Specification =            # type:'object' automatically
		fields:                      # type:'object' automatically
			DEFAULTS:                  # type:'object' automatically
				model:                   # type:'object' automatically
					access:                # type:'object' automatically
						read:    def:[],     type:'array'
						if:      def:null,   type:['null', 'string'] # field name
						write:   def:[],     type:'array'
					active:    def:true,     type:'boolean'
					autoinsert:def:false,    type:'boolean'
					fk:        def:false,    type:'boolean'
					default:   def:null    # type:any by default
					if:                    # type:'object' automatically
						DEFAULTS:            # type:'object' automatically
							fields:         def:[], type:'array'
							note:           def:'', type:'string'
							sections:       def:[], type:'array'
							require_fields: def:[], type:'array'
							hide_fields:    def:[], type:'array'
							hide_sections:  def:[], type:'array'
							require_any:    def:[], type:'array'
							field_warning:  def:{}, type:'object'
							highlight:      def:null, type:'string'
							trigger:        def:[], type:'array'
							readonly:
								fields:   def:[], type:'array'
								sections: def:[], type:'array'
							prefill:        def:{}, type:'object'
							form_label:     def:'', type:'string'
							field_label:    def:{}, type:'object'
					ledger:    def:null,     type:'string'
					max:       def:null,     type:['null', 'number', 'string'] # use string for date/time
					min:       def:null,     type:['null', 'number', 'string'] # use string for date/time
					multi:     def:false,    type:'boolean'
					prefill:   def:[],       type:'array'
					required:  def:false,    type:'boolean'
					required_all:  def:false,type:'boolean'
					rounding:  def:null,     type:'number'
					save:      def:true,     type:'boolean'
					search:    def:null,     type:'string'
					query:    def:null,      type:'string'
					querytemplate: def:null, type:'string'
					source:    def:null,     type:['null', 'string', 'array', 'object']
					source_order: def:null,  type:['null', 'array']
					sourceid:  def:'id',     type:'string'
					track: def:false,        type:'boolean'
					sourcefilter:          # type:'object' automatically
						DEFAULTS:            # type:'object' automatically, id of field in source table
							dynamic:   def:null, type:['null', 'number', 'string']
							static:    def:null, type:['null', 'number', 'string', 'array']
							source:    def:null, type:['null', 'array']
							default:   def:null, type:['null', 'number', 'string']
					subfields:             # type:'object' automatically
						DEFAULTS:            # type:'object' automatically, id of subfield
							label: def:'',       type:'string'
							style: def:{},       type:'object'
							readonly:def:false,  type:'boolean'
							required:def:false,  type:'boolean' # only works in embed field
							multi:   def:false,  type:'boolean'
							offscreen:def:false, type:'boolean'
							source:def:null,     type:['null', 'string', 'array', 'object']
							dynamic: def: null,  type:'string'
							format:  def: null,  source:['null','0,0.[000000]', '$0,0.[000000]', '$0,0.00', '$0,0.0000', '0,0', 'percent', 'us_phone'] # only works in embed field
							sourceid: def: 'id', type:'string'
							sourcefilter:          # type:'object' automatically
								DEFAULTS:            # type:'object' automatically, id of field in source table
									dynamic:   def:null, type:['null', 'number', 'string']
									static:    def:null, type:['null', 'number', 'string', 'array']
									source:    def:null, type:['null', 'array']
									default:   def:null, type:['null', 'number', 'string']
							type:  def:'text',   source:['date', 'datenow', 'datetime', 'decimal', 'int', 'text', 'time', 'timenow', 'timestamp', 'checkbox', 'area']
							class: def: null, type:'string'
					subfields_sort:def:[],   type:'array' # filled in automatically
					template:  def:null,     type:['null', 'string']
					transform: def:[],       type:'array'
					transform_filter:def:[], type:'array'
					transform_post: def:[],  type:'array'
					type:      def:'text',   source:['date', 'datetime', 'decimal', 'image', 'int', 'json', 'xml', 'password', 'subform', 'text', 'time', 'color']
					validate:  def:[],       type:'array'
					dynamic:
						source: def:null,	type:'string'
						type:	def:'text',	source:['text']
						query: def:null,	type:'string'
				view:                    # type:'object' automatically
					form_link_enabled: def:false, type:'boolean'
					class:     def:'',       type:'string'
					add_preset: def:null,   type:'object'
					embed:
						request_type: def:'GET',  type:'string'
						add_form:   def:null,     type:'string'
						form:       def:null,     type: ["null", "string"]
						query:      def:null,     type: ["null", "string"]
						selectable: def:true,     type:'boolean' # flag is only for query embed
						add_preset: def:null,    type:'object'
					control:   def:'',       source:['area', 'barcode', 'checkbox', 'esign', 'file', 'grid', 'inline', 'input', 'link', 'radio', 'paycard', 'picker', 'select', 'subform', 'xml', 'embedded_table', 'raw', 'json']
					columns:   def:1,      	 type:['number', 'string']
					findfilter:def:null,     type:['null', 'string', 'array']
					findmulti :def:false,    type:'boolean' # allow multi-search even if the data is single-select
					findunique:def:false,    type:'boolean' # dropdown filter by distinct values
					findrange:def:false,     type:'boolean' # adds a start/end range to date fields in the find view
					findwildcard:def:false,  type:'boolean' # adds '*' to find query in select2 to do a wildcard search. filter_wc will ignore this in case filter_wc is true and findwildcard is false.
					format:    def:'',       source:['hic', 'ssn', 'us_phone', 'us_zip', 'url', '0,0.[000000]', '$0,0.[000000]', '$0,0.0000', '$0,0.00', '0,0', 'percent']
					grid:
						add:   def:'flyout', source: ['flyout', 'inline', 'none']
						rank:  def:'none',   source: ['none', 'local', 'global']
						hide_cardmenu: def:false, type:'boolean'
						copy:  def:[],       type:'array'   # Must be used in conjunction with grid split to define rows to copy forward
						edit:  def:false,    type:'boolean' # allow subforms to be editable in grid view
						delete: def:true,    type:'boolean' # allow subforms to be deleted in grid view
						fields:def:[],       type:'array'   # allows to override subform DSL.view.grid.fields
						allow_read_wo_id: def:false, type:'boolean' # allow subforms to be read without an id
						label: def:[],       type:'array'
						subfields: def:[],   type: 'array'
						subfields_label:def:[], type: 'array'
						subfields_width: def:[],type:'array'
						split: def:false,    type:'boolean' # allows grid rows to be "split" and fields from gridcopy to be moved to the next row
						splitif: def:null,   type: 'object' # allows grid rows to be "split" and fields from gridcopy to be moved to the next row if the condition is met
						deleteif: def:null,  type: 'object' # allows grid rows to be "deleted" if the condition is met
						text_trim: def:null, type:'number'
						tooltip:def:[],      type:'array'
						width: def:[],       type:'array'
						selectall: def:false,type:'boolean'
					requireall_bypass:def:false,type:'boolean' # if true, ignores the required all client override
					requireif_bypass:def:false, type:'boolean' # if true respects the model.required and ignores the required_if settings
					highlight: def:null,     type:'string'
					label:     def:'',       type:'string'
					max_count:  def:null,    type:['null', 'number'] # limts numbers of rows that could be added to json grid
					note:      def:'',       type:'string'
					reference: def:null,     type:['null', 'string', 'array']
					offscreen: def:false,    type:'boolean'
					readonly:  def:false,    type:'boolean'
					template:  def:null,     type:['null', 'string']
					transform: def:[],       type:'array'
					validate:  def:[],       type:'array'
					_meta:     def:null,     type:['null', 'string', 'array', 'object']

		model:                       # type:'object' automatically
			access:                    # type:'object' automatically
				create:    def:[],         type:'array'
				create_all:def:[],         type:'array'
				read:      def:[],         type:'array'
				read_all:  def:[],         type:'array'
				update:    def:[],         type:'array'
				update_all:def:[],         type:'array'
				delete:    def:[],         type:'array'
				request:   def:[],         type:'array'
				review:    def:[],         type:'array'
				rereview:  def:[],         type:'array' # allow editing reviewed records
				write:     def:[],         type:'array' #//TODO - obsolete this
			bundle:      def:[],         type:'array'
			collections: def:[],         type:'array'
			indexes:                   # type:'object' automatically
				gin:       def:[],         type:'array'
				fulltext:  def:[],         type:'array'
				lower:     def:[],         type:'array'
				many:      def:[],         type:'array'
				unique:    def:[],         type:'array'
			large:       def:false,      type:'boolean' # dynamically set by NES to true to large tables
			ledger:      def:null,       type:'string'
			name:        def:[],         type:['array', 'string']
			prefill:                   # type:'object' automatically
				DEFAULTS:                # type:'object' automatically
					filter:  def:{},         type:'object'
					link:    def:{},         type:'object'
					max:     def:null,       type:['null', 'string'] # refers to field name
					min:     def:null,       type:['null', 'string'] # refers to field name
			reportable:  def:true,       type:'boolean'
			required_if: def:null,       type:['null', 'string'] # refers to field name that must be 'Yes' #(remove)
			save:        def:true,       type:'boolean'
			sections_group:def:[],       type:'array' # either use sections_group OR sections, NEVER BOTH!
			sections:                  # type:'object' automatically
				DEFAULTS:                # type:'object' automatically
					fields:  def:[],         type:'array'
					tab:     def: null,      type:'string'
					tab_toggle:  def:false,  type:'boolean'
					hide_header: def:false,  type:'boolean'
					indent: def:true,        type:'boolean'
					compact: def:false,      type:'boolean'
					group:   def:{},         type:'object' # auto-created if sections_group is used
					note:    def:'',         type:'string'
					area:    def:'header',   type:'string'
					prefill: def:null,       type:['null', 'string']
					modal:   def:false,      type:'boolean'
			sections_order:  def: null,		 type:['null', 'array'] # make sure you add all the available sections to this array or leave blank to auto-populate
			sync_mode:	 def:'none',		 source:['full', 'mixed', 'none'] # table sequence number will be 10000 if value is mixed
			transform:   def:[],         type:'array'
			transform_filter:def:[],     type:'array'
			transform_post: def:[],      type:'array'
			validate:    def:[],         type:'array'

		view:                        # type:'object' automatically
			block:                     # type:'object' automatically
				print:                   # type:'object' automatically
					if:      def:null,       type:['null', 'string'] # field name
					except:  def:[],         type:'array' # list of override roles, can include 'self'
				update:                  # type:'object' automatically
					if:      def:null,       type:['null', 'string'] # field name
					except:  def:[],         type:'array' # list of override roles, can include 'self'
				validate:  def:[],         type:'array'
			comment:     def:'',           type:'string'
			dimensions:     def:{},        type:'object'
			find:                      # type:'object' automatically
				advanced:  def:[],         type:'array'
				basic:     def:[],         type:'array'
			grid:                      # type:'object' automatically
				fields:    def:[],         type:'array'
				width:	   def:[],         type:'array'
				sort:      def:[],         type:'array'
				label: 	   def:[],         type:'array'
				pivot:     def:null,       type:['null', 'string']
				aggregate:
					DEFAULTS:            # type:'object' automatically, id of field in source table
						field: def:null,     type:['null', 'string']
						func: def:null,      type:['null', 'string']
				group_by: def:null,        type:['null', 'string']
				style:
					DEFAULTS:            # type:'object' automatically, id of field in source table
						style: def:null,     type:['null', 'object']
				menu:      def: [],        type: 'array'
				hide_columns:  def:[],     type:'array'
			hide_header:    def:false,     type:'boolean'
			hide_cardmenu:  def:false,     type:'boolean'
			icon:        def:'',           type:'string'
			label:       def:'',           type:'string'
			max_rows:    def:null,         type:['null', 'number']
			open:        def:'read',       source:['read', 'edit']
			transform:   def:[],           type:'array'
			validate:    def:[],           type:'array'
			reference: def:null,           type:['null', 'string', 'array']