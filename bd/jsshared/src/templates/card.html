<script type="text/template" id="template-card">
	<div class="cardlist"></div>
	<div class="cardform"></div>
	<div class="cardread"></div>
</script>

<script type="text/template" id="template-card-list">
	<style>
	.dataTables_wrapper .row {
		margin-left: 0px;
		margin-right:0px;
	}
	</style>
	<h3 class="formtitle hide"></h3>
	<div class="findbar hide">
		<h3 class="formtitle hide"></h3>
		<div class="findbarcontainer">
			<div class="findtypearea">
				<a href="#" class="findtoggle" findtoggle="advanced"><i class="icon-Expand"></i></a>
				<a href="#" class="findtoggle hide" findtoggle="basic"><i class="icon-Collapse"></i></a>
			</div>
			<div class="findbtn pull-right">
				<button class="btn btn-default btn-info find"><p>Find</p><i class="icon-Search"></i></button>
				<button class="btn btn-default btn-danger clear"><p>Clear</p><i class="icon-Cancel"></i></button>
			</div>
			<div class="findnote pull-left"><i class="icon-Search"></i></div>
			<fieldset class="form-inline findtab">
				<span class="findbasic"></span>
				<span class="findadvanced hide"></span>
			</fieldset>
		</div>
	</div>
	<div class="findbarclear"></div>
	<div class="repeaterwrap">
		<div class="col-lg-12 col-sm-12">
		<table  class="display repeater hover" width="100%" style="cursor:pointer"></table>
		</div>
	</div>
</script>


<!-- <script type="text/template" id="template-card-list">
	<h3 class="formtitle hide"></h3>
	<div class="findbar hide">
		<h3 class="formtitle hide"></h3>
		<div class="findbarcontainer">
			<div class="findtypearea">
				<a href="#" class="findtoggle" findtoggle="advanced"><i class="icon-Expand"></i></a>
				<a href="#" class="findtoggle hide" findtoggle="basic"><i class="icon-Collapse"></i></a>
			</div>
			<div class="findbtn pull-right">
				<button class="btn btn-default btn-info find"><p>Find</p><i class="icon-Search"></i></button>
				<button class="btn btn-default btn-danger clear"><p>Clear</p><i class="icon-Cancel"></i></button>
			</div>
			<div class="findnote pull-left"><i class="icon-Search"></i></div>
			<fieldset class="form-inline findtab">
				<span class="findbasic"></span>
				<span class="findadvanced hide"></span>
			</fieldset>
		</div>
	</div>
	<div class="findbarclear"></div>
	<div class="repeaterwrap fuelux">
		<div class="repeater">
			<div class="repeater-viewport">
				<div class="repeater-canvas"></div>
				<div class="loader repeater-loader"></div>
			</div>
			<div class="repeater-footer hide">
				<div class="repeater-footer-left">
					<div class="repeater-counts"></div>
				</div>
				<div class="repeater-footer-right">
					<div class="repeater-pagination">
						<button type="button" class="btn btn-default btn-sm repeater-prev">
							<span class="glyphicon glyphicon-chevron-left"></span>
							<span class="sr-only">Previous Page</span>
						</button>
						<label class="page-label" id="myPageLabel">Page</label>
						<div class="repeater-primaryPaging active">
							<div class="input-group input-append dropdown combobox">
								<input type="number" class="form-control" aria-labelledby="myPageLabel">
							</div>
						</div>
						<input type="number" class="form-control repeater-secondaryPaging" aria-labelledby="myPageLabel">
						<button type="button" class="btn btn-default btn-sm repeater-next">
							<span class="glyphicon glyphicon-chevron-right"></span>
							<span class="sr-only">Next Page</span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</script> -->

<script type="text/template" id="template-card-form">
	<div class="formload">
		<i class="fa-duotone fa-solid fa-circle-notch fa-spin" aria-hidden="true"></i>
	</div>
	<div class="carddrop offscreen pull-right">
	<i id="card-log-icon" class="fa fa-history"></i>
	<div class="card-log-poppover" ></div>
	<i id="card-bars" class="fa fa-bars"></i></div>
	<div class="cardarea offscreen">

	</div>
	<div class="cardbar offscreen col-md-offset-4 col-md-offset-1">
		<div class="alert alert-danger formerror"></div>
			<div class="form-buttons-bottom hide">
				<button class="btn btn-default btn-primary save">Save</button> <button class="btn btn-default btn-primary cancel" >Close</button>
			</div>
	</div>

</script>

<script type="text/template" id="template-card-read">
	<div class="formload">
		<i class="fa-duotone fa-solid fa-circle-notch fa-spin" aria-hidden="true"></i>
	</div>
	<div class="carddrop offscreen pull-right">
		<i id="card-log-icon" class="fa fa-history"></i>
		<div class="card-log-poppover"></div>
		<i id="card-bars" class="fa fa-bars"></i>
	</div>
	<div class="cardbar offscreen pull-right">
		<span class="readonlymode"> <img src="/homebase/public/img/read-only-icon.svg" /> Read-Only Mode</span>
		<span class="newdata col-md-offset-8 col-md-4 col-xs-offset-8 col-xs-4 hide">New Data</span>
		<span class="statuschange hide">Changes: Pending</span>
		<span class="reviewed hide">Reviewed...</span>
	</div>
	<div class="cardarea offscreen"></div>
</script>