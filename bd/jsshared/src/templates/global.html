<script type="text/template" id="template-nosession">
    <center><span class="alert alert-info">
		Your browser does not support the necessary features
		(sessionStorage API) to access Clara application.
	</span></center>
</script>

<script type="text/template" id="template-ui-lock-dialog">
    <span id="ui-lock-dialog" class="container">
		<div class="brand"><div class="navhead col-md-offset-4 col-md-4 col-xs-offset-4 col-xs-4"></div></div>
		 <div class="row">
			<div class="col-md-offset-4 col-md-4 col-xs-offset-4 col-xs-4">
				<div class="form-login">
					<h3>Your session was locked due to inactivity</h3>
					<h4>
						You have been inactive for more than <%=Math.floor(App.company.ui_lock/60)%> minutes.
						Please re-enter your password to continue.
					</h4>
					<hr/>
					<h4 class="username"></h4>
					<input autocomplete="new-password" type="password" id="unlock-password" class="form-control input-lg chat-input" placeholder="Enter your password to unlock..." />
					<span class="help-block errormsg">&nbsp;</span>
    <div class="wrapper">
        <span class="group-btn">
							<a id="unlock" href="#" class="btn btn-primary btn-lg">Unlock <i class="fa fa-unlock"></i></a>
						</span>
        <span class="group-btn pull-right">
							<a id="logout" href="#" class="btn btn-danger btn-lg">Logout <i class="fa fa-sign-out"></i></a>
						</span>
    </div>
    </div>
    </div>
    </div>
    </span>
</script>

<script type="text/template" id="template-field-file">
    <style>
        html,
        body,
        #body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            width: 100%;
            height: 100%;
        }
        
        #body {
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
        }
    </style>
    <div id="body" onclick="ffile.file.click();"></div>
    <form name="ffile" method="POST" enctype="multipart/form-data" action="/api/file/secure/?callback=window.parent.fileUploaded&callbackid=<%=id%>">
        <input autocomplete="off" accept=".pdf, .png, .jpg, .jpeg, .gif, .zip, .json, .doc, .docx, .xls, .xlsx, .csv,.pptx,.ppt,.odt,.bmp,.odp,.ods" 
        id="file" name="file" type="file" onchange="
        uploadingFiles=true;
        let exceedsLimit=0;
        let err = [];
        if(this.files.length > window.parent.MAX_NUMBER_OF_FILES){
            window.parent.numberOfFilesExceeds(window.parent.MAX_NUMBER_OF_FILES + ' files can be selected at once.');
            return;
        }
        for(let i=0;i<this.files.length;i++){
            if(this.files[i].size > window.parent.MAX_FILE_SIZE){
                exceedsLimit++;
                err.push(this.files[i].name);
            };
        }
        if(exceedsLimit > 0){
            this.value='';
            window.parent.fileTooLarge(err.join(',') + ' exceeds allowed file size. Please Select files less than 100MB');
            return;
        }
        window.parent.prettyNotify('Uploading file...');this.form.submit();">
    </form>
</script>

<script type="text/template" id="template-field-file-write">
    <iframe name="ffileupload" src="javascript:document.write(window.parent.FieldFile.draw_frame('<%=id%>'));" tabindex="-1"></iframe>
    <div class="file-upload-area-container">
        <div class="file-icon-area">
            <div class="icon-container">
                <span class="upload-icon"><span>
            </div>
        </div>
        <div class="file-text-area" >
            <div class="text-container">
                <span class="file-upload-text">
                    <a href="#">Click to upload</a>
                </span>
                <p>PDF, PNG, JPG or GIF (max. 20MB)</p>
            </div>
        </div>
    </div>
</script>


<script type="text/template" id="template-field-file-multiple">
    <style>
        html,
        body,
        #body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            width: 100%;
            height: 100%;
        }
        
        #body {
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
        }
    </style>
    <div id="body" onclick="ffile.file.click();"></div>
    <form name="ffile" method="POST" enctype="multipart/form-data" action="/api/file/secure/?callback=window.parent.filesUploaded&callbackid=<%=id%>">
        <input autocomplete="off" accept=".pdf, .png, .jpg, .jpeg, .gif, .zip, .json, .doc, .docx, .xls, .xlsx, .csv,.pptx,.ppt,.odt,.bmp,.odp,.ods"
         id="file" name="file" type="file" multiple onchange="
            uploadingFiles=true;
            let exceedsLimit=0;
            let err = [];
            let that = this;
            if(this.files.length > window.parent.MAX_NUMBER_OF_FILES){
                window.parent.prettyAlert('Files Limit Exceeds!', window.parent.MAX_NUMBER_OF_FILES + ' files selected, discarding other files', false, function(dialog) {
                    for(let i=0;i<that.files.length;i++){
                        if(that.files[i].size > window.parent.MAX_FILE_SIZE){
                            exceedsLimit++;
                            err.push(that.files[i].name);
                        };
                    }
                    if(exceedsLimit > 0){
                        that.value='';
                        window.parent.fileTooLarge(err.join(',') + ' exceeds allowed file size. Please Select files less than 100MB');
                        return;
                    }
                    window.parent.prettyNotify('Uploading file...');
                    that.form.submit();
                })
            } else {
                for(let i=0;i<this.files.length;i++){
                    if(this.files[i].size > window.parent.MAX_FILE_SIZE){
                        exceedsLimit++;
                        err.push(this.files[i].name);
                    };
                }
                if(exceedsLimit > 0){
                    this.value='';
                    window.parent.fileTooLarge(err.join(',') + ' exceeds allowed file size. Please Select files less than 100MB');
                    return;
                }
                window.parent.prettyNotify('Uploading file...');
                this.form.submit();
            }
            ">
    </form>
</script>

<script type="text/template" id="template-field-file-write-multiple">
    <iframe name="ffileupload" src="javascript:document.write(window.parent.FieldFile.draw_file_multiple('<%=id%>'));"></iframe>
    <button class="btn btn-default btn-primary"><i class="glyphicon glyphicon-plus"></i>&nbsp;Add Multiple Files</button>
</script>