Development:

	BBEdit (commercial, vendor Bare Bones Software Inc.)
		Can use any alternative open-source / freeware text-editor

	PGAdmin (open-source)
		http://www.pgadmin.org/

	XCode (commercial, vendor Apple)
		No alternatives



Libraries:

	Ace Embeddable Code Editor (open-source)
		Licence: BSD license
		https://ace.c9.io/

	Backbone.js 0.9.10 (open-source)
		License: MIT
		http://backbonejs.org
		(c) 2010-2012 <PERSON>, DocumentCloud Inc.

	Bootstrap v3.2.0 (http://getbootstrap.com)
		Copyright 2011-2014 Twitter, Inc.
		Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)

	bootstrap-datetimepicker.js
		Copyright 2012 Stefan Petre
		License: Apache License, Version 2.0
		http://www.malot.fr/bootstrap-datetimepicker

	bootstrap3-dialog
		Copyright <EMAIL>
		License: MIT
		https://github.com/nakupanda/bootstrap3-dialog

	ChartJS
		License: MIT
		http://chartjs.org/

	CryptoJS v3.1.2
		License: New BSD License
		https://code.google.com/p/crypto-js

	Date Format 1.2.3
		License: MIT
		(c) 2007-2009 <PERSON> <stevenlevithan.com>

	FileSize JS
		License: BSD 3
		https://filesizejs.com/

	FontAwesome
		License: MIT

	Fuel UX v3.1.0
		Copyright 2012-2014 ExactTarget
		Licensed under the BSD-3-Clause license ()

	FullCalendar v2.2.3
		Docs: http://arshaw.com/fullcalendar/
		License: MIT - http://fullcalendar.io/license/
		(c) 2013 Adam Shaw

	Hammer.JS - v2.0.4 - 2014-09-28
		License: MIT
		Copyright (c) 2014 Jorik Tangelder;
		http://hammerjs.github.io/

	iCheck v1.0.2
		License: MIT
		Copyright (c) Damir Sultanov
		http://git.io/arlzeA

	IcoMoon (no license, using our own font icons)
		https://icomoon.io/

	jQuery
		License: MIT
		https://jquery.com/

	jQuery UI
		License: MIT
		https://jqueryui.com/

	jQuery blockUI plugin
		License: Dual MIT and GPL
		http://malsup.com/jquery/block/
		https://github.com/malsup/blockui/

	jQuery Mouse Wheel
		License: MIT
		https://github.com/jquery/jquery-mousewheel

	Masked Input plugin for jQuery
		Copyright (c) 2007-2013 Josh Bush (digitalbush.com)
		Licensed under the MIT license (http://digitalbush.com/projects/masked-input-plugin/#license)
		Version: 1.3.1

	moment.js
		version : 2.8.4
		authors : Tim Wood, Iskren Chernev, Moment.js contributors
		license : MIT
		momentjs.com

	PDF.js (open-sourcE)
		https://github.com/mozilla/pdf.js
		license: Apache License 2.0

	PDFMake (open-source)
		http://pdfmake.org/#/
		license: MIT

	Pluralize (open-source)
		https://github.com/blakeembrey/pluralize
		license: MIT

	Prototype Includes
		https://tc39.github.io/ecma262/#sec-array.prototype.includes
		License: BSD

	rrule.js
		License https://github.com/jkbrzt/rrule/blob/master/LICENCE
		https://github.com/jkbrzt/rrule

	Select2
		Copyright 2014 Igor Vaynberg
		Version: 3.5.2 Timestamp: Sat Nov  1 14:43:36 EDT 2014
		License: Apache License, Version 2.0

	Signature Pad
		version: 2.3.2
		https://github.com/szimek/signature_pad
		License: MIT

	Sparkline v2.1.2
		License: BSD
		http://omnipotent.net/jquery.sparkline/

	TouchSwipe v1.6.18
		Copyright (c) 2010-2015 Matt Bryson
		Dual licensed under the MIT or GPL Version 2 licenses
		https://github.com/mattbryson/TouchSwipe-Jquery-Plugin

	Tracking Number Validation
		version: 2.0.1
		https://github.com/niradler/tracking-number-validation
		Author: Nir Adler
		License: MIT



Compiling:

	CoffeeScript v1.7.1
	License: MIT

	Less CSS v1.7.3
	License: Apache 2.0

	NodeJS v10.8.0
	License: MIT

	npm v6.2.0
	License: https://www.npmjs.com/policies/npm-license



Testing:

	Google Chrome Browser (open-source)

	Mozilla Firefox Browser (open-source)

	Apple Safari Browser (bundled with OSX / iTunes, vendor Apple)
