{"license": "UNLICENSED", "name": "jsshared", "packageManager": "pnpm@9.5.0", "repository": {"url": "**************:envoylabs/bd.git", "type": "git"}, "coffee_includes": ["src/coffee/global.coffee", "src/coffee/models/*.coffee", "src/coffee/models/base/*.coffee", "src/coffee/models/dsl/*.coffee", "src/coffee/models/dsl/dsl-record/*.coffee", "src/coffee/models/dsl/dsl-text/*.coffee", "src/coffee/views/*.coffee", "src/coffee/helpers/*.coffee", "src/coffee/views/card/*.coffee", "src/coffee/views/base/*.coffee", "src/coffee/views/base/comm/*.coffee", "src/coffee/views/base/dashboard/*.coffee", "src/coffee/views/base/manage/*.coffee", "src/coffee/views/base/patient/*.coffee", "src/coffee/views/base/shipment/*.coffee", "src/coffee/views/base/interactions/*.coffee", "src/coffee/views/base/intake/*.coffee", "src/coffee/views/base/dispensing/*.coffee", "src/coffee/views/base/inventory/*.coffee", "src/coffee/views/base/operations/*.coffee", "src/coffee/views/base/billing/*.coffee", "src/coffee/views/base/query/*.coffee", "src/coffee/views/base/nursing/*.coffee", "src/coffee/views/dsl/*.coffee", "src/coffee/views/dsl/dsl-fields/*.coffee", "src/coffee/views/dsl/dsl-draw-base/*.coffee", "src/coffee/views/dsl/dsl-draw-ui/*.coffee", "src/coffee/views/print/*.coffee", "src/coffee/views/print/patient/*.coffee", "src/coffee/views/flyout/*.coffee", "src/coffee/views/base/surescripts/*.coffee", "src/coffee/views/base/po/*.coffee"], "devDependencies": {"coffeescript": "~1"}, "dependencies": {"eslint-plugin-coffee": "^0.1.15", "request": "^2.88.0"}, "private": true, "version": "1.0.0", "description": "Clara JS Shared Library"}