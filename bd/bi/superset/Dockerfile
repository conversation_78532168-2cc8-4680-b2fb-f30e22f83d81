FROM apache/superset:latest

USER root
RUN apt-get update && apt-get install -y \
    postgresql-client \
    redis-tools \
    python3-psycopg2 \
    libpq-dev \
    && pip install psycopg2-binary \
    && rm -rf /var/lib/apt/lists/*

# Set up Superset configuration
ENV SUPERSET_CONFIG_PATH=/app/superset_config.py
COPY superset_config.py /app/superset_config.py
COPY superset.sh.start /app/superset.sh.start

RUN chmod +x /app/superset.sh.start

USER superset

EXPOSE 8088

CMD ["/app/superset.sh.start"]
