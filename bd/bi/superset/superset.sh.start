#!/bin/bash
set -ex

superset db upgrade

superset init

if superset fab list-users | grep -q "$SS_ADMIN_USER"; then
    echo "$SS_ADMIN_USER user exists"
else
    echo "User does not exist"
    echo "Creating admin user..."
    superset fab create-admin \
        --username "$SS_ADMIN_USER" \
        --firstname Superset \
        --lastname Admin \
        --email $<EMAIL> \
        --password "$SS_ADMIN_PASS"
fi

/usr/bin/run-server.sh
