#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'd-eshoaib-bi'
primary_region = 'dfw'

[build]
  dockerfile = 'Dockerfile'

[http_service]
  internal_port = 8088
  force_https = true
  auto_stop_machines = 'off'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[services]]
  protocol = 'tcp'
  internal_port = 8088

  [[services.ports]]
    port = 80
    handlers = ['http']

[[vm]]
  memory = "4gb"
  cpu_kind = "performance"
  cpus = 2
