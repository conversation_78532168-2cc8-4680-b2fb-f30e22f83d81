
import os

############## env vars ##############
SECRET_KEY = os.getenv('SUPERSET_SECRET_KEY', 'PLEASE_CHANGE_ME')
GUEST_TOKEN_JWT_SECRET = os.getenv('SS_GUEST_TOKEN_JWT_SECRET', 'PLEASE_CHANGE_ME')
SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'PLEASE_CHANGE_ME')
GUEST_TOKEN_JWT_AUDIENCE = os.getenv('SS_GUEST_TOKEN_JWT_AUDIENCE', 'PLEASE_CHANGE_ME')
############## env vars ##############
GUEST_ROLE_NAME = "Gamma"
# Feature flags for embedding
WTF_CSRF_ENABLED = True
FEATURE_FLAGS = {
    'EMBEDDED_SUPERSET': True,
    'DASHBOARD_NATIVE_FILTERS': True,
}
# Security Settings for Embedding
#FRAME_DOMAINS = [domain.strip() for domain in allowed_domains.split(',')]
ENABLE_PROXY_FIX = True
TALISMAN_ENABLED = False
SESSION_COOKIE_SAMESITE = None
PUBLIC_ROLE_LIKE_GAMMA = True
SUPERSET_WEBSERVER_ADDRESS = "0.0.0.0"
SUPERSET_WEBSERVER_PORT = 8088
