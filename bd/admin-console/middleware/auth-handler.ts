import type { Context } from "../types/base.d.ts"
import type { Response, NextFunction } from "npm:express"
// Todo: Dynamic Error Parsing
let allowedRoles = ['Admin']
export const authHandler = async (ctx: Context, res: Response, next: NextFunction) => {
    const { headers } = ctx
    let ekey = headers['clara-api-key']
    let cookie = headers.cookie;
    if (!ekey && !cookie) {
        res.status(403).json({ status: 403, message: 'Unauthorized.' })
        return
    }
    if (ekey && Deno.env.get('CLARA_AUTH_TOKEN') == ekey) {
        next()
        return
    }
    if (cookie) {
        let bobr = await fetch(`https://admin.envoylabs.net/api/v1/users/me`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                'cookie': headers.cookie
            },
        });
        let r = await bobr.json()
        let db = await ctx?.shared?.db.execute(`select * from users where email = '${r.data.email}'`);
        if (db.length > 0) {
            let user = db[0]
            if (allowedRoles.includes(user.role)) {
                next()
                return
            }
        }

    }
    res.status(403).json({ status: 403, message: 'Unauthorized.' })
}
