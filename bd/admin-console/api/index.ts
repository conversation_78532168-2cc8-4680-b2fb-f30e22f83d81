import type { Context } from "../types/base.d.ts"
import type { Response } from "npm:express"

export const get = async (ctx: Context, resp: Response) => {
    let db = await ctx.shared.db.execute('select now()')
    if(db.length > 0){
        resp.status(200).send({status: 200, message: 'Healthy', now: db[0].now});
        return
    }
    resp.status(500).send({status: 500, message: 'DB Connection Error'});
    
}