import type { Context } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { validate } from "../../middleware/validator.ts"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { isFalsy } from "../../utils/fx.ts";

const getValidateSchema: AnyZodObject = z.object({
    query: z.object({
        slug: z.string(),
        env: z.enum(['prod', 'staging', 'testing', 'dev']).transform((val) => val.toLowerCase().trim()),
        fly_nes: z.string()
    }),
})
export const get = [
    validate(getValidateSchema),
    async (ctx: Context, resp: Response) => {
        let cust = await ctx.shared.db.fetch('customers', {
            limit: 1,
            where: [
                ['appname', 'ilike', ctx.query.slug]
            ]
        })
        if (isFalsy(cust)) {
            resp.status(400).json({ status: 400, message: `No customer found with slug ${ctx.query.slug}` })
            return
        }

        const cust_sec = await ctx.shared.db.fetch('customer_secrets', {
            limit: 1,
            where: [
                ['customer_id', 'eq', cust.id],
                ['env::text', 'ilike', ctx.query.env],
                ['secret', 'eq', ctx.query.fly_nes],
                ['code', 'ilike', 'fly_nes']
            ]
        })
        if (isFalsy(cust_sec)) {
            resp.status(400).json({ status: 400, message: `Configuration not found for env with given secret` })
            return
        }

        // Creating Response
        const contacts = ctx.shared.db.fetch('contacts', {
            where: [
                ['customer_id', 'eq', cust.id],
            ]
        })

        const env_config = ctx.shared.db.fetch('environment', {
            limit: 1,
            where: [
                ['customers_id', 'eq', cust.id],
                ['env::text', 'ilike', ctx.query.env]
            ]
        })

        const key_store = ctx.shared.db.fetch('customer_keystore', {
            where: [
                ['customer_id', 'eq', cust.id],
                ['env::text', 'ilike', ctx.query.env]
            ]
        })

        const memberships = ctx.shared.db.execute(`
            SELECT gm.status, gm.code, gm.comments 
            FROM customer_memberships AS cm
                LEFT JOIN group_membership AS gm ON cm.membership_id = gm.id
            WHERE cm.customer_id = ${cust.id}
        `)

        const feature_flags = ctx.shared.db.execute(`
            SELECT ff.name, ff.comments, ff.frontend, ff.backend, cf.status
            FROM customer_features AS cf
                LEFT JOIN feature_flags AS ff on ff.id = cf.feature_flag_id
            WHERE cf.env::text ilike '${ctx.query.env}' and cf.cust_id = ${cust.id}
        `)
        // Todo: remove get by index
        const result = await Promise.all([contacts, env_config, memberships, feature_flags, key_store])
        cust = { ...cust, contacts: result[0], env_config: result[1], memberships: result[2], feature_flags: result[3], key_store: result[4] }
        resp.status(200).send(cust)
    }
]
