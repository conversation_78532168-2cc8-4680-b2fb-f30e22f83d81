import type { Context, FlyMachine } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { z } from "https://deno.land/x/zod/mod.ts";
import { validate } from "../../../middleware/validator.ts"
import { getType } from "../../../utils/fx.ts";
import { authHandler } from "../../../middleware/auth-handler.ts";
import { Machine } from "../../../helper/machine.ts";
import { sendRequest } from "../../../utils/fx.ts";
const flyServer = "https://api.machines.dev";
import { gql, GraphQLClient } from "https://deno.land/x/graphql_request/mod.ts";

let builderPaths = {
    'buildDeploy': '/build_deploy',
    'buildImage': '/build_image',
    'deployOnly': '/deploy_app',
    'setSecrets': '/set_secrets'
}


export const sendFlyRequest = async (url, token = '', method = 'GET', body = null) => {
    let options = {
        method: method,
        headers: {
            "Content-Type": "application/json",
            'Authorization': `Bearer fo1_8nCYRdIsyDubsq8Dj3p1tkD9LnOKyAgkM0Yhg0BoRTY`,
        }
    }
    if (token && typeof token === 'string' && token !== '') {
        options.headers['Authorization'] = `Bearer ${token}`
    }
    if (body) {
        options.body = JSON.stringify(body)
    }
    let res = await fetch(flyServer + url, options)
    return res;
}
async function updateMachines(appName: string, machinesArray) {
    for (let machine of machinesArray) {
        let url = `/v1/apps/${appName}/machines/${machine.id}`;
        let config = machine.config;
        let body = {
            "config": config
        }
        console.log("Updating machine with id: " + machine.id);
        let resp = await sendFlyRequest(url, '', 'POST', body);
        if (!resp.ok) {
            console.error(resp)
            throw new Error("Update Failed App Name: " + appName + " Machine ID: " + machine.id)
        } else {
            console.log(`App Name ${appName} Machine ID: ${machine.id} Updated`)
            resp = await resp.json();
        }
    }
    return { error: false, message: `Machines for App ${appName} updated successfully` };
}
async function getMachinesByAppName(appName: string): Promise<FlyMachine[]> {
    let url: string = '/v1/apps/' + appName + '/machines';
    let machinesArray: FlyMachine[] = [];
    let resp = await sendFlyRequest(url);
    if (!resp.ok) {
        throw new Error("Fly Api Error: " + resp.statusText)
    } else {
        machinesArray = await resp.json();
    }
    return machinesArray;
}

async function setSecrets(appID, secrets, token) {
    // we don't have rest API at the time of using this
    const endpoint = "https://api.fly.io/graphql";
    const graphQLClient = new GraphQLClient(endpoint, {
        headers: {
            authorization: `Bearer ${token}`,
        },
    });

    const query = gql`
    mutation($input: SetSecretsInput!) {
        setSecrets(input: $input) {
            app {
                id
            }
            }
        }
    `;
    const variables = { input: { appId: appID, secrets: secrets } };
    const data = await graphQLClient.request(query, variables);
    return data;
}
async function restartMachinesArray(appName: string, machinesArray: FlyMachine[]) {
    await updateMachines(appName, machinesArray);
    console.log("Machine restarted successfully")
    return { error: false, message: `Machines for App ${appName} restarted successfully` };
}
const setSecretsAndRestart = async (data) => {
    const token = Deno.env.get('FLY_AUTH_TOKEN')
    const appName = `${data.env[0].toLowerCase()}-${data.customer}-nes`
    await setSecrets(appName, data.secrets, token);
    let machinesArray: FlyMachine[] = await getMachinesByAppName(appName);
    return await restartMachinesArray(appName, machinesArray);
}
const requestHandler = async (ctx: Context, resp: Response) => {
    let body = ctx.method == 'GET' ? ctx.query : ctx.body
    body.customer_id = parseInt(body.customer_id)
    let { env, customer_id, tag, event, secrets } = body
    if (event != 'setSecrets' && !tag) {
        resp.status(400).send({ status: 400, message: 'Missing tag in body' });
        return
    }
    if (event == 'dbmDeploy' && !secrets && getType(secrets) != 'object') {
        resp.status(400).send({ status: 400, message: 'Missing secrets object' });
    }
    let m = new Machine({ custID: customer_id, ctx, env })
    if (event == 'dbmDeploy') {
        await m.createOrUpdateSecrets(secrets)
        event = 'buildDeploy'
    }
    body['infra'] = await m.getInfra();
    body['secrets'] = await m.getSecrets();
    try {
        let bobr = await setSecretsAndRestart(body);
        console.log(`Secrets set successfully for ${body.env[0].toLowerCase()}-${body.customer}-nes`)
        resp.status(200).send(bobr);
    } catch (error) {
        resp.status(500).send(error.response.errors);
    }
}


const validateStruct = z.object({
    customer_id: z.preprocess(Number, z.number()),
    env: z.enum(['Prod', 'Staging', 'Testing', 'Dev']),
    event: z.enum(['buildDeploy', 'buildImage', 'deployOnly', 'setSecrets', 'dbmDeploy'])
})

export const get = [
    authHandler,
    validate(z.object({ query: validateStruct })),
    requestHandler,
]

export const post = [
    authHandler,
    validate(z.object({ body: validateStruct })),
    requestHandler,
]