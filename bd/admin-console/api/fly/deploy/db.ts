import type { Context } from "../../types/base.d.ts"
import type { Response } from "npm:express"
import { z } from "https://deno.land/x/zod/mod.ts";
import { validate } from "../../../middleware/validator.ts"
import { authHandler } from "../../../middleware/auth-handler.ts";
import { sendRequest } from "../../../utils/fx.ts";

const validateStruct = z.object({
  customer_id: z.preprocess(Number, z.number()),
  customer: z.string(),
  env: z.enum(['Prod', 'Staging', 'Testing', 'Dev']),
  tag: z.string(),
  event: z.enum(['cloneDB', 'createDBFromSnapshot', 'modifyDbInstance']),
})

const requestHandler = async (ctx: Context, resp: Response) => {
  let body = ctx.method == 'GET' ? ctx.query : ctx.body
  body.customer_id = parseInt(body.customer_id)
  body.db_id = `${body.env.toLowerCase()}-${body.customer}-nes`;
  body.snap_id = Deno.env.get('BASE_SNAP');
  let dbmr = await sendRequest("POST", "DBM_URL", '/database', body)
  let r = await dbmr.json()
  resp.status(r.status || 500).send(r);
  return
}


export const get = [
  authHandler,
  validate(z.object({ query: validateStruct })),
  requestHandler,
]

export const post = [
  authHandler,
  validate(z.object({ body: validateStruct })),
  requestHandler,
]