import type { Context } from "../../types/base.ts"
import type { Response } from "npm:express"
import { validate } from "../../middleware/validator.ts"
import { z, AnyZodObject } from "https://deno.land/x/zod/mod.ts";
import { Machine } from "../../helper/machine.ts";
import { authHandler } from "../../middleware/auth-handler.ts";

const getValidateSchema: AnyZodObject = z.object({
    body: z.object({
        customer_id: z.number(),
        env: z.enum(['Prod', 'Staging', 'Testing', 'Dev'])
    }),
})
export const post = [
    authHandler,
    validate(getValidateSchema),
    async (ctx: Context, resp: Response) => {
        let { customer_id, env, secrets } = ctx.body;
        let m = new Machine({ custID: customer_id, ctx, env })
        let r = await m.createOrUpdateSecrets(secrets)
        resp.status(200).send({ status: 200, message: r })
    }
]
