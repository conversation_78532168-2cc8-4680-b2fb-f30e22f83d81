
const REQUIRED_CONFIGS = ['CLARA_AUTH_TOKEN', 'DBM_URL', 'BUILDER_URL', 'BASE_SNAP']
export const missingConfig = () => {
  let missing = []
  REQUIRED_CONFIGS.forEach((k) => {
    if (!Deno.env.get(k)) {
      missing.push(k)
    }
  })
  return missing
}

export const getType = (val: any): string => {
  if (val === null) {
    return 'null'
  }
  if (typeof val === 'boolean') {
    return 'boolean'
  }
  if (typeof val === 'string') {
    return 'string'
  }
  if (typeof val === 'number') {
    return 'number'
  }
  if ((typeof val === 'object') && (val instanceof Array)) {
    return 'array'
  }
  if ((typeof val === 'object') && !(val instanceof Array)) {
    return 'object'
  }
  return 'undefined'
}


export const isFalsy = (val: any): boolean => {
  const tp: string = getType(val)
  let falsy = false
  if (['null', 'boolean', 'string', 'number', 'undefined'].includes(tp)) {
    falsy = !val
  } else {
    if (tp == 'array' && val.length == 0) {
      falsy = true
    } else if (tp == 'object' && Object.keys(val).length == 0) {
      falsy = true
    }
  }
  return falsy
}

export const equalIgnoreCase = (...args: Array<string>) => {
  return new Set(args.map(v => v.toLowerCase())).size == 1
}

export const pickKeys = (obj, keys: Array<String>, def: object) => {
  let trimed: object = {}
  keys.forEach((k) => {
    trimed[k] = obj[k] || def[k] || null
  })
  return trimed
}

export const sendRequest = async (method, server, path, body) => {
  let res = await fetch(`${Deno.env.get(server)}${path}`, {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "clara-api-key": Deno.env.get('CLARA_AUTH_TOKEN'),
    },
    body: JSON.stringify(body),
  })
  return res;
}
