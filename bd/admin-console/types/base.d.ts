import type { Request } from "npm:express"
export interface Context extends Request {
    query: Query,
    shared: Shared,
    headers: Head<PERSON> & { cookie: string },
    body: {
        customer_id: string, env: string, secrets: Array<any>
    }
}
export interface Query {
    slug: String,
    env: 'prod' | 'staging' | 'testing' | 'dev',
    fly_nes: String
}
export interface DBConnection {
    execute: Function
    exists: Function
    fetch: Function
}

export interface Machine {
    getCustomer: Function
    createOrUpdateSecrets: Function
    getSecrets: Function
    createSecret: Function
    updateSecret: Function
    updateStatus: Function
}

export interface ExecuteOptions {
    dump: boolean,
}
export interface DBFilter {
    //field, cond, val
    where: Array<[string, string, any]>,
    limit?: number
}
export interface Logger {
    info: Function,
    debug: Function,
    warn: Function,
    error: Function
}
export interface Shared {
    db: DBConnection,
    logger: Logger,
}


interface FlyMachine {
    id: string;
    name: string;
    state: string;
    region: string;
    image_ref: ImageRef;
    instance_id: string;
    private_ip: string;
    created_at: string;
    updated_at: string;
    config: any; // Replace with the actual type if known
    events: Event[];
}


interface ImageRef {
    registry: string;
    repository: string;
    tag?: string;
    digest: string;
}
