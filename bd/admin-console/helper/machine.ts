import type { Context } from "../../types/base.d.ts"
import { isFalsy, getType, pickKeys } from "../utils/fx.ts";
import { MachineError } from "../utils/exceptions.ts";

let infraDefaults = {
    'Prod': {
        "vm_type": "dedicated-cpu-1x",
        "min_instance_count": "2",
        "max_instance_count": "4",
        "primary_region": "ord",
        "secondary_region": "dfw",
        "ram": "2048"
    },
    'Staging': {
        "vm_type": "dedicated-cpu-1x",
        "min_instance_count": "1",
        "max_instance_count": "2",
        "primary_region": "ord",
        "secondary_region": "dfw",
        "ram": "1024"
    },
    'Testing': {
        "vm_type": "dedicated-cpu-1x",
        "min_instance_count": "1",
        "max_instance_count": "2",
        "primary_region": "sin",
        "secondary_region": "ord",
        "ram": "1024"
    },
    'Dev': {
        "vm_type": "dedicated-cpu-1x",
        "min_instance_count": "1",
        "max_instance_count": "2",
        "primary_region": "sin",
        "secondary_region": "ord",
        "ram": "1024"
    }
}

export class Machine {
    custID: number = 0
    env: string = ''
    ctx: Context = null
    constructor({ ctx, custID, env }) {
        custID = parseInt(custID)
        if (getType(custID) != 'number' || !env || !ctx) {
            throw new MachineError('Invalid machine credentials.')
        }
        this.custID = custID
        this.env = env
        this.ctx = ctx
    }
    async getCustomer() {
        let cust = await this.ctx.shared.db.fetch('customers', {
            limit: 1,
            where: [
                ['id', 'eq', this.custID]
            ]
        })
        if (isFalsy(cust)) {
            throw new MachineError(`Customer Not Found`)
        }
        return cust
    }

    async getSecrets() {
        let cust = await this.getCustomer()
        const cust_sec = await this.ctx.shared.db.fetch('customer_secrets', {
            where: [
                ['customer_id', 'eq', cust.id],
                ['env::text', 'ilike', this.env],
            ]
        })
        return cust_sec.map((e) => { return { "key": e.code, "value": e.secret } })
    }

    async getInfra() {
        let keys = ["vm_type", "min_instance_count", "max_instance_count", "primary_region", "secondary_region", "ram"]
        let cust = await this.getCustomer()
        const infra = await this.ctx.shared.db.fetch('environment', {
            limit: 1,
            where: [
                ['customers_id', 'eq', cust.id],
                ['env::text', 'ilike', this.env],
            ]
        })
        return pickKeys(infra, keys, infraDefaults[this.env])
    }

    async createOrUpdateSecrets(secrets: object) {
        let created: Array<string> = []
        let updated: Array<string> = []
        if (isFalsy(secrets) || getType(secrets) != 'object') {
            return { created, updated }
        }
        let newSec = Object.keys(secrets)
        let cust = await this.getCustomer()

        const cust_sec = await this.ctx.shared.db.fetch('customer_secrets', {
            where: [
                ['customer_id', 'eq', cust.id],
                ['env::text', 'ilike', this.env],
            ]
        })

        for (let i = 0; i < newSec.length; i++) {
            let code = newSec[i]
            let code_id = null
            cust_sec.forEach((sec) => {
                if (code == sec.code) {
                    code_id = sec.id;
                }
            })
            if (code_id) {
                await this.updateSecret(code_id, secrets[code])
                updated.push(code)
            } else {
                await this.createSecret(code, secrets[code])
                created.push(code)
            }
        }
        return {
            created,
            updated
        }
    }
    // Prod Staging Testing Dev
    async createSecret(code, secret) {
        await this.ctx.shared.db.execute(`
            INSERT INTO public."customer_secrets" ("customer_id","code","secret", "env")
            VALUES (${this.custID}, '${code}','${secret}', '${this.env}');
        `)
    }

    async updateSecret(code_id, secret) {
        await this.ctx.shared.db.execute(`
            UPDATE public."customer_secrets" SET "secret" = '${secret}'
            WHERE id = ${parseInt(code_id)};
        `)
    }

    async updateStatus(msg: any) {

    }

}