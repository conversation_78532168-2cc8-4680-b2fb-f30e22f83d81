
import { DenonConfig } from "https://deno.land/x/denon@2.5.0/mod.ts";
import { config as env } from "https://deno.land/x/dotenv/mod.ts";

const config: DenonConfig = {
  scripts: {
    start: {
      cmd: "deno run --allow-read --allow-net --allow-env --allow-write --unstable --no-check index.ts",
      desc: "Starting Server",
      env: env(),
    },
    dev: {
      cmd: "denon run --allow-read --allow-net --allow-env --allow-write --unstable --no-check index.ts",
      desc: "Starting Dev",
      env: env(),
    },
  },
};

export default config;