domain: envoylabs.net
prod_domain: clararx.com
fqdn: "{{ ansible_hostname }}.{{ domain }}"
srv_groups:
  - elabs_dev
  - elabs_ops
srv_users:
  - username: byoung
    group: elabs_dev,elabs_ops,docker
  - username: pdenney
    group: elabs_dev,elabs_ops,docker
  - username: cmehta
    group: elabs_dev,elabs_ops,docker
  - username: ozubair
    group: elabs_dev,elabs_ops,docker
  - username: mshoaib
    group: elabs_dev,elabs_ops,docker

common_packages:
  - git
  - jq
  - tmux
  - awscli
  - build-essential
  - python3-boto3
  - python3-pip
  - apt-transport-https
  - ca-certificates
  - curl
  - software-properties-common
  - libsystemd-dev
  - unzip

docker_packages:
  - "docker-ce"
  - "docker-ce-cli"
  - "containerd.io"

needed_packages:
  - gnupg
  - gpg

compose_version: 2.4.1
install_docker: true
