---
domain: clararx.com
fqdn: "{{ ansible_hostname }}.{{ domain }}"
# These would only have write access to update route53 records
aws_access_key: "{{ lookup('env', 'AWS_ACCESS_KEY') }}"
aws_secret_key: "{{ lookup('env', 'AWS_SECRET_KEY') }}"
aws_domain_zone: clararx.com
aws_domain_zone_id: Z074474919X4TQK7GS6XY
fly_root_domain: fly.dev

fly_servers:
  dev:
      - hostname: d-eosama-nes
        shortname: osama
      - hostname: d-ebrandon-nes
        shortname: byoung
      - hostname: d-clararx-nes
        shortname: dev
      - hostname: d-altaf-nes
        shortname: altaf
      - hostname: d-eali-nes
        shortname: ali
      - hostname: d-earbaz-nes
        shortname: arbaz
      - hostname: d-epatrick-nes
        shortname: patrick
      - hostname: d-eshoaib-nes
        shortname: shoaib
  stage:
      - hostname: d-heritage-nes
        shortname: heritage-demo
  prod:
