
# Example config file for ansible -- https://ansible.com/
# =======================================================

# Nearly all parameters can be overridden in ansible-playbook
# or with command line flags. Ansible will read ANSIBLE_CONFIG,
# ansible.cfg in the current working directory, .ansible.cfg in
# the home directory, or /etc/ansible/ansible.cfg, whichever it
# finds first

# For a full list of available options, run ansible-config list or see the
# documentation: https://docs.ansible.com/ansible/latest/reference_appendices/config.html.

[defaults]
inventory       = inventory/servers
allow_world_readable_tmpfiles = true
host_key_checking = False
gathering = implicit
become_exe = sudo
timeout = 10
stdout_callback = yaml
[ssh_connection]
ssh_args = -o ForwardAgent=yes -o ServerAliveInterval=30 -o ControlMaster=auto -o ControlPersist=10m
pipelining = True
[accelerate]
accelerate_port = 5099
accelerate_timeout = 30
accelerate_connect_timeout = 5.0
accelerate_daemon_timeout = 30

[privilege_escalation]
become = yes
become_method = sudo

command_warnings = False
deprecation_warnings = False
