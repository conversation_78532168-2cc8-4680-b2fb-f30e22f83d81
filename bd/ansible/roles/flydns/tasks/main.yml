---
- include_vars: group_vars/dns.yml
  connection: local


- name: Install boto3 and botocore with pip3 module
  pip:
    name: 
      - boto3
      - botocore

- name: create all host records
  set_fact:
    all_hosts = "{{ fly_servers.values()| flatten(1)}}"
  when: env is not defined

- debug:
    msg: "{{ item.hostname }}"
  loop: "{{fly_servers.values()| flatten(1)}}"
    

- debug:
    var: all_hosts
- name: create r53 records for main hostname
  route53:
    state: present
    access_key: "{{ lookup('env', 'AWS_ACCESS_KEY') }}"
    secret_key: "{{ lookup('env', 'AWS_SECRET_KEY') }}"
    state: present
    ttl: 300
    type: CNAME
    record: "{{ item.hostname }}.{{ domain }}"
    value: "{{ item.hostname }}.{{ fly_root_domain }}"
    hosted_zone_id: "{{ aws_domain_zone_id }}"
  loop: "{{ fly_servers.values()| flatten(1) if env is undefined else fly_servers[env] }}"
  delegate_to: 127.0.0.1
  when: fly_servers is defined 

- name: create r53 records for shortname
  route53:
    state: present
    access_key: "{{ lookup('env', 'AWS_ACCESS_KEY') }}"
    secret_key: "{{ lookup('env', 'AWS_SECRET_KEY') }}"
    state: present
    ttl: 300
    type: CNAME
    record: "{{ item.shortname }}.{{ domain }}"
    value: "{{ item.hostname }}.{{ fly_root_domain }}"
    hosted_zone_id: "{{ aws_domain_zone_id }}"
  delegate_to: localhost
  loop: "{{ fly_servers[env] if env is defined else fly_servers.values()| flatten(1) }}"
  when: fly_servers is defined 
