---
- name: Apt  update the os.
  apt:
    upgrade: dist
    update_cache: yes
  register: apt_result
  retries: 100
  delay: 5
  until: apt_result is success or ('Failed to lock apt for exclusive operation' not in apt_result.msg and '/var/lib/dpkg/lock' not in apt_result.msg)
  when: packer_build is defined

- name: Add Docker s GPG key for ubuntu from official site
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  tags:
    - docker
  when: install_docker|bool == true

- name: Verify gpg key with the fingerprint
  apt_key:
    id: 0EBFCD88
    state: present
  tags:
    - docker
  when: install_docker|bool == true

- name: Configure Docker for ubuntu stable repository
  apt_repository:
    repo: deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable
    state: present
    update_cache: yes
  tags:
    - docker
  when: install_docker|bool == true
  until: apt_result is success or ('Failed to lock apt for exclusive operation' not in apt_result.msg and '/var/lib/dpkg/lock' not in apt_result.msg)

- name: install common packages
  apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  with_items: "{{ common_packages }}"
  tags:
    - install_common
  register: apt_result
  retries: 100
  delay: 5
  until: apt_result is success or ('Failed to lock apt for exclusive operation' not in apt_result.msg and '/var/lib/dpkg/lock' not in apt_result.msg)

- name: install docker packages
  apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  with_items: "{{ docker_packages }}"
  tags:
    - install_docker
  register: apt_result
  retries: 100
  delay: 5
  until: apt_result is success or ('Failed to lock apt for exclusive operation' not in apt_result.msg and '/var/lib/dpkg/lock' not in apt_result.msg)
  when: install_docker|bool == true

- name: Install docker-compose latest from official repo
  get_url:
    url : "https://github.com/docker/compose/releases/download/v{{ compose_version}}/docker-compose-linux-x86_64"
    dest: /usr/local/bin/docker-compose
    mode: 0755
    group: docker
  tags:
    - install_common
    - install_compose
  when: install_docker|bool == true

- name: create groups
  group:
    name: "{{ item }}"
    state: present
  with_items: "{{ srv_groups }}"
  when: srv_groups is defined
  tags:
    - create_groups
    - user_and_group

- name: create users
  user:
    name: "{{ item.username }}"
    groups: "{{ item.group }}"
    createhome: yes
    shell: /bin/bash
  with_items: "{{ srv_users }}"
  when: srv_users is defined
  tags:
    - create_users
    - user_and_group

- name: adding ssh keys to authorized_keys.
  authorized_key:
    user: "{{ item.username }}"
    key: "{{ lookup('file', 'keys/user/'+ item.username + '.pub') }}"
  with_items: "{{ srv_users }}"
  when: srv_users is defined
  tags:
    - add_authorized_keys
    - user_and_group

- name: remove old users
  user:
    name: "{{ item }}"
    state: absent
    force: yes
  with_items: "{{ deleted_users }}"
  when: deleted_users is defined
  tags:
    - delete_users
    - user_and_group

- name: create sudoers file
  template:
    src: sudoers.j2
    dest: /etc/sudoers.d/bs_sudoers
    mode: 0440
    validate: 'visudo -cf %s'
  when: srv_groups is defined and srv_users is defined
  tags:
    - create_sudoers_file
    - user_and_group

- debug: var=inventory_hostname
  tags:
    - set_hosts

- name: set hostname on system
  hostname:
    name: "{{ inventory_hostname }}"
  tags:
    - set_hosts

- name: set sshd_config
  template:
    src: sshd_config.j2
    dest: /etc/ssh/sshd_config
    mode: 0644
    validate: "/usr/sbin/sshd -t -f %s"
  notify: reload_sshd
  tags:
    - ssh_config

- name: set hostname in /etc/hostname
  template: src=hostname.j2 dest=/etc/hostname mode=0644
  tags:
    - set_hosts

- name: set hosts file
  template:
    src: hosts.j2
    dest: /etc/hosts
    mode: 0644
  tags:
    - set_hosts
    - vagrant
