openapi: 3.0.0
info:
  title: Console API
  description: >-
    # Introduction

    Console APIs provide different serverices they ability to access configuration from a centralized location.

    # Authentication

    Console apis currently offer authentication through fly_nes secrets, each sercet is linked to a single fly application and only allow user to access that applications configuration.

    <SecurityDefinitions />


  version: 0.1.0
servers:
  - url: https://admin.envoylabs.net/
tags:
  - name: Config
paths:
  /service/:
    get:
      tags:
        - Config
      description: 'Checks server status and returns 200 if server is running with an established database connection.'
      summary: Healthy
      parameters: []
      x-codeSamples:
        - lang: 'cURL'
          label: 'CLI'
          source: |
            curl --request GET \
            --url "https://admin.envoylabs.net/service/"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
              example:
                status: 200
                message: Healthy
                now: '2023-01-10T14:40:21.502Z'
        '500':
          description: Database Connection Error
          content:
            application/json:
              schema:
                type: object
              example:
                status: 500
                message: 'DB Connection Error'
  /service/config/nes/:
    get:
      tags:
        - Config
      summary: Fly Config
      description: Returns app configuration/setting, used by fly and nes
      x-codeSamples:
        - lang: 'cURL'
          label: 'CLI'
          source: |
            curl --request GET \
            --url "https://admin.envoylabs.net/service/config/nes/?slug=doc&fly_nes=d39ix7l0YGOsz1wtf0LQd67VX9F9xpj6&env=dev"        
      parameters:
        - name: slug
          in: query
          required: true
          schema:
            type: string
          example: 'doc'
        - name: fly_nes
          in: query
          required: true
          schema:
            type: string
          example: 'hIl738SpGcpyhhP'
        - name: env
          in: query
          required: true
          schema:
            type: string
            items:
              enum:
                - prod
                - staging
                - test
                - dev
              default: dev
      responses:
        '200':
          description: OK
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json; charset=utf-8
          content:
            application/json:
              schema:
                type: object
              example:
                name: Dev Docu
                appname: doc
                email: <EMAIL>
                created_on: '2022-11-21T17:33:39.516Z'
                contacts: []
                feature_flags: []
                slug: doc
                location: ''
                app_link: ''
                parent_corp: ''
                size: ''
                region: ''
                industry: ''
                address: null
                user_count: '999'
                s3_url: null
                login_html: null
                vm_type: null
                instance_count: null
                primary_region: null
                secondary_region: null
                env: null
                type: Demo
                secrets:
                  - id: 17
                    customer_id: 3
                    code: fly_nes
                    secret: d39ix7l0YGOsz1wtf0LQd67VX9F9xpj6
                    env: Dev
                  - id: 18
                    customer_id: 3
                    code: DATABASE_URL
                    secret: >-
                      *******************************************************************/hb?sslmode=disable
                    env: Dev
                  - id: 19
                    customer_id: 3
                    code: ENV_DB_URL
                    secret: >-
                      *******************************************************************/hb?sslmode=disable
                    env: Dev
                env_config:
                  id: 7
                  s3_url: ''
                  login_html: ''
                  vm_type: ''
                  instance_count: ''
                  env: Dev
                  customers_id: 3
                  secondary_region: ord
                  primary_region: ord
                memberships: []
        '400':
          description: Bad Request
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json; charset=utf-8
          content:
            application/json:
              schema:
                type: object
              examples:
                example-0:
                  summary: Invalid Env Type
                  value:
                    issues:
                      - received: env
                        code: invalid_enum_value
                        options:
                          - prod
                          - staging
                          - testing
                          - dev
                        path:
                          - query
                          - env
                        message: >-
                          Invalid enum value. Expected 'prod' | 'staging' |
                          'testing' | 'dev', received 'env'
                    name: ZodError
                example-1:
                  summary: Missing Required Query Params
                  value:
                    issues:
                      - code: invalid_type
                        expected: string
                        received: undefined
                        path:
                          - query
                          - slug
                        message: Required
                      - expected: '''prod'' | ''staging'' | ''testing'' | ''dev'''
                        received: undefined
                        code: invalid_type
                        path:
                          - query
                          - env
                        message: Required
                      - code: invalid_type
                        expected: string
                        received: undefined
                        path:
                          - query
                          - fly_nes
                        message: Required
                    name: ZodError
  /service/secret:
    post:
      tags:
        - Infra
      summary: App Secrets
      parameters:
        - name: Clara-API-Key
          in: header
          schema:
            type: string
        - name: Authentication
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                secrets:
                  MANDRILL_KEY: Qwc4234zdfSADE4
                  PUBLIC_ENV: d#fsqwesdfsdf
                env: Dev
                customer_id: 7
                customer: ejawi
      responses:
        '200':
          description: Successful response
  /service/fly/deploy/app:
    post:
      tags:
        - Infra
      summary: Deploy App
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                customer: eahmad
                customer_id: 8
                env: Testing
                event: setSecrets
      parameters:
        - name: Authorization
          in: header
          schema:
            type: string
        - name: Clara-API-Key
          in: header
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}
        '400':
          description: Bad Request
          content:
            application/json: {}
  /service/fly/deploy/db:
    post:
      tags:
        - Infra
      summary: Deploy DB
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example:
                customer_id: 18
                customer: ewahab
                env: Dev
                tag: HB-4626
                event: createDBFromSnapshot
      parameters:
        - name: Clara-API-Key
          in: header
          schema:
            type: string
        - name: Authorization
          in: header
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json: {}
