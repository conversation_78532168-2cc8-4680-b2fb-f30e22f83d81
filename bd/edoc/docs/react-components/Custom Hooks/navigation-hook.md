---
sidebar_position: 19
---

# useNavigation

useNaviagtion is a custom hook use for setting a route component and also indicates the tree from parent to its child. It manages the routes from parent to child and child to parent.

# Location

It can be find at `src/core/navigation`

# Description
In the given usage shown `useNavigation` hook sets the component route in which it can be accessable. It also depends on what is the parent of this child component so that it manages the route and set final route. for instance if you have parent which is `managment` and the chid compononet is `drugs` and within that child component you have this component `MyReactPage` so it will make a route i.e `/managment/drugs/MyReactPage` .It also indicates whether its parent is active or not and this component is active or not.

# Usage

``` jsx useNaviagation Hook
import { useNavigation } from 'src/core/navigation'

export default function Management(props) {
    const nav = useNavigation(props, '/management')

    return <Drugs {...nav}/>
    
}
```

``` jsx useNaviagation Hook
import { useNavigation } from 'src/core/navigation'

export default function Drugs(props) {
   const nav =  useNavigation(props, '/drugs')

    return <MyReactPage {...nav}/>
}
```

``` jsx useNaviagation Hook
import { useNavigation } from 'src/core/navigation'

export default function MyReactPage(props) {
    useNavigation(props, '/MyReactPage')

    return (
        //.......JSX Code.......
    )
}
```

# Routed Component Props
Props | type | default | Description
--- | --- | --- | ---
isActive | boolean | false | if the form is active or not
isParentActive | boolean | false | if the parent of the form is active or not
navToURL | string | '' | URL for external linking
urlCallback | void | () => {} | Callback function when URL redirects to form

# Example URL

![Navigation Sample URL](../../../static/img/navigation-url.png)

# Example UI

![Navigation Sample URL](../../../static/img/navigation.png)

