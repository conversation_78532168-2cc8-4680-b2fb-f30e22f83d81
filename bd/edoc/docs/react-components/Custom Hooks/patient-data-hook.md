---
sidebar_position: 18
---

# usePatientData (Deprecated) 

usePatientData is a custom hook use for getting data of patient and setting in state.

# Location

It can be find at `src/hooks/patient-data`

# Description
In the given usage shown `usePatientData` returns two things in a state one is `refresh` function that is use to refresh the component and other is `patient` i.e 
data of patient

# Usage

``` jsx usePatientData Hook
import { usePatientData } from "src/hooks/patient-data"

export default function MyReactPage() {
    const [patient, refresh] = usePatientData(props.linkMap.linkid.patient)

	useEffect(() => {
		refresh()
	}, [props.updateNeed])

    return (
        //.......JSX Code.......
    )
}
```

# Parameters

Props | type | default | Description
--- | --- | --- | ---
patient_id | number or string | | Id of patient 


