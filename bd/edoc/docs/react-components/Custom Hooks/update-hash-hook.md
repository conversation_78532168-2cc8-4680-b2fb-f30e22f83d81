---
sidebar_position: 18
---

# useUpdateHash

useUpdateHash is a custom hook use for getting generated hash to keep trak for rendering oof component 

# Location

It can be find at `src/hooks/update-hash`

# Description
In the given usage shown `useUpdateHash` returns two things in a state one is `updateNeed` shows update needed or not and the other is `updateRefreshHash` function to update hash.

# Usage

``` jsx useUpdateHash Hook
import { useUpdateHash } from "src/hooks/update-hash";

export default function MyReactPage() {
    const [updateNeed, updateRefreshHash] = useUpdateHash()

    return (
        //.......JSX Code.......
    )
}
```

