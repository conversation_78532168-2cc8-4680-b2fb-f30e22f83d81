---
sidebar_position: 3
---

# DSL Grid View

DSL Grid View Component shows the grid view of a form

# Location

It can be find at `src/blocks/dsl-grid-view`

# Usage

``` jsx DSL Grid View
import DSLFindView from "src/blocks/dsl-grid-view/dsl-grid-view"

export default function MyReactPage() {
  return (
    <DSLGridView
      link='patient' // If you want to limit data againts particular link
      links={['patient']}
      linkid={{patient: 1}}
      form={form}
      setRef={(ref: DSLGridViewRef) => {
        refs.grid = ref
      }}
      onRowEvent={(opts) => {
        // On Table Row Click
      }}
    />
  );
}
```
# Props

Props | type | default | Description
--- | --- | --- | ---
form | string | '' | Form name
gridparams | object |  | For grid params
setRef | Function | optional | For setting the ref
onRowEvent | Function | optional | When clicking on row this event is called
link | string |  | To link
links | string[] |  | All links
linkid | number |  | Link id 

# Example

![DSL Grid View](../../../static/img/grid-view.png)


