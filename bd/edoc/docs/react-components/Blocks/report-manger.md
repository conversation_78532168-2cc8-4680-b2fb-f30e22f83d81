---
sidebar_position: 8
---

# Report Manager

The Report Manager component will be utilized to display, download, and print reports based on the provided code.

# Location

It can be find at `/src/components/arjs`

# Usage

``` jsx report-manager
// Import the ReportManager component from the specified path
import { ReportManager } from "../../components/arjs/report-manager";

// Invoke the ReportManager component inside your own component
// Use it to view a report with the specified actionType, parameters, and code
return (
  <ReportManager
    actionType="view"
    parameters={{ patient_id: "23" }}
    code="patient_snap"
  />
);

```
# Props

Props | type | default | Description
--- | --- | --- | ---
code | string | undefined | It would be a report code that has been designed in the /setting/report or/setting/report-template.
actionType | string | undefined | Expected values are 'download' , 'print' , 'view'
parameters? | object | null | The values, such as `{patient_id: '121', intake_id: '5'}`, are determined by the settings configured during the design of the report in the Report Parameters.
