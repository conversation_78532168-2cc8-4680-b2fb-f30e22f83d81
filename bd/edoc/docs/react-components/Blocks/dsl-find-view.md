---
sidebar_position: 2
---

# DSL Find View

Its used to render DSL driven find/filter view

# Location

It can be find at `src/blocks/dsl-find-view`

# Usage

```jsx DSL Find View
import DSLFindView from "src/blocks/dsl-find-view/dsl-find-view";

export default function MyReactPage() {
  return (
    <DSLFindView
      link=""
      links={[]}
      linkid={{}}
      form={"patient"}
      setRef={(ref: DSLFindRef) => {
        //Returns DSLFindView Ref
        refs.find = ref;
      }}
      onFind={(event) => {
        //On Find Click
      }}
      onClear={(event) => {
        //On Clear Click
      }}
    />
  );
}
```

# Props

| Props      | type     | default                  | Description                                        |
| ---------- | -------- | ------------------------ | -------------------------------------------------- |
| form       | string   | ''                       | Form name                                          |
| preset     | object   | `{ [key: string]: any }` | If wanted to preset                                |
| allowRadio | boolean  | false                    | Allow radio or not                                 |
| setRef     | Function | optional                 | For setting the ref                                |
| onFind     | Function | optional                 | When onFind button clicks this function is called  |
| onClear    | Function | optional                 | When onClean button clicks this function is called |
| onChange   | Function | optional                 | When any change is set this function is called     |
| onClear    | Function | optional                 | When onClear button clicks this function is called |
| link       | string   |                          | To link                                            |
| links      | string[] |                          | All links                                          |
| linkid     | number   |                          | Link id                                            |

# Example

![DSL Find View](../../../static/img/find-view.png)
