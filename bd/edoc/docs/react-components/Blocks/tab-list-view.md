---
sidebar_position: 5
---

# Tab List View

Tab List View Component shows the tabs and also manages them.

# Location

It can be find at `src/blocks/tab-list`

# Usage

``` jsx Min List View
import { TabList } from "src/blocks/tab-list/tab-list"

export default function MyReactPage() {
  return (
    <TabList
        random={random}
        activeTabId={active}
        openTabs={openTabs}
        onDragEnded={(dragEvent, updateList) => {
            setOpenTabs((tabs) => updateList(tabs))
        }}
        optionsProps={{
            label: 'Options',
            enabled: true
        }}
        addProps={{
            enabled: false
        }}
        styles={{
            tabListStyle: 'flt-tab-list',
        }}
        tabCanClose={true}
        draggable={true}
        onTabClick={(tab) => {
            setActive(tab.id)
        }}
        onTabClose={closeTab}
    />
  );
}
```
# Props

Props | type | default | Description
--- | --- | --- | ---
random | string | '' | Random string
optionsProps | object | `{label?: string, enabled?: boolean}` | To show options or not and the label 
addProps | object | `{label?: string, enabled?: boolean}` | Able to add tabs or not
tabCanClose | boolean | true | Tab can close or not
styles | object | `{tabListStyle?: string}`; | To change the style of tabs just pass the class in tabListStyle
draggable | boolean | false | Tab can be draggable or not
activeTabId | number |  | Active tab id
openTabs | TabData[] |  | Open tabs data
onDragEnded | void |  | When the draging ends this function is called
onTabClick | void |  | Clicking on tab calls this function
onTabClose | void |  | Closing the tab calls this function
openNewTab | void |  | Opening a new tab calls this function
onDropOutside | void |  | If the tab is dragged outside this function is called

# TabData Props

Props | type | default | Description
--- | --- | --- | ---
id | string | '' | id 
form | string | | Form name 
mode | string | | Opened mode
label | string |  | Label of tab
icon | unknown | | Icon for the tab
componentProps | `Record<string,unknown>` | | Card props
[key: string] | unknown |  |

# Example

![DSL List View](../../../static/img/tab-list.png)
