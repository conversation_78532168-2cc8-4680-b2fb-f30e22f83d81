---
sidebar_position: 1
---

# DSL Card View

DSL Card View Component shows the card view of a form.
This components can be used to open dsl froms in multiple forms view (basically its a form renderer like formik which uses DSL to draw the forms)

# Location

It can be find at `src/blocks/dsl-card-view`

# Usage
``` jsx DSL Card View
import DSLCardView from "src/blocks/dsl-card-view/dsl-card-view"

export default function MyReactPage() {
  return (
    <DSLCardView
      link='patient'
      links={['patient']}
      linkid={{patient: 1}}
      onSaved={(fd: TabData, ref)=>{}}
      closeTab={(id: string, ref?: unknown) => { }}
      key={'patient_insurance_1'}
      isActive={true}
      isParentActive={true}
      xid={1}
      card={'read'}
      form={'patient_insurance'}
      ddRef={(ref) => {
      }}
    />
  );
}
```
# Props

Props | type | default | Description
--- | --- | --- | ---
form | string | '' | Form name
record | number | undefined | If mode is add or addfill it should be undefined otherwise it should be the id
xid | number | undefined | Not necessary
card | string | '' | Expected values are `'read' , 'edit' , 'add' , 'addfill'` the mode of card view.
parent | object | {} | Object of parent if parent exists
addRef | void | () => {} | Returns refrence of dsl
isActive | boolean | false | if the form is active or not
isParentActive | boolean | false |if the parent of the form is active or not
navToURL | string | '' | URL for external linking
urlCallback | void | () => {} | Callback function when URL redirects to form
link | string |  | To link
links | string[] |  | All links
linkid | number |  | Link id 

# Example

![DSL Card View](../../../static/img/card-view.png)




