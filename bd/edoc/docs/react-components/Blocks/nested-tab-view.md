---
sidebar_position: 6
---

# Nested Tabs View

Nested tabs view shows the nested tabs within a specific tabs. 
In nested tabs we can give the form and or the component to render.

# Location

It can be find at `src/blocks/nested-tab-view`

# Description

In the given usage example it shows that nested tab view getting an array of tabs that is `map` and it shows that label of tab and the nested tabs objects are against in `view` key.
if a nested tab is a form it should have form key so it will render the form if we want to render a component in a nested tab we use a `component` object as shown in the example

# Usage

``` jsx Nested Tab View
import { NestedTabView } from "src/blocks/nested-tab-view/nested-tab-view"
const BardCodeScanner = () => <div style={{width: '100%', height: '100%'}}>Start Product Scan?</div>
export default function MyReactPage() {
    const nav = useNavigation(props, '/inventory')
	useEffect(()=>{
		if(props.navToURL?.startsWith('/inventory')){
			props.setActiveTab('inventory')
		}
	}, [props.navToURL])

	const map = useMemo(() => [
		{
			label: 'Management',
			id: 'management',
			view: [
				{
					label: 'Item list',
					id: 'item',
					form: 'patient'
				},
				{
					label: 'Category, Subcategory Lists',
					id: 'category',
					form: 'encounter'
				},
				{
					label: 'Bin List',
					id: 'bin',
					form: 'user'
				},
				{
					label: 'Supplier / Vendor list',
					id: 'vendor',
					form: 'physician'
				}
			]
		},
		{
			label: 'Transactions',
			id: 'transactions',
			view: [
				{
					label: 'Inventory Ledger',
					id: 'ledger',
					form: 'inventory'
				},
				{
					label: 'Item Receipt',
					id: 'receipt',
					form: 'ongoing'
				},
				{
					label: 'Transfer/Movements',
					id: 'transfer',
					form: 'assessment'
				},
				{
					label: 'Adjustments',
					id: 'adjustmennts',
					form: 'lab'
				},
				{
					label: 'Barcode scanner for receipt, movement',
					id: 'scanner',
					component: {
						renderComponent: BardCodeScanner,
						componentProps: {},
						path: '/barcode_scanner'

					}
				}
			]
		},
		{
			label: 'Reports / Printing',
			id: 'reports_printing',
			view: [
				{
					label: 'Inventory-on-hand',
					id: 'on_hand',
					form: 'site'
				},
				{
					label: 'Inventory Valuation',
					id: 'valution',
					form: 'unit'
				},
				{
					label: 'Ledger details',
					id: 'ledger',
					form: 'lab'
				}
			]
		}
	], [])
	// for overriding the level style
	const levelStyle = {levels: {}, dsl:{}}
  return (<NestedTabView {...{ ...props, ...nav, map, levelStyle }} />)
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
isActive | boolean | false | if the form is active or not
isParentActive | boolean | false |if the parent of the form is active or not
navToURL | string | '' | URL for external linking
urlCallback | void | () => {} | Callback function when URL redirects to form

# Example

![Nested Tab View](../../../static/img/nested-tabs.png)
