---
sidebar_position: 7
---

# Portal Modal

Protal Modal is component that makes modal and gets a promise and when the promise is rejected or resolved it cloeses the modal and returns the resultant data.
It can be openend anywhere.

# Location

It can be find at `src/blocks/portal-modal`

# Description

In the given usage example it shows that the createPortalModal gets the model renderer and modal props can be empty and then a object with a parent and resolve and reject keys exists in a promise which returns the component that the promise is reolved or not.

# Usage

``` jsx Portal Modal
import { createPortalModal } from "src/blocks/portal-modal/portal-modal";
export default function MyReactPage() {
   const promise = { resolve, reject }
    createPortalModal(SelectIntakePopOver, {}, { parent, promise }).catch( () => promise.reject({success: false, error: "Unexprected Error"}) )
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
onRequestClose | void | () => {} | When closing the modal it calls this function
onAfterOpen | void | () => {} | After opening the modal this function is called


# Example

![Nested Tab View](../../../static/img/portal-modal.png)
