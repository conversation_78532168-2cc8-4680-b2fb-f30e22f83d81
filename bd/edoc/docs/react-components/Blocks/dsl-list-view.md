---
sidebar_position: 4
---

# Min List View

Min List View Component shows the items present in a form in a list view.
It is the combination of DSL Grid and DSL Find View.

# Location

It can be find at `src/blocks/dsl-list-view`

# Usage

``` jsx Min List View
import DSLListView from "src/blocks/dsl-list-view/dsl-list-view"

export default function MyReactPage() {
  let linkMap = {
    link='patient' // If you want to limit data againts particular link
    links={['patient']}
    linkid={{patient: 1}}
  }
  return (
    <DSLListView 
        label={'Patient'}
        hideFilter={true}
        key={`patient:list`}
        form={'patient'}
        rowClicked={(event: TabData) => {}}
        onSaved={(fd: TabData, ref)=>{}}
        closeTab={(id: string, ref?: unknown) => { }}
        {...linkMap}
        linkMap={linkMap}
        isActive
        isParentActive={props.isActive}
        canAdd={true}
    />
  );
}
```
# Props

Props | type | default | Description
--- | --- | --- | ---
form | string | '' | Form name
record | number | undefined | If mode is add or addfill it should be undefined otherwise it should be the id
xid | number | undefined | Not necessary
card | string | '' | Expected values are `'read' , 'edit' , 'add' , 'addfill'` the mode of card view.
parent | object | {} | Object of parent if parent exists
addRef | void | () => {} | Returns refrence of dsl
isActive | boolean | false | if the form is active or not
isParentActive | boolean | false |if the parent of the form is active or not
navToURL | string | '' | URL for external linking
urlCallback | void | () => {} | Callback function when URL redirects to form
link | string |  | To link
links | string[] |  | All links
linkid | number |  | Link id 

# Example

![DSL List View](../../../static/img/grid-view.png)
