---
sidebar_position: 12
---

# Timeline Range Filters

Timeline Range Filters for filtering in timeline.

# Location

It can be find at `src/components/timeline/control/timeline-range-filter`

# Usage

``` jsx Timeline Range Filter
import { TimeRangeFilter } from "src/components/timeline/control/timeline-range-filter";

export default function MyReactPage() {
    return  <TimeRangeFilter value={timeFilter} onChange={updateTimeLineFilter} />
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
value | TimeLineRangeFilterOptions | | Value of inputs
onChange | (object: TimeLineRangeFilterOptions) => void | | onChange is triggered when the value of inputs change

# Props TimeLineRangeFilterOptions

Props | type | default | Description
--- | --- | --- | ---
duraton | number | | Value of duration input
unit | string | | Units can be `months` , `days` , `years` 

# Example

![Timeline Range Filters](../../../static/img/timeline-range-filters.png)
