---
sidebar_position: 16
---

# Widget Container

Snapshot Widget Container component is a container for all snapshot widgets that keep them in line and intact

# Location

It can be find at `src/components/wgts/wgt-container`

# Description
In the given usage example `SnapshotWgtsContainer` component is working as HOC component that is expecting all data and showing snapshot widgets component accordingly

# Usage

``` jsx Snapshot Widget
import { SnapshotWgtsContainer } from "src/components/wgts/wgt-container/wgt-container";

export default function MyReactPage() {
    return  <SnapshotWgtsContainer wgts={wgts} parentProps={props} tabActions={tabActions} linkMap={linkMap} updateNeed={updateNeed}/>
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
wgts | WgtData | | Widgets Data
parentProps | `Record<string, unknown>` | | 
tabActions |  TabViewActions | | Tabactions that can be performed
updateNeed | string | | 
linkMap | DSLDrawLinkMap | | linkMap

# Props DSLDrawLinkMap

Props | type | default | Description
--- | --- | --- | ---
link | string |  | To link
links | string[] |  | All links
linkid | number |  | Link id 

# Props WgtData

Props | type | default | Description
--- | --- | --- | ---
label | string |  | Label For Widget
component | string[] |  | Component for widget
rkey | number |  | Relational key 
open | `{form: string, mode: string, link: string}` |  | 

# Example

![Widget Container](../../../static/img/widget-container.png)
