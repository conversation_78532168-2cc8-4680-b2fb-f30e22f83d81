---
sidebar_position: 11
---

# Timeline Button

Timeline Button shows above on the time line

# Location

It can be find at `src/components/timeline/control/timeline-btn`

# Usage

``` jsx Timeline Action Control
import { TimeLineButton } from "src/components/timeline/control/timeline-btn";

export default function MyReactPage() {
    return  <TimeLineButton label="Pharmacy" sub="(P)" rkey="pharmacy" onTypeClick={onTypeClick} selectedTypes={selectedTypes} />
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
label | string | | Label of button
sub | string | | Subtext in button that shows below the label
rkey | string | | Key identifier
onTypeClick | void | | Clicking on button triggers this function
selectedTypes | string[] | | Selected types

# Example

![Timeline Buttons](../../../static/img/timeline-btn.png)
