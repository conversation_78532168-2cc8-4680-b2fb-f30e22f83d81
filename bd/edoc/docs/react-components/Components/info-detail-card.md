---
sidebar_position: 8
---

# Info Detail Card

Info detail card have two types of card which have different types of ui which are shown in example.

# Location

It can be find at `src/components/cards/info-detail-card`

# Usage

``` jsx Info Detail Card
import { InfoCountCard } from "src/components/cards/info-detail-card/info-detail-card";

export default function MyReactPage() {
    return <InfoCountCard 
                key={'fills_dispensed_8'}
                label="Fills Dispensed"
                type='emp'
                data={}
                value={8}
                dataLoc={(data: Record<string, unknown>) => "8"}
            />
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
label | string | | Label for card
type | string | | style of card can be `card` and `emp`
data | `Record<string, unknown>` | | Data to show on card
value | string | | if value is provided data and dataLoc props are ignored
dataLoc | (data: `Record<string, unknown>`) => string | If value props is not present dataLoc function is excuted to get the value and data prop is made avaible to dataLoc function through arg, any kinda of data modifcation or mutation could be done using this function i.e date format| 




# Example For Type Info Card

![Info Detail Card](../../../static/img/info-card.png)

# Example For Type EMP Card

![Info Detail Card](../../../static/img/emp-card.png)



