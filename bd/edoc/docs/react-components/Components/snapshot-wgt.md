---
sidebar_position: 15
---

# Snapshot Widget

Snapshot Widget component shows different kind of widgets in snapshot

# Location

It can be find at `src/components/wgts/snapshot-wgt`

# Description
In the given usage example `SnapshotWgt` component is working as HOC component that gets a component that needs to render within a widget

# Usage

``` jsx Snapshot Widget
import { SnapshotWgt } from "src/components/snapshot-wgt/snapshot-wgt";

export default function MyReactPage() {
    return  (
        <SnapshotWgt
            key={wgt.rkey}
            title={wgt.label}
            provided={provided}
            snapshot={snapshot}
            wgtData={wgt}
            tabActions={props.tabActions}
            linkMap={props.linkMap}
        >
            <wgt.component
                {...props.parentProps}
                tabActions={props.tabActions}
                updateNeed={props.updateNeed}
                linkMap={props.linkMap}
                index={index}
            />
        </SnapshotWgt>
    )
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
title | string | | Title for widget
provided | object | | 
snapshot | object | | Snapshot data
wgtData | object | | Widget data to show
tabActions | object | | Tabactions to perform
linkMap | DSLDrawLinkMap | | linkMap

# Props DSLDrawLinkMap

Props | type | default | Description
--- | --- | --- | ---
link | string |  | To link
links | string[] |  | All links
linkid | number |  | Link id 

# Example

![Snapshot Widget](../../../static/img/snapshot-widget.png)
