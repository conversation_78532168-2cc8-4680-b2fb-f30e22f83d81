---
sidebar_position: 9
---

# Warning Card

Warning card is used to show warning in a card view 

# Location

It can be find at `src/components/cards/warning-card`

# Usage

``` jsx Warning Card
import { WarningCard } from "src/components/cards/warning-card/warning-card";

export default function MyReactPage() {
    return  <WarningCard warning="Primary contact is wife, she prefers to be SMSed over called"/>
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
warning | string | | Text to show as a warning in a card

# Example

![Warning Card](../../../static/img/warning-card.png)




