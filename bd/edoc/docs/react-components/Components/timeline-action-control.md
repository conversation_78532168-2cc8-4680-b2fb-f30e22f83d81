---
sidebar_position: 10
---

# Timeline Action Control

Timeline Action Control components that shows the action buttons which acts on timeline.

# Location

It can be find at `src/components/timeline/control/timeline-action-control`

# Usage

``` jsx Timeline Action Control
import { TimeLineActionControl } from "src/components/timeline/control/timeline-action-control";

export default function MyReactPage() {
    return  <TimeLineActionControl onClear={onClear} onApply={onApply} />
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
onClear | void | | This function is triggered when Clear button is called
onApply | void | | This function is triggered when Apply button is called

# Example

![Timeline Action Control](../../../static/img/timeline-action-control.png)




