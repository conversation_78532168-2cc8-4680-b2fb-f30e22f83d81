---
sidebar_position: 14
---

# Timeline Popover

Timeline popover is a modal that shows upon a timeline when clicks on a specific timeline 

# Location

It can be find at `src/components/timeline/timeline-popover`

# Usage

``` jsx Timeline Popover
import { TimeLinePopover } from "src/components/timeline/timeline-popover/timeline-popover";

export default function MyReactPage() {
    return  (
        <TimeLinePopover
            data={pod.data}
            anchorEl={pod.anchorEl}
            open={popoverOpen}
            onClose={() => {
                setPopoverOpen(false);
            }}
            onOpen={
                tabActions.openTab
            }
		/>
    )
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
onClose | void | | This event is triggered when popover is closed
anchorEl | HTMLElement | | 
open | boolean | | If popover is open or not
onOpen | (id: string`,` label: string`,` mode: string`,` form: string`,` componentProps: `Record<string, unknown>`) => void; | | This event is triggered when popover is opnened

# Example

![Timeline Popover](../../../static/img/timeline-popover.png)
