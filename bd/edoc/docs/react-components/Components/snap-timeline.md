---
sidebar_position: 13
---

# Snapshot Timeline 

Snapshot timeline component shows timeline in snapshot of patient

# Location

It can be find at `src/components/timeline/snap-timeline`

# Usage

``` jsx Timeline Range Filter
import { SnapTimeLine } from "src/components/timeline/snap-timeline/snap-timeline";

export default function MyReactPage() {
    return  (
        <SnapTimeLine key={updateNeed} items={timelineData} onItemClick={(ic, el) => {
                setPod({
                    data: ic,
                    anchorEl: el.currentTarget
                })
                setPopoverOpen(true)
            }}
        />
    )
}
```

# Props

Props | type | default | Description
--- | --- | --- | ---
items | TimeLineItem[] | | Timeline items
onItemClick | (ic: TimeLineItem, el: SyntheticEvent) => void | | onItemClick is triggerd when item is clicked

# Props TimeLineItem

Props | type | default | Description
--- | --- | --- | ---
type | string | | Type of timeline item
title | string | | Title for timeline item
event | string | | Event can be `create``,` `update``,``archive`
date | Date | | Date for timeline item
form_id | number | | Form id 
form | string | | Form name
rkey | string | | Relation key
form_data | `Record<string, unknown>` | | Form data for to show in timeline

# Example

![Timeline Range Filters](../../../static/img/snap-timeline.png)
