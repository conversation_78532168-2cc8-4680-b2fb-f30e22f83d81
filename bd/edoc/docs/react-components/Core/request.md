---
sidebar_position: 17
---

# Request

Request is a generic function that is able to call every API and returns if its reolved or not.

# Location

It can be find at `src/core/request`

# Usage

``` jsx Request Function
import { request } from "src/core/request/request";

export default function MyReactPage() {
    request({
		url: '/form/user/?filter=id:' + data
	}).then(resp => {
        setUser(resp.data[0])
	}).catch((err) => {
        console.log("error",err)
    })
    return (
        //.......JSX Code.......
    )
}
```

# Request Parameters

Props | type | default | Description
--- | --- | --- | ---
method | string | | Method of API
url | string | | URL to hit
body | object | | Body object 
headers | object | | headers object
raw | boolean | | Is it raw or not

# Response Object

Props | type | default | Description
--- | --- | --- | ---
success | boolean |  | API was success or not
data | object |  | The response ddata from API

