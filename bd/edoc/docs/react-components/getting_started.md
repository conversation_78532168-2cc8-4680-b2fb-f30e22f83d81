---
sidebar_position: 1
---

# Getting Started With React

## What are Components
In context of this source code components are being refered to react components which perform as single task can be placed anywhere without a need to write any helper functions. i.e. popup, wgt, info card etc.

## What Are Blocks?
Block are UI component that relay on multiple react-components to create a new feature that is much more complex than showing simple data, for example DSLFindView, DSLCardView etc.


## Practices 
* If you are encapsuting a `coffee` code inside ReactJs make sure to use `React.memo` HOC and pass in custom prop compersion function which will ensure that react doesn't interfare with coffee functionality.
* It is recommand that you create a custom hook as i.e `usePatientData` if you need to perform same operations in multiple location.
* Keep each components as simple as possible and number of state being used within a components should not exceed more than ~4. 
* Use of memorization is highly recommanded in case where you have to some calcuation against data before rendering. (One should never memorize every little calculation as memorize it's self is a costy process)
* Make Sure to documents any component being added to `blocks` or `components` directory
* Generic/Resuable components height/magin should be controlled by the its parent component not by the component it self
* Similar components should be bunched together into a a directory i.e. card components `bd/tshomebase/src/components/cards`

## Coding Style
* Creating new component
    * If you are creating a new component 'DSLTabView' file structure should be like this
    ```
        dsl-tab-view (directory)
            |
            ------- dsl-tab-view.tsx
            |
            ------- dsl-tab-view.less
    ```
* For less styling please make use of less scoping
* Make sure to write props types interfaces and they should be placed about the component function
* Make sure to not replicate code
* For Now assests must be placed in `/src/public` directory which also includes icon as well. assets must be catogries in to directories


