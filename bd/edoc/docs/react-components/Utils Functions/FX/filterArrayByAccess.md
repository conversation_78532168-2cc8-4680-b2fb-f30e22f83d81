---
sidebar_position: 2
---

# Filter Array By Access

Filter Array By Access is a function that accepts an array of object and return array with only those object to which user have access.

# Location

It can be find at `src/utils/filterArrayByAccess`

# Description
`filterArrayByAccess` will accept array of object and is used to get only those objects to which user have access. Each object in array should must have path key in it.

Note: <PERSON><PERSON> will have access to all
# Usage

``` jsx Get Type Functions
import { filterArrayByAccess } from "../../../utils/fx";


export default function MyReactPage() {
    let buttons: Record<string, string>[] = [
        {
            label: "Initial",
            form: "assessment",
            path: "patient/*/snap/initial",
        },
        {
            label: "Ongoing",
            form: "ongoing",
            path: "patient/*/snap/ongoing",
        }
    ];s
	buttons = filterArrayByAccess(buttons);
    console.log(buttons) //this will have only those object to whom user have access
    return (
        //.......JSX Code.......
    )
}
```
