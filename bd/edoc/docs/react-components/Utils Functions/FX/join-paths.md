---
sidebar_position: 1
---

# Join Paths

Join Paths is a function accepts path of parent and current path and join them

# Location

It can be find at `src/utils/fx`

# Description
In the given usage shown `joinPaths` is used to join two different paths and join them and make a final url that is also discussed in `Hooks/useNavigation` section

# Usage

``` jsx Join PathsFunctions
import { joinPaths } from "src/utils/fx";

export default function MyReactPage() {
	const joinedPaths = joinPaths(props.navToURL.replace(route, ''), '')
    return (
        //.......JSX Code.......
    )
}
```
