---
sidebar_position: 1
---

# UUID

UUID is a function that generates random unique id.

# Location

It can be find at `src/utils/fx`

# Description
In the given usage shown `uuid` generates random id that is unique and returns it.

# Usage

``` jsx Get New Mode Functions
import { uuid } from 'src/utils/fx'

export default function MyReactPage() {
	const UUID = uuid()
    return (
        //.......JSX Code.......
    )
}
```
