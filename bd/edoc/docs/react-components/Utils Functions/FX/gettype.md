---
sidebar_position: 1
---

# Get Type

Get Type is a function accepts any kind of value and returns its type in string.

# Location

It can be find at `src/utils/fx`

# Description
In the given usage shown `getType` is used to get type of `sampleString` and it will return the type `string`

# Usage

``` jsx Get Type Functions
import { getType } from "src/utils/fx";

export default function MyReactPage() {
	const type = getType('sampleString')
    return (
        //.......JSX Code.......
    )
}
```
