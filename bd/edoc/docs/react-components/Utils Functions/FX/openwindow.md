---
sidebar_position: 1
---

# Open Window

Open window is a function that opens any external url in new tab if the url exist `https`.

# Location

It can be find at `src/utils/fx`

# Description
In the given usage shown `openWindow` is used to open external link in new tab.

# Usage

``` jsx Join PathsFunctions
import { openWindow } from "src/utils/fx";

export default function MyReactPage() {
	 openWindow('https://www.google.com')
    return (
        //.......JSX Code.......
    )
}
```

# Parameters

Props | type | default | Description
--- | --- | --- | ---
link | string | | Link must be string and should have https to make it work 

