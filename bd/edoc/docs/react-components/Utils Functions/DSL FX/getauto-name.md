---
sidebar_position: 1
---

# Get Auto Name

Get Auto Name is a function that returns the auto name from the table. it requires the form name and id.

# Location

It can be find at `src/utils/dsl-fx`

# Description
In the given usage shown `getAutoName` accepts form name and id and return the auto name.

# Usage

``` jsx Get Auto Name Functions
import { getAutoName } from "src/utils/dsl-fx"

export default function MyReactPage() {
    const autoName = getAutoName(form, id)
    return (
        //.......JSX Code.......
    )
}
```
