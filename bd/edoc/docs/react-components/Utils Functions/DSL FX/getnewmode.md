---
sidebar_position: 1
---

# Get New Mode

Get New Mode is a function that returns the auto name from the table. it requires the form name and id.

# Location

It can be find at `src/utils/dsl-fx`

# Description
In the given usage shown `getNewMode` accepts form name and returns if the form can be opened in add or adfill or any other mode.

# Usage

``` jsx Get New Mode Functions
import { getNewMode } from "src/utils/dsl-fx"

export default function MyReactPage() {
        getNewMode(form).then((mode)=>{
		}).catch(error => {
			console.log(error)
		})
    return (
        //.......JSX Code.......
    )
}
```
