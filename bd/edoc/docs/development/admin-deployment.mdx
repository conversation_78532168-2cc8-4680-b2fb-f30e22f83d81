---
sidebar_position: 5
---

# Admin Server Deployments
In order to deploy on admin server user must have ssh access to aws servers.

## Deployment
You can find the deployment script in `REPO_BASE_DIR` named as admin_server.js
You can deploy following application using admin-server script
* fly-builder
* db-manager
* admin-console
To deploy an application run following command
```
node admin_server.js -a={appname}
node admin_server.js -a=admin-console
```
Once deployed you have to manually restart the service by going to respective server.

:::danger Important
It is of utmost importance that these services are only restarted in off-hours, as shutting these services down during working hours may result in a domino effect causing  fly instances (including production) to go down temporarily
:::