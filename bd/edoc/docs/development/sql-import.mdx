---
sidebar_position: 2
---

# SQL Import/Export

In this guide, we'll walk you through the Grunt commands necessary for exporting and importing  SQL files in your Database.

## Importing SQL Files

To Import SQL files to your DB, These are the commands you will have to use:

```
grunt pg-import-post --file={file_name} --database-url={database_url} --sync={sync_mode}
```
So in this command if file flag is not present it will import all the SQL files, Also if you don't give a `database_url` flag it will skip.

Sync Mode:

`sync` flag depends on the type of file you are importing.

If you give: 
```
grunt pg-import-post --database-url={database_url} --sync=mixed
```
it will import only mixed files.

If you give 
```
grunt pg-import-post --database-url={database_url} --sync=full
```
it will import only full files.  

If you give 
```
grunt pg-import-post --database-url={database_url} --sync=all
```
it will import all files.  

If sync flag is missing it will import all files except mixed and full.

# NOTE:

You can import multiple files by giving multiple file names `--file=file_name_1,file_name_2`.


## Exporting Mixed/Full Files

To Export SQL files from your DB, These are the commands you will have to use:

To Export Mixed files:

```
grunt nes-export-mixed --form={form_name}
```


To Export Full files:

```
grunt nes-export-full --form={form_name}
```

`form` flag decides to which form the sql files will be exported.

# NOTE:
To export SQL file from a form, There should be a sync_mode flag in model of CSON file.
