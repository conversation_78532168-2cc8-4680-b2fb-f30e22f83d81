---
sidebar_position: 3
---
# Developing on Remote

Dev server works similar to node<PERSON>, for any change detected in nes codebase, it will reflect on remote and nes/react-dev server will be restarted.
Currently Dev server only works on dev instances/apps, with instance count 1


## Using Dev Server
Run following command to do remote development

```
clara fly watch [APP]
```

Note: Whenever fly will restart, again deploy backend and frontend

## SSh into fly
```
fly ssh console
```

In case there is no fly.toml file it can throw error of no App name you can pass the `-a` arg in this scenario
```
fly ssh console -a=d-eshoaib-nes
```
