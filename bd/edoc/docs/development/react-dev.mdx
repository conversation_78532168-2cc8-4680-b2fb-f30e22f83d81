---
sidebar_position: 7
---

# ReactJS + Coffee
In order to use a react component in coffee script it should first be registered using registerComponent function

```jsx
//React Component
function TestComponent() {
  ....
}

import { registerComponent } from './@coffee'
registerComponent('Demo',TestComponent)
```
`registerComponent` function takes two arguments, component identifier as first args and jsx/tsx component as second argument.

In `.coffee` file you can access and inject this component using the component identifier
```coffee
class IssuesCardView
	load: ->
        #loadComponent('Demo',@el)
        loadComponent('Demo',document.getElementById('issues'))
```

`loadComponent` takes two arguments, first one component identifier and second the dom object in which you want to inject the React component.

You can see all the registered react components by accessing `window.rxRegistry` where as `window.rxDom` maintains the the list/references of all the react components that are currently being rendered on dom. 

You have option to manually unmount a component by calling `window.rxDom[{ComponentIdentifier}].unmount()`

:::danger Caution
Never register two react components with identifier
:::
